<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0fcf7f3b-9d49-4ea8-a5a1-1aa06b2f4807" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.expo-shared/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.expo-shared/assets.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.npmrc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.vscode/settings.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app.json" beforeDir="false" afterPath="$PROJECT_DIR$/app.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Screens/Account_Information/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/Screens/Account_Information/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Screens/Ai/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/Screens/Ai/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Screens/Search/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/Screens/Search/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Screens/UserInbox/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/Screens/UserInbox/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Screens/VoiceAi/_compo/ExpoSpeechVoiceAnimation.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/Screens/VoiceAi/_compo/ExpoSpeechVoiceAnimation.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Screens/VoiceAi/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/Screens/VoiceAi/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/Screens/discover/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/Screens/discover/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/babel.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/babel.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ConsultantS.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/ConsultantS.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/eas.json" beforeDir="false" afterPath="$PROJECT_DIR$/eas.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/metro.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/metro.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/tsconfig.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="2zXDlL7ogq42EW279fe8Ed816I1" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Nityasha",
    "settings.editor.selected.configurable": "AndroidSdkUpdater"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0fcf7f3b-9d49-4ea8-a5a1-1aa06b2f4807" name="Changes" comment="" />
      <created>1751868685888</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751868685888</updated>
    </task>
    <servers />
  </component>
</project>