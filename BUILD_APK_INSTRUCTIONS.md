# Building an APK for Nityasha

This document provides instructions on how to build an APK for the Nityasha app.

## Prerequisites

- Node.js and npm installed
- Expo CLI installed (`npm install -g expo-cli`)
- EAS CLI installed (`npm install -g eas-cli`)
- An Expo account (you're already logged in as "nityashacom")

## Building with EAS Build (Cloud Build)

EAS Build is the recommended way to build your app. It handles all the complexities of building native apps in the cloud.

1. Make sure you're logged in to your Expo account:
   ```
   npx eas whoami
   ```

2. Build the APK using the "apk" profile:
   ```
   npx eas build --platform android --profile apk
   ```

3. Follow the prompts in the terminal. The build will be submitted to EAS Build servers.

4. Once the build is complete, you'll receive a link to download the APK.

## Building Locally (Alternative)

If you prefer to build locally, you can use the following steps:

1. Generate native code:
   ```
   npx expo prebuild
   ```

2. Navigate to the Android directory:
   ```
   cd android
   ```

3. Build the APK using Gradle:
   ```
   ./gradlew assembleRelease
   ```
   or on Windows:
   ```
   gradlew.bat assembleRelease
   ```

4. The APK will be available at:
   ```
   android/app/build/outputs/apk/release/app-release.apk
   ```

## Troubleshooting

- If you encounter Java version compatibility issues, make sure you're using a compatible Java version (JDK 11 is recommended for Android builds).
- If you see errors related to missing modules, try running `npm install` to ensure all dependencies are installed.
- For EAS Build issues, check the EAS Build logs for detailed error messages.

## Additional Resources

- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [Android Build Process](https://docs.expo.dev/build-reference/android-builds/)
- [Troubleshooting EAS Build](https://docs.expo.dev/build-reference/troubleshooting/)
