# Nityasha Chat Backend

This backend provides API endpoints for the Nityasha chat application, specifically for the UserInbox functionality.

## New Single-File Backend (server.js)

A new single-file backend implementation is now available in `server.js`. This file contains all the necessary functionality for the UserInbox component, including:

- Firebase integration for real-time chat
- Unread message calculations
- User online status tracking
- Phone number matching system
- Single API endpoint for all user data

This implementation is designed to be easier to deploy and maintain, as all the code is in a single file.

## Features

- User matching system based on phone numbers
- Unread message calculations
- Online status tracking
- Firebase integration for real-time updates
- Caching with Redis for improved performance

## API Endpoints

### User Inbox Data

```
GET /api/v1/user-inbox/user-inbox-data/:userId
```

Returns all data needed for the UserInbox screen in a single request:
- Matched users (contacts who are also app users)
- Unread message counts
- Total unread count
- User statuses

### Match Contacts

```
POST /api/v1/user-inbox/match-contacts
```

Matches phone numbers with users in the database.

**Request Body:**
```json
{
  "userId": "123",
  "phoneNumbers": ["1234567890", "9876543210"]
}
```

### Update Online Status

```
POST /api/v1/user-inbox/update-online-status
```

Updates a user's online status in both MySQL and Firebase.

**Request Body:**
```json
{
  "userId": "123",
  "isOnline": true
}
```

### Mark Messages as Read

```
POST /api/v1/user-inbox/mark-messages-read
```

Marks messages from a specific sender as read.

**Request Body:**
```json
{
  "userId": "123",
  "senderId": "456"
}
```

### Get Unread Counts

```
GET /api/v1/user-inbox/unread-counts/:userId
```

Returns unread message counts for a user.

### Add Contact

```
POST /api/v1/user-inbox/add-contact
```

Adds a new contact for a user.

**Request Body:**
```json
{
  "userId": "123",
  "contactName": "John Doe",
  "phoneNumber": "1234567890"
}
```

### Get Contacts

```
GET /api/v1/user-inbox/contacts/:userId
```

Returns all contacts for a user.

## Firebase Integration

The backend integrates with Firebase Realtime Database for:
- Real-time online status updates
- Message status tracking
- Unread message counts

## Database Schema

The backend uses the following database tables:
- `users` - User information including online status
- `contacts` - User contacts
- `messages` - Chat messages
- `chat_rooms` - Chat room information
- `user_chats` - User chat relationships
- `user_typing` - User typing status

## Caching

Redis is used for caching:
- User inbox data
- Unread counts
- Contacts

## Setup

1. Run the database setup script:
```sql
source database-setup.sql
```

2. Install dependencies:
```bash
npm install
```

3. Set up Firebase service account:
   - Go to the [Firebase Console](https://console.firebase.google.com/)
   - Select your project (or create a new one)
   - Go to Project Settings > Service accounts
   - Click "Generate new private key"
   - Save the downloaded JSON file as `firebase-service-account.json` in the Backend folder
   - Verify your setup by running `node setup-firebase.js`

4. Start the server:
```bash
node server.js  # For the new single-file backend
# OR
node index.js   # For the original backend
```

## Firebase Cloud Functions

Firebase Cloud Functions are used to sync data between Firebase and MySQL:
- `syncMessages` - Syncs message data
- `syncUserStatus` - Syncs user online status
- `syncUnreadCounts` - Syncs unread counts

To deploy the Firebase functions:
```bash
cd firebase-functions
firebase deploy --only functions
```
