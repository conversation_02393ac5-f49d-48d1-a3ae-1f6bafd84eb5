# Nityasha UserInbox Backend

यह बैकएंड सर्वर Nityasha ऐप के UserInbox पेज के लिए है। इसमें निम्नलिखित फीचर्स शामिल हैं:

- Firebase इंटीग्रेशन
- अनरीड मैसेज कैलकुलेशन
- यूजर ऑनलाइन स्टेटस ट्रैकिंग
- फोन नंबर मैचिंग सिस्टम
- सभी यूजर डेटा के लिए एक ही API एंडपॉइंट

## सेटअप

1. आवश्यक पैकेज इंस्टॉल करें:

```bash
npm install
```

2. Firebase सर्विस अकाउंट सेटअप करें:

- Firebase कंसोल से सर्विस अकाउंट की JSON फाइल डाउनलोड करें
- इस फाइल को `firebase-service-account.json` नाम से प्रोजेक्ट रूट में रखें
- `server.js` में अपने Firebase डेटाबेस URL को अपडेट करें

3. डेटाबेस कॉन्फिगरेशन:

`server.js` में MySQL कनेक्शन डिटेल्स अपडेट करें या निम्न एनवायरनमेंट वेरिएबल्स सेट करें:

- `DB_HOST`
- `DB_USER`
- `DB_PASSWORD`
- `DB_NAME`

## सर्वर चलाना

डेवलपमेंट मोड में चलाने के लिए:

```bash
npm run dev
```

प्रोडक्शन मोड में चलाने के लिए:

```bash
npm start
```

सर्वर डिफॉल्ट रूप से पोर्ट 3000 पर चलेगा। पोर्ट बदलने के लिए `PORT` एनवायरनमेंट वेरिएबल सेट करें।

## API एंडपॉइंट्स

### यूजर इनबॉक्स डेटा
- `GET /api/user-inbox-data/:userId`
- सभी यूजर्स की जानकारी, अनरीड मैसेज काउंट और ऑनलाइन स्टेटस प्राप्त करें

### मैसेज रीड मार्किंग
- `POST /api/mark-messages-read`
- मैसेज को पढ़ा हुआ मार्क करें
- बॉडी: `{ userId, senderId }`

### अनरीड काउंट
- `GET /api/unread-counts/:userId`
- यूजर के लिए अनरीड मैसेज काउंट प्राप्त करें

### ऑनलाइन स्टेटस अपडेट
- `POST /api/update-online-status`
- यूजर ऑनलाइन स्टेटस अपडेट करें
- बॉडी: `{ userId, isOnline, lastSeen }`

### फोन नंबर मैचिंग
- `POST /api/match-contacts`
- फोन नंबर को यूजर्स के साथ मैच करें
- बॉडी: `{ contacts: [{ id, name, phoneNumbers: [] }] }`

### कॉन्टैक्ट्स
- `GET /api/contacts/:userId`
- यूजर के सभी कॉन्टैक्ट प्राप्त करें

### कॉन्टैक्ट जोड़ना
- `POST /api/add-contact`
- नया कॉन्टैक्ट जोड़ें
- बॉडी: `{ userId, contactUserId }`

### चैट हिस्ट्री क्लियर करना
- `POST /api/clear-chat-history`
- यूजर के लिए चैट हिस्ट्री साफ करें
- बॉडी: `{ userId, otherUserId }`

### Firebase सिंक
- `POST /api/firebase-sync`
- Firebase से डेटा सिंक करें
- बॉडी: `{ type, data }`
