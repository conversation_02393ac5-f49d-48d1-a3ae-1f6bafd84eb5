-- Database setup script for <PERSON><PERSON><PERSON><PERSON>t Backend

-- Make sure we're using the right database
USE kzzuezbs_31aa9913123139jmasr;

-- Add firebase_id column to messages table if it doesn't exist
ALTER TABLE messages
ADD COLUMN IF NOT EXISTS firebase_id VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS read_at DATETIME NULL;

-- Create contacts table if it doesn't exist
CREATE TABLE IF NOT EXISTS contacts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  contact_name VARCHAR(255) NOT NULL,
  phone_number VARCHAR(20) NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_phone_number (phone_number)
);

-- Add is_online and last_seen columns to users table if they don't exist
ALTER TABLE users
ADD COLUMN IF NOT EXISTS is_online TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_seen D<PERSON><PERSON><PERSON><PERSON> NULL,
ADD COLUMN IF NOT EXISTS last_message VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS last_message_time VARCHAR(50) NULL;

-- Create user_chats table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_chats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  other_user_id INT NOT NULL,
  room_id VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NULL,
  UNIQUE KEY unique_chat (user_id, other_user_id),
  INDEX idx_user_id (user_id),
  INDEX idx_other_user_id (other_user_id),
  INDEX idx_room_id (room_id)
);

-- Create chat_rooms table if it doesn't exist
CREATE TABLE IF NOT EXISTS chat_rooms (
  id INT AUTO_INCREMENT PRIMARY KEY,
  room_id VARCHAR(255) NOT NULL UNIQUE,
  user1_id INT NOT NULL,
  user2_id INT NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NULL,
  INDEX idx_room_id (room_id),
  INDEX idx_user1_id (user1_id),
  INDEX idx_user2_id (user2_id)
);

-- Create user_typing table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_typing (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  chat_with_user_id INT NOT NULL,
  is_typing TINYINT(1) DEFAULT 0,
  typing_timestamp DATETIME NULL,
  UNIQUE KEY unique_typing (user_id, chat_with_user_id),
  INDEX idx_user_id (user_id),
  INDEX idx_chat_with_user_id (chat_with_user_id)
);

-- Create stored procedure to update user's last message
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS update_user_last_message(
  IN p_user_id INT,
  IN p_last_message VARCHAR(255),
  IN p_last_message_time VARCHAR(50)
)
BEGIN
  UPDATE users
  SET last_message = p_last_message,
      last_message_time = p_last_message_time
  WHERE id = p_user_id;
END //
DELIMITER ;

-- Create stored procedure to mark messages as read
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS mark_messages_as_read(
  IN p_receiver_id INT,
  IN p_sender_id INT
)
BEGIN
  UPDATE messages
  SET status = 'read',
      read_at = NOW()
  WHERE receiver_id = p_receiver_id
    AND sender_id = p_sender_id
    AND status = 'pending';
END //
DELIMITER ;

-- Create stored procedure to get matched contacts
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS get_matched_contacts(
  IN p_user_id INT
)
BEGIN
  SELECT 
    u.id,
    u.username,
    u.email,
    u.is_online,
    u.last_seen,
    u.last_message,
    u.last_message_time,
    c.contact_name,
    c.phone_number
  FROM contacts c
  JOIN users u ON REPLACE(REPLACE(u.email, '+', ''), '-', '') = REPLACE(REPLACE(c.phone_number, '+', ''), '-', '')
  WHERE c.user_id = p_user_id;
END //
DELIMITER ;

-- Create stored procedure to get unread message counts
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS get_unread_message_counts(
  IN p_user_id INT
)
BEGIN
  SELECT 
    sender_id,
    COUNT(*) as unread_count
  FROM messages
  WHERE receiver_id = p_user_id
    AND status = 'pending'
  GROUP BY sender_id;
END //
DELIMITER ;
