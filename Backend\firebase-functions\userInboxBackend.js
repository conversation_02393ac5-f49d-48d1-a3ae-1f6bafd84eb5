/**
 * Comprehensive Backend for UserInbox
 * 
 * This file contains all the backend functionality for the UserInbox page:
 * - Firebase integration
 * - Unread message calculations
 * - User online status tracking
 * - Phone number matching system
 * - Single API endpoint for all user data
 */

const functions = require('firebase-functions');
const admin = require('firebase-admin');
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

// Initialize Firebase Admin SDK
admin.initializeApp();

// Get Firebase Realtime Database reference
const db = admin.database();

// Create Express app
const app = express();
app.use(cors({ origin: true }));
app.use(bodyParser.json());

// Database configuration - replace with your actual database configuration
const mysql = require('mysql2/promise');
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'nityasha',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Constants
const FIREBASE_CHAT_PREFIX = 'user_messages';
const FIREBASE_USER_PREFIX = 'users';
const FIREBASE_CHAT_INFO_PREFIX = 'user_chats';

/**
 * Helper function to clean and format phone numbers
 * @param {string} phoneNumber - The phone number to clean
 * @returns {string} - The cleaned phone number
 */
function cleanPhoneNumber(phoneNumber) {
  if (!phoneNumber) return '';
  
  // Remove all non-numeric characters
  let cleaned = phoneNumber.replace(/\D/g, '');
  
  // If the number starts with a country code, handle it
  // This is a simple example - you may need to adjust for your specific requirements
  if (cleaned.length > 10) {
    // If it has a leading 1 (US/Canada) and is 11 digits, remove the 1
    if (cleaned.length === 11 && cleaned.startsWith('1')) {
      cleaned = cleaned.substring(1);
    }
    // For other country codes, you might want to keep them or handle differently
  }
  
  return cleaned;
}

/**
 * API endpoint to get all user inbox data in a single request
 * This includes:
 * - User information
 * - Unread message counts
 * - Online status
 * - Last seen timestamps
 */
app.get('/api/user-inbox-data/:userId', async (req, res) => {
  const userId = req.params.userId;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }
  
  try {
    // Get a database connection
    const connection = await pool.getConnection();
    
    // Get all users except the current user
    const [users] = await connection.query(
      `SELECT 
        id, 
        username AS name, 
        phone, 
        email, 
        profile_image AS logo, 
        is_online, 
        last_seen, 
        verified,
        (SELECT text FROM messages 
         WHERE (sender_id = users.id AND receiver_id = ?) 
            OR (sender_id = ? AND receiver_id = users.id) 
         ORDER BY created_at DESC LIMIT 1) AS last_message,
        (SELECT created_at FROM messages 
         WHERE (sender_id = users.id AND receiver_id = ?) 
            OR (sender_id = ? AND receiver_id = users.id) 
         ORDER BY created_at DESC LIMIT 1) AS last_message_time
      FROM users 
      WHERE id != ? 
      ORDER BY last_message_time DESC NULLS LAST
      LIMIT 500`,
      [userId, userId, userId, userId, userId]
    );
    
    // Get unread message counts for this user
    const [unreadCounts] = await connection.query(
      `SELECT 
        sender_id, 
        COUNT(*) as unread_count
      FROM messages 
      WHERE receiver_id = ? AND status = 'pending' 
      GROUP BY sender_id`,
      [userId]
    );
    
    // Format the unread counts
    const unreadCountsMap = {};
    let totalUnread = 0;
    
    unreadCounts.forEach(row => {
      unreadCountsMap[row.sender_id] = row.unread_count;
      totalUnread += row.unread_count;
    });
    
    // Get user statuses from Firebase
    const userStatusesSnapshot = await db.ref(`${FIREBASE_USER_PREFIX}`).once('value');
    const firebaseStatuses = userStatusesSnapshot.val() || {};
    
    // Map the users with their online status and format the response
    const mappedUsers = users.map(user => {
      const firebaseUser = firebaseStatuses[user.id] || {};
      const firebaseStatus = firebaseUser.status || {};
      
      // Determine if the user is online based on both MySQL and Firebase
      const isOnline = user.is_online === 1 || firebaseStatus.state === 'online';
      
      // Get the last seen timestamp (prefer Firebase if available)
      const lastSeen = firebaseStatus.lastSeen || user.last_seen || null;
      
      return {
        id: user.id,
        name: user.name || 'Unknown',
        phone: user.phone || '',
        email: user.email || '',
        logo: user.profile_image || 'https://via.placeholder.com/150',
        is_online: isOnline ? 1 : 0,
        last_seen: lastSeen,
        verified: user.verified === 1,
        last_message: user.last_message || '',
        last_message_time: user.last_message_time || null,
        unread_count: unreadCountsMap[user.id] || 0
      };
    });
    
    // Release the connection
    connection.release();
    
    // Return the response
    return res.json({
      users: mappedUsers,
      unreadCounts: unreadCountsMap,
      totalUnread: totalUnread,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Error fetching user inbox data:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * API endpoint to mark messages as read
 */
app.post('/api/mark-messages-read', async (req, res) => {
  const { userId, senderId } = req.body;
  
  if (!userId || !senderId) {
    return res.status(400).json({ error: 'User ID and sender ID are required' });
  }
  
  try {
    // Update in MySQL
    const connection = await pool.getConnection();
    await connection.query(
      'UPDATE messages SET status = "read", read_at = NOW() WHERE receiver_id = ? AND sender_id = ? AND status = "pending"',
      [userId, senderId]
    );
    connection.release();
    
    // Update in Firebase
    const chatId = [userId, senderId].sort().join('_');
    
    // Get all messages in this chat
    const messagesRef = db.ref(`${FIREBASE_CHAT_PREFIX}/${chatId}`);
    const snapshot = await messagesRef.once('value');
    const messages = snapshot.val() || {};
    
    // Prepare batch updates
    const updates = {};
    
    // Update unread count in user_chats
    updates[`${FIREBASE_CHAT_INFO_PREFIX}/${userId}/${senderId}/unread`] = 0;
    
    // Mark all messages from senderId as read
    Object.entries(messages).forEach(([messageId, message]) => {
      if (message.senderId === senderId && message.receiverId === userId && (!message.isRead || message.status !== 'read')) {
        updates[`${FIREBASE_CHAT_PREFIX}/${chatId}/${messageId}/isRead`] = true;
        updates[`${FIREBASE_CHAT_PREFIX}/${chatId}/${messageId}/readAt`] = Date.now();
        updates[`${FIREBASE_CHAT_PREFIX}/${chatId}/${messageId}/status`] = 'read';
      }
    });
    
    // Apply all updates in a single operation
    if (Object.keys(updates).length > 0) {
      await db.ref().update(updates);
    }
    
    return res.json({ success: true });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * API endpoint to get unread message counts
 */
app.get('/api/unread-counts/:userId', async (req, res) => {
  const userId = req.params.userId;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }
  
  try {
    // Get unread counts from MySQL
    const connection = await pool.getConnection();
    const [results] = await connection.query(
      `SELECT 
        sender_id, 
        COUNT(*) as unread_count
      FROM messages 
      WHERE receiver_id = ? AND status = 'pending' 
      GROUP BY sender_id`,
      [userId]
    );
    connection.release();
    
    // Format the response
    const pendingMessages = {};
    let totalPending = 0;
    
    results.forEach(row => {
      pendingMessages[row.sender_id] = row.unread_count;
      totalPending += row.unread_count;
    });
    
    return res.json({
      pendingMessages,
      totalPending
    });
  } catch (error) {
    console.error('Error fetching unread counts:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * API endpoint to update user online status
 */
app.post('/api/update-online-status', async (req, res) => {
  const { userId, isOnline, lastSeen } = req.body;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }
  
  try {
    // Update in MySQL
    const connection = await pool.getConnection();
    await connection.query(
      'UPDATE users SET is_online = ?, last_seen = ? WHERE id = ?',
      [isOnline ? 1 : 0, lastSeen || new Date(), userId]
    );
    connection.release();
    
    // Update in Firebase
    const userStatusRef = db.ref(`${FIREBASE_USER_PREFIX}/${userId}/status`);
    await userStatusRef.update({
      state: isOnline ? 'online' : 'offline',
      lastSeen: lastSeen || Date.now()
    });
    
    return res.json({ success: true });
  } catch (error) {
    console.error('Error updating online status:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * API endpoint to match phone numbers with users
 */
app.post('/api/match-contacts', async (req, res) => {
  const { contacts } = req.body;
  
  if (!contacts || !Array.isArray(contacts)) {
    return res.status(400).json({ error: 'Contacts array is required' });
  }
  
  try {
    // Clean phone numbers
    const cleanedContacts = contacts.map(contact => ({
      ...contact,
      phoneNumbers: contact.phoneNumbers.map(phone => cleanPhoneNumber(phone))
    }));
    
    // Extract all phone numbers for the query
    const allPhoneNumbers = [];
    cleanedContacts.forEach(contact => {
      contact.phoneNumbers.forEach(phone => {
        if (phone) allPhoneNumbers.push(phone);
      });
    });
    
    if (allPhoneNumbers.length === 0) {
      return res.json({ matches: [] });
    }
    
    // Find matching users in the database
    const connection = await pool.getConnection();
    const [users] = await connection.query(
      `SELECT 
        id, 
        username AS name, 
        phone, 
        email, 
        profile_image AS logo, 
        is_online, 
        last_seen, 
        verified
      FROM users 
      WHERE REPLACE(REPLACE(REPLACE(phone, '-', ''), ' ', ''), '+', '') IN (?)`,
      [allPhoneNumbers]
    );
    connection.release();
    
    // Map users to their phone numbers
    const phoneToUser = {};
    users.forEach(user => {
      const cleanedPhone = cleanPhoneNumber(user.phone);
      if (cleanedPhone) {
        phoneToUser[cleanedPhone] = user;
      }
    });
    
    // Match contacts with users
    const matches = [];
    cleanedContacts.forEach(contact => {
      contact.phoneNumbers.forEach(phone => {
        if (phone && phoneToUser[phone]) {
          matches.push({
            contactId: contact.id,
            contactName: contact.name,
            user: phoneToUser[phone]
          });
        }
      });
    });
    
    return res.json({ matches });
  } catch (error) {
    console.error('Error matching contacts:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * API endpoint to get all contacts for a user
 */
app.get('/api/contacts/:userId', async (req, res) => {
  const userId = req.params.userId;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }
  
  try {
    // Get all contacts from the database
    const connection = await pool.getConnection();
    const [contacts] = await connection.query(
      `SELECT 
        c.id, 
        c.user_id, 
        c.contact_user_id, 
        u.username AS name, 
        u.phone, 
        u.email, 
        u.profile_image AS logo, 
        u.is_online, 
        u.last_seen, 
        u.verified
      FROM contacts c
      JOIN users u ON c.contact_user_id = u.id
      WHERE c.user_id = ?`,
      [userId]
    );
    connection.release();
    
    return res.json(contacts);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * API endpoint to add a contact
 */
app.post('/api/add-contact', async (req, res) => {
  const { userId, contactUserId } = req.body;
  
  if (!userId || !contactUserId) {
    return res.status(400).json({ error: 'User ID and contact user ID are required' });
  }
  
  try {
    // Check if the contact already exists
    const connection = await pool.getConnection();
    const [existingContacts] = await connection.query(
      'SELECT * FROM contacts WHERE user_id = ? AND contact_user_id = ?',
      [userId, contactUserId]
    );
    
    if (existingContacts.length > 0) {
      connection.release();
      return res.json({ success: true, message: 'Contact already exists' });
    }
    
    // Add the contact
    await connection.query(
      'INSERT INTO contacts (user_id, contact_user_id, created_at) VALUES (?, ?, NOW())',
      [userId, contactUserId]
    );
    connection.release();
    
    return res.json({ success: true });
  } catch (error) {
    console.error('Error adding contact:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * API endpoint to clear chat history
 */
app.post('/api/clear-chat-history', async (req, res) => {
  const { userId, otherUserId } = req.body;
  
  if (!userId || !otherUserId) {
    return res.status(400).json({ error: 'User ID and other user ID are required' });
  }
  
  try {
    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, otherUserId].sort().join('_');
    
    // Clear messages in Firebase
    await db.ref(`${FIREBASE_CHAT_PREFIX}/${chatRoomId}`).remove();
    
    // Clear chat info in Firebase
    await db.ref(`${FIREBASE_CHAT_INFO_PREFIX}/${userId}/${otherUserId}`).remove();
    
    // Clear messages in MySQL (optional - you might want to keep them for records)
    // Uncomment if you want to delete from MySQL as well
    /*
    const connection = await pool.getConnection();
    await connection.query(
      `DELETE FROM messages 
       WHERE (sender_id = ? AND receiver_id = ?) 
          OR (sender_id = ? AND receiver_id = ?)`,
      [userId, otherUserId, otherUserId, userId]
    );
    connection.release();
    */
    
    return res.json({ success: true });
  } catch (error) {
    console.error('Error clearing chat history:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Firebase webhook to sync data with MySQL
 */
app.post('/api/firebase-sync', async (req, res) => {
  const { type, data } = req.body;
  
  if (!type || !data) {
    return res.status(400).json({ error: 'Type and data are required' });
  }
  
  try {
    const connection = await pool.getConnection();
    
    if (type === 'message') {
      // Sync a new message
      const { id, senderId, receiverId, text, timestamp, status } = data;
      
      // Check if message already exists
      const [existingMessages] = await connection.query(
        'SELECT * FROM messages WHERE firebase_id = ?',
        [id]
      );
      
      if (existingMessages.length === 0) {
        // Insert new message
        await connection.query(
          `INSERT INTO messages 
            (firebase_id, sender_id, receiver_id, text, created_at, status) 
           VALUES (?, ?, ?, ?, FROM_UNIXTIME(?/1000), ?)`,
          [id, senderId, receiverId, text, timestamp, status || 'pending']
        );
      } else {
        // Update existing message
        await connection.query(
          `UPDATE messages 
           SET status = ?, read_at = ? 
           WHERE firebase_id = ?`,
          [
            status || existingMessages[0].status,
            status === 'read' ? new Date() : existingMessages[0].read_at,
            id
          ]
        );
      }
    } else if (type === 'status') {
      // Sync user status
      const { userId, isOnline, lastSeen } = data;
      
      // Update user status
      await connection.query(
        'UPDATE users SET is_online = ?, last_seen = ? WHERE id = ?',
        [isOnline ? 1 : 0, new Date(lastSeen), userId]
      );
    } else if (type === 'unread') {
      // Sync unread counts
      const { userId, otherUserId, unreadCount } = data;
      
      // We don't need to store this in MySQL as it's calculated on-the-fly
      // But you could store it if needed
    }
    
    connection.release();
    return res.json({ success: true });
  } catch (error) {
    console.error('Error syncing data with MySQL:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Firebase Cloud Function to sync message data with MySQL when a message is created or updated
 */
exports.syncMessages = functions.database.ref(`${FIREBASE_CHAT_PREFIX}/{chatId}/{messageId}`)
  .onWrite(async (change, context) => {
    const { chatId, messageId } = context.params;
    
    // If the message was deleted, we don't need to sync
    if (!change.after.exists()) {
      return null;
    }
    
    // Get the message data
    const messageData = change.after.val();
    
    try {
      // Send the data to the API endpoint
      const axios = require('axios');
      await axios.post('https://your-api-domain.com/api/firebase-sync', {
        type: 'message',
        data: {
          id: messageId,
          chatId: chatId,
          senderId: messageData.senderId,
          receiverId: messageData.receiverId,
          text: messageData.text,
          timestamp: messageData.timestamp,
          status: messageData.status || 'pending',
          isRead: messageData.isRead || false,
          readAt: messageData.readAt || null
        }
      });
      
      // Update unread count for the receiver
      if (messageData.senderId && messageData.receiverId) {
        const userChatRef = db.ref(`${FIREBASE_CHAT_INFO_PREFIX}/${messageData.receiverId}/${messageData.senderId}`);
        const snapshot = await userChatRef.child('unread').once('value');
        const currentUnread = snapshot.val() || 0;
        
        // Only increment if the message is not read
        if (!messageData.isRead) {
          await userChatRef.update({
            unread: currentUnread + 1
          });
        }
      }
      
      console.log(`Successfully synced message ${messageId} in chat ${chatId}`);
      return null;
    } catch (error) {
      console.error(`Error syncing message ${messageId}:`, error);
      return null;
    }
  });

/**
 * Firebase Cloud Function to sync user online status with MySQL when a user's status changes
 */
exports.syncUserStatus = functions.database.ref(`${FIREBASE_USER_PREFIX}/{userId}/status`)
  .onWrite(async (change, context) => {
    const { userId } = context.params;
    
    // If the status was deleted, we don't need to sync
    if (!change.after.exists()) {
      return null;
    }
    
    // Get the status data
    const statusData = change.after.val();
    
    try {
      // Send the data to the API endpoint
      const axios = require('axios');
      await axios.post('https://your-api-domain.com/api/firebase-sync', {
        type: 'status',
        data: {
          userId: userId,
          isOnline: statusData.state === 'online',
          lastSeen: statusData.lastSeen || Date.now()
        }
      });
      
      console.log(`Successfully synced status for user ${userId}`);
      return null;
    } catch (error) {
      console.error(`Error syncing status for user ${userId}:`, error);
      return null;
    }
  });

/**
 * Firebase Cloud Function to sync unread counts with MySQL when a user's unread count changes
 */
exports.syncUnreadCounts = functions.database.ref(`${FIREBASE_CHAT_INFO_PREFIX}/{userId}/{otherUserId}/unread`)
  .onWrite(async (change, context) => {
    const { userId, otherUserId } = context.params;
    
    // Get the unread count
    const unreadCount = change.after.exists() ? change.after.val() : 0;
    
    try {
      // Send the data to the API endpoint
      const axios = require('axios');
      await axios.post('https://your-api-domain.com/api/firebase-sync', {
        type: 'unread',
        data: {
          userId: userId,
          otherUserId: otherUserId,
          unreadCount: unreadCount
        }
      });
      
      console.log(`Successfully synced unread count for user ${userId} from ${otherUserId}`);
      return null;
    } catch (error) {
      console.error(`Error syncing unread count for user ${userId}:`, error);
      return null;
    }
  });

// Export the Express app as a Firebase Function
exports.api = functions.https.onRequest(app);
