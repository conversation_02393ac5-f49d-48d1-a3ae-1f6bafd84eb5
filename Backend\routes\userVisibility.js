const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');

// Get user visibility status
router.get('/:userId/visibility/:currentUserId', async (req, res) => {
  try {
    const { userId, currentUserId } = req.params;
    
    // Check if the user is visible to the current user
    const visibilityRef = admin.database().ref(`user_visibility/${userId}/visible_to/${currentUserId}`);
    const snapshot = await visibilityRef.once('value');
    const visibilityData = snapshot.val();
    
    if (visibilityData && visibilityData.visible === true) {
      return res.status(200).json({
        visible: true,
        timestamp: visibilityData.timestamp
      });
    } else {
      return res.status(200).json({
        visible: false
      });
    }
  } catch (error) {
    console.error('Error getting user visibility:', error);
    return res.status(500).json({ error: 'Failed to get user visibility status' });
  }
});

// Set user as visible to a specific user
router.post('/:userId/visible_to/:targetUserId', async (req, res) => {
  try {
    const { userId, targetUserId } = req.params;
    
    // Set the user as visible to the target user
    const visibilityRef = admin.database().ref(`user_visibility/${userId}/visible_to/${targetUserId}`);
    await visibilityRef.set({
      visible: true,
      timestamp: admin.database.ServerValue.TIMESTAMP
    });
    
    return res.status(200).json({
      success: true,
      message: `User ${userId} is now visible to user ${targetUserId}`
    });
  } catch (error) {
    console.error('Error setting user visibility:', error);
    return res.status(500).json({ error: 'Failed to set user visibility' });
  }
});

// Remove user visibility to a specific user
router.delete('/:userId/visible_to/:targetUserId', async (req, res) => {
  try {
    const { userId, targetUserId } = req.params;
    
    // Remove the user's visibility to the target user
    const visibilityRef = admin.database().ref(`user_visibility/${userId}/visible_to/${targetUserId}`);
    await visibilityRef.remove();
    
    return res.status(200).json({
      success: true,
      message: `User ${userId} is no longer visible to user ${targetUserId}`
    });
  } catch (error) {
    console.error('Error removing user visibility:', error);
    return res.status(500).json({ error: 'Failed to remove user visibility' });
  }
});

// Clear all visibility settings for a user
router.delete('/:userId/visible_to', async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Remove all visibility settings for the user
    const visibilityRef = admin.database().ref(`user_visibility/${userId}/visible_to`);
    await visibilityRef.remove();
    
    return res.status(200).json({
      success: true,
      message: `All visibility settings cleared for user ${userId}`
    });
  } catch (error) {
    console.error('Error clearing user visibility:', error);
    return res.status(500).json({ error: 'Failed to clear user visibility settings' });
  }
});

module.exports = router;
