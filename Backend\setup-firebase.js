/**
 * Firebase Service Account Setup Helper
 * 
 * This script helps you set up the firebase-service-account.json file
 * required for the backend to connect to Firebase.
 * 
 * Instructions:
 * 1. Go to the Firebase Console (https://console.firebase.google.com/)
 * 2. Select your project
 * 3. Go to Project Settings > Service accounts
 * 4. Click "Generate new private key"
 * 5. Save the downloaded JSON file as "firebase-service-account.json" in the Backend folder
 * 6. Run this script to verify your setup
 */

const fs = require('fs');
const path = require('path');

// Check if firebase-service-account.json exists
const serviceAccountPath = path.join(__dirname, 'firebase-service-account.json');

console.log('Checking for Firebase service account file...');

if (fs.existsSync(serviceAccountPath)) {
  try {
    // Try to parse the file to make sure it's valid JSON
    const serviceAccount = require(serviceAccountPath);
    
    // Check if it has the required fields
    if (serviceAccount.type === 'service_account' && 
        serviceAccount.project_id && 
        serviceAccount.private_key && 
        serviceAccount.client_email) {
      
      console.log('✅ Firebase service account file found and is valid!');
      console.log(`Project ID: ${serviceAccount.project_id}`);
      console.log(`Client Email: ${serviceAccount.client_email}`);
      
      // Check if the database URL in server.js matches the project ID
      const serverJsPath = path.join(__dirname, 'server.js');
      const serverJs = fs.readFileSync(serverJsPath, 'utf8');
      
      const databaseUrlRegex = /databaseURL: "https:\/\/([^"]+)"/;
      const match = serverJs.match(databaseUrlRegex);
      
      if (match && match[1]) {
        const databaseUrl = match[1];
        if (databaseUrl.startsWith(serviceAccount.project_id)) {
          console.log('✅ Database URL in server.js matches the project ID');
        } else {
          console.log('⚠️ Warning: Database URL in server.js does not match the project ID');
          console.log(`Database URL: ${databaseUrl}`);
          console.log(`Project ID: ${serviceAccount.project_id}`);
          console.log('You may need to update the databaseURL in server.js to match your Firebase project');
        }
      }
      
      console.log('\nYour Firebase setup is ready! You can now start the server with:');
      console.log('node server.js');
    } else {
      console.log('❌ Firebase service account file is not valid!');
      console.log('Make sure you downloaded the correct file from the Firebase Console');
      printInstructions();
    }
  } catch (error) {
    console.log('❌ Error parsing Firebase service account file:');
    console.log(error.message);
    printInstructions();
  }
} else {
  console.log('❌ Firebase service account file not found!');
  printInstructions();
}

function printInstructions() {
  console.log('\nTo set up your Firebase service account:');
  console.log('1. Go to the Firebase Console (https://console.firebase.google.com/)');
  console.log('2. Select your project');
  console.log('3. Go to Project Settings > Service accounts');
  console.log('4. Click "Generate new private key"');
  console.log('5. Save the downloaded JSON file as "firebase-service-account.json" in the Backend folder');
  console.log('6. Run this script again to verify your setup');
}
