const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const mysql = require('mysql2/promise');
const Redis = require('ioredis');

// Database configuration
const dbConfig = {
  host: 'mercury.nityasha.com',
  user: 'kzzuezbs_31aa9913123139jmasr',
  password: 'N4(I9_P9>!lPo:vmT0',
  database: 'kzzuezbs_31aa9913123139jmasr',
  connectionLimit: 10
};

// Create MySQL connection pool
const pool = mysql.createPool(dbConfig);

// Redis configuration
const redis = new Redis({
  host: 'mercury.nityasha.com',
  port: 26739,
  password: 'Amber@!23',
  db: 1,
  maxRetriesPerRequest: null,
});

// Initialize Firebase Admin if not already initialized
let firebaseApp;
try {
  firebaseApp = admin.app();
} catch (error) {
  firebaseApp = admin.initializeApp({
    credential: admin.credential.cert({
      projectId: "black-function-380411",
      clientEmail: "<EMAIL>",
      // Replace with your actual private key
      privateKey: process.env.FIREBASE_PRIVATE_KEY || "YOUR_PRIVATE_KEY_HERE"
    }),
    databaseURL: "https://black-function-380411-default-rtdb.firebaseio.com"
  });
}

// Get Firebase Realtime Database reference
const db = admin.database();

// Standardized phone number cleaning function (same as in the frontend)
const cleanPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';

  // Step 1: Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // Step 2: Handle country codes
  // Remove leading country code if present (e.g., 91 for India)
  if (cleaned.startsWith('91') && cleaned.length > 10) {
    cleaned = cleaned.substring(2);
  }

  // Step 3: Remove leading zeros
  cleaned = cleaned.replace(/^0+/, '');

  // Step 4: Ensure we have a valid number (at least 10 digits for India)
  if (cleaned.length < 10) {
    // Number is too short, but we'll return it anyway
  }

  return cleaned;
};

// Cache keys and expiry times
const CACHE_EXPIRY_TIME = 60 * 30; // 30 minutes in seconds

/**
 * Get all user data for the inbox in a single request
 * This endpoint combines user data, unread counts, and online status
 */
router.get('/user-inbox-data/:userId', async (req, res) => {
  const userId = req.params.userId;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  try {
    // Check if we have cached data for this user
    const cacheKey = `user_inbox_data:${userId}`;
    const cachedData = await redis.get(cacheKey);
    
    if (cachedData) {
      // Return cached data
      return res.json(JSON.parse(cachedData));
    }

    // Get all users from the database
    const connection = await pool.getConnection();
    const [users] = await connection.query(
      'SELECT id, username, email, last_message, last_message_time, is_online, last_seen FROM users WHERE id != ? LIMIT 500',
      [userId]
    );

    // Get unread message counts for this user
    const [unreadCounts] = await connection.query(
      `SELECT 
        sender_id, 
        COUNT(*) as unread_count
      FROM messages 
      WHERE receiver_id = ? AND status = 'pending' 
      GROUP BY sender_id`,
      [userId]
    );

    // Get user's contacts from the database
    const [contacts] = await connection.query(
      'SELECT contact_name, phone_number FROM contacts WHERE user_id = ?',
      [userId]
    );

    // Create a map of phone numbers to contact names for fast lookups
    const contactsMap = new Map();
    contacts.forEach(contact => {
      const cleanedNumber = cleanPhoneNumber(contact.phone_number);
      if (cleanedNumber) {
        contactsMap.set(cleanedNumber, contact.contact_name);
      }
    });

    // Process users and match with contacts
    const matchedUsers = [];
    const unreadCountsMap = new Map();
    
    // Convert unread counts to a map for faster lookups
    unreadCounts.forEach(count => {
      unreadCountsMap.set(count.sender_id.toString(), count.unread_count);
    });

    // Process each user
    for (const user of users) {
      // Clean the email field (which contains the phone number)
      const userNumber = cleanPhoneNumber(user.email);
      
      // Skip if not a valid phone number
      if (!userNumber || !/^\d+$/.test(userNumber)) {
        continue;
      }

      // Check if this user is in the contacts
      const contactName = contactsMap.get(userNumber);
      
      // Skip users not in contacts
      if (!contactName) {
        continue;
      }

      // Get unread count for this user
      const unreadCount = unreadCountsMap.get(user.id.toString()) || 0;

      // Format last seen time
      let lastSeen = null;
      if (user.last_seen) {
        const lastSeenDate = new Date(user.last_seen);
        lastSeen = lastSeenDate.toISOString();
      }

      // Create mapped user with only necessary properties
      matchedUsers.push({
        id: user.id,
        name: contactName || user.username || 'Anonymous',
        initial: (contactName?.[0] || user.username?.[0] || 'A').toUpperCase(),
        contactName: contactName,
        phoneNumber: userNumber,
        originalEmail: user.email,
        lastMessage: user.last_message || 'Hey there!',
        timestamp: user.last_message_time || '2:17:25 PM',
        is_online: user.is_online === 1,
        last_seen: lastSeen,
        unreadCount: unreadCount,
        messageStatus: unreadCount > 0 ? 'pending' : undefined
      });
    }

    // Get statuses for all users
    const [statuses] = await connection.query(
      'SELECT id, user_id, media_url, created_at FROM statuses WHERE created_at >= NOW() - INTERVAL 1 DAY'
    );

    // Create a map of user IDs to statuses
    const statusesMap = new Map();
    statuses.forEach(status => {
      const userId = status.user_id;
      if (!statusesMap.has(userId)) {
        statusesMap.set(userId, []);
      }
      statusesMap.get(userId).push({
        id: status.id,
        user_id: status.user_id,
        media_url: `http://status.api.nityasha.com/uploads/${path.basename(status.media_url)}`,
        created_at: status.created_at
      });
    });

    // Add statuses to matched users
    matchedUsers.forEach(user => {
      const userStatuses = statusesMap.get(user.id.toString());
      if (userStatuses && userStatuses.length > 0) {
        user.statuses = userStatuses;
        user.hasUnseenStory = true;
        user.hasStatus = true;
      }
    });

    connection.release();

    // Calculate total unread count
    const totalUnread = Array.from(unreadCountsMap.values()).reduce((sum, count) => sum + count, 0);

    // Prepare response data
    const responseData = {
      users: matchedUsers,
      unreadCounts: Object.fromEntries(unreadCountsMap),
      totalUnread: totalUnread,
      statuses: Array.from(statusesMap.values()).flat()
    };

    // Cache the response data
    await redis.set(cacheKey, JSON.stringify(responseData), 'EX', CACHE_EXPIRY_TIME);

    // Return the response
    res.json(responseData);
  } catch (error) {
    console.error('Error fetching user inbox data:', error);
    res.status(500).json({ error: 'Failed to fetch user inbox data' });
  }
});

/**
 * Match phone numbers with users
 * This endpoint takes a list of phone numbers and returns matching users
 */
router.post('/match-contacts', async (req, res) => {
  const { userId, phoneNumbers } = req.body;
  
  if (!userId || !phoneNumbers || !Array.isArray(phoneNumbers)) {
    return res.status(400).json({ error: 'User ID and phone numbers array are required' });
  }

  try {
    const connection = await pool.getConnection();
    
    // Clean all phone numbers
    const cleanedNumbers = phoneNumbers.map(number => cleanPhoneNumber(number)).filter(Boolean);
    
    if (cleanedNumbers.length === 0) {
      connection.release();
      return res.json({ matches: [] });
    }

    // Get all users whose email (phone number) matches any of the provided numbers
    const placeholders = cleanedNumbers.map(() => '?').join(',');
    const [users] = await connection.query(
      `SELECT id, username, email, is_online, last_seen 
       FROM users 
       WHERE email IN (${placeholders})`,
      cleanedNumbers
    );

    connection.release();

    // Map users to their phone numbers
    const matches = users.map(user => ({
      id: user.id,
      username: user.username,
      phoneNumber: user.email,
      is_online: user.is_online === 1,
      last_seen: user.last_seen
    }));

    res.json({ matches });
  } catch (error) {
    console.error('Error matching contacts:', error);
    res.status(500).json({ error: 'Failed to match contacts' });
  }
});

/**
 * Update user online status in both MySQL and Firebase
 */
router.post('/update-online-status', async (req, res) => {
  const { userId, isOnline } = req.body;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  try {
    // Update in MySQL
    const connection = await pool.getConnection();
    await connection.query(
      'UPDATE users SET is_online = ?, last_seen = NOW() WHERE id = ?',
      [isOnline ? 1 : 0, userId]
    );
    connection.release();

    // Update in Firebase
    const userStatusRef = db.ref(`users/${userId}/status`);
    await userStatusRef.set({
      state: isOnline ? 'online' : 'offline',
      lastSeen: Date.now()
    });

    // Invalidate cache for this user
    await redis.del(`user_inbox_data:${userId}`);

    res.json({ 
      success: true, 
      userId, 
      isOnline, 
      lastSeen: new Date().toISOString() 
    });
  } catch (error) {
    console.error('Error updating online status:', error);
    res.status(500).json({ error: 'Failed to update online status' });
  }
});

/**
 * Mark messages as read
 */
router.post('/mark-messages-read', async (req, res) => {
  const { userId, senderId } = req.body;
  
  if (!userId || !senderId) {
    return res.status(400).json({ error: 'User ID and sender ID are required' });
  }

  try {
    // Update in MySQL
    const connection = await pool.getConnection();
    await connection.query(
      'UPDATE messages SET status = "read", read_at = NOW() WHERE receiver_id = ? AND sender_id = ? AND status = "pending"',
      [userId, senderId]
    );
    connection.release();

    // Update in Firebase
    const chatId = [userId, senderId].sort().join('_');
    
    // Get all messages in this chat
    const messagesRef = db.ref(`user_messages/${chatId}`);
    const snapshot = await messagesRef.once('value');
    const messages = snapshot.val();
    
    if (messages) {
      const updates = {};
      
      // Find messages from the sender that aren't marked as read
      Object.entries(messages).forEach(([messageId, message]) => {
        if (message.senderId === senderId.toString() && message.status !== 'read') {
          updates[`user_messages/${chatId}/${messageId}/status`] = 'read';
          updates[`user_messages/${chatId}/${messageId}/isRead`] = true;
          updates[`user_messages/${chatId}/${messageId}/readAt`] = Date.now();
        }
      });
      
      // Apply all updates in a single operation if there are any
      if (Object.keys(updates).length > 0) {
        await db.ref().update(updates);
      }
    }
    
    // Update unread count in user_chats
    const userChatRef = db.ref(`user_chats/${userId}/${senderId}`);
    await userChatRef.update({ unread: 0 });

    // Invalidate cache for this user
    await redis.del(`user_inbox_data:${userId}`);

    res.json({ success: true });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({ error: 'Failed to mark messages as read' });
  }
});

/**
 * Get unread message counts for a user
 */
router.get('/unread-counts/:userId', async (req, res) => {
  const userId = req.params.userId;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  try {
    // Check if we have cached data
    const cacheKey = `unread_counts:${userId}`;
    const cachedData = await redis.get(cacheKey);
    
    if (cachedData) {
      return res.json(JSON.parse(cachedData));
    }

    // Get unread counts from MySQL
    const connection = await pool.getConnection();
    const [results] = await connection.query(
      `SELECT 
        sender_id, 
        COUNT(*) as unread_count
      FROM messages 
      WHERE receiver_id = ? AND status = 'pending' 
      GROUP BY sender_id`,
      [userId]
    );
    connection.release();

    // Format the response
    const pendingMessages = {};
    let totalPending = 0;

    results.forEach(row => {
      pendingMessages[row.sender_id] = row.unread_count;
      totalPending += row.unread_count;
    });

    const responseData = {
      pendingMessages,
      totalPending
    };

    // Cache the response
    await redis.set(cacheKey, JSON.stringify(responseData), 'EX', CACHE_EXPIRY_TIME);

    res.json(responseData);
  } catch (error) {
    console.error('Error fetching unread counts:', error);
    res.status(500).json({ error: 'Failed to fetch unread counts' });
  }
});

/**
 * Add a contact to the database
 */
router.post('/add-contact', async (req, res) => {
  const { userId, contactName, phoneNumber } = req.body;
  
  if (!userId || !contactName || !phoneNumber) {
    return res.status(400).json({ error: 'User ID, contact name, and phone number are required' });
  }

  try {
    // Clean the phone number
    const cleanedNumber = cleanPhoneNumber(phoneNumber);
    
    if (!cleanedNumber) {
      return res.status(400).json({ error: 'Invalid phone number' });
    }

    // Check if contact already exists
    const connection = await pool.getConnection();
    const [existingContacts] = await connection.query(
      'SELECT * FROM contacts WHERE user_id = ? AND phone_number = ?',
      [userId, cleanedNumber]
    );

    if (existingContacts.length > 0) {
      // Update existing contact
      await connection.query(
        'UPDATE contacts SET contact_name = ? WHERE user_id = ? AND phone_number = ?',
        [contactName, userId, cleanedNumber]
      );
    } else {
      // Add new contact
      await connection.query(
        'INSERT INTO contacts (user_id, contact_name, phone_number, created_at) VALUES (?, ?, ?, NOW())',
        [userId, contactName, cleanedNumber]
      );
    }

    // Check if this phone number matches a user
    const [matchingUsers] = await connection.query(
      'SELECT id, username, email, is_online FROM users WHERE email = ?',
      [cleanedNumber]
    );

    connection.release();

    // Invalidate cache for this user
    await redis.del(`user_inbox_data:${userId}`);

    res.json({
      success: true,
      contact: {
        name: contactName,
        phoneNumber: cleanedNumber,
        matchedUser: matchingUsers.length > 0 ? {
          id: matchingUsers[0].id,
          username: matchingUsers[0].username,
          is_online: matchingUsers[0].is_online === 1
        } : null
      }
    });
  } catch (error) {
    console.error('Error adding contact:', error);
    res.status(500).json({ error: 'Failed to add contact' });
  }
});

/**
 * Get all contacts for a user
 */
router.get('/contacts/:userId', async (req, res) => {
  const userId = req.params.userId;
  
  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  try {
    // Check if we have cached data
    const cacheKey = `contacts:${userId}`;
    const cachedData = await redis.get(cacheKey);
    
    if (cachedData) {
      return res.json(JSON.parse(cachedData));
    }

    const connection = await pool.getConnection();
    const [contacts] = await connection.query(
      'SELECT contact_name, phone_number FROM contacts WHERE user_id = ?',
      [userId]
    );

    // Get all users whose email (phone number) matches any of the contacts
    if (contacts.length > 0) {
      const phoneNumbers = contacts.map(contact => cleanPhoneNumber(contact.phone_number)).filter(Boolean);
      const placeholders = phoneNumbers.map(() => '?').join(',');
      
      const [matchingUsers] = await connection.query(
        `SELECT id, username, email, is_online, last_seen 
         FROM users 
         WHERE email IN (${placeholders})`,
        phoneNumbers
      );

      // Create a map of phone numbers to users
      const userMap = new Map();
      matchingUsers.forEach(user => {
        userMap.set(user.email, {
          id: user.id,
          username: user.username,
          is_online: user.is_online === 1,
          last_seen: user.last_seen
        });
      });

      // Add matched user info to contacts
      contacts.forEach(contact => {
        const cleanedNumber = cleanPhoneNumber(contact.phone_number);
        contact.matchedUser = userMap.get(cleanedNumber) || null;
      });
    }

    connection.release();

    // Cache the response
    await redis.set(cacheKey, JSON.stringify(contacts), 'EX', CACHE_EXPIRY_TIME);

    res.json(contacts);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({ error: 'Failed to fetch contacts' });
  }
});

/**
 * Firebase webhook to sync data with MySQL
 * This endpoint is called by Firebase Cloud Functions when data changes
 */
router.post('/firebase-sync', async (req, res) => {
  const { type, data } = req.body;
  
  if (!type || !data) {
    return res.status(400).json({ error: 'Type and data are required' });
  }

  try {
    const connection = await pool.getConnection();

    if (type === 'message') {
      // Sync a new message
      const { id, senderId, receiverId, text, timestamp, status } = data;
      
      // Check if message already exists
      const [existingMessages] = await connection.query(
        'SELECT * FROM messages WHERE firebase_id = ?',
        [id]
      );

      if (existingMessages.length === 0) {
        // Insert new message
        await connection.query(
          `INSERT INTO messages 
           (firebase_id, room_id, sender_id, receiver_id, content, status, created_at) 
           VALUES (?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?))`,
          [id, `${senderId}_${receiverId}`, senderId, receiverId, text, status || 'pending', timestamp / 1000]
        );
      } else {
        // Update existing message
        await connection.query(
          `UPDATE messages 
           SET status = ?, read_at = ? 
           WHERE firebase_id = ?`,
          [status, status === 'read' ? new Date() : null, id]
        );
      }

      // Invalidate caches
      await redis.del(`user_inbox_data:${senderId}`);
      await redis.del(`user_inbox_data:${receiverId}`);
      await redis.del(`unread_counts:${receiverId}`);
    } else if (type === 'status') {
      // Sync user online status
      const { userId, isOnline, lastSeen } = data;
      
      await connection.query(
        'UPDATE users SET is_online = ?, last_seen = FROM_UNIXTIME(?) WHERE id = ?',
        [isOnline ? 1 : 0, lastSeen / 1000, userId]
      );

      // Invalidate cache
      await redis.del(`user_inbox_data:${userId}`);
    }

    connection.release();
    res.json({ success: true });
  } catch (error) {
    console.error('Error syncing with Firebase:', error);
    res.status(500).json({ error: 'Failed to sync with Firebase' });
  }
});

module.exports = router;
