# Prodiston - Production Mode Toggle

Prodiston is a feature that allows you to toggle between development and production API endpoints in the Nityasha app. This is useful for testing and debugging purposes.

## Overview

The Prodiston feature adds a toggle switch to the ConsultantCommunity screen that allows you to switch between development and production API endpoints. When enabled, the app will use production API endpoints. When disabled, the app will use development API endpoints.

## Implementation Details

### Configuration

The Prodiston feature is implemented in the following files:

- `utils/config.ts`: Contains the configuration for development and production API endpoints, as well as functions to toggle between them.
- `components/prodiston/ProdistonToggle.tsx`: A reusable component that provides a toggle switch for enabling/disabling Prodiston.
- `app/Screens/ConsultantCommunity/index.tsx`: Uses the Prodiston toggle and API endpoints.

### API Endpoints

#### Development Endpoints

- Base URL: `https://api-asia-communtiy.nityasha.com/api/channels`

#### Production Endpoints

- Base URL: `https://api.nityasha.com/api/channels`

### Usage

To use the Prodiston feature:

1. Navigate to the ConsultantCommunity screen.
2. Look for the toggle switch in the header.
3. Toggle the switch to enable/disable Prodiston.
4. The app will automatically refresh data from the selected API endpoints.

### Persistence

The Prodiston state is persisted using AsyncStorage, so it will remain enabled/disabled even if you close and reopen the app.

## Technical Implementation

### Configuration

The `utils/config.ts` file contains the following functions:

- `setProdiston(enabled: boolean)`: Enables or disables Prodiston mode.
- `isProdistonEnabled()`: Checks if Prodiston is enabled.
- `getProdistonState()`: Gets the current Prodiston state without async.
- `getProdistonConfig()`: Gets the configuration based on the current Prodiston state.

### Toggle Component

The `components/prodiston/ProdistonToggle.tsx` component provides a toggle switch with the following props:

- `onToggle`: A callback function that is called when the toggle is switched.
- `showLabel`: Whether to show the "Prodiston" label next to the toggle.
- `style`: Additional styles to apply to the toggle.

### API Integration

The ConsultantCommunity screen uses the Prodiston configuration to determine which API endpoints to use. It does this by:

1. Getting the base URL using the `getApiBaseUrl()` function.
2. Using the base URL to construct API endpoints for fetching polls, chat history, etc.
3. Refreshing data when the Prodiston mode changes.

## Adding Prodiston to Other Screens

To add Prodiston to other screens:

1. Import the ProdistonToggle component:
   ```tsx
   import ProdistonToggle from '@/components/prodiston/ProdistonToggle';
   ```

2. Add the toggle to your screen:
   ```tsx
   <ProdistonToggle 
     showLabel={false} 
     onToggle={handleProdistonToggle}
     style={tw`mr-2`}
   />
   ```

3. Implement the handleProdistonToggle function:
   ```tsx
   const handleProdistonToggle = useCallback((isEnabled: boolean) => {
     // Refresh data when prodiston mode changes
     console.log(`Prodiston mode ${isEnabled ? 'enabled' : 'disabled'}`);
     // Refresh your data here
   }, []);
   ```

4. Use the getApiBaseUrl function to get the correct API base URL:
   ```tsx
   const getApiBaseUrl = useCallback(() => {
     const isProdistonMode = getProdistonState();
     return isProdistonMode 
       ? 'https://api.nityasha.com/your-endpoint'
       : 'https://api-asia-communtiy.nityasha.com/your-endpoint';
   }, []);
   ```

5. Use the base URL in your API calls:
   ```tsx
   const fetchData = async () => {
     const baseUrl = getApiBaseUrl();
     const response = await fetch(`${baseUrl}/your-endpoint`);
     // ...
   };
   ```

## Troubleshooting

If you encounter issues with the Prodiston feature:

1. Check if the toggle is working correctly by looking at the console logs.
2. Verify that the API endpoints are correct for both development and production.
3. Make sure the AsyncStorage is working correctly for persisting the Prodiston state.
4. Try restarting the app if the changes don't take effect immediately.

## Future Improvements

- Add a global Prodiston context to make it easier to access the Prodiston state from any component.
- Add more configuration options for different environments (e.g., staging, testing).
- Add a visual indicator to show which environment is currently active.
