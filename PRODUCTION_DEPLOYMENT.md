# Nityasha Production Deployment Guide

This document provides instructions for deploying the Nityasha app to production.

## Prerequisites

- Node.js and npm installed
- Expo CLI installed (`npm install -g expo-cli`)
- EAS CLI installed (`npm install -g eas-cli`)
- An Expo account (logged in as "nityashacom")
- Git installed

## Environment Configuration

The app uses environment variables for different build profiles (development, production). These are configured in `eas.json` and accessed through the `utils/config.ts` file.

### Production Environment Variables

The following environment variables are set for production builds:

- `EXPO_PUBLIC_API_URL`: Main API URL
- `EXPO_PUBLIC_FIREBASE_URL`: Firebase Realtime Database URL
- `EXPO_PUBLIC_SUPABASE_URL`: Supabase URL
- `EXPO_PUBLIC_TOGETHER_API_URL`: Together AI API URL
- `EXPO_PUBLIC_BALANCE_WS_URL`: Balance WebSocket URL
- `EXPO_PUBLIC_USER_API_URL`: User API URL
- `EXPO_PUBLIC_SEARCH_API_URL`: Search API URL

## Building for Production

### Using the Build Script

The easiest way to build for production is to use the provided PowerShell script:

```powershell
./build-production.ps1
```

This script will:
1. Check if EAS CLI is installed
2. Verify you're logged in to EAS
3. Optionally update the app version
4. Start the production build process

### Manual Build Process

If you prefer to build manually:

1. Make sure you're logged in to your Expo account:
   ```
   npx eas whoami
   ```

2. Build the app using the "production" profile:
   ```
   npx eas build --platform android --profile production
   ```

3. Follow the prompts in the terminal. The build will be submitted to EAS Build servers.

4. Once the build is complete, you'll receive a link to download the AAB file.

## Submitting to Google Play Store

After building the app:

1. Download the AAB file from the EAS Build dashboard.

2. Go to the [Google Play Console](https://play.google.com/console/).

3. Navigate to your app > Release > Production.

4. Create a new release and upload the AAB file.

5. Fill in the release notes and submit for review.

## Backend Deployment

The backend server should be deployed to a production environment before releasing the app. Make sure all API endpoints are properly configured and accessible.

### Backend Environment Variables

The backend server uses the following environment variables:

- `PORT`: Server port (default: 3000)
- `DB_HOST`: Database host
- `DB_USER`: Database user
- `DB_PASSWORD`: Database password
- `DB_NAME`: Database name
- `FIREBASE_PRIVATE_KEY`: Firebase service account private key

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check the EAS Build logs for detailed error messages
   - Verify that all dependencies are correctly installed
   - Make sure the app.json configuration is valid

2. **API Connection Issues**
   - Verify that all API endpoints are accessible from the production environment
   - Check that environment variables are correctly set in eas.json

3. **Firebase Integration Issues**
   - Ensure the Firebase service account has the correct permissions
   - Verify that the Firebase database URL is correct

## Additional Resources

- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [Android App Bundle Documentation](https://developer.android.com/guide/app-bundle)
- [Google Play Console Help](https://support.google.com/googleplay/android-developer/)
