{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "nity<PERSON>a", "version": "4.8", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "nity<PERSON>a", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/image.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSContactsUsageDescription": "This app requires access to your contacts to help you connect with your friends.", "NSPhoneNumbersUsageDescription": "This app requires access to your phone number to simplify the registration process."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "package": "com.nityasha.com.nityasha", "versionCode": 45, "googleServicesFile": "./google-services.json", "softwareKeyboardLayoutMode": "pan", "permissions": ["READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "READ_PHONE_STATE", "READ_PHONE_NUMBERS", "READ_CONTACTS", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS"], "playStoreUrl": "https://play.google.com/store/apps/details?id=com.nityasha.com.nityasha"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "7dadf964-baa4-448e-b91a-57efad1557f5"}}, "plugins": ["expo-router", "expo-font", "@react-native-voice/voice", "./plugins/PhonepePlugin", "./plugins/AndroidXMigrationPlugin", ["expo-splash-screen", {"backgroundColor": "#ffffff", "image": "./assets/images/image.png", "dark": {"image": "./assets/images/image.png", "backgroundColor": "#ffffff"}, "imageWidth": 200}], ["expo-build-properties", {"android": {"extraManifests": ["./extra-android-manifest.xml"]}}], "expo-secure-store", "expo-audio", "expo-video", "@livekit/react-native-expo-plugin", "@config-plugins/react-native-webrtc"]}}