import { View, Text, TouchableOpacity, TextInput, Alert, ImageBackground } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Picker } from '@react-native-picker/picker';
import tw from 'twrnc';
import { useFonts } from 'expo-font';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ChevronLeft } from 'lucide-react-native';
import Button from '@/components/ui/Button';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Account_Information({ navigation }) {
  const [loaded] = useFonts({
    'Helvetica_bold': require('@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf'),
  });

  const [userInfo, setUserInfo] = useState({
    username: '',
    contact_no: '',
    email: '',
    country: '',
    GENDER: '0', // Default to male (0)
    birth_date: '',
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        const { userId } = JSON.parse(userSession);
        const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
        const data = await response.json();
        setUserInfo({
          username: data.username.trim(),
          contact_no: data.contact_no,
          email: data.email,
          country: 'india', // Default or fetched country
          GENDER: data.GENDER || '0', // Assuming the API returns '0', '1', or '2'
          birth_date: data.birth_date ? new Date(data.birth_date).toISOString().split('T')[0] : '',
        });
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, []);

  const handleSave = async () => {
    // Check if the phone number is exactly 10 digits long and contains only numbers
    if (!/^\d{10}$/.test(userInfo.contact_no)) {
      Alert.alert("Error", "Phone number must be exactly 10 digits and contain only numbers.");
      return; // Exit the function if the validation fails
    }

    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const { userId } = JSON.parse(userSession);
      
      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
        method: 'PUT', // Use PUT to update user information
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: userInfo.username,
          contact_no: userInfo.contact_no,
          email: userInfo.email,
          GENDER: userInfo.GENDER, // Send GENDER as '0', '1', or '2'
          birth_date: userInfo.birth_date,
          // Include any additional fields you want to update
        }),
      });

      if (response.ok) {
        Alert.alert("Success", "Your information has been saved successfully.");
        navigation.navigate('Profile', { reload: true }); // Pass reload parameter
      } else {
        const errorData = await response.json();
        Alert.alert("Error", errorData.error || "Failed to save user information.");
      }
    } catch (error) {
      console.error('Error saving user data:', error);
      Alert.alert("Error", "An unexpected error occurred. Please try again.");
    }
  };

  return (
    <ImageBackground style={tw`px-5 bg-white h-full pt-5`} source={require('@/assets/screens/screen7th.png')}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={tw`flex items-start justify-start flex-row gap-1`}>
        <ChevronLeft size={30} color={"#000"} />
        <Text style={[tw`text-[#39434F] font-medium text-base h-full flex items-center justify-center mt-1`, { fontFamily: 'Helvetica_bold' }]}>
          Account Information
        </Text>
      </TouchableOpacity>

      <Text style={[tw`mt-5`, { fontFamily: 'Helvetica_bold' }]}>User Name</Text>
      <TextInput 
        placeholder='alwissuryatmaja' 
        value={userInfo.username} 
        onChangeText={(text) => setUserInfo({...userInfo, username: text})}
        style={tw`w-full px-5 mt-2 py-2 border-2 rounded-[12px] border-[#000]`} 
      />
      
      <Text style={[tw`mt-5`, { fontFamily: 'Helvetica_bold' }]}>Phone Number</Text>
      <TextInput 
        placeholder='+62 ***********' 
        keyboardType='numeric' 
        value={userInfo.email} 
        onChangeText={(text) => setUserInfo({...userInfo, email: text})}
        style={tw`w-full px-5 mt-2 py-2 border-2 rounded-[12px] border-[#000]`} 
      />

      <Text style={[tw`mt-5`, { fontFamily: 'Helvetica_bold' }]}>Country</Text>
      <TextInput 
        placeholder='India' 
        value={userInfo.country} 
        onChangeText={(text) => setUserInfo({...userInfo, country: text})}
        style={tw`w-full px-5 mt-2 py-2 border-2 rounded-[12px] border-[#000]`} 
      />
      
      {userInfo.GENDER !== '0' && ( // Show only if GENDER is not male (0)
        <>
          <Text style={[tw`mt-5`, { fontFamily: 'Helvetica_bold' }]}>GENDER</Text>
          <Picker
            selectedValue={userInfo.GENDER}
            style={tw`w-full h-12 border-2 rounded-[12px] border-[#000]`}
            onValueChange={(itemValue) => setUserInfo({...userInfo, GENDER: itemValue})}>
            <Picker.Item label="Male" value="0" />
            <Picker.Item label="Female" value="1" />
            <Picker.Item label="Other" value="2" />
          </Picker>
        </>
      )}
      
      <Text style={[tw`mt-5`, { fontFamily: 'Helvetica_bold' }]}>Birth Date</Text>
      <TextInput 
        placeholder='YYYY-MM-DD' 
        value={userInfo.birth_date} 
        onChangeText={(text) => setUserInfo({...userInfo, birth_date: text})}
        style={tw`w-full px-5 mt-2 py-2 border-2 rounded-[12px] border-[#000]`} 
      />

      <View style={tw`w-full flex gap-2 flex-row mt-4 items-center justify-center`}>
        <Button onPress={handleSave} variant='primaryfifthi'>Save</Button>
        <TouchableOpacity onPress={() => navigation.navigate('Profile')} style={tw`px-5 text-[14px] py-3 flex items-center justify-center text-center rounded-[8px] bg-white shadow-none w-[50%] border-[#D7D9DC] border-2`}>
          <Text style={{ fontFamily: 'Helvetica_bold' }}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </ImageBackground>
  );
}
