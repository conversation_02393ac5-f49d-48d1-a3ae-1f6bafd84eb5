import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  PermissionsAndroid,
  Linking,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { AntDesign } from '@expo/vector-icons';
import { ChevronLeft } from 'lucide-react-native';
import tw from 'twrnc';
import { MotiView } from 'moti';
import { Easing } from 'react-native-reanimated';
import { ImageBackground } from 'react-native';
import * as Contacts from 'expo-contacts';
import { BottomNavigation } from 'react-native-paper';

interface Route {
  key: string;
  title: string;
  focusedIcon: string;
  unfocusedIcon: string;
}

const ANIMATION_CONFIG = {
  from: {
    opacity: 0,
    scale: 0.95,
  },
  animate: {
    opacity: 1,
    scale: 1,
  },
  transition: {
    type: 'timing' as const,
    duration: 300,
    easing: Easing.bezier(0.4, 0, 0.2, 1),
  },
};

const AddContact = () => {
  const navigation = useNavigation();
  const [name, setName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [index, setIndex] = useState(0);

  // Bottom navigation routes
  const [routes] = useState<Route[]>([
    {
      key: 'home',
      title: 'Home',
      focusedIcon: 'home',
      unfocusedIcon: 'home-outline',
    },
    {
      key: 'chat',
      title: 'Chat',
      focusedIcon: 'chat',
      unfocusedIcon: 'chat-outline',
    },
    {
      key: 'profile',
      title: 'Profile',
      focusedIcon: 'account',
      unfocusedIcon: 'account-outline',
    },
  ]);

  const renderScene = ({ route, jumpTo }: { route: Route; jumpTo: (key: string) => void }) => {
    switch (route.key) {
      case 'home':
        return null;
      case 'chat':
        return null;
      case 'profile':
        return null;
      default:
        return null;
    }
  };

  useEffect(() => {
    checkPermission();
  }, []);

  const checkPermission = async () => {
    try {
      const { status } = await Contacts.getPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status !== 'granted') {
        const { status: newStatus } = await Contacts.requestPermissionsAsync();
        setHasPermission(newStatus === 'granted');
      }
    } catch (error) {
      console.error('Error checking contacts permission:', error);
      Alert.alert('Error', 'Failed to access contacts. Please check your permissions.');
    }
  };

  const handleAddContact = async () => {
    if (!name.trim() || !phoneNumber.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    // Basic phone number validation
    const phoneRegex = /^[0-9]{10}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\D/g, ''))) {
      Alert.alert('Error', 'Please enter a valid 10-digit phone number');
      return;
    }

    if (!hasPermission) {
      Alert.alert('Permission Required', 'Please grant contacts permission to add new contacts.');
      await checkPermission();
      return;
    }

    setLoading(true);
    try {
      // Create new contact
      const contact = {
        name: name,
        contactType: Contacts.ContactTypes.Person,
        phoneNumbers: [
          {
            number: phoneNumber,
            isPrimary: true,
            label: 'mobile',
          },
        ],
      };

      // Add contact to device
      const newContact = await Contacts.addContactAsync(contact);
      
      if (newContact) {
        // Also add to your app's database/API
        try {
          // Here you would make an API call to add the contact to your backend
          // await fetch('your-api-endpoint', {
          //   method: 'POST',
          //   body: JSON.stringify({
          //     name,
          //     phoneNumber,
          //     deviceContactId: newContact.id
          //   })
          // });

          Alert.alert(
            'Success',
            'Contact added successfully',
            [
              {
                text: 'OK',
                onPress: () => navigation.goBack()
              }
            ]
          );
        } catch (apiError) {
          console.error('Error adding contact to API:', apiError);
          // Still show success since contact was added to device
          Alert.alert(
            'Partial Success',
            'Contact added to device but failed to sync with app. Please try again later.',
            [
              {
                text: 'OK',
                onPress: () => navigation.goBack()
              }
            ]
          );
        }
      }
    } catch (error) {
      console.error('Error adding contact:', error);
      Alert.alert('Error', 'Failed to add contact. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const requestPermission = async () => {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'This app needs access to contacts to add new contacts.',
          [
            {
              text: 'Cancel',
              style: 'cancel'
            },
            {
              text: 'Open Settings',
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting contacts permission:', error);
      Alert.alert('Error', 'Failed to request contacts permission.');
    }
  };

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1`}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={tw`flex-1`}
      >
        <MotiView {...ANIMATION_CONFIG}>
          <View style={tw`w-full items-center justify-between flex-row px-5 py-3`}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <ChevronLeft size={30} color={'#000'} />
            </TouchableOpacity>
            <Text style={[tw`text-black text-2xl`, { fontFamily: 'Helvetica_bold' }]}>Add Contact</Text>
            <View style={tw`w-8`} /> {/* Spacer for alignment */}
          </View>
        </MotiView>

        <ScrollView style={tw`flex-1 px-5`} contentContainerStyle={tw`py-5`}>
          <View style={tw`bg-white rounded-2xl p-5 shadow-sm`}>
            {!hasPermission && (
              <TouchableOpacity
                onPress={requestPermission}
                style={tw`bg-blue-500 p-4 rounded-xl items-center justify-center mb-5`}
              >
                <Text style={tw`text-white font-bold text-lg`}>Grant Contacts Permission</Text>
              </TouchableOpacity>
            )}

            <View style={tw`mb-5`}>
              <Text style={tw`text-gray-600 mb-2`}>Name</Text>
              <TextInput
                style={tw`bg-gray-50 p-4 rounded-xl text-black`}
                placeholder="Enter name"
                value={name}
                onChangeText={setName}
                placeholderTextColor="#999"
              />
            </View>

            <View style={tw`mb-5`}>
              <Text style={tw`text-gray-600 mb-2`}>Phone Number</Text>
              <TextInput
                style={tw`bg-gray-50 p-4 rounded-xl text-black`}
                placeholder="Enter phone number"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="phone-pad"
                maxLength={10}
                placeholderTextColor="#999"
              />
            </View>

            <TouchableOpacity
              onPress={handleAddContact}
              disabled={loading || !hasPermission}
              style={[
                tw`bg-green-500 p-4 rounded-xl items-center justify-center`,
                (loading || !hasPermission) && tw`opacity-70`
              ]}
            >
              {loading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={tw`text-white font-bold text-lg`}>Add Contact</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>

        <BottomNavigation
          navigationState={{ index, routes }}
          onIndexChange={setIndex}
          renderScene={renderScene}
          style={tw`bg-white border-t border-gray-200`}
          barStyle={tw`bg-white`}
          activeColor="#075E54"
          inactiveColor="#666"
          sceneAnimationEnabled={true}
          sceneAnimationType="shifting"
          labeled={true}
          safeAreaInsets={{ bottom: 0 }}
        />
      </KeyboardAvoidingView>
    </ImageBackground>
  );
};

export default AddContact; 