import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import tw from 'twrnc';

const SuggestionChips = () => {
  const suggestions = [
    { id: 1, text: 'Weekend plan', icon: '📅', color: 'text-purple-500' },
    { id: 2, text: 'Tell me a joke', icon: '😂', color: 'text-yellow-500' },
    { id: 3, text: 'Tell me a story', icon: '📖', color: 'text-gray-500' },
    { id: 4, text: 'Surprise me', icon: '✨', color: 'text-yellow-500' },
    { id: 5, text: 'Tell me about...', icon: '💬', color: 'text-green-500' },
    { id: 6, text: 'Who are you?', icon: '🤖', color: 'text-blue-500' },
    { id: 7, text: 'Help me focus', icon: '🎯', color: 'text-red-500' },
  ];

  // Split into 2 rows manually
  const midpoint = Math.ceil(suggestions.length / 2);
  const firstRow = suggestions.slice(0, midpoint);
  const secondRow = suggestions.slice(midpoint);

  const renderChips = (data: typeof suggestions) =>
    data.map((item) => (
      <TouchableOpacity
        key={item.id}
        style={tw`flex-row items-center bg-white border border-gray-300 rounded-full px-4 py-2 mr-3`}
        onPress={() => console.log('Selected:', item.text)}
        activeOpacity={0.7}
      >
        <Text style={tw`${item.color} mr-2 text-base`}>{item.icon}</Text>
        <Text style={tw`text-gray-800 text-base`}>{item.text}</Text>
      </TouchableOpacity>
    ));

  return (
    <View style={tw`bg-white p-4 h-30`}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={tw`mb-2`}
      >
        <View style={tw`flex-row`}>{renderChips(firstRow)}</View>
      </ScrollView>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
      >
        <View style={tw`flex-row`}>{renderChips(secondRow)}</View>
      </ScrollView>
    </View>
  );
};

export default SuggestionChips;
