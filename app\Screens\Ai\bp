import { StatusBar, TouchableOpacity, Text, ScrollView, TextInput, ActivityIndicator, Alert, Clipboard, ImageBackground, Keyboard, KeyboardEvent, Platform, ToastAndroid, Linking, LayoutChangeEvent } from 'react-native'
import React, { useState, useRef, useEffect, useMemo } from 'react'
import { SafeAreaView, View, MotiView, Image } from 'moti';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { MoveLeft, EllipsisVertical, Send, Copy, Battery as BatteryIcon, Sun, Compass, MessageCircle, Clock, ChefHat, Mic } from 'lucide-react-native';
import { checkForIllegalWords } from '@/app/utils/wordFilter';
import { KeyboardAvoidingView } from "react-native-keyboard-controller";
import * as ExpoBattery from 'expo-battery';
import * as Brightness from 'expo-brightness';
import * as Location from 'expo-location';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { navigate } from 'expo-router/build/global-state/routing';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Contacts from 'expo-contacts';
import useAuth from '@/hooks/auth';
import Question from './_compo/question'
import { RootStackParamList } from '@/app/index';
import * as Speech from 'expo-speech';

// Auto-scrolling component for suggestion chips
const AutoScrollView = ({ data, direction = 'left', onChipPress }: { data: any[], direction?: 'left' | 'right', onChipPress: (text: string) => void }) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [contentWidth, setContentWidth] = useState(0);
  const scrollPosition = useRef(0);
  const intervalId = useRef<NodeJS.Timeout | null>(null);
  const isTouched = useRef(false);
  const resumeScrollTimer = useRef<NodeJS.Timeout | null>(null); // To resume scrolling after inactivity

  // Duplicate the data for a seamless loop
  const loopedData = [...data, ...data];

  const handleLayout = (event: LayoutChangeEvent) => {
    // Total width of the original content (not the doubled one)
    const newContentWidth = event.nativeEvent.layout.width / 2;
     if (newContentWidth > 0 && contentWidth === 0) { // Set width only once
        setContentWidth(newContentWidth);
        if (direction === 'right') {
            scrollPosition.current = newContentWidth;
        }
    }
  };

  const stopScrolling = () => {
    isTouched.current = true;
    if (intervalId.current) {
      clearInterval(intervalId.current);
      intervalId.current = null;
    }
    // Also clear the resume timer if a new touch starts
    if (resumeScrollTimer.current) {
        clearTimeout(resumeScrollTimer.current);
        resumeScrollTimer.current = null;
    }
  };
  
  const startScrolling = () => {
      isTouched.current = false; // Reset touch status
      if (intervalId.current) return; // Already scrolling

      intervalId.current = setInterval(() => {
        if(isTouched.current) {
            if (intervalId.current) clearInterval(intervalId.current);
            return
        };

        const speed = 0.5; // Controls scroll speed
        if (direction === 'left') {
          scrollPosition.current += speed;
          if (scrollPosition.current >= contentWidth) {
            scrollPosition.current = 0; // Loop back
          }
        } else { // direction === 'right'
          scrollPosition.current -= speed;
          if (scrollPosition.current <= 0) {
            scrollPosition.current = contentWidth; // Loop back
          }
        }
        scrollViewRef.current?.scrollTo({ x: scrollPosition.current, animated: false });
      }, 1000 / 60); // ~60fps
  }

  // Schedule auto-scrolling to resume after a period of inactivity
  const scheduleResumeScrolling = () => {
    if (resumeScrollTimer.current) {
        clearTimeout(resumeScrollTimer.current);
    }
    resumeScrollTimer.current = setTimeout(() => {
        if (isTouched.current) { // Only resume if it was manually stopped
             startScrolling();
        }
    }, 5000); // 5 seconds of inactivity
  };


  useEffect(() => {
    if (contentWidth > 0 && !isTouched.current) {
      startScrolling();
    }
    return () => {
      // Cleanup timers on component unmount
      if (intervalId.current) {
        clearInterval(intervalId.current);
      }
      if (resumeScrollTimer.current) {
        clearTimeout(resumeScrollTimer.current);
      }
    };
  }, [contentWidth]);

  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      scrollEventThrottle={16}
      onTouchStart={stopScrolling}
      onMomentumScrollEnd={scheduleResumeScrolling} // When scroll momentum ends
      onScrollEndDrag={scheduleResumeScrolling}   // When user lifts finger after dragging
      bounces={false}
      style={tw`my-1`}
    >
      <View onLayout={handleLayout} style={tw`flex-row gap-3 px-2`}>
        {loopedData.map((chip, index) => (
          <TouchableOpacity
            key={`${chip.id}-${index}`}
            onPress={() => {
                stopScrolling(); // Stop scrolling when a chip is pressed
                onChipPress(chip.text);
            }}
            activeOpacity={0.7}
            style={tw`flex-row items-center bg-white border border-gray-300 rounded-full px-4 py-2  h-10`}
          >
            <Text style={tw`${chip.color} mr-2 text-base`}>{chip.icon}</Text>
            <Text style={tw`text-gray-800 text-base`}>{chip.text}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
};


// Battery and Brightness Info Component
const SystemInfo = () => {
  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
  const [brightness, setBrightness] = useState<number | null>(null);

  useEffect(() => {
    const getBatteryLevel = async () => {
      const level = await ExpoBattery.getBatteryLevelAsync();
      setBatteryLevel(level);
    };

    const getBrightness = async () => {
      const level = await Brightness.getBrightnessAsync();
      setBrightness(level);
    };

    getBatteryLevel();
    getBrightness();

    // Update battery level every minute
    const batteryInterval = setInterval(getBatteryLevel, 60000);
    // Update brightness every 5 seconds
    const brightnessInterval = setInterval(getBrightness, 5000);

    return () => {
      clearInterval(batteryInterval);
      clearInterval(brightnessInterval);
    };
  }, []);

  return (
    <View style={tw`flex-row justify-end items-center gap-2 px-4 py-2`}>
      <View style={tw`flex-row items-center gap-1 bg-black/10 px-2 py-1 rounded-full`}>
        <BatteryIcon size={16} color="#000" />
        <Text style={tw`text-black text-sm`}>
          {batteryLevel !== null ? `${Math.round(batteryLevel * 100)}%` : '...'}
        </Text>
      </View>
      <View style={tw`flex-row items-center gap-1 bg-black/10 px-2 py-1 rounded-full`}>
        <Sun size={16} color="#000" />
        <Text style={tw`text-black text-sm`}>
          {brightness !== null ? `${Math.round(brightness * 100)}%` : '...'}
        </Text>
      </View>
    </View>
  );
};

// Message component to display chat messages
const MessageBubble = ({ message, isUser }: any) => {
  const displayText = isUser ? (message.text || '') : (message.text ? String(message.text) : '');

  // Function to handle copying message text
  const copyMessageToClipboard = () => {
    Clipboard.setString(message.text);
    // Show a platform-specific notification
    if (Platform.OS === 'android') {
      ToastAndroid.show('Message copied to clipboard!', ToastAndroid.SHORT);
    } else {
      // Fallback for other platforms (like iOS) - you could use Alert or nothing
      Alert.alert('Copied!', 'Message copied to clipboard.');
    }
  };

  // Function to render AI text with bold formatting
  const renderAIResponse = (text: string) => {
    // Split the text by **
    const parts = text.split('**');
    const elements = [];

    for (let i = 0; i < parts.length; i++) {
      // Even indices are non-bold text
      if (i % 2 === 0) {
        if (parts[i]) { // Only add if the part is not empty
          elements.push(
            <Text key={`part-${i}`} style={{ fontFamily: 'GoogleSans-Regular', fontSize: 16, color: 'black' }}>
              {parts[i]}
            </Text>
          );
        }
      }
      // Odd indices are text that should be bold
      else {
        if (parts[i]) { // Only add if the part is not empty
          elements.push(
            <Text key={`part-${i}`} style={{ fontFamily: 'GoogleSans-Bold', fontSize: 16, color: 'black' }}>
              {parts[i]}
            </Text>
          );
        }
      }
    }
    return elements;
  };

  return (
    <MotiView
      style={[
        tw`my-2 max-w-[80%]`,
        isUser ? tw`self-end` : tw`self-start flex-row`
      ]}
    >
      <TouchableOpacity
        onPress={copyMessageToClipboard}
        activeOpacity={0.8}
          onLongPress={() => {
    if (message.text) {
      Speech.speak(message.text, {
        language: 'hi-IN', // or 'en-IN' for English-Indian
        rate: 1.0,
      });
    }
  }}
        style={[
          tw``,
          isUser
            ? tw`bg-[#F6F6F6] rounded-full p-3 py-3.5`
            : tw`rounded-bl-none`
        ]}
      >
        {isUser ? (
          <Text
            style={[
              tw`text-base`,
              tw`text-black`,
              { fontFamily: 'GoogleSans-Regular' }
            ]}
          >
            {message.text || ''}
          </Text>
        ) : (
          <Text style={{ fontSize: 16, color: 'black', fontFamily: 'GoogleSans-Regular' }}>
            {renderAIResponse(displayText)}
          </Text>
        )}

      </TouchableOpacity>
    </MotiView>
  );
};

// Typing indicator compon  ent
const TypingIndicator = ({ isVisible }: { isVisible: boolean }) => {
  if (!isVisible) return null;

  const AnimatedDot = ({ delay = 0 }: { delay?: number }) => (
    <MotiView
      from={{ translateY: 0 }} // Start at the baseline
      animate={{ translateY: -4 }} // Move up by 4 units
      transition={{
        type: 'timing',
        duration: 400, // Duration for one part of the movement (up or down)
        delay, // Stagger the delay for each dot
        repeat: Infinity, // Loop indefinitely
        repeatReverse: true, // Move up and then back down
      }}
      style={tw`w-2 h-2 rounded-full bg-gray-600 mx-0.5`} // Small gray dots
    />
  );


  return (
    <MotiView
      style={tw`my-2 self-start`}
    >
      <View style={tw`p-3 rounded-[20px] rounded-bl-none flex-row items-center`}>

        <AnimatedDot delay={0} />
        <AnimatedDot delay={250} />
        <AnimatedDot delay={500} />

      </View>
    </MotiView>
  );
};

// Helper function to try and parse JSON from a string,
// potentially enclosed in markdown code blocks.
const tryParseJsonFromResponseText = (text: string): any | null => {
  if (!text) return null;

  // Remove markdown code block indicators if present (e.g., ```json\n...\n``` or ```\n...\n```)
  let cleanedText = text.replace(/```(?:json)?\n/, '').replace(/\n```$/, '');

  // Try to parse the cleaned text as JSON
  try {
    const parsed = JSON.parse(cleanedText);

    // Check if the parsed result is actually an object and has an 'action' field
    if (typeof parsed === 'object' && parsed !== null && parsed.action) {
      // Check for specific actions and their required fields
      if (parsed.action === "send_email" && parsed.to && parsed.subject && parsed.body) {
        return parsed; // Valid email action structure
      }
      // Check for open_app actions
      if (parsed.action === "open_app" && parsed.app) {
        // Further checks for uri/target/query are done in sendMessageToAI
        return parsed; // Valid open_app structure (at least has action and app)
      }
      // Check for Call action
      if (parsed.action === "Call" && parsed.number) {
        return parsed; // Valid Call action structure
      }
      // The previous 'search_app' action is now covered by 'open_app' with target:'search'
      // If your API might still send 'search_app', keep this check:
      // if (parsed.action === "search_app" && parsed.app && parsed.query) {
      //    return parsed; // Valid search_app action structure
      // }

      // Add checks for other potential actions here
    }
  } catch (e) {
    // Parsing failed, it's likely not JSON or not the expected action structure
    console.log("Failed to parse response text as JSON or expected action structure:", e);
  }

  return null; // Return null if parsing fails or result isn't an object with a valid action
};

// API endpoint for AI chat
const AI_API_URL = 'https://nx.ai.api.nityasha.com/chat';

// New component for the chat input area
const ChatInputArea = ({ onSendMessage, isLoading }: { onSendMessage: (message: string) => void; isLoading: boolean }) => {
  const [inputText, setInputText] = useState('');
  const inputRef = useRef<TextInput>(null);
  const navigation = useNavigation<any>(); // ✅ FIXED: properly typed navigation

  const handleSendPress = () => {
    if (inputText.trim() && !isLoading) {
      onSendMessage(inputText);
      setInputText(''); // Clear input after sending
    }
  };

  return (
   <View style={tw`px-4 pb-4 flex-row items-center`}>
  <TextInput
    ref={inputRef}
    placeholder="Type your message..."
    placeholderTextColor="#999"
    style={tw`flex-1 bg-white text-base p-3 pl-4 rounded-full border border-gray-300`}
    value={inputText}
    onChangeText={setInputText}
    editable={!isLoading}
    onSubmitEditing={handleSendPress}
  />
  <TouchableOpacity
    style={tw`ml-2 bg-[#2B2B2B] p-3 rounded-full items-center justify-center`}
    onPress={() => {
          if (inputText.trim()) {
            handleSendPress();
          } else {
            navigation.navigate('VoiceAi' as keyof RootStackParamList); // ✅ FIXED: properly typed navigation
          }
        }}
    disabled={isLoading}
  >
    {isLoading && inputText.trim() ? (
      <ActivityIndicator size="small" color="#ffffff" />
    ) : inputText.trim() ? (
      <Send size={20} color="#ffffff" />
    ) : (
      <Mic size={20} color="#ffffff" />
    )}
  </TouchableOpacity>
</View>
  );
};

export default function AI({ }: any) {
  const navigation = useNavigation<any>(); // ✅ FIXED: properly typed navigation
  const authUserId = useAuth(); // Capture the userId returned by useAuth

  type UserDetails = {
    username: string;
    [key: string]: any;
  };

  const [userName, setUserName] = useState<UserDetails | null>(null);

  type Message = {
    id: number;
    text: string;
    isUser: boolean;
    time?: string;
  };

  // Initialize messages as an empty array directly
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  // Removed the separate userid state, will use authUserId directly or if needed,
  // initialize state from authUserId if it's needed for local modifications later.
  // For now, let's use authUserId directly in sendMessageToAI.
  // const [userid , setUserid] = useState(''); // This line is removed

  // Add state for system prompt
  const [systemPrompt, setSystemPrompt] = useState("You are a helpful assistant."); // Initial default prompt
  // State to manage visibility of the system prompt input area
  const [isSystemPromptInputVisible, setIsSystemPromptInputVisible] = useState(false);
  // State to hold the text currently being edited in the prompt input
  const [editingSystemPrompt, setEditingSystemPrompt] = useState(systemPrompt);


  // Save system prompt to AsyncStorage whenever it changes
  useEffect(() => {
    AsyncStorage.setItem('systemPrompt', systemPrompt).catch(err => {
      console.error("Failed to save system prompt:", err);
    });
  }, [systemPrompt]); // Dependency array means this runs when systemPrompt changes

  // --- Request Contact Permissions ---
  useEffect(() => {
    (async () => {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant contact permissions to enable calling contacts by name.'
        );
      }
    })();
  }, []); // Empty dependency array means this runs once on mount

  // Function to check battery level
  const checkBatteryLevel = async () => {
    try {
      const batteryLevel = await ExpoBattery.getBatteryLevelAsync();
      return `Your battery level is ${Math.round(batteryLevel * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't check your battery level.";
    }
  };

  // Function to check brightness
  const checkBrightness = async () => {
    try {
      const brightness = await Brightness.getBrightnessAsync();
      return `Your screen brightness is set to ${Math.round(brightness * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't check your screen brightness.";
    }
  };

  // Function to set brightness
  const setBrightness = async (level: number) => {
    try {
      await Brightness.setSystemBrightnessAsync(level);
      return `Screen brightness has been set to ${Math.round(level * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't adjust your screen brightness.";
    }
  };

  // Function to fetch weather information
  const checkWeather = async () => {
    try {
      // Request permission to access location
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        return "Sorry, I need location permission to fetch the weather.";
      }

      // Get current location
      let location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      const weatherApiUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&hourly=temperature_2m`;

      const response = await fetch(weatherApiUrl);
      if (!response.ok) {
        throw new Error(`Weather API request failed with status ${response.status}`);
      }
      const data = await response.json();

      if (data && data.hourly && data.hourly.time && data.hourly.temperature_2m) {
        // Find the current hour's temperature
        const now = new Date();
        // Ensure consistent format between API time and current time string
        const currentHourString = now.toISOString().slice(0, 13) + ':00';


        const currentTimeIndex = data.hourly.time.findIndex((time: string) => time.startsWith(currentHourString));

        if (currentTimeIndex !== -1 && currentTimeIndex < data.hourly.temperature_2m.length) {
          const currentTemperature = data.hourly.temperature_2m[currentTimeIndex];
          return `The current temperature at your location is ${currentTemperature}°C.`;
        } else {
          // Fallback to the first available temperature if current hour not found
          if (data.hourly.temperature_2m.length > 0) {
            const currentTemperature = data.hourly.temperature_2m[0];
            return `The temperature at your location is currently ${currentTemperature}°C according to the latest forecast data.`;
          }
          return "Could not find current temperature data for your location.";
        }
      } else {
        return "Sorry, I couldn't retrieve the weather information for your location.";
      }
    } catch (error) {
      console.error('Error fetching weather or location:', error);
      return "Sorry, I couldn't fetch the weather information for your location at the moment.";
    }
  };

  // Function to scroll to bottom of messages
  const scrollToBottom = () => {
    if (scrollViewRef.current) {
      // Use scrollToEnd directly for reliable scrolling
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    // Scroll to bottom after a short delay when messages change.
    // A small delay helps ensure the new message has been rendered
    // and the scroll view has updated its content size.
    const timer = setTimeout(() => {
      scrollToBottom();
    }, 150); // Use a single, moderate delay (adjust if needed)

    // Cleanup the timeout if the component unmounts or messages change again
    return () => clearTimeout(timer);
  }, [messages]); // Dependency array means this effect runs when 'messages' state changes


  // Handle keyboard events to ensure scrolling when keyboard appears
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      scrollToBottom(); // Scroll to bottom when keyboard appears
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      // Optional: scroll down slightly when keyboard hides if input was near the bottom
      // scrollToBottom(100); // Keep or remove depending on desired behavior
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);


  // Function to send message to AI API
  const sendMessageToAI = async (userMessage: string) => {
    let finalChatResponse = '';
    try {
      setIsLoading(true);

      // Check for illegal words
      const { hasIllegalWords } = checkForIllegalWords(userMessage);
      if (hasIllegalWords) {
        Alert.alert(
          'Inappropriate Content',
          `Your message contains inappropriate content that violates our terms of service. Please revise your message.`,
          [{ text: 'OK' }]
        );
        setIsLoading(false);
        return;
      }

      const lowerMessage = userMessage.toLowerCase();

      // --- Step 1: Check for local commands first ---
      // If a local command is matched, handle it and set finalChatResponse.
      // Otherwise, proceed to call the API.
      if (lowerMessage.includes('battery') || lowerMessage.includes('battery level')) {
        finalChatResponse = await checkBatteryLevel();
      } else if (lowerMessage.includes('brightness') || lowerMessage.includes('screen brightness')) {
        if (lowerMessage.includes('set') || lowerMessage.includes('change')) {
          const match = lowerMessage.match(/\d+/);
          if (match) {
            const level = parseInt(match[0]) / 100;
            finalChatResponse = await setBrightness(Math.min(Math.max(level, 0), 1));
          } else {
            finalChatResponse = "Please specify a brightness level between 0 and 100.";
          }
        } else {
          finalChatResponse = await checkBrightness();
        }
      } else if (lowerMessage.includes('weather') || lowerMessage.includes('temperature')) {
        finalChatResponse = await checkWeather();
      } else if (lowerMessage.includes('train status') || lowerMessage.includes('check train')) {
        const trainNumberMatch = lowerMessage.match(/\d+/);
        if (trainNumberMatch) {
          const trainNumber = trainNumberMatch[0];
          // TODO: Implement checkTrainStatus function
          finalChatResponse = `Checking status for train number ${trainNumber}... (Train status check not yet implemented)`;
        } else {
          finalChatResponse = "Please provide a train number to check the status.";
        }
      }
      // --- Step 2: If no local command, call the external AI API ---
      else {
        try {
          const apiResponse = await fetch(AI_API_URL, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': 'supersecrettoken123'
            },
            body: JSON.stringify({
              message: userMessage, // Send the user's message
              user_id: authUserId, // Use the userId obtained from useAuth
            }),
          });

          if (!apiResponse.ok) {
            throw new Error(`API request failed with status ${apiResponse.status}`);
          }

          const data = await apiResponse.json();
          console.log("Raw API response data:", data);

          // --- Step 3: Process API response for actions or text ---
          const apiTextResponse = data.response || data.message || "";

          const embeddedAction = tryParseJsonFromResponseText(apiTextResponse);

          if (embeddedAction) {
            console.log("Detected action in embedded JSON:", embeddedAction);
            // Handle different actions based on the 'action' field
            if (embeddedAction.action === "send_email") {
              handleSendEmail(embeddedAction.to, embeddedAction.subject, embeddedAction.body);
              finalChatResponse = "Okay, I've prepared the email. Please check your email client.";
            } else if (embeddedAction.action === "open_app") {
              if (embeddedAction.target === "search" && embeddedAction.query) {
                handleSearchApp(embeddedAction.app, embeddedAction.query);
                finalChatResponse = `Okay, attempting to search ${embeddedAction.app || 'the requested app'} for "${embeddedAction.query}"...`;
              } else if (embeddedAction.uri) {
                handleOpenApp(embeddedAction.app, embeddedAction.uri, embeddedAction.target);
                finalChatResponse = `Okay, attempting to open content in ${embeddedAction.app || 'the requested app'}...`;
              } else if (embeddedAction.app) {
                handleOpenApp(embeddedAction.app);
                finalChatResponse = `Okay, attempting to open ${embeddedAction.app || 'the requested app'}...`;
              }
              else {
                console.warn("Received open_app action with unexpected structure:", embeddedAction);
                finalChatResponse = "I received a request to open an app, but the details were unclear.";
              }
            } else if (embeddedAction.action === "Call") {
              // Handle Call action
              const numberOrName = embeddedAction.number;
              // Call the handleCallAction and get the response message from it
              finalChatResponse = await handleCallAction(numberOrName); // handleCallAction now returns the response message
            }
            // If your API might still send 'search_app' as a top-level action:
            // else if (embeddedAction.action === "search_app") {
            //     handleSearchApp(embeddedAction.app, embeddedAction.query);
            //     finalChatResponse = `Okay, attempting to search ${embeddedAction.app || 'the requested app'} for "${embeddedAction.query}"...`;
            // }

            // Add handling for other actions here
          } else {
            console.log("No embedded action detected, using raw text response");
            finalChatResponse = apiTextResponse || "I'm sorry, I couldn't process that request.";
          }

          // --- Important Change: Stop loading BEFORE adding message ---
          setIsLoading(false); // Stop loading here

        } catch (error) {
          console.error('Error during API call or processing:', error);
          finalChatResponse = "I'm sorry, I encountered an error processing your request. Please try again later.";
          // Ensure loading stops even on API error
          setIsLoading(false);
        }
      }

      // --- Step 4: Add the determined response to messages ---
      // This now happens AFTER setIsLoading(false)
      if (finalChatResponse) { // Only add if there's something to show
        setMessages(prevMessages => [
          ...prevMessages,
          {
            id: Date.now() + 1,
            text: finalChatResponse,
            isUser: false,
            time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          }
        ]);
      }

    } catch (error) {
      // This catch block handles errors outside the API call try block
      console.error('General error in sendMessageToAI:', error);
      finalChatResponse = "An unexpected error occurred."; // Provide a fallback message
      // Ensure loading stops on any error
      setIsLoading(false);
    }
  };

  // Function to handle sending emails using the device's mail client
  const handleSendEmail = (to: string, subject: string, body: string) => {
    console.log("Attempting to open email client:", { to, subject, body });

    const url = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    Linking.openURL(url).catch(err => {
      console.error('Could not open email client:', err);
      Alert.alert(
        "Error",
        "Could not open email client. Please check if you have a mail app configured on your device."
      );
    });
  };

  // Function to handle opening external apps using Linking
  const handleOpenApp = (app: string, uri?: string, target?: string) => {
    console.log("Attempting to open app:", { app, uri, target });

    let urlToOpen = uri; // Default to provided uri

    // Define known app schemes/universal links
    const appLinks: { [key: string]: { scheme?: string; fallbackUrl?: string } } = {
      'spotify': { scheme: 'spotify://', fallbackUrl: 'https://open.spotify.com/' },
      'youtube': { scheme: 'youtube://', fallbackUrl: 'https://www.youtube.com/' },
      // Add other apps here
    };

    const appInfo = appLinks[app.toLowerCase()];

    if (uri) {
      // If a URI is provided, prioritize opening that specific URI
      if (appInfo && appInfo.scheme && uri.startsWith(appInfo.scheme)) {
        // If URI matches a known scheme, try the URI directly
        Linking.openURL(uri).catch(() => {
          console.log(`Failed to open URI via scheme: ${uri}, attempting HTTPS fallback`);
          if (appInfo.fallbackUrl) {
            const httpUri = uri.replace(appInfo.scheme!, appInfo.fallbackUrl).replace(/(track|album|artist|playlist):/, '$1/'); // Simple replacement for Spotify, might need adjustment for others
            Linking.openURL(httpUri).catch(err => {
              console.error(`Could not open ${app} content via URI or HTTPS:`, err);
              Alert.alert(
                "Error Opening Content",
                `Could not open content in ${app || 'the requested app'}.`
              );
            });
          } else {
            Alert.alert(
              "Error Opening Content",
              `Could not open content in ${app || 'the requested app'}.`
            );
          }
        });

      } else if (uri.startsWith('http://') || uri.startsWith('https://')) {
        // If it's a standard web URL, open it directly
        Linking.openURL(uri).catch(err => {
          console.error(`Could not open URL for ${app}: ${uri}`, err);
          Alert.alert(
            "Error Opening Link",
            `Could not open the link for ${app || 'the requested application'}.`
          );
        });
      }
      else {
        // If it's a URI but not a recognized scheme or http/s, try opening it anyway as a fallback
        Linking.openURL(uri).catch(err => {
          console.error(`Could not open unrecognized URI for ${app}: ${uri}`, err);
          Alert.alert(
            "Error Opening URI",
            `Could not open the specific content in ${app || 'the requested application'}.`
          );
        });
      }

    } else if (appInfo && appInfo.scheme) {
      // If no URI but a known app scheme exists, try opening the app using the scheme
      Linking.openURL(appInfo.scheme).catch(() => {
        console.log(`Failed to open app via scheme: ${appInfo.scheme}, attempting HTTPS fallback`);
        if (appInfo.fallbackUrl) {
          // Fallback to the app's main HTTPS URL if the scheme fails
          Linking.openURL(appInfo.fallbackUrl).catch(err => {
            console.error(`Could not open ${app} via scheme or HTTPS URL:`, err);
            Alert.alert(
              "Error Opening App",
              `Could not open ${app || 'the requested application'}. Please ensure it is installed.`
            );
          });
        } else {
          Alert.alert(
            "Error Opening App",
            `Could not open ${app || 'the requested application'}.`
          );
        }
      });
    } else if (appInfo && appInfo.fallbackUrl) {
      // If no URI or scheme, but a fallback URL exists, try opening the fallback URL
      Linking.openURL(appInfo.fallbackUrl).catch(err => {
        console.error(`Could not open ${app} via fallback URL:`, err);
        Alert.alert(
          "Error Opening App",
          `Could not open ${app || 'the requested application'}.`
        );
      });
    }
    else {
      // If no specific handling is defined for the app and no URI/URL provided
      console.warn(`No specific open handling for app: ${app}, and no URI provided.`);
      Alert.alert(
        "Unsupported App",
        `Opening ${app || 'this application'} is not currently supported.`
      );
    }
  };

  // Function to handle searching within external apps using Linking
  const handleSearchApp = (app: string, query: string) => {
    console.log("Attempting to search in app:", { app, query });

    const encodedQuery = encodeURIComponent(query);

    if (app.toLowerCase() === 'spotify') {
      const spotifySearchUri = `spotify:search:${encodedQuery}`;
      const spotifySearchHttpUrl = `https://open.spotify.com/search/${encodedQuery}`;

      Linking.openURL(spotifySearchUri)
        .catch(() => {
          console.log(`Failed to open Spotify search URI: ${spotifySearchUri}, attempting HTTPS fallback: ${spotifySearchHttpUrl}`);
          Linking.openURL(spotifySearchHttpUrl)
            .catch(err => {
              console.error('Could not open Spotify search via URI or HTTPS:', err);
              Alert.alert(
                "Error Searching Spotify",
                "Could not perform search in Spotify. Please ensure the Spotify app is installed and you have an internet connection."
              );
            });
        });
    } else if (app.toLowerCase() === 'youtube') {
      // YouTube search using their URI scheme
      const youtubeSearchUri = `youtube://results?search_query=${encodedQuery}`;
      const youtubeSearchHttpUrl = `https://www.youtube.com/results?search_query=${encodedQuery}`;

      Linking.openURL(youtubeSearchUri)
        .catch(() => {
          console.log(`Failed to open YouTube search URI: ${youtubeSearchUri}, attempting HTTPS fallback: ${youtubeSearchHttpUrl}`);
          Linking.openURL(youtubeSearchHttpUrl)
            .catch(err => {
              console.error('Could not open YouTube search via URI or HTTPS:', err);
              Alert.alert(
                "Error Searching YouTube",
                "Could not perform search in YouTube. Please ensure the YouTube app is installed and you have an internet connection."
              );
            });
        });
    }
    else {
      Alert.alert(
        "Search Not Supported",
        `Searching directly in ${app || 'this application'} is not currently supported.`
      );
    }
  };

  // Function to handle initiating a phone call
  // Now attempts to search contacts by name if the input doesn't look like a number
  const handleCallAction = async (numberOrName: string): Promise<string> => { // Return a string promise for the chat response
    console.log("Attempting to initiate call with:", numberOrName);

    const cleanedNumberOrName = numberOrName.trim();
    const isNumeric = /^\+?\d[\d\s()-]*$/.test(cleanedNumberOrName); // Basic check for a number format (allows +, digits, spaces, hyphens, parens)

    let callUrl = `tel:${cleanedNumberOrName}`;
    let responseMessage = '';

    if (!isNumeric && cleanedNumberOrName) {
      // It looks like a name, try to search contacts
      console.log(`Input "${cleanedNumberOrName}" looks like a name, attempting contact search.`);
      try {
        const { status } = await Contacts.getPermissionsAsync();
        if (status !== 'granted') {
          responseMessage = `Please grant contact permissions to search for "${cleanedNumberOrName}".`;
        } else {
          const { data } = await Contacts.getContactsAsync({
            name: cleanedNumberOrName, // Search by name
            fields: [Contacts.Fields.PhoneNumbers],
          });

          if (data && data.length > 0) {
            // Found contacts matching the name
            const firstContact = data[0];
            if (firstContact.phoneNumbers && firstContact.phoneNumbers.length > 0) {
              // Found a phone number for the contact
              const phoneNumber = firstContact.phoneNumbers[0].number;
              if (phoneNumber) {
                console.log(`Found number for ${cleanedNumberOrName}: ${phoneNumber}`);
                callUrl = `tel:${phoneNumber.replace(/\D/g, '')}`; // Clean the number (remove non-digits) for tel: URI
                responseMessage = `Okay, attempting to call ${firstContact.name || cleanedNumberOrName}...`;
              } else {
                console.warn(`Contact "${cleanedNumberOrName}" found, but no phone number available.`);
                // Fallback to trying the name in tel: URI if no number is found
                responseMessage = `Found contact "${firstContact.name || cleanedNumberOrName}", but no number is listed. Attempting to open dialer with the name.`;
                callUrl = `tel:${cleanedNumberOrName}`; // Use the original name in the URI
              }

            } else {
              console.warn(`Contact "${cleanedNumberOrName}" found, but has no phone numbers.`);
              // Fallback to trying the name in tel: URI if no numbers are listed
              responseMessage = `Found contact "${firstContact.name || cleanedNumberOrName}", but has no phone numbers. Attempting to open dialer with the name.`;
              callUrl = `tel:${cleanedNumberOrName}`; // Use the original name in the URI
            }
          } else {
            // No contacts found matching the name
            console.log(`No contact found matching "${cleanedNumberOrName}".`);
            responseMessage = `Couldn't find a contact named "${cleanedNumberOrName}". Attempting to open dialer with the name provided.`;
            callUrl = `tel:${cleanedNumberOrName}`; // Use the original name in the URI
          }
        }
      } catch (error) {
        console.error('Error searching contacts:', error);
        responseMessage = `An error occurred while searching for "${cleanedNumberOrName}" in contacts. Attempting to open dialer with the name.`;
        callUrl = `tel:${cleanedNumberOrName}`; // Use the original name in the URI
      }
    } else if (!cleanedNumberOrName) {
      // Input was empty or just whitespace
      responseMessage = "Please provide a name or number to call.";
      // No call will be attempted
      console.log("Call action received with empty input.");
      return Promise.resolve(responseMessage); // Return early
    }
    else {
      // It looks like a number
      responseMessage = `Okay, attempting to call ${cleanedNumberOrName}...`;
      // callUrl is already set to tel:cleanedNumberOrName
    }

    // Attempt to open the dialer with the determined URL
    try {
      const supported = await Linking.canOpenURL(callUrl);
      if (!supported) {
        console.warn(`Can't handle url: ${callUrl}`);
        // If linking is not supported, update the response message
        if (responseMessage.includes("Attempting to call")) { // Check if it's one of the "attempting to call" messages
          responseMessage += " (Linking not supported on this device).";
        } else {
          responseMessage = `Cannot initiate call with "${cleanedNumberOrName}" (Linking not supported).`;
        }

      } else {
        await Linking.openURL(callUrl);
        // The success message is already set in the logic above
      }
    } catch (err) {
      console.error(`Linking.openURL error for ${callUrl}:`, err);
      // Update response message on Linking error
      if (responseMessage.includes("Attempting to call")) { // Check if it's one of the "attempting to call" messages
        responseMessage += " (Call initiation failed).";
      } else {
        responseMessage = `Could not initiate call with "${cleanedNumberOrName}".`;
      }
    }

    // Return the final response message to be displayed in the chat
    return Promise.resolve(responseMessage);
  };


  // Handle sending a message (slightly modified to receive message as argument)
  const handleSendMessage = (message: string) => { // Now explicitly expects message string
    // Check if message is empty or loading
    if (!message || isLoading) return;

    // Add user message to chat
    const newMessage = {
      id: Date.now(),
      text: message,
      isUser: true,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prevMessages => [...prevMessages, newMessage]);

    // Ensure scroll happens after message is added
    scrollToBottom();

    // Note: inputText clear is handled by ChatInputArea now

    // Send to AI API
    sendMessageToAI(message);

    // Additional scroll after a short delay to ensure UI has updated
    scrollToBottom();
  };

  const suggestions = [
    { id: 1, text: 'Weekend plan', icon: '📅', color: 'text-purple-500' },
    { id: 2, text: 'Tell me a joke', icon: '😂', color: 'text-yellow-500' },
    { id: 3, text: 'Tell me a story', icon: '📖', color: 'text-gray-500' },
    { id: 4, text: 'Surprise me', icon: '✨', color: 'text-yellow-500' },
    { id: 5, text: 'Tell me about...', icon: '💬', color: 'text-green-500' },
    { id: 6, text: 'Who are you?', icon: '🤖', color: 'text-blue-500' },
    { id: 7, text: 'Help me focus', icon: '🎯', color: 'text-red-500' },
  ];

  // Split into 2 rows manually
  const midpoint = Math.ceil(suggestions.length / 2);
  const firstRow = suggestions.slice(0, midpoint);
  const secondRow = suggestions.slice(midpoint);

  useEffect(() => {
    const fetchUserName = async () => {
      try {
        const userDetailsString = await AsyncStorage.getItem('userDetails');
        if (userDetailsString) {
          const userDetails = JSON.parse(userDetailsString);
          if (userDetails && userDetails.username) {
            setUserName(userDetails);
          }
          console.log(userDetails);
        }
      } catch (error) {
        console.error("Failed to load user name from storage", error);
      }
    };
    fetchUserName();
  }, []);

  return (
    <ImageBackground
      source={require('@/assets/screens/AIBackgroundScreen.png')}
      style={tw`flex-1`}
      resizeMode="cover"
    >
      <SafeAreaView style={tw`flex-1`}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={tw`flex-1`}
        >
          {messages.length === 0 ? (
            <>
             <View style={tw`flex-row justify-between items-center p-4`}>
                <TouchableOpacity onPress={() => {
                  navigation.reset({
                    index: 0,
                    routes: [
                      { name: 'BottomTabs' as keyof RootStackParamList, params: { initialTab: 'Search' } }
                    ],
                  });
                }}>
                  <MoveLeft size={24} color="#B9B9B9" />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => { }}>
                  <EllipsisVertical size={24} color="#B9B9B9" />
                </TouchableOpacity>
              </View>
              <View style={tw`flex-1 items-center justify-center gap-2`}>
                <Text style={[tw`text-[25px] capitalize`, { fontFamily: 'Geist-Medium' }]}>
                  Hey {(userName?.username
  ? userName.username.length > 5
    ? userName.username.slice(0, 5) + '...'
    : userName.username
  : 'User')}👋

                </Text>
                <Text style={[tw`text-[25px]`, { fontFamily: 'Geist-Medium' }]}>
                  How Can I help you?
                </Text>
                <View style={tw`p-4 h-35`}>
                  <AutoScrollView data={firstRow} direction="left" onChipPress={handleSendMessage} />
                  <AutoScrollView data={secondRow} direction="right" onChipPress={handleSendMessage} />
                </View>
              </View>
              <ChatInputArea onSendMessage={handleSendMessage} isLoading={isLoading} />
            </>
          ) : (
            <>
              <View style={tw`flex-row justify-between items-center p-4`}>
                <TouchableOpacity onPress={() => {
                  navigation.reset({
                    index: 0,
                    routes: [
                      { name: 'BottomTabs' as keyof RootStackParamList, params: { initialTab: 'Search' } }
                    ],
                  });
                }}>
                  <MoveLeft size={24} color="#B9B9B9" />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => { }}>
                  <EllipsisVertical size={24} color="#B9B9B9" />
                </TouchableOpacity>
              </View>
              <ScrollView
                ref={scrollViewRef}
                contentContainerStyle={tw`p-4 flex-grow`}
                keyboardShouldPersistTaps="handled"
              >
                {messages.map((item) => (
                  <MessageBubble key={item.id} message={item} isUser={item.isUser} />
                ))}
                <TypingIndicator isVisible={isLoading} />
              </ScrollView>
              <ChatInputArea onSendMessage={handleSendMessage} isLoading={isLoading} />
            </>
          )}
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
}