import {
  View,
  Text,
  ImageBackground,
  StatusBar,
  TextInput,
  TouchableOpacity,
  Keyboard,
  ScrollView,
  ActivityIndicator,
  Pressable
} from 'react-native';
import React, { useState, useRef, useEffect } from 'react';
import tw from 'twrnc';
import { useColorScheme } from 'react-native';
import useAuth from '@/hooks/auth';
import { useKeepAwake } from 'expo-keep-awake';
import { Svg, Path } from 'react-native-svg';
import { ArrowDown, Delete, EllipsisVertical, Info, Send, Trash, X, Mic } from 'lucide-react-native';
import { Image } from 'moti';
import axios from 'axios';
import { MotiView } from 'moti';
import { AnimatePresence } from 'moti';
import { useNavigation } from '@react-navigation/native';
import { Platform, KeyboardAvoidingView, Dimensions } from 'react-native';
import { TouchableWithoutFeedback } from 'react-native';
import { BackHandler } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Easing } from 'react-native-reanimated';

// Define message type
interface Message {
  role: 'user' | 'bot';
  text: string;
}

export default function Ai() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  useKeepAwake();
  const authUserId = useAuth();
  const navigation = useNavigation<any>();
  const textInputRef = useRef<TextInput>(null);

  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const scrollViewRef = useRef<ScrollView>(null);
  const [typing, setTyping] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const [infoVisible, setInfoVisible] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [selectedModel, setSelectedModel] = useState('Neorox Flash') // Default
  const [modelSelectVisible, setModelSelectVisible] = useState(false)

 
  const toggleMenu = () => setMenuVisible((prev) => !prev);

  const handleSend = async () => {
    if (!message.trim() || loading) return;
    Keyboard.dismiss();
    setLoading(true);
    setTyping(true);

    const userMessage: Message = { role: 'user', text: message };
    setMessages((prev) => [...prev, userMessage]);
    setMessage('');

    try {
      const res = await axios.post('https://nx.ai.api.nityasha.com/chat', {
        message,
        user_id: authUserId,
      });

      const botMessage: Message = {
        role: 'bot',
        text: res.data.response || 'Sorry, no response.',
      };
      setMessages((prev) => [...prev, botMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prev) => [
        ...prev,
        { role: 'bot', text: '❌ Error getting response' },
      ]);
    }

    setTyping(false);
    setLoading(false);
  };

  useEffect(() => {
    const showSub = Keyboard.addListener("keyboardDidShow", (e) => {
      setKeyboardVisible(true);
      setKeyboardHeight(e.endCoordinates.height);
    });
    const hideSub = Keyboard.addListener("keyboardDidHide", () => {
      setKeyboardVisible(false);
      setKeyboardHeight(0);
    });
    return () => {
      showSub.remove();
      hideSub.remove();
    };
  }, []);

  const focusInput = () => {
    textInputRef.current?.focus();
  };

  return (
    <View style={tw`flex-1`}>
      <ImageBackground
        source={
          isDark
            ? require('@/assets/screens/AIBackgroundScreenDark.png')
            : require('@/assets/screens/AIBackgroundScreen.png')
        }
        style={tw`flex-1`}
      >
        <StatusBar
          barStyle={isDark ? "light-content" : "dark-content"}
          backgroundColor={isDark ? "#000" : "#fff"}
          translucent
        />
        
        <View style={tw`absolute`}>
          <Svg width="440" height="947" viewBox="0 0 440 947" fill="none">
            <Path
              d="M80.463 8.00733V122.627M80.463 750.284H3.05556H140.556V773.308M80.463 750.284V693.725M80.463 750.284V887.928M140.556 773.308V796.332V957M140.556 773.308L114.074 797.834L220.509 693.725M220.509 693.725V281.794M220.509 693.725L80.9722 557.582M220.509 693.725H80.463M220.509 693.725L359.537 557.582M220.509 693.725L326.944 798.334M220.509 693.725L359.359 692.223M220.509 693.725C220.509 772.222 220.509 824.389 220.509 890.43M220.509 1V69.0712M80.463 693.725H21.3889V887.928H80.463M80.463 693.725V670.701M80.463 693.725C80.463 769.637 80.463 914.756 80.463 887.928M80.463 887.928L1.52778 809.846M80.463 670.701L1.01852 593.119M80.463 670.701V534.058M1.01852 692.223L141.065 555.219M439.491 693.725L220 477.999L141.065 555.219M80.463 474.996L1.01852 397.415M80.463 474.996V477.999M80.463 474.996V417.936M80.463 477.999H21.8981V281.794H218.981M80.463 477.999V534.058M218.981 281.794H220.509M218.981 281.794L80.463 145.651M359.537 477.999H419.12L418.611 281.794H220.509M359.537 477.999V534.058M359.537 477.999V475.496M220.509 281.794L359.537 417.936V475.496M220.509 281.794L80.463 417.936M220.509 281.794L331.019 173.578M220.509 281.794V69.0712M80.463 417.936V259.27M1.01852 339.854H141.065V534.058M141.065 555.219V534.058M141.065 534.058H80.463M220.509 957C220.509 932.682 220.509 910.89 220.509 890.43M359.537 557.582V534.058M359.537 557.582L359.386 671.702M359.537 534.058H298.935V339.854H359.537M440 339.854H359.537M359.537 339.854V259.27M359.537 1V65.5675M359.537 65.5675L418.102 4.50366V65.5675H359.537ZM359.537 65.5675V122.627M359.537 122.627H299.444V1M359.537 122.627V145.651M359.537 145.651L331.019 173.578M359.537 145.651V259.27M331.019 173.578L220.509 69.0712M331.019 173.578L438.472 280.793M220.509 69.0712L0 281.794M359.537 259.27L438.472 182.69M80.463 145.651V259.27M80.463 145.651V122.627M80.463 259.27L1.01852 181.689M299.444 798.334V750.284H359.282M439.491 750.284H419.12M419.12 750.284L420.648 827.865L437.963 810.347M419.12 750.284H359.282M419.12 750.284V693.725L359.359 692.223M359.282 750.284L359.359 692.223M359.359 692.223L359.386 671.702M359.386 671.702L439.491 592.619M359.537 475.496L439.491 397.415M80.463 122.627H141.065L142.083 1M220.509 890.43L193.009 915.457M220.509 890.43L244.954 915.457M21.8981 8.00733V65.5675H80.463L21.8981 8.00733Z"
              stroke={isDark ? '#BBEAC3' : '#9CC2A4'}
              strokeOpacity="0.23"
              strokeWidth="3"
            />
          </Svg>
        </View>

        {modelSelectVisible && (
          <Pressable
            onPress={() => setModelSelectVisible(false)}
            style={tw`absolute top-0 left-0 right-0 bottom-0 z-40`}
          />
        )}

        {/* Info Panel */}
        <AnimatePresence>
          {infoVisible && (
            <MotiView
              from={{ translateX: 400, opacity: 0 }}
              animate={{ translateX: 0, opacity: 1 }}
              exit={{ translateX: 400, opacity: 0 }}
              transition={{ type: 'timing', duration: 300 }}
              style={tw`absolute top-0 left-0 w-full h-full ${isDark ? 'bg-black' : 'bg-white'}  z-100 p-5`}
            >
              {/** Define once */}
              {(() => {
                const textColor = isDark ? 'text-white' : 'text-black';

                return (
                  <>
                    <View style={tw`flex-row justify-left items-center mb-5`}>
                      <TouchableOpacity onPress={() => setInfoVisible(false)}>
                        <X size={24} color={isDark ? '#fff' : '#000'} />
                      </TouchableOpacity>
                    </View>

                    <View style={tw`items-center justify-center`}>
                      <View style={tw`w-20 h-20`}>
                        <Image
                          source={require('@/assets/images/image.png')}
                          style={tw`w-full h-full scale-150`}
                          resizeMode="contain"
                          tintColor={isDark ? '#fff' : undefined} // 👈 Invert using tint
                        />
                      </View>
                      <Text style={[tw`${textColor} text-[20px] font-bold`,]}>Nityasha</Text>
                    </View>

                    <Text style={tw`${textColor} text-base mt-6`}>Model Info</Text>
                    <View>
                      <Text style={tw`${textColor} text-base mt-6 font-bold`}>Auto</Text>
                      <Text style={tw`${textColor} text-[12px]`}>Use the right model for my request</Text>
                    </View>
                  </>
                );
              })()}
            </MotiView>
          )}
        </AnimatePresence>

        {/* Header */}
        <View style={tw`absolute top-0 left-0 right-0 flex-row items-center justify-between px-4 py-5 z-50`}>
          <TouchableOpacity onPress={() => setModelSelectVisible(!modelSelectVisible)} style={tw`flex-row items-center justify-center`}>
            <Text style={[tw`text-[16px] ${isDark ? "text-white" : "text-black"}`, { fontFamily: 'Helvetica_bold' }]}>{selectedModel}</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={toggleMenu}>
            <EllipsisVertical size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>

          <AnimatePresence>
            {menuVisible && (
              <MotiView
                from={{ opacity: 0, translateY: -10 }}
                animate={{ opacity: 1, translateY: 0 }}
                exit={{ opacity: 0, translateY: -10 }}
                transition={{ type: 'timing', duration: 200 }}
                style={tw`absolute top-16 right-1 ${isDark ? 'bg-black border-zinc-700' : 'bg-white border-gray-200'} rounded px-4 w-[140px] py-1 z-50 border `}
              >
                <TouchableOpacity onPress={() => { setMessages([]); setMenuVisible(false); }} style={tw`py-3 flex-row items-center gap-2`}>
                  <Trash size={20} color={isDark ? '#fff' : '#000'} />
                  <Text style={tw`${isDark ? 'text-white' : 'text-black'} text-base`}>Delete</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => { setInfoVisible(true); setMenuVisible(false); }} style={tw`py-3 flex-row items-center gap-2`}>
                  <Info size={20} color={isDark ? '#fff' : '#000'} />
                  <Text style={tw`${isDark ? 'text-white' : 'text-black'} text-base`}>View Info</Text>
                </TouchableOpacity>
              </MotiView>
            )}
          </AnimatePresence>
        </View>

        {/* Welcome Text */}
        {messages.length === 0 && (
          <View style={tw`absolute w-full items-center justify-center top-70 opacity-55`}>
            <Text style={tw`${isDark ? 'text-white' : 'text-black'} text-[22px] font-bold`}>Nityasha</Text>
            <Text style={tw`${isDark ? 'text-white' : 'text-black'} text-[22px] font-bold`}>Your Personal</Text>
            <Text style={tw`${isDark ? 'text-white' : 'text-black'} text-[22px] font-bold`}>Assistant</Text>
          </View>
        )}

        {/* Logo */}
        {messages.length === 0 && (
          <View style={tw`absolute items-center justify-center w-full top-43`}>
            <Image
              source={require('@/assets/images/image.png')}
              style={tw`w-30 h-30`}
              resizeMode="contain"
              tintColor={isDark ? '#fff' : undefined} // 👈 Invert using tint
            />
          </View>
        )}

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          keyboardShouldPersistTaps="handled"
          style={tw`w-full flex-1 px-2 pt-20`}
          contentContainerStyle={{ paddingBottom: keyboardVisible ? keyboardHeight + 100 : 100 }}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          {messages.map((m, idx) => (
            <MotiView
              key={idx}
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 300, delay: idx * 100 }}
              style={tw`flex w-full mb-5 ${m.role === 'user' ? 'items-end' : 'items-start'}`}
            >
              <View
                style={[
                  tw`max-w-[80%] rounded-2xl py-2 px-2`,
                  m.role === 'user'
                    ? isDark
                      ? tw`bg-[#2f2f2f]`   // User msg dark mode bg
                      : tw`bg-gray-100`   // User msg light mode bg
                    : isDark
                      ? tw`bg-[#1a1a1a]`     // AI msg dark mode bg
                      : tw`bg-white`         // AI msg light mode bg
                ]}
              >
                <Text
                  style={tw`text-[15px] ${m.role === 'user'
                    ? 'text-black'
                    : isDark
                      ? 'text-white'
                      : 'text-black'
                    }`}
                >
                  {m.text}
                </Text>
              </View>
            </MotiView>
          ))}

          {typing && (
            <MotiView
              from={{ opacity: 0, translateY: 10 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 200 }}
              style={tw`w-full items-start mt-2`}
            >
              <View style={tw`bg-gray-200 px-1 py-3 rounded-[7px] max-w-[70%] flex-row`}>
                {[0, 1, 2].map((index) => (
                  <MotiView
                    from={{ opacity: 0.3, translateY: 0 }}
                    animate={{ opacity: 1, translateY: -4 }}
                    transition={{ type: 'timing', duration: 500, delay: index * 150, loop: true, repeatReverse: true }}
                    key={index}
                    style={tw`w-2 h-2 rounded-full bg-black mx-0.5`}
                  />
                ))}
              </View>
            </MotiView>
          )}
        </ScrollView>

        {/* Input Container - Fast response */}
        <MotiView
          animate={{ 
            bottom: keyboardVisible ? keyboardHeight : 0,
          }}
          transition={{ 
            type: 'timing', 
            duration: 150,
            easing: Easing.out(Easing.cubic)
          }}
          style={tw`absolute left-0 right-0 px-2 pb-3`}
        >
          <MotiView
            animate={{ 
              scale: keyboardVisible ? 1.01 : 1,
            }}
            transition={{ 
              type: 'timing', 
              duration: 100 
            }}
            style={[
              tw`border-2 w-full rounded-[20px] px-1 pl-4 h-15 flex-row items-center justify-between`,
              {
                backgroundColor: isDark ? '#1a1a1a' : '#fff',
                borderColor: isDark ? '#333' : '#ccc',
              },
            ]}
          >
            <TextInput
              ref={textInputRef}
              value={message}
              onChangeText={setMessage}
              placeholder="Ask anything here..."
              placeholderTextColor={isDark ? '#888' : '#888'}
              multiline
              textAlignVertical="top"
              autoFocus={false}
              blurOnSubmit={false}
              style={[
                tw`flex-1 py-2 pr-2`,
                {
                  color: isDark ? '#fff' : '#000',
                  fontFamily: 'Helvetica_bold',
                  maxHeight: 120,
                },
              ]}
            />
            <MotiView
              animate={{ 
                scale: message.trim() ? 1.05 : 1,
                rotate: message.trim() ? '3deg' : '0deg'
              }}
              transition={{ 
                type: 'timing', 
                duration: 100 
              }}
            >
              <TouchableOpacity
                onPress={() => {
                  if (message.trim()) {
                    handleSend();
                  } else {
                    navigation.navigate('VoiceAi');
                  }
                }}
                disabled={loading}
                style={tw`h-[48px] w-[48px] items-center justify-center rounded-[14px] bg-black`}
              >
                {typing && message.trim() ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : message.trim() ? (
                  <Send size={20} color="#fff" />
                ) : (
                  <Mic size={20} color="#ffffff" />
                )}
              </TouchableOpacity>
            </MotiView>
          </MotiView>
        </MotiView>
      </ImageBackground>
    </View>
  );
}
