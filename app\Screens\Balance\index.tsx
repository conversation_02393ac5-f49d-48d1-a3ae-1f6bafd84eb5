import React, { useState, useEffect } from 'react';
import { 
  Alert, ActivityIndicator, Text, TouchableOpacity, TextInput, View, StatusBar, Image, KeyboardAvoidingView, ScrollView, Keyboard, TouchableWithoutFeedback, Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import RazorpayCheckout from 'react-native-razorpay';
import { LinearGradient } from 'expo-linear-gradient';
import * as NavigationBar from 'expo-navigation-bar';
import { ArrowLeftIcon, UserIcon, WalletIcon } from 'lucide-react-native';
import PaymentPopup from '@/components/paymentpopup'; 

// Add these types at the top of the file after the imports
type UserData = {
  userId: string;
  balance: number;
  // add other user properties as needed
};

type RechargeOption = {
  amount: number;
  // add other properties if they exist
};

export default function Balance() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(false);
  const [customAmount, setCustomAmount] = useState('');
  const [selectedAmount, setSelectedAmount] = useState(0);
  const navigation = useNavigation<any>();
  const [rechargeOptions, setRechargeOptions] = useState<RechargeOption[]>([]);

  const handleRecharge = (amount: number) => setSelectedAmount(amount);

  const handleCustomRecharge = () => {
    const amount = parseFloat(customAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid amount greater than 0');
      return;
    }
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handlePayment = async () => {
    try {
      // Check if amount is selected
      if (!selectedAmount || selectedAmount <= 0) {
        Alert.alert('Error', 'Please select or enter an amount first');
        return;
      }

      setLoading(true);

      // Retrieve user session data
      const userSession = await AsyncStorage.getItem('userSession');
      const user = userSession ? JSON.parse(userSession) : null;

      if (!user) {
        Alert.alert('Error', 'Please login to continue with payment');
        navigation.navigate('Login');
        return;
      }

      const response = await fetch('https://api.nityasha.com/api/v1/paymemt/2');
      const data = await response.json();

      if (!data || !data.api) {
        Alert.alert('Error', 'Failed to fetch Razorpay API key');
        return;
      }

      const options = {
        description: 'Wallet Recharge',
        image: 'https://nityasha.com/logo-dark.svg',
        currency: 'INR',
        key: data.api,
        amount: selectedAmount * 100,
        name: 'Nityasha Services',
        theme: { color: data.color },
        order_id: '', // Add this required field - you should get this from your backend
      };


      const paymentResponse = await RazorpayCheckout.open(options);

      if (paymentResponse.razorpay_payment_id) {
        const updatedBalance = user.balance + selectedAmount;
        const balanceUpdateResponse = await fetch(`https://nityasha.vercel.app/api/v1/users/${user.userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            balance: updatedBalance,
          }),
        });

        if (balanceUpdateResponse.ok) {
          // Update user data in AsyncStorage
          const updatedUser = { ...user, balance: updatedBalance };
          await AsyncStorage.setItem('userSession', JSON.stringify(updatedUser));

          Alert.alert('Success', 'Payment successful and balance updated!');
          setSelectedAmount(0);
        } else {
          Alert.alert('Error', 'Failed to update balance. Please try again.');
        }
      }
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert('Error', 'Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const userId = JSON.parse(userSession).userId;
          const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);

          if (response.ok) {
            const data = await response.json();
            setUserData(data);
          } else {
            Alert.alert('Error', 'Failed to fetch user data');
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        Alert.alert('Error', 'Could not fetch user data.');
      }
    };

    fetchUserData();
  }, []);
  useEffect(() => {
    const fetchRechargeOptions = async () => {
      try {
        const response = await fetch('https://nityasha.vercel.app/api/v1/rechargeoptions');
        if (response.ok) {
          const data = await response.json();
          setRechargeOptions(data);
        } else {
          Alert.alert('Error', 'Failed to fetch recharge options');
        }
      } catch (error) {
        console.error('Error fetching recharge options:', error);
        Alert.alert('Error', 'Could not fetch recharge options.');
      }
    };
  
    fetchRechargeOptions();
  }, []);

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === "ios" ? "padding" : "height"} 
        style={tw`flex-1`}
      >
        <LinearGradient colors={['#fdfcfb', '#e2d1c3']} style={tw`flex-1`}>
          <StatusBar backgroundColor="#fdfcfb" />

          {/* Header */}
          <View style={tw`flex-row justify-between items-center px-5 bg-white h-16 shadow-lg`}>
            <View style={tw`flex-row items-center gap-2`}>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <ArrowLeftIcon size={24} color="black" />
              </TouchableOpacity>
              <View style={tw`w-10 h-10 bg-black rounded-[15px] flex items-center justify-center`}>
                <WalletIcon size={24} color="white" />
              </View>
              <Text style={tw`text-black text-lg font-semibold`}>Nityasha Services</Text>
            </View>
            <TouchableOpacity style={tw`flex-row items-center gap-2`} onPress={() => navigation.navigate('Profile')}>
              <UserIcon size={24} color="black" />
            </TouchableOpacity>
          </View>

          <ScrollView contentContainerStyle={tw`flex-grow items-center px-4 pb-20`}>
            {/* Profile and Info */}
            <View style={tw`items-center gap-2 mt-10`}>
              <View style={tw`w-20 h-20 bg-black rounded-full flex items-center justify-center overflow-hidden`}>
                <Image source={require('../../../assets/images/icon.png')} style={tw`w-30 h-30`} />
              </View>
              <Text style={tw`text-black text-lg font-semibold`}>Add Money to Wallet</Text>
              <Text style={[tw`text-black text-sm`, { fontFamily: "Geist-SemiBold" }]}>You can add money to your wallet using UPI</Text>
              <View style={tw`flex-row items-center gap-2 rounded-full bg-black py-1 px-2`}>
              <Text style={[tw`text-white text-sm`, { fontFamily: "Geist-SemiBold" }]}>Available Balance: ₹ {userData?.balance || '0.00'}</Text>
              </View>
            </View>

            {/* Recharge Options */}
            <View style={tw`flex-wrap flex-row justify-center gap-2 max-w-[500px] mt-5`}>
              {rechargeOptions.map((amount, index) => (
                <TouchableOpacity
                  key={index}
                  style={tw`border-2 border-black rounded-lg h-[40px] px-4 w-[30%] flex items-center justify-center mb-2`}
                  onPress={() => handleRecharge(amount.amount)}
                >
                  <Text style={tw`text-center text-base font-semibold`}>₹ {amount.amount}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Custom Amount Input */}
            <View style={tw`flex-row items-center gap-2 mt-6`}>
              <TextInput
                style={[tw`border-2 border-black rounded-lg h-[40px] px-4 w-[75%] text-lg`, { fontFamily: "Geist-SemiBold" }]}
                placeholder="Enter Amount"
                placeholderTextColor="black"
                keyboardType="numeric"
                value={customAmount}
                onChangeText={setCustomAmount}
              />
              <TouchableOpacity style={tw`bg-black px-5 py-2 rounded-lg`} onPress={handleCustomRecharge}>
                <Text style={[tw`text-white text-[20px] font-semibold`, { fontFamily: "Geist-SemiBold" }]}>Add</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>

          <View style={tw`absolute bottom-0 left-0 right-0 bg-white shadow-lg p-2 flex-row justify-between items-center px-5`}>
            <Text style={tw`text-black text-lg font-semibold`}>₹ {selectedAmount || '0.00'}</Text>
            <TouchableOpacity 
              onPress={handlePayment}
              style={tw`bg-black px-6 py-3 rounded-lg w-[75%] flex items-center justify-center`}
            >
              <Text style={tw`text-white text-[16px] font-semibold`}>Pay Now</Text>
            </TouchableOpacity>
          </View>        
        </LinearGradient>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}
