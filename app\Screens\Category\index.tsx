import React, { useEffect, useState } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  Text,
  ScrollView,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import Headingsixth from '@/components/ui/Headingsixth';
import { useNavigation } from '@react-navigation/native';
import { BlurView } from "expo-blur";
import PaymentPopup from '@/components/paymentpopup';
import { Bell } from 'lucide-react-native';
import Wallets from '@/components/Wallet';
import Logo from '@/assets/images/image.png'

const Category = ({ route }) => {
  const { id } = route.params; // Category ID passed from the route
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [balance, setBalance] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [isNewUser, setIsNewUser] = useState(null);
  const navigation = useNavigation();
  const rechargeMinimumAmount = 1; // Set a minimum recharge amount
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [showPaymentPopup, setShowPaymentPopup] = useState(false)
  const [selectedRate, setSelectedRate] = useState(0);
  const [Username, setUsername] = useState('')

  // Fetch consultants from the API and filter by category id
  const fetchConsultants = async () => {
    setLoading(true);
    try {
      const response = await fetch('https://nityasha.vercel.app/api/v1/home/<USER>');
      if (!response.ok) throw new Error('Failed to fetch consultants.');
      const data = await response.json();
      // Filter consultants based on the category id
      const filteredConsultants = data.filter(consultant => consultant.categories === id);
      setConsultants(filteredConsultants);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch user balance
  const fetchUserBalance = async (userId) => {
    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
      if (!response.ok) throw new Error('Failed to fetch balance');
      const userData = await response.json();
      setBalance(parseFloat(userData.balance) || 0);
    } catch (err) {
      setError(err.message);
    }
  };

  // Check login status and fetch consultants
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const userId = JSON.parse(userSession).userId;
          await fetchUserBalance(userId);
          const userStatus = await AsyncStorage.getItem('isNewUser');
          setIsNewUser(userStatus === '0');
        } else {
          navigation.navigate('Welcome');
        }
      } catch (error) {
        console.error("Error checking login status:", error);
      }
    };

    checkLoginStatus();
    fetchConsultants();

    const intervalId = setInterval(fetchConsultants, 6000);
    return () => clearInterval(intervalId);
  }, [navigation]);

  // Update user balance
  const updateBalance = async (newBalance) => {
    const userSession = await AsyncStorage.getItem('userSession');
    const userId = JSON.parse(userSession).userId;

    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ balance: newBalance }),
      });

      if (response.ok) {
        if (newBalance !== balance) {
          Alert.alert(`Balance updated. Your new balance is ₹${newBalance}.`);
          setBalance(newBalance);
        }
      } else {
        const errorData = await response.json();
        Alert.alert(`Error updating balance: ${errorData.message || 'Unknown error'}`);
      }
    } catch (error) {
      Alert.alert("Error updating balance.");
    }
  };

  // Handle recharge action
  const handleRecharge = () => {
    const rechargeValue = parseFloat(rechargeAmount);
    if (!isNaN(rechargeValue) && rechargeValue >= rechargeMinimumAmount) {
      const newBalance = balance + rechargeValue;
      updateBalance(newBalance);
      setModalVisible(false);
      setRechargeAmount('');
    } else {
      Alert.alert("Please enter a valid amount to recharge.");
    }
  };

  // Handle chat now action
  const handleChatNow = async (consultantId: string, perMinuteRate: number, categoryName: string) => {
    // Pehle recharge check karein
    if (balance === 0) {
      setSelectedRate(perMinuteRate);
      setShowPaymentPopup(true);
      return;
    }

    // Phir astrology check karein
    if (categoryName.toLowerCase() === 'astrology') {
      navigation.navigate('ServicePage', { consultantId });
      return;
    }
  
    const userSession = await AsyncStorage.getItem('userSession');
    if (!userSession) {
      setError('User session not found');
      return;
    }
  
    const userId = JSON.parse(userSession).userId;
  
    // Check karein agar user free chat ka use kar chuka hai
    const freeChatUsed = await AsyncStorage.getItem(`freeChat_${userId}`);
  
    if (!freeChatUsed) {
      setIsNewUser(false);
      await AsyncStorage.setItem(`freeChat_${userId}`, 'true');
      navigation.navigate('Chat', { consultantId, balance: '0', chatDuration: 3 });
      return;
    }
  
    // Balance check karein
    if (balance === 0) {
      setSelectedRate(perMinuteRate);
      setShowPaymentPopup(true);
    } else {
      const maxChatDuration = Math.floor(balance / perMinuteRate);
  
      if (maxChatDuration <= 0) {
        setSelectedRate(perMinuteRate);
        setShowPaymentPopup(true);
      } else {
        const totalCost = maxChatDuration * perMinuteRate;
        const chatDuration = maxChatDuration;
        const newBalance = balance - totalCost;
  
        if (newBalance >= 0) {
          await updateBalance(newBalance);
          navigation.navigate('Chat', { consultantId, balance: balance, chatDuration });
        } else {
          setSelectedRate(perMinuteRate);
          setShowPaymentPopup(true);
        }
      }
    }
  };

  if (error) {
    return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
  }

  return (
    <View style={tw`flex bg-white h-full px-1`}>
        <View style={tw`flex items-center justify-between w-full flex-row px-3`}>
        <View style={tw`flex items-center justify-center flex-row`}>
          <Image source={Logo} style={tw`w-13 h-13`} />
         <Text style={[tw`font-medium text-[1.2rem] ml-0.5`, { fontFamily: 'ItalicFont' }]}>Nityasha</Text>
        </View>
        <View style={tw`flex gap-3 flex-row items-center justify-center`}>
          <Wallets />
          <TouchableOpacity onPress={() => navigation.navigate('Notification')}>
            <Bell size={24} color={"#000"} />
          </TouchableOpacity>
        </View>
      </View>
      <View style={tw`flex-row flex-wrap items-center justify-center mt-5`}>
        {consultants.length === 0 ? (
          <Text style={tw`text-center w-full`}>No consultants available.</Text>
        ) : (
          consultants.map((consultant) => (
            <View key={consultant.id} style={tw`w-[130px] flex items-center rounded-[8px] py-3 border-2 border-gray-500 px-1 mr-[5px] overflow-hidden`}>
            <TouchableOpacity
              style={tw`flex rounded-full`}
              
            >
              <Image
                source={{
                  uri: consultant.pfp || `https://ui-avatars.com/api/?name=${consultant.username.slice(0, 1)}...`,
                }}
                style={tw`w-[68px] h-[68px] rounded-full border border-black`}
              />
                {consultant.active === 1 && (
                    <View
                      style={tw.style(
                        `absolute top-0 right-0 p-2 rounded-full bg-green-500`
                      )}
                    ></View>
                  )}
            </TouchableOpacity>
            <Text style={[tw`mt-2 text-[14px] font-[#39434F] h-5`, { fontFamily: 'Geist-Bold' }]}>{consultant.name}</Text>
            <Text style={tw`text-sm mt-1`}>₹{consultant.per_minute_rate}/min</Text>
            <Text style={tw`text-sm mt-1`}>{consultant.category_name}</Text>
            <TouchableOpacity
              style={tw`mt-2 bg-[#000] flex items-center justify-center p-1 px-3 rounded-full`}
              disabled={consultant.active === 0} // Disable the button when active is 0
              onPress={() => {
                setUsername(consultant.name)
                  handleChatNow(consultant.id, consultant.per_minute_rate, consultant.category_name);
                }
              }
            >
              <Text style={[tw`text-white text-sm`, { fontFamily: 'Helvetica_bold' }]}>
                {isNewUser ? 'Free Chat' : 'Chat Now'}
              </Text>
            </TouchableOpacity>
          </View>
          ))
        )}
      </View>
      <Modal visible={modalVisible} animationType="slide" transparent={true}>
        <BlurView
          intensity={50}
          tint="light"
          style={{
            height: "21%",
            width: "100%",
            position: "absolute",
            bottom: 0,
            elevation: 8,
          }}
        >
          <View style={tw`flex-1 justify-center items-center absolute bottom-0 w-full`}>
            <View style={tw`p-6 rounded-lg w-full`}>
              <Text style={[tw`text-lg font-semibold mb-4 w-full text-center`, { fontFamily: 'Helvetica_bold' }]}>
                Your Balance is ₹{balance.toFixed(2)}
              </Text>
              <TextInput
                style={[tw`border-2 p-2 px-3 rounded-xl font-bold`, { fontFamily: 'Helvetica_bold' }]}
                placeholder="Enter recharge amount"
                keyboardType="numeric"
                value={rechargeAmount}
                onChangeText={setRechargeAmount}
              />
              <View style={tw`flex flex-row gap-2 mt-3 justify-center`}>
                <TouchableOpacity onPress={handleRecharge} style={tw`w-[50%] flex bg-black px-5 py-2  rounded-lg consultants-center justify-center`}>
                  <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>Recharge</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setModalVisible(false)} style={tw`w-[50%] flex bg-black px-5 py-2 rounded-lg consultants-center justify-center`}>
                  <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </BlurView>
      </Modal>
      <PaymentPopup
        visible={showPaymentPopup}
        onClose={() => setShowPaymentPopup(false)}
        rate={selectedRate}
        username={Username}
      />
    </View>
  );
};

export default Category;
