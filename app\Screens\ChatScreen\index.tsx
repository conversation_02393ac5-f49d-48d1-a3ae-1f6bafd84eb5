import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  FlatList,
  Text,
  StyleSheet,
  ImageBackground,
  StatusBar,
  Image,
  ActivityIndicator,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase'; // Ensure correct path
import { ref, onValue } from 'firebase/database';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import Backgroundchat from '@/assets/images/backgroundchat.png';

const InboxChat = ({ route }) => {
  const { roomN, consultant } = route.params;
  const [messages, setMessages] = useState([]);
  const [userSession, setUserSession] = useState(null);
  const [consultantData, setConsultantData] = useState(null);
  const [loadingQuote, setLoadingQuote] = useState(true);
  const [loadingMessages, setLoadingMessages] = useState(true);
  const flatListRef = useRef(null);
  const navigation = useNavigation();

  useEffect(() => {
    const loadUserSession = async () => {
      const session = await AsyncStorage.getItem('userSession');
      const parsedSession = JSON.parse(session);
      setUserSession(parsedSession);
    };
    loadUserSession();
  }, []);

  useEffect(() => {
    const messagesRef = ref(db, 'messages');
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messageArray = Object.values(data).filter(
          (message) => message.roomName === roomN
        );
        setMessages(messageArray);
      }
      setLoadingMessages(false); // Stop loading when messages are fetched
    });
    return () => unsubscribe();
  }, [roomN]);

  useEffect(() => {
    const fetchQuote = async () => {
      try {
        const response = await fetch(
          `https://nityasha.vercel.app/api/v1/consultantss/${consultant}`
        );
        if (!response.ok) throw new Error('Network response was not ok');
        const data = await response.json();
        setConsultantData(data); // Set the consultant data directly
      } catch (error) {
        console.error('Error fetching consultant data:', error); // Debug log
      } finally {
        setLoadingQuote(false);
      }
    };
    fetchQuote();
  }, [consultant]);

  // Scroll to the end of the list when messages are updated
  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  const renderMessageItem = ({ item }) => (
    <View
      style={tw`p-2 my-1 ${
        item.username === (userSession ? userSession.username : 'User')
          ? 'bg-orange-500 self-end text-white mr-2 rounded-lg rounded-tr-none'
          : 'bg-orange-500 ml-2 self-start rounded-lg rounded-tl-none'
      }`}
    >
      <Text style={[tw`text-white`, { fontFamily: 'Helvetica_bold' }]}>
        {item.text}
      </Text>
    </View>
  );

  return (
    <ImageBackground source={Backgroundchat} style={tw`flex h-full`}>
      <StatusBar barStyle="dark-content"  />
      {loadingQuote ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : consultantData ? (
        <View style={tw`flex-row pb-2 w-full flex items-center justify-center bg-white`}>
          <Image source={{ uri: consultantData.pfp }} style={styles.profileImage} />
          <Text style={tw`ml-2 font-bold`}>{consultantData.name}</Text>
        </View>
      ) : (
        <Text>No consultant data available</Text>
      )}
      <View style={styles.container}>
        <View style={styles.messagesContainer}>
          {loadingMessages ? (
            <ActivityIndicator size="large" color="#0000ff" />
          ) : (
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessageItem}
              keyExtractor={(item) => item.id.toString()}
              initialNumToRender={10} // Adjust based on your needs
              windowSize={5} // Adjust based on your needs
            />
          )}
        </View>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    marginBottom: 10,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 210,
  },
});

export default InboxChat;
