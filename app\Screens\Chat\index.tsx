"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  View,
  TextInput,
  FlatList,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  StatusBar,
  Animated,
  Platform,
  SafeAreaView,
  InteractionManager,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { db } from "@/lib/Firebase"; // Adjust the import path as necessary
import { ref, onValue, set } from "firebase/database";
import tw from "twrnc";
import { useNavigation } from "@react-navigation/native";
import { ArrowLeft, Clock, Send, X } from "lucide-react-native";
import * as NavigationBar from "expo-navigation-bar";
import { BackHandler } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import { useToast } from "../../../context/ToastContext";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";

const generateUsername = (userId) => {
  return `User_${userId}`;
};

const generateRoomName = () => {
  return Math.random().toString(36).substring(2, 12); // Generates a random 10-letter string
};

const ChatScreen = ({ route }) => {
  const {
    consultantId,
    balance,
    chatDuration,
    totalCost,
    isFreeChat,
    astrologer,
  } = route.params;
  const navigation = useNavigation();
  const { showToast } = useToast();
  NavigationBar.setVisibilityAsync("hidden");

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState("");
  const [roomName] = useState(generateRoomName());
  const [userSession, setUserSession] = useState(null);
  const flatListRef = useRef(null);
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dataSent, setDataSent] = useState(false);
  const [isApproved, setIsApproved] = useState(false);
  const [approvalChecked, setApprovalChecked] = useState(false);
  const inputRef = useRef(null);
  const [remainingTime, setRemainingTime] = useState(chatDuration * 60);
  const [balanceDeducted, setBalanceDeducted] = useState(false);
  const [currentBalance, setCurrentBalance] = useState(balance);
  const socketRef = useRef(null);
  const animationRunRef = useRef(false);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const scrollTimeout = useRef(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Only run entrance animation once when component mounts
    if (!animationRunRef.current) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start(() => {
        animationRunRef.current = true;
      });
    }
  }, [fadeAnim, slideAnim, scaleAnim]);

  useEffect(() => {
    let interval;
    if (isApproved && remainingTime > 0) {
      // Deduct balance when chat is approved
      const deductBalance = async () => {
        if (!isFreeChat && !balanceDeducted) {
          const userSession = await AsyncStorage.getItem("userSession");
          const userId = JSON.parse(userSession).userId;
          const newBalance = currentBalance - totalCost;

          try {
            const response = await fetch(
              `https://nityasha.vercel.app/api/v1/users/${userId}`,
              {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ balance: newBalance }),
              }
            );

            if (response.ok) {
              setBalanceDeducted(true);
            } else {
              navigation.goBack();
            }
          } catch (error) {
            console.error("Error updating balance:", error);
            navigation.goBack();
          }
        }
      };
            
      deductBalance();

      interval = setInterval(() => {
        setRemainingTime((prevTime) => prevTime - 1);
      }, 1000);
    }

    if (remainingTime === 0) {
      navigation.goBack(); // Go back when time runs out
    }

    return () => clearInterval(interval); // Cleanup interval on unmount or if the approval changes
  }, [
    isApproved,
    remainingTime,
    navigation,
    balanceDeducted,
    isFreeChat,
    currentBalance,
    totalCost,
  ]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (!isApproved) {
        fetchApprovalStatus(); // Keep checking approval status until approved
      } else {
        clearInterval(intervalId); // Clear the interval once approved
      }
    }, 10000);

    return () => clearInterval(intervalId); // Cleanup the interval on unmount
  }, [isApproved]);

  // Updated code block to also send a Firebase message with profile1 details:
  useEffect(() => {
    const checkAstrologerProfile = async () => {
      if (astrologer) {
        try {
          const profileData = await AsyncStorage.getItem("userProfile");
          const parsedProfile = profileData ? JSON.parse(profileData) : null;

          // Updated to match the correct data structure
          if (parsedProfile?.profiles?.profile1) {
            const details = parsedProfile.profiles.profile1;
            const messageText = `Hi,
Below are my details:
Name: ${details.Name}
Gender: ${details.gender}
DOB: ${new Date(details.dob).toLocaleDateString()}
TOB: ${new Date(details.userbirthtime).toLocaleTimeString()}
POB: ${details.birthplace}`;

            const firebaseMessage = {
              id: Date.now(),
              text: messageText,
              username: sendMessage(user2),
              roomName: roomName,
            };

            await set(
              ref(db, `messages/${firebaseMessage.id}`),
              firebaseMessage
            );
          }
        } catch (error) {
          console.error(
            "Error fetching or sending astrologer profile message:",
            error
          );
        }
      }
    };

    checkAstrologerProfile();
  }, [astrologer, roomName]);

  const user1 = generateUsername("1");
  const user2 = generateUsername("2");

  useEffect(() => {
    const loadUserSession = async () => {
      const session = await AsyncStorage.getItem("userSession");
      setUserSession(session);
    };

    loadUserSession();
  }, []);

  const fetchApprovalStatus = async () => {
    setIsApproved(null);
    setLoading(true);
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      const response = await fetch(`https://nityasha.vercel.app/api/${userId}`);

      if (!response.ok) {
        showToast("Failed to check approval status", "error");
        throw new Error("Network response was not ok");
      }

      const data = await response.json();
      const consultantData = data.find(
        (item) => item.consultantId === consultantId
      );
      setIsApproved(consultantData?.status === "approved" || false);
    } catch (error) {
      console.error("Error fetching approval status:", error);
      showToast("Error checking approval status", "error");
      setIsApproved(false);
    } finally {
      setLoading(false);
    }
  };

  const sendRoomData = async () => {
    if (dataSent) {
      return;
    }

    try {
      const userSession = await AsyncStorage.getItem("userSession");
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      const roomDetails = {
        userId: String(userId),
        consultantId: String(consultantId),
        bel: String(balance),
        time: String(chatDuration),
        RoomN: roomName,
      };

      const response = await fetch("https://nityasha.vercel.app/api/v1/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(roomDetails),
      });

      if (!response.ok) {
        showToast("Failed to initialize chat room", "error");
        throw new Error(`Failed to send room data: ${response.statusText}`);
      }

      setDataSent(true);
    } catch (error) {
      console.error("Error sending room data:", error);
      showToast("Error setting up chat room", "error");
    }
  };

  const handleScroll = useCallback((event) => {
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;
    // For inverted list, we check if we're at the "bottom" (actually top due to inversion)
    const threshold = 20; // Allow small margin of error
    const isBottom = contentOffset.y >= -threshold;
    setIsAtBottom(isBottom);
  }, []);

  const scrollToLatestMessage = useCallback(() => {
    if (!isAtBottom || !flatListRef.current) return;

    // Clear any pending scroll operations
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    scrollTimeout.current = setTimeout(() => {
      InteractionManager.runAfterInteractions(() => {
        try {
          flatListRef.current?.scrollToIndex({
            index: 0,
            animated: true,
          });
        } catch (error) {
          // Fallback to offset if scrollToIndex fails
          flatListRef.current?.scrollToOffset({
            offset: 0,
            animated: true,
          });
        }
      });
    }, 100);
  }, [isAtBottom]);

  useEffect(() => {
    const messagesRef = ref(db, 'messages');
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messageArray = Object.values(data)
          .filter(message => message.roomName === roomName)
          .sort((a, b) => b.id - a.id); // Sort by timestamp, newest first
        setMessages(messageArray);
      }
    });

    return () => unsubscribe();
  }, [roomName]);

  useEffect(() => {
    const handleDataFetch = async () => {
      if (userSession && consultantId && !approvalChecked) {
        try {
          setLoading(true);
          await sendRoomData();
          setApprovalChecked(true);
        } catch (error) {
          console.error(
            "Error in sending room data or fetching approval status:",
            error
          );
        } finally {
          setLoading(false);
        }
      }
    };

    handleDataFetch();
  }, [userSession, consultantId, approvalChecked]);

  // Assuming the Expo push token is stored as `expoToken` for each consultant
  const fetchConsultants = async () => {
    try {
      const response = await fetch(
        `https://nityasha.vercel.app/api/v1/consultants/${consultantId}`
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();

      setConsultants([data]);

      const expoToken = data.push_token; // Assuming the actual field for push_token is correct
      if (expoToken && expoToken !== "0") {
        sendExpoPushToken(expoToken);
      } else {
        console.warn("Invalid or missing expo token:", expoToken);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const sendExpoPushToken = async (expoToken) => {
    try {
      const response = await fetch("https://exp.host/--/api/v2/push/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          to: expoToken, // Using 'to' as per your example
          sound: "default",
          title: "New Chat Request",
          body: "You have a new Chat Request from a user.",
        }),
      });

      if (response.ok) {
        console.log("Notification sent successfully!");
      } else {
        throw new Error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error sending Expo push notification:", error);
    }
  };

  useEffect(() => {
    fetchConsultants();
  }, [consultantId]);

  const sendMessage = (user) => {
    if (messageText.trim()) {
      const newMessage = {
        id: Date.now(),
        text: messageText,
        username: user,
        roomName: roomName,
      };
      set(ref(db, "messages/" + newMessage.id), newMessage);
      setMessageText("");

      // Animate the send button
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const formatTime = (time) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;

    const hoursFormatted = hours > 0 ? String(hours) : "";
    const minutesFormatted =
      minutes > 0 || hours > 0
        ? String(minutes).padStart(2, "0")
        : String(minutes);
    const secondsFormatted = String(seconds).padStart(2, "0");

    if (hoursFormatted && minutesFormatted) {
      return `${hoursFormatted}:${minutesFormatted}:${secondsFormatted}`;
    } else if (minutesFormatted) {
      return `${minutesFormatted}:${secondsFormatted}`;
    } else {
      return `${secondsFormatted}`;
    }
  };

  const handleCloseChat = async () => {
    try {
      // Animate out before closing
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 50,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(async () => {
        const userSession = await AsyncStorage.getItem("userSession");
        const parsedSession = JSON.parse(userSession);
        const userId = parsedSession?.userId || userSession;

        // Send system message about user leaving
        const leftMessage = {
          id: Date.now(),
          text: "User has left the chat",
          username: "system",
          roomName: roomName,
        };
        await set(ref(db, "messages/" + leftMessage.id), leftMessage);

        if (!isApproved) {
          const response = await fetch(
            `https://nityasha.vercel.app/api/v1/users/${userId}`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ balance: balance }),
            }
          );

          if (!response.ok) {
            showToast("Failed to update user status", "error");
            throw new Error("Failed to update user status");
          }

          navigation.reset({
            index: 0,
            routes: [{ name: "BottomTabs" }],
          });
          return;
        }

        const usedTime = chatDuration * 60 - remainingTime;
        const balanceUsed = (usedTime / (chatDuration * 60)) * balance;
        const remainingBalance = Math.max(
          0,
          Number.parseFloat((balance - balanceUsed).toFixed(2))
        );

        const response = await fetch(
          `https://nityasha.vercel.app/api/v1/users/${userId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ balance: remainingBalance }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to update balance");
        }

        navigation.reset({
          index: 0,
          routes: [{ name: "BottomTabs" }],
        });
      });
    } catch (error) {
      console.error("Error when closing the chat:", error);
      showToast("Error ending chat session", "error");
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const updateUserBalance = async (newBalance) => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      const response = await fetch(
        `https://nityasha.vercel.app/api/v1/users/${userId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ balance: newBalance }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update balance");
      }

      return true;
    } catch (error) {
      console.error("Error updating balance:", error);
      throw error;
    }
  };

  const renderMessageItem = ({ item, index }) => {
    const isUser = item.username === user2;
    const isSystem = item.username === "system";
    const showAvatar =
      index === 0 ||
      (index > 0 && messages[index - 1].username !== item.username);

    // Message animation - only animate new messages
    const messageOpacity = new Animated.Value(
      index >= messages.length - 1 ? 0 : 1
    );
    const messageTranslateY = new Animated.Value(
      index >= messages.length - 1 ? 10 : 0
    );

    if (index >= messages.length - 1) {
      Animated.parallel([
        Animated.timing(messageOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(messageTranslateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }

    if (isSystem) {
      return (
        <Animated.View
          style={[
            tw`my-2 self-center px-4 py-2 rounded-full bg-gray-200/70`,
            {
              opacity: messageOpacity,
            },
          ]}
        >
          <Text
            style={[tw`text-gray-600 text-xs`, { fontFamily: "Geist-Medium" }]}
          >
            {item.text}
          </Text>
        </Animated.View>
      );
    }

    return (
      <Animated.View
        style={[
          tw`flex-row ${isUser ? "justify-end" : "justify-start"} my-1 mx-2`,
        ]}
      >
        {!isUser && showAvatar && consultants.length > 0 && (
          <Image
            source={{ uri: consultants[0].pfp }}
            style={tw`w-8 h-8 rounded-full mr-2 mt-1`}
          />
        )}
        {!isUser && !showAvatar && <View style={tw`w-8 mr-2`} />}
        <View
          style={[
            tw`px-4 py-3 max-w-[80%] rounded-2xl`,
            isUser
              ? tw`bg-indigo-600 rounded-tr-none`
              : tw`bg-gray-100 rounded-tl-none`,
          ]}
        >
          <Text
            style={[
              isUser ? tw`text-white` : tw`text-gray-800`,
              { fontFamily: "Geist-Regular", lineHeight: 20 },
            ]}
          >
            {item.text}
          </Text>
        </View>
        {isUser && showAvatar && (
          <Image
            source={{
              uri: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2c/Default_pfp.svg/1024px-Default_pfp.svg.png",
            }}
            style={tw`w-8 h-8 rounded-full ml-2 mt-1`}
          />
        )}
        {isUser && !showAvatar && <View style={tw`w-8 ml-2`} />}
      </Animated.View>
    );
  };

  useEffect(() => {
    if (remainingTime === 300) {
      // 5 minutes remaining
      showToast("5 minutes remaining in your session", "warning");
    } else if (remainingTime === 60) {
      // 1 minute remaining
      showToast("1 minute remaining in your session", "warning");
    }
  }, [remainingTime, showToast]);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Return true to prevent default back action
        return true;
      };

      // Add the event listener and store the subscription
      const subscription = BackHandler.addEventListener("hardwareBackPress", onBackPress);

      // Cleanup the event listener on screen unfocus
      return () => {
        subscription.remove();
      };
    }, [])
  );

  useEffect(() => {
    const connectWebSocket = async () => {
      const userSession = await AsyncStorage.getItem("userSession");
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId;

      // Close existing connection if any
      if (socketRef.current) {
        socketRef.current.close();
      }

      const socket = new WebSocket("wss://balance-app-api.nityasha.com");
      socketRef.current = socket;

      socket.onopen = () => {
        socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));

        // Set up interval to request balance updates
        const balanceInterval = setInterval(() => {
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
          }
        }, 1000); // Check every second

        // Store interval ID for cleanup
        socketRef.current.intervalId = balanceInterval;
      };

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.balance !== undefined) {
            const newBalance = Number.parseFloat(data.balance) || 0;
            setCurrentBalance(newBalance);
          }
        } catch (err) {
          console.error("Error parsing WebSocket message:", err);
        }
      };

      socket.onerror = (error) => {
        console.error("WebSocket error:", error);
      };

      socket.onclose = () => {
        console.log("WebSocket disconnected");
      };
    };

    connectWebSocket();

    // Cleanup function
    return () => {
      if (socketRef.current) {
        clearInterval(socketRef.current.intervalId);
        socketRef.current.close();
      }
    };
  }, []);

  // Calculate time color based on remaining time
  const getTimeColor = () => {
    if (remainingTime <= 60) return tw`text-red-500`;
    if (remainingTime <= 300) return tw`text-amber-500`;
    return tw`text-green-500`;
  };

  const handleSendRoomData = useRef(sendRoomData).current;
  const handleFetchConsultants = useRef(fetchConsultants).current;

  useEffect(() => {
    const handleDataFetch = async () => {
      if (userSession && consultantId && !approvalChecked) {
        try {
          setLoading(true);
          await handleSendRoomData();
          setApprovalChecked(true);
        } catch (error) {
          console.error(
            "Error in sending room data or fetching approval status:",
            error
          );
        } finally {
          setLoading(false);
        }
      }
    };

    handleDataFetch();
  }, [userSession, consultantId, approvalChecked, handleSendRoomData]);

  useEffect(() => {
    handleFetchConsultants();
  }, [consultantId, handleFetchConsultants]);

  return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={tw`flex-1`}>
          <LinearGradient
            colors={["#ffffff", "#f9fafb"]}
            style={tw`px-4 py-3 border-b border-gray-200 z-10`}
          >
            <View style={tw`flex-row items-center justify-between`}>
              <View style={tw`flex-row items-center`}>
                {Array.isArray(consultants) && consultants.length > 0 ? (
                  <View style={tw`flex-row items-center`}>
                    <Image
                      source={{ uri: consultants[0].pfp }}
                      style={tw`w-10 h-10 rounded-full border-2 border-white shadow-sm`}
                    />
                    <View style={tw`ml-3`}>
                      <Text
                        style={[
                          tw`text-gray-900 text-lg`,
                          { fontFamily: "Geist-SemiBold" },
                        ]}
                      >
                        {consultants[0].name}
                      </Text>
                      <View style={tw`flex-row items-center`}>
                        <View
                          style={tw`w-2 h-2 rounded-full ${
                            isApproved ? "bg-green-500" : "bg-amber-500"
                          } mr-1.5`}
                        />
                        <Text
                          style={[
                            tw`text-gray-500 text-xs`,
                            { fontFamily: "Geist-Regular" },
                          ]}
                        >
                          {isApproved ? "Online" : "Waiting for approval"}
                        </Text>
                      </View>
                    </View>
                  </View>
                ) : (
                  <Text
                    style={[tw`text-gray-500`, { fontFamily: "Geist-Regular" }]}
                  >
                    Loading consultant...
                  </Text>
                )}
              </View>

              <View style={tw`flex-row items-center`}>
                <View
                  style={tw`bg-gray-100 rounded-full px-3 py-1.5 flex-row items-center mr-2`}
                >
                  <Clock size={14} style={getTimeColor()} />
                  <Text
                    style={[
                      tw`ml-1.5 ${getTimeColor()}`,
                      { fontFamily: "Geist-Medium" },
                    ]}
                  >
                    {formatTime(remainingTime)}
                  </Text>
                </View>

                <TouchableOpacity
                  onPress={handleCloseChat}
                  style={tw`p-2 rounded-full bg-red-100`}
                >
                  <X size={18} color="#ef4444" />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>

          <Animated.View
            style={[
              tw`flex-1`,
              {
                opacity: fadeAnim,

              },
            ]}
          >
            <View style={tw`flex-1 bg-white`}>
              {messages.length === 0 && !loading && (
                <View style={tw`flex-1 items-center justify-center p-6`}>
                  <Text
                    style={[
                      tw`text-gray-400 text-center`,
                      { fontFamily: "Geist-Medium" },
                    ]}
                  >
                    No messages yet. Start the conversation!
                  </Text>
                </View>
              )}

<FlatList
    ref={flatListRef}
    data={[...messages].reverse()} // Reverse the messages array
    renderItem={renderMessageItem}
    keyExtractor={(item) => item.id.toString()}
    inverted={true} // Add this prop
    contentContainerStyle={{ flexDirection: 'column-reverse' }} // Add this style
/>
            </View>
          </Animated.View>

          {loading ? (
            <BlurView
              intensity={80}
              tint="light"
              style={tw`py-4 px-4 border-t border-gray-200`}
            >
              <View
                style={tw`flex-row items-center justify-center bg-gray-100/80 py-4 px-4 rounded-xl`}
              >
                <Text
                  style={[
                    tw`text-gray-600 mr-3`,
                    { fontFamily: "Geist-Medium" },
                  ]}
                >
                  Connecting to consultant
                </Text>
                <ActivityIndicator size="small" color="#6366f1" />
              </View>
            </BlurView>
          ) : (
            <>
              {isApproved === false && (
                <BlurView
                  intensity={80}
                  tint="light"
                  style={tw`py-4 px-4 border-t border-gray-200`}
                >
                  <View
                    style={tw`flex-row items-center justify-center bg-amber-50 py-4 px-4 rounded-xl border border-amber-200`}
                  >
                    <Text
                      style={[
                        tw`text-amber-700 mr-3`,
                        { fontFamily: "Geist-Medium" },
                      ]}
                    >
                      Waiting for consultant approval
                    </Text>
                    <ActivityIndicator size="small" color="#f59e0b" />
                  </View>
                </BlurView>
              )}

              {isApproved === true && (
                <BlurView
                  intensity={80}
                  tint="light"
                  style={tw`py-4 px-4 border-t border-gray-200`}
                >
                  <View style={tw`flex-row items-center`}>
                    <TextInput
                      ref={inputRef}
                      value={messageText}
                      onChangeText={setMessageText}
                      placeholder="Type your message..."
                      placeholderTextColor="#9ca3af"
                      style={[
                        tw`flex-1 bg-gray-100 rounded-full px-4 py-3 mr-2`,
                        { fontFamily: "Geist-Regular" },
                      ]}
                      multiline={true}
                      maxLength={500}
                    />
                    <Animated.View
                    >
                      <TouchableOpacity
                        onPress={() => sendMessage(user2)}
                        style={tw`bg-indigo-600 p-3 rounded-full shadow-sm`}
                        activeOpacity={0.8}
                      >
                        <Send size={20} color="#ffffff" />
                      </TouchableOpacity>
                    </Animated.View>
                  </View>
                </BlurView>
              )}
            </>
          )}
        </View>
      </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
});

export default ChatScreen;
