import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import tw from 'twrnc';
import { Bell } from 'lucide-react-native';
import Wallets from '@/components/Wallet';
import Chatasb from '@/components/Chatab';
import SkeletonLoader from '@/components/ui/SkeletonLoader';

interface Category {
    name: string;
}

interface Consultant {
    id: string;
    name: string;
    category_name: string;
    rate_per_minute: number;
}

interface ChatabProps {
    navigation: any;
}

export default function Chatab({ navigation }: ChatabProps) {
    const [categories, setCategories] = useState<Category[]>([]);
    const [allConsultants, setAllConsultants] = useState<Consultant[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedCategory, setSelectedCategory] = useState('All');
    const [balance, setBalance] = useState(1);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [categoriesRes, consultantsRes] = await Promise.all([
                    fetch('https://nityasha.vercel.app/api/v1/categories/get'),
                    fetch('https://nityasha.vercel.app/api/v1/consultants')
                ]);

                if (!categoriesRes.ok || !consultantsRes.ok) throw new Error('Network response failed');

                const categoriesJson = await categoriesRes.json();
                const consultantsJson = await consultantsRes.json();

                setCategories([{ name: 'All' }, ...categoriesJson]);
                setAllConsultants(consultantsJson);
            } catch (error) {
                console.error('Error fetching data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const consultants = useMemo(() => {
        return selectedCategory === 'All'
            ? allConsultants
            : allConsultants.filter((consultant) => consultant.category_name.toLowerCase() === selectedCategory.toLowerCase());
    }, [selectedCategory, allConsultants]);

    const handleChatPress = useCallback(
        (consultant: Consultant) => {
            const ratePerMinute = consultant.rate_per_minute || 20;
            const minimumRequiredBalance = ratePerMinute * 3;

            if (balance < minimumRequiredBalance) {
                alert(`Insufficient balance. You need at least ₹${minimumRequiredBalance} (for 3 minutes) to start chatting.`);
                return;
            }

            navigation.navigate('Chat', { consultant, ratePerMinute });
        },
        [balance, navigation]
    );

    const handleRecharge = useCallback((amount: number) => {
        setBalance((prev) => {
            const newBalance = prev + amount;
            alert(`Successfully recharged ₹${amount}. New balance: ₹${newBalance}`);
            return newBalance;
        });
    }, []);

    if (loading) {
        return (
            <View style={tw`flex-1 items-center justify-center px-3 gap-2`}>
                {Array.from({ length: 4 }).map((_, index) => (
                    <SkeletonLoader key={index} width={'100%'} height={200} borderRadius={16} />
                ))}
            </View>
        );
    }

    return (
        <ScrollView style={tw`flex h-full bg-[#FFF8E1] w-full px-4`}>
            <View style={tw`flex flex-row justify-between items-center w-full`}>
                <Text style={tw`font-medium text-[1.2rem]`} fontFamily="ItalicFont">Nityasha</Text>
                <View style={tw`flex-row gap-3 items-center`}>
                    <Wallets />
                    <TouchableOpacity onPress={() => navigation.navigate("Notification")}>
                        <Bell size={24} color={"#000"} />
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={tw`mt-5`}>
                {categories.map((category, index) => (
                    <TouchableOpacity
                        key={index}
                        style={[
                            tw`border-2 h-7 px-2 rounded-md mr-2 flex items-center justify-center`,
                            selectedCategory === category.name ? tw`border-black bg-black/10` : tw`border-zinc-500`
                        ]}
                        onPress={() => setSelectedCategory(category.name)}
                    >
                        <Text style={tw`font-bold`} fontFamily="Helvetica_bold">{category.name}</Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>

            {consultants.length > 0 ? (
                consultants.map((consultant) => (
                    <TouchableOpacity key={consultant.id} onPress={() => handleChatPress(consultant)}>
                        <Chatasb consultant={consultant} />
                    </TouchableOpacity>
                ))
            ) : (
                <Text style={tw`text-center mt-10 text-gray-500`}>No consultants available for this category</Text>
            )}
        </ScrollView>
    );
}
