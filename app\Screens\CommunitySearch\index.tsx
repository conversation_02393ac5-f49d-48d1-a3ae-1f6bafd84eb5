import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  View,
  Text,
  ImageBackground,
  TouchableOpacity,
  StatusBar,
  FlatList,
  ActivityIndicator,
  RefreshControl
} from "react-native";
import { Searchbar } from "react-native-paper";
import { ChevronLeft } from "lucide-react-native";
import { useNavigation } from "@react-navigation/native";
import tw from "twrnc";
import axios from "axios";
import { MotiView } from "moti";
import StarBadge from "../Community/icons/star";
import { debounce } from "lodash";

// Define interface for community data
interface Community {
  id: string | number;
  name: string;
  // Add other properties as needed
}

// Define navigation type
type RootStackParamList = {
  CommunityView: { communityId: string | number };
  // Add other screens as needed
};

type NavigationProp = {
  navigate: (screen: keyof RootStackParamList, params?: any) => void;
  goBack: () => void;
};

export default function CommunitySearch() {
  const navigation = useNavigation<NavigationProp>();
  const searchInputRef = useRef<any>(null);
  const [communities, setCommunities] = useState<Community[]>([]);
  const [filteredCommunities, setFilteredCommunities] = useState<Community[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Set status bar color to white
  StatusBar.setBackgroundColor('#BBEAC4');
  StatusBar.setBarStyle('dark-content');

  // Fetch all communities
  const fetchCommunities = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `http://posts-api.nityasha.com/api/channels`
      );

      if (response.data && response.data.success) {
        const newCommunities = response.data.data || [];
        setCommunities(newCommunities);
        setFilteredCommunities(newCommunities);
      } else {
        console.log('No communities available or invalid response format');
        setCommunities([]);
        setFilteredCommunities([]);
      }
    } catch (err) {
      console.error('API Error:', err);
      setCommunities([]);
      setFilteredCommunities([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Initial fetch on component mount
  useEffect(() => {
    fetchCommunities();
  }, [fetchCommunities]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchText: string) => {
      if (!searchText.trim()) {
        setFilteredCommunities(communities);
        return;
      }

      const searchLower = searchText.toLowerCase();
      const filtered = communities.filter(community =>
        community.name.toLowerCase().includes(searchLower)
      );

      setFilteredCommunities(filtered);
    }, 300),
    [communities]
  );

  // Handle search input change
  const handleSearch = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  // Handle pull-to-refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchCommunities();
  }, [fetchCommunities]);

  // Render community item
  const renderCommunityItem = useCallback(({ item: community }: { item: Community }) => {
    // Skip rendering if community data is invalid
    if (!community || !community.id) {
      return null;
    }

    return (
      <MotiView
        from={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ type: 'timing', duration: 500 }}
        style={tw`px-2`}
      >
        <TouchableOpacity
          onPress={() => navigation.navigate('CommunityView', { communityId: community.id })}
          style={tw`rounded-[30px] w-full p-4 bg-[#F3F0E8] mb-4 flex-row justify-between items-center`}
        >
          <View style={tw`flex-row items-center`}>
            <StarBadge number={4} size={48} color="#A1AFF2" />
            <View style={tw`ml-5`}>
              <Text style={[tw`text-[#1F2937] text-lg`, { fontFamily: 'Helvetica_bold' }]}>{community.name || 'Unnamed Community'}</Text>
            </View>
          </View>
          <View style={tw`h-12 w-12 rounded-full bg-yellow-500`} />
        </TouchableOpacity>
      </MotiView>
    );
  }, [navigation]);

  return (
    <MotiView
      from={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ type: 'timing', duration: 300 }}
      style={tw`flex-1`}
    >
      <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1`}>
        <View style={tw`w-full items-center justify-between flex-row px-5 py-3`}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <ChevronLeft size={30} color={'#000'} />
          </TouchableOpacity>
          <Text style={[tw`text-black text-2xl`, { fontFamily: 'Helvetica_bold' }]}>Search Community</Text>
          <View style={tw`w-8`} />
        </View>

        <View style={tw`px-4 mb-4`}>
          <Searchbar
            placeholder="Search communities..."
            onChangeText={handleSearch}
            value={searchQuery}
            iconColor="#999"
            clearIcon="close-circle"
            ref={searchInputRef}
            autoFocus={true}
            style={{
              borderRadius: 300,
              elevation: 3,
              backgroundColor: "#F5FCF5",
            }}
            placeholderTextColor="#999"
            onClearIconPress={() => {
              setSearchQuery('');
              setFilteredCommunities(communities);
            }}
            autoCorrect={false}
          />
        </View>

        {loading && !refreshing ? (
          <View style={tw`flex-1 items-center justify-center`}>
            <ActivityIndicator size="large" color="#4CAF50" />
          </View>
        ) : (
          <FlatList
            data={filteredCommunities}
            renderItem={renderCommunityItem}
            keyExtractor={(item) => `community-${item.id?.toString() || Math.random().toString()}`}
            contentContainerStyle={tw`px-2 pb-5`}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#0000ff']}
                tintColor="#0000ff"
              />
            }
            initialNumToRender={10}
            maxToRenderPerBatch={20}
            windowSize={21}
            removeClippedSubviews={true}
            ListEmptyComponent={
              <View style={tw`flex-1 items-center justify-center py-10`}>
                <Text style={tw`text-gray-500 text-center`}>
                  {searchQuery.trim() ? 'No communities found matching your search' : 'No communities available'}
                </Text>
              </View>
            }
          />
        )}
      </ImageBackground>
    </MotiView>
  );
}
