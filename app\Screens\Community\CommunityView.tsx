import { View, Text, ImageBackground, Image, TouchableOpacity, FlatList, RefreshControl, ActivityIndicator } from 'react-native';
import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import tw from 'twrnc';
import { Search, MoreVertical, MessageCircle, Eye, ChevronLeft } from 'lucide-react-native';
import axios from 'axios';
import { MotiView } from 'moti'; // Import MotiView for animation
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define post interface
interface Post {
  id: string | number;
  content: string;
  author_name: string;
  created_at: string;
  reply_count: number;
  view_count: number;
  [key: string]: any; // For any other properties
}

// Define navigation types
type RootStackParamList = {
  CreatePost: { communityId: string | number };
  OpenFullPost: { post: Post };
  RequestCommunity: undefined;
  // Add other screens as needed
};

type NavigationProp = {
  navigate: (screen: keyof RootStackParamList, params?: any) => void;
  goBack: () => void;
};

type RouteProps = {
  params: {
    communityId: string | number;
  };
};

export default function CommunityView({ navigation, route }: { navigation: NavigationProp, route: RouteProps }) {
  const { communityId } = route.params;
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false); // State to track pull-to-refresh
  const [page, setPage] = useState(1); // Current page for pagination
  const [hasMorePosts, setHasMorePosts] = useState(true); // Flag to check if more posts are available
  const [loadingMore, setLoadingMore] = useState(false); // Loading state for pagination
  const POSTS_PER_PAGE = 10; // Number of posts to load per page
  const [searchQuery, setSearchQuery] = useState('');
  const [cachedDataLoaded, setCachedDataLoaded] = useState(false);

  // Cache key for posts
  const getCacheKey = (communityId: string | number) => `community_posts_${communityId}`;

  // Load cached posts first for instant display
  const loadCachedPosts = useCallback(async () => {
    try {
      const cacheKey = getCacheKey(communityId);
      const cachedData = await AsyncStorage.getItem(cacheKey);
      if (cachedData) {
        const { posts: cachedPosts, timestamp } = JSON.parse(cachedData);
        const isExpired = Date.now() - timestamp > 2 * 60 * 1000; // 2 minutes cache

        if (!isExpired && cachedPosts.length > 0) {
          setPosts(cachedPosts);
          setCachedDataLoaded(true);
          setLoading(false);
          return true;
        }
      }
    } catch (error) {
      console.log('Error loading cached posts:', error);
    }
    return false;
  }, [communityId]);

  // Optimized fetch posts with caching
  const fetchPosts = useCallback(async (pageNum = 1, shouldRefresh = false) => {
    try {
      // Don't show loading if we have cached data and it's not a refresh
      if (!cachedDataLoaded || shouldRefresh) {
        if (pageNum === 1) {
          setLoading(true);
        } else {
          setLoadingMore(true);
        }
      }

      // Auto-join community (fire and forget for better performance)
      axios.post(`https://posts-api.nityasha.com/api/channels/join`, {
        channel_id: communityId
      }).catch(() => {}); // Silently handle errors

      // Fetch posts
      const response = await axios.get(
        `https://posts-api.nityasha.com/api/posts?channel_id=${communityId}&page=${pageNum}&limit=${POSTS_PER_PAGE}`
      );

      if (response.data && response.data.data) {
        const newPosts = response.data.data || [];

        // Check if we've reached the end of available posts
        setHasMorePosts(newPosts.length >= POSTS_PER_PAGE);

        // Update posts state based on whether we're refreshing or loading more
        if (shouldRefresh || pageNum === 1) {
          setPosts(newPosts);
          setPage(1);

          // Cache the fresh data
          const cacheKey = getCacheKey(communityId);
          await AsyncStorage.setItem(cacheKey, JSON.stringify({
            posts: newPosts,
            timestamp: Date.now()
          }));
        } else {
          setPosts(prevPosts => [...prevPosts, ...newPosts]);
        }
      } else {
        if (shouldRefresh || pageNum === 1) {
          setPosts([]);
        }
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
      if (shouldRefresh || pageNum === 1) {
        setPosts([]);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  }, [communityId, cachedDataLoaded, POSTS_PER_PAGE]);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchPosts(1, true); // Refresh with page 1
  }, [communityId]);

  // Handle loading more posts when reaching end of list
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMorePosts && posts.length > 0) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchPosts(nextPage);
    }
  }, [loadingMore, hasMorePosts, page, communityId, posts.length]);

  // Load cached data first, then fetch fresh data
  useEffect(() => {
    const initializeData = async () => {
      const hasCachedData = await loadCachedPosts();
      if (!hasCachedData) {
        fetchPosts(1);
      } else {
        // Fetch fresh data in background after showing cached data
        setTimeout(() => fetchPosts(1), 100);
      }
    };

    initializeData();
  }, [communityId, loadCachedPosts, fetchPosts]);

  // Memoized filtered posts for better performance
  const filteredPosts = useMemo(() => {
    if (!searchQuery.trim()) {
      return posts;
    }
    const lower = searchQuery.toLowerCase();
    return posts.filter(
      post =>
        (post.content && post.content.toLowerCase().includes(lower)) ||
        (post.author_name && post.author_name.toLowerCase().includes(lower))
    );
  }, [searchQuery, posts]);

  // Function to track post view - moved outside of renderPostItem
  const trackPostView = useCallback((postId: string | number) => {
    try {
      axios.post(`https://posts-api.nityasha.com/api/posts/visit/post/${postId}`).catch(e => {
        console.log('Error tracking post view:', e);
      });
    } catch (e) {
      console.log('Error tracking post view:', e);
    }
  }, []);

  // Memoized post item component for better performance
  const PostItem = memo(({ post }: { post: Post }) => {
    // Skip rendering if post data is invalid
    if (!post || !post.id) {
      return null;
    }

    // Format date safely
    const formattedDate = useMemo(() => {
      try {
        return post.created_at ? new Date(post.created_at).toLocaleString() : 'Unknown date';
      } catch (e) {
        return 'Unknown date';
      }
    }, [post.created_at]);

    const handlePostPress = useCallback(() => {
      trackPostView(post.id);
      navigation.navigate('OpenFullPost', { post: post });
    }, [post.id, post]);

    return (
      <TouchableOpacity
        onPress={handlePostPress}
        style={tw`bg-white rounded-xl border border-[#b5b5b8] mb-4 overflow-hidden`}
      >
        {/* Post Header */}
        <View style={tw`p-4 flex-row justify-between items-center`}>
          <View style={tw`flex-row items-center`}>
            <Image
              source={{ uri: `https://ui-avatars.com/api/?name=${post.author_name}&format=png` }}
              style={tw`w-10 h-10 rounded-full`}
            />
            <View style={tw`ml-3`}>
              <View style={tw`flex-row items-center`}>
                <Text style={[tw`text-[#1F2937] font-bold`, { fontFamily: 'Helvetica_bold' }]}>
                  {post.author_name || 'Unknown User'}
                </Text>
                <Text style={tw`text-gray-500 ml-2`}>shared a</Text>
                <Text style={tw`text-[#1F2937] font-bold ml-2`}>post</Text>
              </View>
              <Text style={tw`text-gray-500 text-xs`}>{formattedDate}</Text>
            </View>
          </View>
          <TouchableOpacity>
            <MoreVertical size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Post Content */}
        <View style={tw`px-4 pb-3`}>
          <Text style={tw`text-[#1F2937] mb-3`}>
            {post.content || 'No content'}
          </Text>

          {/* Post Stats */}
          <View style={tw`flex-row items-center`}>
            <TouchableOpacity style={tw`flex-row items-center mr-4`}>
              <MessageCircle size={18} color="#6B7280" />
              <Text style={tw`text-gray-500 ml-1`}>{post.reply_count || 0} Reply</Text>
            </TouchableOpacity>
            <TouchableOpacity style={tw`flex-row items-center`}>
              <Eye size={18} color="#6B7280" />
              <Text style={tw`text-gray-500 ml-1`}>{post.view_count || 0} Views</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  });

  // Render post item component
  const renderPostItem = useCallback(({ item: post }: { item: Post }) => (
    <PostItem post={post} />
  ), []);

  // Render skeleton loader item
  const renderSkeletonItem = useCallback(({ index }: { index: number }) => (
    <MotiView
      key={index}
      from={{ opacity: 0.5 }}
      animate={{ opacity: 1 }}
      transition={{ type: 'timing', duration: 1000, loop: true }}
      style={tw`bg-white rounded-xl border border-[#b5b5b8] mb-4 overflow-hidden`}
    >
      {/* Post Header Skeleton */}
      <View style={tw`p-4 flex-row justify-between items-center`}>
        <View style={tw`flex-row items-center`}>
          <MotiView style={tw`w-10 h-10 bg-gray-200 rounded-full`} />
          <View style={tw`ml-4`}>
            <MotiView style={tw`w-32 h-4 bg-gray-200`} />
            <MotiView style={tw`w-28 h-4 bg-gray-200 mt-2`} />
          </View>
        </View>
      </View>

      {/* Post Content Skeleton */}
      <View style={tw`px-4 pb-3`}>
        <MotiView style={tw`w-full h-6 bg-gray-200 mb-3`} />
        <View style={tw`flex-row items-center`}>
          <MotiView style={tw`w-16 h-4 bg-gray-200`} />
          <MotiView style={tw`w-16 h-4 bg-gray-200 ml-4`} />
        </View>
      </View>
    </MotiView>
  ), []);

  // Render footer component (loading indicator for pagination)
  const renderFooter = useCallback(() => {
    if (!loadingMore) return null;

    return (
      <View style={tw`py-4 flex items-center justify-center`}>
        <ActivityIndicator size="small" color="#0000ff" />
        <Text style={tw`text-gray-500 mt-2`}>Loading more posts...</Text>
      </View>
    );
  }, [loadingMore]);

  // Render post creation button
  const renderPostCreationButton = useCallback(() => (
    <TouchableOpacity
      onPress={() => navigation.navigate('CreatePost', { communityId: communityId })}
      style={tw`w-full bg-[#A8E6B6] px-3 gap-3 py-2 rounded-lg mb-4 border border-[#b5b5b8]`}
    >
      <View style={tw`flex-row items-center justify-start gap-2 w-full`}>
        <View style={tw`h-10 bg-white w-10 rounded-full`}>
          <Image
            source={{ uri: 'https://ui-avatars.com/api/?name=ME&format=png' }}
            style={tw`w-full h-full rounded-full`}
          />
        </View>
        <Text style={[tw`text-[#1F2937]`, { fontFamily: 'Helvetica_bold' }]}>What do you think right now?</Text>
      </View>
      <View style={tw`w-full items-end justify-end pb-3`}>
        <Text>Post</Text>
      </View>
    </TouchableOpacity>
  ), [navigation, communityId]);

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 px-3 py-2 bg-white`}>
      <View style={tw`w-full items-center justify-between flex-row`}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ChevronLeft size={30} color={'#000'} />
        </TouchableOpacity>
        <Text style={[tw`text-black text-lg`, { fontFamily: 'Helvetica_bold' }]}>Community</Text>
        <TouchableOpacity onPress={() => setSearchQuery(searchQuery ? '' : 'search')}>
        <Search size={24} color={"#000"} />
        </TouchableOpacity>
      </View>

      {loading ? (
        // Show skeleton loader when loading initial data
        <FlatList
          data={Array(5).fill(0)}
          renderItem={renderSkeletonItem}
          keyExtractor={(_, index) => `skeleton-${index}`}
          style={tw`mt-4`}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={renderPostCreationButton}
        />
      ) : (
        <FlatList
          data={filteredPosts}
          renderItem={renderPostItem}
          keyExtractor={(item, index) => `post-${item.id || index}`}
          style={tw`mt-4`}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#0000ff']}
              tintColor="#0000ff"
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.3}
          ListHeaderComponent={renderPostCreationButton}
          ListFooterComponent={renderFooter}
          initialNumToRender={3}
          maxToRenderPerBatch={5}
          windowSize={5}
          removeClippedSubviews={true}
          getItemLayout={(_, index) => ({
            length: 200, // Approximate item height
            offset: 200 * index,
            index,
          })}
          ListEmptyComponent={
            <View style={tw`flex-1 items-center justify-center py-10`}>
              <Text style={tw`text-gray-500 text-center`}>No posts available. Be the first to post!</Text>
            </View>
          }
        />
      )}
    </ImageBackground>
  );
}

