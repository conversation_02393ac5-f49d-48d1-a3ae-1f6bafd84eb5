import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import {
  Send, MoreVertical
} from "lucide-react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase } from "@/utils/supabase";
import tw from "twrnc";

const CommunityView = ({ route, navigation }) => {
  const { communityId } = route.params;
  const [community, setCommunity] = useState(null);
  const [members, setMembers] = useState([]);``
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [hasPaid, setHasPaid] = useState(false);
  const scrollViewRef = useRef(null);

  const handleDeleteCommunity = async () => {
    if (!community || !community.creator_id || !userId) {
      Alert.alert("Error", "Community data not found");
      return;
    }

    if (String(community?.creator_id) !== String(userId)) {
      Alert.alert(
        "Error",
        "Only the community creator can delete this community"
      );
      return;
    }

    if (!communityId) {
      Alert.alert("Error", "Invalid community ID");
      return;
    }

    Alert.alert(
      "Delete Community",
      "Are you sure you want to delete this community? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const { error } = await supabase
                .from("communities")
                .delete()
                .eq("id", communityId);

              if (error) throw error;

              Alert.alert("Success", "Community deleted successfully");
              navigation.goBack();
            } catch (error) {
              console.error("Error deleting community:", error);
              Alert.alert("Error", "Failed to delete community");
            }
          },
        },
      ]
    );
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          setUserId(parsedSession?.userId || userSession);
        }
      } catch (error) {
        console.error("Error getting user session:", error);
      }
    };

    const fetchCommunityData = async () => {
      try {
        const { data, error } = await supabase
          .from("communities")
          .select("*")
          .eq("id", communityId)
          .single();

        if (error) throw error;
        setCommunity(data);

        // Fetch members
        const { data: membersData, error: membersError } = await supabase
          .from("community_members")
          .select("user_id")
          .eq("community_id", communityId);

        if (membersError) throw membersError;

        const memberDetails = await Promise.all(
          membersData.map(async (member) => {
            const response = await fetch(
              `https://nityasha.vercel.app/api/v1/users/${member.user_id}`
            );
            const userData = await response.json();
            return {
              id: member.user_id,
              username: userData.username || "Anonymous",
              pfp:
                userData.pfp ||
                `https://ui-avatars.com/api/?name=${
                  userData.username?.charAt(0) || "A"
                }`,
            };
          })
        );

        setMembers(memberDetails);
      } catch (error) {
        console.error("Error fetching community data:", error);
        Alert.alert("Error", "Failed to load community data");
      }
    };

    const fetchMessages = async () => {
      try {
        const { data, error } = await supabase
          .from("community_messages")
          .select("*")
          .eq("community_id", communityId)
          .order("created_at", { ascending: true });

        if (error) throw error;

        const messagesWithUserDetails = await Promise.all(
          data.map(async (message) => {
            const response = await fetch(
              `https://nityasha.vercel.app/api/v1/users/${message.user_id}`
            );
            const userData = await response.json();
            return {
              ...message,
              username: userData.username || "Anonymous",
              pfp:
                userData.pfp ||
                `https://ui-avatars.com/api/?name=${
                  userData.username?.charAt(0) || "A"
                }`,
            };
          })
        );

        setMessages(messagesWithUserDetails);
      } catch (error) {
        console.error("Error fetching messages:", error);
        Alert.alert("Error", "Failed to load messages");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
    fetchCommunityData();
    fetchMessages();

    // Subscribe to new messages
    const subscription = supabase
      .channel("community_messages")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "community_messages",
          filter: `community_id=eq.${communityId}`,
        },
        async (payload) => {
          if (payload.eventType === "INSERT") {
            const response = await fetch(
              `https://nityasha.vercel.app/api/v1/users/${payload.new.user_id}`
            );
            const userData = await response.json();
            const newMessage = {
              ...payload.new,
              username: userData.username || "Anonymous",
              pfp:
                userData.pfp ||
                `https://ui-avatars.com/api/?name=${
                  userData.username?.charAt(0) || "A"
                }`,
            };
            setMessages((prev) => [...prev, newMessage]);
            scrollViewRef.current?.scrollToEnd({ animated: true });
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [communityId]);

  const handlePayment = async () => {
    if (!userId) {
      Alert.alert("Error", "You must be logged in to make a payment");
      return;
    }

    if (!community.payment_amount || community.payment_amount <= 0) {
      Alert.alert("Error", "Invalid payment amount");
      return;
    }

    try {
      // Check if user has already paid
      const { data: existingPayment, error: checkError } = await supabase
        .from("community_payments")
        .select("*")
        .eq("community_id", communityId)
        .eq("user_id", userId)
        .single();

      if (existingPayment) {
        Alert.alert(
          "Info",
          "You have already made a payment for this community"
        );
        return existingPayment;
      }

      // Create a payment record
      const { data: paymentData, error: paymentError } = await supabase
        .from("community_payments")
        .insert([
          {
            community_id: communityId,
            user_id: userId,
            amount: community.payment_amount,
            status: "completed",
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (paymentError) throw paymentError;

      Alert.alert(
        "Success",
        `Payment of ${community.payment_amount} processed successfully. You can now send messages in this community.`
      );
      return paymentData;
    } catch (error) {
      console.error("Error processing payment:", error);
      Alert.alert("Error", "Failed to process payment. Please try again.");
      return null;
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !userId) return;

    try {
      // Fetch user role in the community
      const { data: adminData, error: adminError } = await supabase
        .from("community_members")
        .select("role")
        .eq("community_id", communityId)
        .eq("user_id", userId)
        .maybeSingle(); // Ensure it doesn't throw an error if no data is found

      const isAdmin = adminData?.role === "admin"; // Fix: Ensure correct admin check

      // Fetch community details (ensure latest data)
      const { data: updatedCommunity, error: communityError } = await supabase
        .from("communities")
        .select("admin_only")
        .eq("id", communityId)
        .single();

      if (communityError) throw communityError;

      // If the community is admin-only (1), only allow admins
      if (updatedCommunity.admin_only === 1 && !isAdmin) {
        Alert.alert("Error", "Only admins can send messages in this community");
        return;
      }

      // If the community is paid (2), ensure payment is done
      if (updatedCommunity.admin_only === 2 && !isAdmin) {
        const { data: paymentData, error: paymentError } = await supabase
          .from("community_payments")
          .select("*")
          .eq("community_id", communityId)
          .eq("user_id", userId)
          .maybeSingle();

        if (!paymentData) {
          Alert.alert(
            "Paid Community",
            "This is a paid community. Would you like to make a payment to send messages?",
            [
              { text: "Cancel", style: "cancel" },
              {
                text: "Make Payment",
                onPress: async () => {
                  const payment = await handlePayment();
                  if (payment) {
                    handleSendMessage(); // Try sending the message again
                  }
                },
              },
            ]
          );
          return;
        }
      }

      // If all checks pass, send the message
      const { error } = await supabase.from("community_messages").insert([
        {
          community_id: communityId,
          user_id: userId,
          content: newMessage.trim(),
        },
      ]);

      if (error) throw error;

      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
      Alert.alert("Error", "Failed to send message");
    }
  };

  useEffect(() => {
    const checkAdminAndPaymentStatus = async () => {
      if (!userId) return;

      // Check if user is admin
      const { data: adminData } = await supabase
        .from("community_members")
        .select("role")
        .eq("community_id", communityId)
        .eq("user_id", userId)
        .single();

      setIsAdmin(adminData?.role === "admin");

      // Check payment status for paid communities
      if (community?.admin_only === 2) {
        const { data: paymentData } = await supabase
          .from("community_payments")
          .select("*")
          .eq("community_id", communityId)
          .eq("user_id", userId)
          .maybeSingle();

        setHasPaid(!!paymentData);
      }
    };

    checkAdminAndPaymentStatus();
  }, [userId, communityId, community]);

  if (!community) {
    return (
      <SafeAreaView style={tw`flex-1 bg-white justify-center items-center`}>
        <ActivityIndicator size="large" color="#189e7f" />
        <Text style={tw`mt-4 text-gray-600`}>Loading community...</Text>
      </SafeAreaView>
    );
  }
  const isAdminOnly = parseInt(community.admin_only) === 1; // Ensure it's a number
  const isCreator = String(community.creator_id) === String(userId); // Check if current user is the creator

  const canSendMessage = !isAdminOnly || isCreator; // Only creator can send if admin-only is enabled

  console.log("Admin Only:", isAdminOnly);
  console.log("User is Creator:", isCreator);
  console.log("Can Send Message:", canSendMessage);

  return (
    <SafeAreaView style={tw`flex-1 bg-white`}>
      <StatusBar backgroundColor="white" />
      <View
        style={tw`p-4 flex-row items-center justify-between border-b border-gray-200`}
      >
       <TouchableOpacity
  onPress={() => navigation.navigate('ProfileCommunity',{communityId})}
  style={tw`flex-row items-center`}
>
  <Image
    source={{ uri: community.pfp }}
    style={tw`w-10 h-10 rounded-full`}
  />
  <View style={tw`items-start ml-3`}>
    <Text style={tw`text-sm font-bold`}>
      {community?.name || "Loading..."}
    </Text>
    <Text style={tw`text-gray-600`}>{members.length} members</Text>
  </View>
</TouchableOpacity>

        {community?.creator_id &&
          userId &&
          String(community?.creator_id) === String(userId) && (
            <TouchableOpacity onPress={handleDeleteCommunity}>
              <MoreVertical size={24} color="black" />
            </TouchableOpacity>
          )}
      </View>

      <View style={tw`flex-1 p-4`}>
        {community.admin_only === 2 && !isAdmin && !hasPaid && (
          <View style={tw`bg-gray-100 p-4 rounded-lg mb-4`}>
            <Text style={tw`text-center text-gray-600 mb-4`}>
              This is a paid community. Please make a payment to access the
              messages.
            </Text>
            <TouchableOpacity
              style={tw`bg-[#189e7f] p-3 rounded-lg`}
              onPress={handlePayment}
            >
              <Text style={tw`text-white text-center font-bold`}>
                Make Payment (${community.payment_amount})
              </Text>
            </TouchableOpacity>
          </View>
        )}

        <ScrollView
          style={tw`flex-1 mb-4`}
          ref={scrollViewRef}
          onContentSizeChange={() =>
            scrollViewRef.current?.scrollToEnd({ animated: true })
          }
          onLayout={() =>
            scrollViewRef.current?.scrollToEnd({ animated: true })
          }
          showsVerticalScrollIndicator={false}
        >
          {messages.map((message) => (
            <View
              key={message.id}
              style={tw`flex-row mb-4 ${message.user_id === userId ? "justify-start" : "justify-end"}`}
            >
              <Image
                source={{ uri: community.pfp }}
                style={tw`w-8 h-8 rounded-full mr-2`}
              />
              <View
                style={tw`${message.user_id === userId ? "bg-gray-200" : "bg-[#189e7f]"} p-3 rounded-lg max-w-[80%]`}
              >
                <Text style={tw`text-xs font-bold mb-1 flex-row items-center`}>
                  {message.username}
                </Text>
                <Text style={tw`${message.user_id === userId ? "text-black" : "text-white"}`}>
                  {message.content}
                </Text>
              </View>
            </View>
          ))}
          {messages.length === 0 && (
            <View style={tw`flex-1 justify-center items-center py-4`}>
              <Text style={tw`text-gray-500 text-center`}>
                No messages found
              </Text>
            </View>
          )}
        </ScrollView>
        {canSendMessage ? (
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            keyboardVerticalOffset={100}
          >
            <View
              style={tw`flex-row items-center bg-gray-100 rounded-[30px] px-4 py-2`}
            >
              <TextInput
                value={newMessage}
                onChangeText={setNewMessage}
                placeholder="Type a message..."
                style={tw`flex-1 mr-2`}
                multiline
                returnKeyType="send"
                blurOnSubmit={false}
              />
              <TouchableOpacity
                onPress={handleSendMessage}
                style={tw`bg-[#189e7f] p-2 rounded-full`}
                disabled={!newMessage.trim()}
              >
                <Send size={20} color="white" />
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>
        ) : (
          <View style={tw`bg-gray-100 p-4 rounded-lg mb-4`}>
            <Text style={tw`text-center text-gray-600`}>
              🚫 This is an admin-only community. Only the creator can send
              messages.
            </Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

export default CommunityView;