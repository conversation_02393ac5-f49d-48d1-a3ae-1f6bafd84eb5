import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  StatusBar,
  ActivityIndicator,
  Image
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import { ArrowLeft } from "lucide-react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase } from "@/utils/supabase";
import tw from "twrnc";

const CreateCommunity = ({ navigation }) => {
  const [communityName, setCommunityName] = useState("");
  const [loading, setLoading] = useState(false);
  const [userId, setUserId] = useState(null);
  const [pfpUri, setPfpUri] = useState(null); // State to store the selected image URI

  useEffect(() => {
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          setUserId(parsedSession?.userId || userSession);
        }
      } catch (error) {
        console.error("Error getting user session:", error);
      }
    };
    getUserId();
  }, []);

  // Function to pick an image from the device
  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5
    });

    if (!result.canceled) {
      setPfpUri(result.assets[0].uri); // Set the selected image URI
    }
  };

  // Function to upload the image to Supabase Storage
  const uploadImageToSupabase = async (fileUri, fileName) => {
    try {
      const filePath = `${userId}/${fileName}`; // Path in Supabase Storage
      const fileBlob = await fetch(fileUri).then((response) => response.blob());

      const { data, error } = await supabase.storage
        .from("community-pfps") // Replace with your bucket name
        .upload(filePath, fileBlob, {
          cacheControl: "3600",
          upsert: false
        });

      if (error) throw error;

      return supabase.storage.from("community-pfps").getPublicUrl(filePath).data
        .publicUrl;
    } catch (error) {
      console.error("Error uploading image:", error);
      throw error;
    }
  };

  const handleCreateCommunity = async () => {
    if (!communityName.trim()) {
      Alert.alert("Error", "Please enter a community name");
      return;
    }

    setLoading(true);

    try {
      let pfpUrl = `https://api.dicebear.com/7.x/initials/png?seed=${encodeURIComponent(
        communityName.trim()
      )}`;

      // If a custom PFP is selected, upload it to Supabase Storage
      if (pfpUri) {
        const fileName = `${Date.now()}-${communityName
          .trim()
          .replace(/\s+/g, "-")}.png`;
        pfpUrl = await uploadImageToSupabase(pfpUri, fileName);
      }

      const { data, error } = await supabase
        .from("communities")
        .insert([
          {
            name: communityName.trim(),
            creator_id: userId,
            user_id: userId,
            color: "#" + Math.floor(Math.random() * 16777215).toString(16),
            pfp: pfpUrl,
            admin_only: 0
          }
        ])
        .select("id");

      if (error) throw error;

      const communityId = data[0]?.id;

      if (communityId) {
        const { error: memberError } = await supabase
          .from("community_members")
          .insert([
            {
              community_id: communityId,
              user_id: userId,
              role: "admin"
            }
          ]);

        if (memberError) throw memberError;
      }

      Alert.alert("Success", "Community created successfully!");
      navigation.goBack();
    } catch (error) {
      console.error("Error creating community:", error);
      Alert.alert("Error", "Failed to create community");
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={tw`flex-1 bg-white`}>
      <StatusBar backgroundColor="white" />
      <View style={tw`p-4 flex-row items-center border-b border-gray-200`}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ArrowLeft size={24} color="black" />
        </TouchableOpacity>
        <Text style={tw`ml-4 text-lg font-bold`}>Create Community</Text>
      </View>
      <View style={tw`p-4`}>
        <Text style={tw`text-lg font-bold mb-2`}>Community Name</Text>
        <TextInput
          value={communityName}
          onChangeText={setCommunityName}
          placeholder="Enter community name"
          style={tw`border border-gray-300 rounded-lg p-3 mb-4`}
          maxLength={50}
        />

        {/* Profile Picture Section */}
        <Text style={tw`text-lg font-bold mb-2`}>Profile Picture</Text>
        <TouchableOpacity
          style={tw`border border-gray-300 rounded-lg p-3 mb-4 justify-center items-center`}
          onPress={pickImage}
        >
          {pfpUri ? (
            <Image
              source={{ uri: pfpUri }}
              style={tw`w-24 h-24 rounded-full`}
            />
          ) : (
            <Text style={tw`text-gray-500`}>Upload PFP</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={tw`bg-[#189e7f] p-3 rounded-lg ${loading ? "opacity-70" : ""}`}
          onPress={handleCreateCommunity}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={tw`text-white text-center font-bold`}>
              Create Community
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default CreateCommunity;
