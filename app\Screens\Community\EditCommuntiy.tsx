import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import { supabase } from "@/utils/supabase";
import tw from "twrnc";
import { ArrowLeft } from "lucide-react-native";
import { useNavigation } from "@react-navigation/native";

const EditCommunityScreen = ({ route }) => {
  const { community } = route.params;
  const [editedName, setEditedName] = useState(community.name);
  const [editedImage, setEditedImage] = useState(null);
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission Denied",
        "Please allow access to your photo library."
      );
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1
    });

    if (!result.canceled) {
      setEditedImage(result.assets[0]);
    }
  };

  const uploadImageToSupabase = async (image) => {
    try {
      const response = await fetch(image.uri);
      const blob = await response.blob();

      const fileExt = image.uri.split(".").pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `community-images/${fileName}`;

      const { data, error } = await supabase.storage
        .from("community-images")
        .upload(filePath, blob, {
          contentType: `image/${fileExt}`
        });

      if (error) throw error;

      // Get the public URL of the uploaded image
      const { data: publicUrl } = supabase.storage
        .from("community-images")
        .getPublicUrl(filePath);

      return publicUrl.publicUrl;
    } catch (error) {
      console.error("Image upload failed:", error);
      Alert.alert("Error", "Failed to upload image");
      return null;
    }
  };

  const saveEditedCommunity = async () => {
    setLoading(true);

    try {
      let imagePath = community.image;

      // Upload new image if editedImage is set
      if (editedImage) {
        const uploadedImageUrl = await uploadImageToSupabase(editedImage);
        if (uploadedImageUrl) {
          imagePath = uploadedImageUrl;
        }
      }

      // Update community details in the database
      const { error } = await supabase
        .from("communities")
        .update({ name: editedName, pfp: imagePath })
        .eq("id", community.id);

      if (error) throw error;

      Alert.alert("Success", "Community updated successfully");
      navigation.goBack();
    } catch (error) {
      console.error("Error updating community:", error);
      Alert.alert("Error", "Failed to update community");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={tw`flex-1 p-4 bg-white`}>
      <View style={tw`flex-row items-center justify-between mb-4`}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ArrowLeft size={24} />
        </TouchableOpacity>

        <Text style={tw`text-lg font-bold`}>Edit Community</Text>

        <TouchableOpacity onPress={saveEditedCommunity} disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color="#0000ff" />
          ) : (
            <Text style={tw`text-blue-500 font-bold`}>Save</Text>
          )}
        </TouchableOpacity>
      </View>

      <TextInput
        value={editedName}
        onChangeText={setEditedName}
        placeholder="Community Name"
        style={tw`border border-gray-300 p-2 rounded mb-4`}
      />

      <TouchableOpacity
        onPress={pickImage}
        style={tw`bg-blue-500 p-2 rounded mb-4`}
      >
        <Text style={tw`text-white text-center`}>Upload Image</Text>
      </TouchableOpacity>

      {editedImage && (
        <Image
          source={{ uri: editedImage.uri }}
          style={tw`w-full h-40 rounded mb-4`}
        />
      )}

      {community.image && !editedImage && (
        <Image
          source={{ uri: community.image }}
          style={tw`w-full h-40 rounded mb-4`}
        />
      )}
    </View>
  );
};

export default EditCommunityScreen;
