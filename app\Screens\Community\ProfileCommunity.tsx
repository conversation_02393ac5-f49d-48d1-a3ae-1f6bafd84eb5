import React, { useEffect, useState } from 'react';
import { View, Text, Image, ScrollView, TouchableOpacity, FlatList, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import tw from 'twrnc';
import { supabase } from '@/utils/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ProfileCommunity = ({ route, navigation }) => {
  const { communityId } = route.params;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [community, setCommunity] = useState(null);
  const [members, setMembers] = useState([]);
  const [userId, setUserId] = useState(null);
  const [isMember, setIsMember] = useState(false);

  useEffect(() => {
    loadUserData();
    fetchCommunityData();
    
    // Setup subscriptions and store their references
    const communitySubscription = supabase
      .channel('public:communities')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'communities', filter: `id=eq.${communityId}` },
        () => fetchCommunityData()
      )
      .subscribe();

    const membersSubscription = supabase
      .channel('public:community_members')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'community_members', filter: `community_id=eq.${communityId}` },
        () => fetchCommunityData()
      )
      .subscribe();

    return () => {
      // Cleanup subscriptions individually
      communitySubscription.unsubscribe();
      membersSubscription.unsubscribe();
    };
  }, [communityId]);

  const loadUserData = async () => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        setUserId(parsedSession?.userId || userSession);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const fetchCommunityData = async () => {
    try {
      // Fetch community details
      const { data: communityData, error: communityError } = await supabase
        .from('communities')
        .select('*')
        .eq('id', communityId)
        .single();

      if (communityError) throw communityError;

      // Fetch members
      const { data: membersData, error: membersError } = await supabase
        .from('community_members')
        .select('user_id, role')
        .eq('community_id', communityId);

      if (membersError) throw membersError;

      // Check if current user is a member
      if (userId) {
        const { data: membershipData } = await supabase
          .from('community_members')
          .select()
          .eq('community_id', communityId)
          .eq('user_id', userId)
          .single();

        setIsMember(!!membershipData);
      }

      // Fetch user details for each member
      const memberDetails = await Promise.all(
        membersData.map(async (member) => {
          try {
            const response = await fetch(
              `https://nityasha.vercel.app/api/v1/users/${member.user_id}`
            );
            const userData = await response.json();
            return {
              id: member.user_id,
              name: userData.username || 'Anonymous',
              avatar: userData.pfp || `https://ui-avatars.com/api/?name=${userData.username?.charAt(0) || 'A'}`
            };
          } catch (error) {
            console.error('Error fetching user data:', error);
            return {
              id: member.user_id,
              name: 'Anonymous',
              avatar: `https://ui-avatars.com/api/?name=A`
            };
          }
        })
      );

      setCommunity(communityData);
      setMembers(memberDetails);
    } catch (error) {
      console.error('Error fetching community data:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const setupSubscriptions = () => {
    // Subscribe to community changes
    supabase
      .channel('public:communities')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'communities', filter: `id=eq.${communityId}` },
        () => fetchCommunityData()
      )
      .subscribe();

    // Subscribe to members changes
    supabase
      .channel('public:community_members')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'community_members', filter: `community_id=eq.${communityId}` },
        () => fetchCommunityData()
      )
      .subscribe();
  };

  const handleJoinLeave = async () => {
    if (!userId) {
      Alert.alert('Please login to join communities');
      return;
    }

    try {
      if (isMember) {
        // Leave community
        const { error } = await supabase
          .from('community_members')
          .delete()
          .eq('community_id', communityId)
          .eq('user_id', userId);

        if (error) throw error;
        setIsMember(false);
        Alert.alert('Success', 'You have left the community');
      } else {
        // Join community
        const { error } = await supabase
          .from('community_members')
          .insert([{
            community_id: communityId,
            user_id: userId,
            role: 'member'
          }]);

        if (error) throw error;
        setIsMember(true);
        Alert.alert('Success', 'You have joined the community');
      }
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('Error', error.message);
    }
  };

  const handleReport = () => {
    Alert.alert(
      'Report Community',
      'Are you sure you want to report this community?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Report',
          style: 'destructive',
          onPress: () => Alert.alert('Reported', 'Thank you for your report. We will review it shortly.')
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={tw`flex-1 justify-center items-center bg-white`}>
        <ActivityIndicator size="large" color="#000" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={tw`flex-1 justify-center items-center bg-white`}>
        <Text style={tw`text-red-500`}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={tw`flex-1 bg-white`}>
      {/* Group Header */}
      <View style={tw`items-center pt-6 pb-4 border-b border-gray-200`}>
        <Image
          source={{ uri: community?.pfp || 'https://i.pravatar.cc/300?img=60' }}
          style={tw`w-32 h-32 rounded-full mb-4`}
        />
        <Text style={tw`text-xl font-bold text-center`}>{community?.name || 'Loading...'}</Text>
        <Text style={tw`text-sm text-gray-500 mt-1`}>Community • {members.length} members</Text>
      </View>

      {/* Group Description */}
      <View style={tw`p-4 border-b border-gray-200 flex items-center justify-center`}>
        <Text style={tw`text-base text-gray-600  items-center justify-center `}>
          {community?.description || 'No description available.'}
          {community?.created_at && `\nCreated on ${new Date(community.created_at).toLocaleDateString()}`}
        </Text>
      </View>

      {/* Members Section */}
      <View style={tw`p-4`}>
        <Text style={tw`text-lg font-semibold mb-4`}>Members</Text>
        <FlatList
          data={members}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
          renderItem={({ item }) => (
            <View style={tw`flex-row items-center mb-4`}>
              <Image
                source={{ uri: item.avatar }}
                style={tw`w-12 h-12 rounded-full`}
              />
              <Text style={tw`ml-3 text-base`}>{item.name}</Text>
            </View>
          )}
        />
      </View>
    </ScrollView>
  );
};

export default ProfileCommunity;