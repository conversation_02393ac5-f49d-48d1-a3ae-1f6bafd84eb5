import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  ImageBackground,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import tw from 'twrnc';
import axios from 'axios';
import {
  Button,
  Chip,
  HelperText,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { ArrowLeft } from 'lucide-react-native';
import useAuth from '@/hooks/auth';
import { useNavigation } from '@react-navigation/native';


export default function RequestCommunity() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const userid = useAuth();
  const categories = ['Technology', 'Sports', 'Arts', 'Education', 'Business', 'Health', 'Gaming'];
const navigation = useNavigation();

  const validateForm = () => {
    const newErrors = {};
    if (!name.trim()) newErrors.name = 'Community name is required';
    if (!description.trim()) newErrors.description = 'Description is required';
    if (!category) newErrors.category = 'Please select a category';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      const formData = new FormData();
      formData.append('name', name);
      formData.append('description', description);
      formData.append('category', category);
      formData.append('requested_by', userid); // TODO: Replace with logged-in user's ID

      const response = await axios.post('https://posts-api.nityasha.com/api/channels/request', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
Alert.alert('✅ Success', 'Community request submitted!', [
  {
    text: 'OK',
    onPress: () => navigation.goBack(),
  },
]);
        setName('');
        setDescription('');
        setCategory('');
        setSelectedTags([]);
        setErrors({});
      } else {
        Alert.alert('❌ Failed', 'Could not submit request');
      }
    } catch (err) {
      console.error('Submission error:', err);
      Alert.alert('❌ Error', err.message || 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  const toggleTag = (tag) => {
    setSelectedTags((prev) =>
      prev.includes(tag)
        ? prev.filter((t) => t !== tag)
        : [...prev, tag]
    );
  };

  return (
    <ImageBackground
      source={require('@/assets/screens/screen7th.png')}
      style={tw`flex-1`}
    >
      <View style={tw`items-center justify-center h-full`}>
        <View style={tw`flex-1 justify-center items-center w-full`}>
          {/* Header */}
          <View style={tw`flex-row items-center justify-between w-full px-5 mt-4`}>
            <View style={tw`bg-[#E1E8EB] h-10 w-10 rounded-full items-center justify-center`}>
              <ArrowLeft size={24} color="#000" />
            </View>
            <Text style={[tw`text-black text-2xl`, { fontFamily: 'Helvetica_bold' }]}>
              New Community
            </Text>
            <View style={tw`bg-[#E1E8EB] h-10 w-10 rounded-full items-center justify-center`}>
              <MaterialCommunityIcons name="help" size={24} color="#000" />
            </View>
          </View>

          {/* Form Area */}
          <ScrollView style={tw`p-6 w-full`} keyboardShouldPersistTaps="handled">
            <TextInput
              style={[tw`border-2 py-4 px-5 rounded-[18px] font-bold`, { fontFamily: 'Helvetica_bold' }]}
              placeholder="Enter community name"
              value={name}
              onChangeText={setName}
            />
            {errors.name && <HelperText type="error">{errors.name}</HelperText>}

            <TextInput
              style={[tw`border-2 py-4 px-5 rounded-[18px] mt-4`, { fontFamily: 'Helvetica_bold' }]}
              placeholder="Enter description"
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
            />
            {errors.description && <HelperText type="error">{errors.description}</HelperText>}

            {/* Category Picker */}
            <View style={tw`mt-4 mb-4`}>
              <Text style={[tw`text-lg mb-2`, { fontFamily: 'Helvetica_bold' }]}>Select Category</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {categories.map((cat) => (
                  <Chip
                    key={cat}
                    mode="outlined"
                    selected={category === cat}
                    onPress={() => setCategory(cat)}
                    style={tw`mr-2 mb-2`}
                  >
                    {cat}
                  </Chip>
                ))}
              </ScrollView>
              {errors.category && <HelperText type="error">{errors.category}</HelperText>}
            </View>

            {/* Submit Button */}
            <Button
              mode="contained"
              style={tw`h-[55px] items-center justify-center bg-black mt-2`}
              textColor="white"
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
            >
              Submit Request
            </Button>
          </ScrollView>
        </View>
      </View>
    </ImageBackground>
  );
}
