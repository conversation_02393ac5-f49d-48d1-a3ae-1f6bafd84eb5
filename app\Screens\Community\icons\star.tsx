import React from 'react';
import { View, Text } from 'react-native';
import Svg, { Path } from 'react-native-svg';

// This is a preview of how the component would look in React Native
// Note: This code is for demonstration and would be used in a React Native app

const StarBadge = ({ number = 4, size = 100, color = '#b8b5e6' }) => {
  // Create a star shape with 12 points
  const createStarPath = () => {
    const outerRadius = size / 2;
    const innerRadius = outerRadius * 0.7;
    const points = 12;
    const angleStep = Math.PI / points;

    let path = '';

    for (let i = 0; i < points * 2; i++) {
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const angle = i * angleStep;
      const x = size / 2 + radius * Math.sin(angle);
      const y = size / 2 - radius * Math.cos(angle);

      if (i === 0) {
        path += `M ${x},${y} `;
      } else {
        path += `L ${x},${y} `;
      }
    }

    path += 'Z';
    return path;
  };

  return (
    <View style={{ width: size, height: size, alignItems: 'center', justifyContent: 'center' }}>
      <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <Path
          d={createStarPath()}
          fill={color}
          stroke="none"
        />
      </Svg>
      <Text
        style={{
          position: 'absolute',
          fontSize: size * 0.4,
          fontWeight: 'bold',
          color: '#000',
          textAlign: 'center',
        }}
      >
        {number}
      </Text>
    </View>
  );
};

export default StarBadge;