import { View, Text, ImageBackground, FlatList, StatusBar, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import React, { useEffect, useState, useCallback } from 'react';
import tw from 'twrnc';
import { ChevronLeft, Plus, Search } from 'lucide-react-native';
import StarBadge from './icons/star';
import { useNavigation, CommonActions } from '@react-navigation/native';
import axios from 'axios';
import { Searchbar } from 'react-native-paper';

// Define interface for community data
interface Community {
  id: string | number;
  name: string;
  // Add other properties as needed
}

// Skeleton Loader Component
const SkeletonLoader = () => (
  <View
    style={tw`rounded-[30px] w-full p-4 bg-[#F3F0E8] mb-4 flex-row justify-between items-center`}
  >
    <View style={tw`flex-row items-center`}>
      <View style={tw`w-12 h-12 bg-gray-300 rounded-full`} />
      <View style={tw`ml-5`}>
        <View style={tw`w-32 h-4 bg-gray-300 rounded`} />
        <View style={tw`w-24 h-4 bg-gray-300 rounded mt-2`} />
      </View>
    </View>
    <View style={tw`h-12 w-12 rounded-full bg-gray-300`} />
  </View>
);

// Define navigation type
type RootStackParamList = {
  CommunityView: { communityId: string | number };
  CommunitySearch: {};
  // Add other screens as needed
};

type NavigationProp = {
  navigate: (screen: keyof RootStackParamList, params?: any) => void;
  goBack: () => void;
  dispatch: (action: any) => void;
};

export default function Community() {
  const navigation = useNavigation<NavigationProp>();

  // Set status bar color to white
  StatusBar.setBackgroundColor('#BBEAC4');
  StatusBar.setBarStyle('dark-content');

  // State for community data
  const [communities, setCommunities] = useState<Community[]>([]);
  const [filteredCommunities, setFilteredCommunities] = useState<Community[]>([]); // For search results
  const [searchQuery, setSearchQuery] = useState(''); // Search query
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMoreCommunities, setHasMoreCommunities] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const ITEMS_PER_PAGE = 10;

  // Fetch community data with pagination
  const fetchCommunities = useCallback(async (pageNum = 1, shouldRefresh = false) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      // Add pagination parameters to the API request
      const response = await axios.get(
        `https://posts-api.nityasha.com/api/channels?page=${pageNum}&limit=${ITEMS_PER_PAGE}`
      );

      if (response.data && response.data.success) {
        const newCommunities = response.data.data || [];

        // Check if we've reached the end of available communities
        if (newCommunities.length < ITEMS_PER_PAGE) {
          setHasMoreCommunities(false);
        } else {
          setHasMoreCommunities(true);
        }

        // Update communities state based on whether we're refreshing or loading more
        if (shouldRefresh || pageNum === 1) {
          setCommunities(newCommunities);
          setFilteredCommunities(newCommunities); // Update filtered list
          setPage(1);
        } else {
          setCommunities(prevCommunities => [...prevCommunities, ...newCommunities]);
          setFilteredCommunities(prevCommunities => [...prevCommunities, ...newCommunities]); // Update filtered list
        }
      } else {
        console.log('No communities available or invalid response format');

        if (shouldRefresh || pageNum === 1) {
          setCommunities([]);
          setFilteredCommunities([]); // Clear filtered list
        }
      }
    } catch (err) {
      console.error('API Error:', err);

      if (shouldRefresh || pageNum === 1) {
        setCommunities([]);
        setFilteredCommunities([]); // Clear filtered list
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  }, []);

  // Handle search query change
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);

    if (query.trim() === '') {
      setFilteredCommunities(communities); // Reset to full list if query is empty
    } else {
      const filtered = communities.filter(community =>
        community.name.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredCommunities(filtered);
    }
  }, [communities]);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchCommunities(1, true);
  }, [fetchCommunities]);

  // Handle loading more communities
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMoreCommunities && communities.length > 0) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchCommunities(nextPage);
    }
  }, [loadingMore, hasMoreCommunities, page, fetchCommunities, communities.length]);

  // Initial fetch on component mount
  useEffect(() => {
    fetchCommunities(1);
  }, [fetchCommunities]);

  // Render community item
  const renderCommunityItem = useCallback(
    ({ item: community, index }: { item: Community; index: number }) => {
        // Skip rendering if community data is invalid
        if (!community || !community.id) {
          return null;
        }

      return (
        <View
          style={tw`px-2`}
        >
          <TouchableOpacity
            onPress={() => navigation.navigate('CommunityView', { communityId: community.id })}
            style={tw`rounded-[30px] w-full p-4 bg-[#F3F0E8] mb-4 flex-row justify-between items-center`}
          >
            <View style={tw`flex-row items-center`}>
              <StarBadge
                number={index + 1} // Display the index as the number
                size={48}
                color={index < 9 ? '#FFD700' : '#A1AFF2'} // Yellow for the first 9 items, normal color for others
              />
              <View style={tw`ml-5`}>
                <Text style={[tw`text-[#1F2937] text-lg`, { fontFamily: 'Helvetica_bold' }]}>
                  {`${community.name || 'Unnamed Community'}`}
                </Text>
              </View>
            </View>
            <View style={tw`h-12 w-12 rounded-full bg-yellow-500 items-center justify-center `}>
              <Text style={[tw`text-lg`, { fontFamily: 'Helvetica_bold' }]}>
                {community.name?.charAt(0).toUpperCase() || ''}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      );
    },
    [navigation]
  );

  // Render skeleton item
  const renderSkeletonItem = useCallback(({ index }: { index: number }) => (
    <View style={tw`px-2`}>
      <SkeletonLoader key={index} />
    </View>
  ), []);

  // Render footer component (loading indicator for pagination)
  const renderFooter = useCallback(() => {
    if (!loadingMore) return null;

    return (
      <View style={tw`py-4 flex items-center justify-center`}>
        <ActivityIndicator size="small" color="#0000ff" />
        <Text style={tw`text-gray-500 mt-2`}>Loading more communities...</Text>
      </View> 
    );
  }, [loadingMore]);

  return (
    <View
      style={tw`flex-1`}
    >
      <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex- `}>
        <View style={tw`w-full items-center justify-between flex-row px-5 py-3 pt-9`}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <ChevronLeft size={30} color={'#000'} />
          </TouchableOpacity>
          <Text style={[tw`text-black text-2xl`, { fontFamily: 'Helvetica_bold' }]}>Community</Text>
          <TouchableOpacity onPress={() => navigation.navigate('RequestCommunity')}>
            <Plus size={24} color={'#000'} />
          </TouchableOpacity>
        </View>

        <View style={tw`px-4 `}>
          <Searchbar
            placeholder="Search communities..."
            value={searchQuery}
            onChangeText={handleSearch}
            style={{
              borderRadius: 300,
              elevation: 3,
              backgroundColor: "#F5FCF5",
            }}
            placeholderTextColor="#999"
          />
        </View>

        {loading && !refreshing ? (
          // Show skeleton loader while loading initially
          <FlatList
            data={Array(5).fill(0)}
            renderItem={renderSkeletonItem}
            keyExtractor={(_, index) => `skeleton-${index}`}
            style={tw`mt-5`}
            contentContainerStyle={tw`px-2`}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <FlatList
            data={filteredCommunities} // Use filtered list
            renderItem={renderCommunityItem}
            keyExtractor={(item, index) => {
              // Use item.id as the unique key if it exists.
              if (item.id !== null && item.id !== undefined) {
                return `community-${item.id.toString()}`;
              }
              // If item.id is missing, falling back to the index can cause duplicate keys
              // when the list is updated. Log a warning and use a key that highlights the issue.
              // This fallback might still lead to duplicate keys if there are multiple items
              // without IDs that end up at the same index across renders.
              console.warn(`Community item at index ${index} is missing a unique ID. Key may not be stable.`);
              return `community-missing-id-${index}`;
            }}
            style={tw`mt-5`}
            contentContainerStyle={tw`px-2 pb-5`}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#0000ff']}
                tintColor="#0000ff"
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            ListFooterComponent={renderFooter}
            initialNumToRender={5}
            maxToRenderPerBatch={10}
            windowSize={10}
            removeClippedSubviews={true}
            ListEmptyComponent={
              <View style={tw`flex-1 items-center justify-center py-10`}>
                <Text style={tw`text-gray-500 text-center`}>No communities available</Text>
              </View>
            }
          />
        )}
      </ImageBackground>
    </View>
  );
}
