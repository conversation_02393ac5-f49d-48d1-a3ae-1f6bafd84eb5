import React, { useEffect, useRef, useState } from "react";
import {
  View,
  TouchableOpacity,
  Alert,
  ScrollView,
  Share,
  PanResponder,
  Image, Animated,
  TextInput,
  ActivityIndicator,
  Text as RNText,  // Explicit import for clarity
  ImageBackground
} from "react-native";
import tw from "twrnc";
import { Bell, EllipsisVertical, Send } from "lucide-react-native";
import CardCommunities from "@/components/cardcommunities";
import { supabase } from "@/utils/supabase";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CommentsSheet from "@/components/CommentsSheet";
import { Avatar, Button, Card, IconButton, Text, AnimatedFAB } from "react-native-paper";
import Logo from "@/assets/images/image.png";
import { CommonActions, useNavigation } from "@react-navigation/native";
import Updates from "@/components/Home/Update";
import Posts from "@/components/community/postsown";
import tailwind from "twrnc";

const Community = ({ navigation }) => {
  const [isExtended, setIsExtended] = useState(true);

  const onScroll = ({ nativeEvent }) => {
    const currentScrollPosition = Math.floor(nativeEvent?.contentOffset?.y) ?? 0;
    setIsExtended(currentScrollPosition <= 0);
  };

  const swipeAnim = new Animated.Value(0);

  const fetchJoinedCommunities = async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (!userSession) return;

      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      const { data: memberData, error: memberError } = await supabase
        .from("community_members")
        .select("community_id")
        .eq("user_id", userId);

      // Ensure any text output is wrapped in Text component
      if (memberError) {
        return <Text>Error fetching communities</Text>;
      }

      return memberData;
    } catch (error) {
      console.error(error);
      return <Text>An error occurred</Text>;
    }
  };

  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: `Join our community on Nityasha!`,
        title: "Share Community"
      });
    } catch (error) {
      Alert.alert("Error", "Failed to share community");
    }
  };
  const [activeTab, setActiveTab] = useState("feed");
  const [hasCreatedCommunity, setHasCreatedCommunity] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newCommunityName, setNewCommunityName] = useState("");
  const [joinedCommunities, setJoinedCommunities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [posts, setPosts] = useState([]);
  const [likedPosts, setLikedPosts] = useState({});
  const [newPostContent, setNewPostContent] = useState("");
  const [userId, setUserId] = useState(null);
  const [expandedPost, setExpandedPost] = useState(null);
  const [commentText, setCommentText] = useState("");
  const [postId, setSelectedPostid] = useState([]);
  const [selectedComments, setSelectedComments] = useState([]);
  const [showComments, setShowComments] = useState(false);
  const [tabData, setTabData] = useState({
    feed: { loaded: false, loading: false, data: null },
    communities: { loaded: false, loading: false, data: null },
    updates: { loaded: false, loading: false, data: null },
    connects: { loaded: false, loading: false, data: null }
  });
  const fetchPosts = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("posts")
        .select(
          `
          id,
          content,
          created_at,
          user_id,
          likes:likes(user_id),
          comments:comments(*)
        `
        )
        .limit(50); // Fetch more posts for better randomization

      if (!error && data) {
        // Randomize the posts array
        const randomizedData = data
          .sort(() => Math.random() - 0.5) // Shuffle the array randomly
          .slice(0, 10); // Take only 10 random posts

        const postsWithUserDetails = await Promise.all(
          randomizedData.map(async (post) => {
            try {
              const response = await fetch(
                `https://nityasha.vercel.app/api/v1/users/${post.user_id}`
              );
              const userData = await response.json();

              // Check if the current user has liked this post
              const hasLiked = post.likes?.some(
                (like) => like.user_id === userId
              );
              setLikedPosts((prev) => ({ ...prev, [post.id]: hasLiked }));

              return {
                ...post,
                likes: post.likes?.length || 0,
                comments: post.comments?.length || 0,
                commentsList: post.comments || [],
                username: userData.username,
                pfp: userData.pfp || `https://ui-avatars.com/api/?name=${userData.username?.charAt(0) || "A"}`
              };
            } catch (error) {
              console.error("Error fetching user data:", error);
              return null;
            }
          })
        );

        setPosts(postsWithUserDetails.filter(post => post !== null));
        setTabData(prev => ({
          ...prev,
          feed: { loaded: true, loading: false, data: postsWithUserDetails }
        }));
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  };;
  const handleAddComment = async (postId) => {
    if (!commentText.trim() || !userId) {
      Alert.alert(
        "Error",
        "Please enter a comment and ensure you are logged in."
      );
      return;
    }

    try {
      // First insert the comment
      const { data: newComment, error: insertError } = await supabase
        .from("comments")
        .insert([
          {
            post_id: postId,
            user_id: userId,
            content: commentText.trim(),
            created_at: new Date().toISOString(),
            parent_id: null
          }
        ])
        .select()
        .single();

      if (insertError) throw insertError;

      // Get user details for the new comment
      const response = await fetch(
        `https://nityasha.vercel.app/api/v1/users/${userId}`
      );
      const userData = await response.json();

      // Update the posts state with the new comment
      setPosts((currentPosts) =>
        currentPosts.map((post) => {
          if (post.id === postId) {
            const updatedCommentsList = [
              ...(post.commentsList || []),
              {
                ...newComment,
                user_id: userData.id || '1',
                username: userData.username || "Anonymous",
                user_pfp:
                  userData.pfp ||
                  `https://ui-avatars.com/api/?name=${userData.username?.charAt(0) || "A"
                  }`
              }
            ];
            return {
              ...post,
              comments: (post.comments || 0) + 1,
              commentsList: updatedCommentsList
            };
          }
          return post;
        })
      );

      // Clear the comment text
      setCommentText("");
    } catch (error) {
      console.error("Error adding comment:", error);
      Alert.alert("Error", "Failed to add comment");
    }
  };
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gestureState) => {
        const maxSwipeDistance = 200;
        const dx = Math.min(
          Math.max(gestureState.dx, -maxSwipeDistance),
          maxSwipeDistance
        );
        const normalizedDx = dx / maxSwipeDistance;

        // Map swipe position to tab index
        const tabCount = 4; // Number of tabs
        const animValue = Math.max(0, Math.min(1, (1 + normalizedDx) / tabCount));
        swipeAnim.setValue(animValue);
      },
      onPanResponderRelease: (_, gestureState) => {
        const velocity = Math.abs(gestureState.vx);
        const isQuickSwipe = velocity > 0.3;

        const tabCount = 4; // Number of tabs
        const swipeThreshold = 40; // Minimum swipe distance to trigger tab change

        if (Math.abs(gestureState.dx) > swipeThreshold || isQuickSwipe) {
          const currentTabIndex = [
            "feed",
            "updates",
            "communities",
            "connects"
          ].indexOf(activeTab);
          let newTabIndex = currentTabIndex;

          if (gestureState.dx > 0) {
            // Swipe right: Move to previous tab
            newTabIndex = Math.max(0, currentTabIndex - 1);
          } else {
            // Swipe left: Move to next tab
            newTabIndex = Math.min(tabCount - 1, currentTabIndex + 1);
          }

          const newTab = ["feed", "updates", "communities", "connects"][
            newTabIndex
          ];
          switchTab(newTab);

          Animated.spring(swipeAnim, {
            toValue: newTabIndex / (tabCount - 1), // Normalize index to range [0, 1]
            useNativeDriver: true,
            tension: 65,
            friction: 10
          }).start();
        } else {
          // Return to original position
          const currentTabIndex = [
            "feed",
            "updates",
            "communities",
            "connects"
          ].indexOf(activeTab);
          Animated.spring(swipeAnim, {
            toValue: currentTabIndex / (tabCount - 1), // Normalize index to range [0, 1]
            useNativeDriver: true,
            tension: 65,
            friction: 10
          }).start();
        }
      }
    })
  ).current;


  const handleDeleteCommunity = async () => {
    Alert.alert(
      "Delete Community",
      "Are you sure you want to delete this community?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const { error } = await supabase
                .from("communities")
                .delete()
                .eq("creator_id", userId);
              if (error) throw error;
              setHasCreatedCommunity(false);
              Alert.alert("Success", "Community deleted successfully");
            } catch (error) {
              console.error("Error deleting community:", error);
              Alert.alert("Error", "Failed to delete community");
            }
          }
        }
      ]
    );
  };
  const switchTab = React.useCallback(
    (tab) => {
      console.log("Switching to tab:", tab);

      // Check if tab is valid
      const validTabs = ["feed", "updates", "communities", "connects"];
      if (!validTabs.includes(tab)) {
        console.error(`Invalid tab: ${tab}`);
        return;
      }

      setActiveTab(tab);

      // Prefetch data for the selected tab if not already loaded
      if (!tabData[tab]?.loaded) {
        // Initialize tab data if it doesn't exist
        if (!tabData[tab]) {
          setTabData((prev) => ({
            ...prev,
            [tab]: { loaded: false, data: [] }
          }));
        }
      }

      // Animate swipe based on tab index
      const tabIndex = ["feed", "updates", "communities", "connects"].indexOf(
        tab
      );
      Animated.spring(swipeAnim, {
        toValue: tabIndex / 3, // Normalize index to range [0, 1]
        useNativeDriver: true,
        tension: 80,
        friction: 8,
        velocity: 2,
        restSpeedThreshold: 100,
        restDisplacementThreshold: 40
      }).start();
    },
    [tabData]
  );
  // Prefetch data for all tabs
  const prefetchTabData = React.useCallback(async () => {
    const tabs = ['feed', 'communities', 'updates', 'connects'];
    await Promise.all(tabs.map(async (tab) => {
      if (!tabData[tab].loaded) {
        switch (tab) {
          case 'feed':
            await fetchPosts();
            break;
          case 'communities':
            await fetchJoinedCommunities();
            break;
          // Add other tab data prefetching as needed
        }
      }
    }));
  }, [tabData]);

  // Initialize prefetching when component mounts
  React.useEffect(() => {
    prefetchTabData();
  }, [prefetchTabData]);
  const handleLike = async (postId) => {
    if (!userId) {
      Alert.alert("Error", "You must be logged in to like posts.");
      return;
    }

    try {
      const hasLiked = likedPosts[postId];

      if (hasLiked) {
        // Remove like from Supabase
        await supabase
          .from("likes")
          .delete()
          .match({ post_id: postId, user_id: userId });
      } else {
        // Add like to Supabase
        await supabase
          .from("likes")
          .insert([{ post_id: postId, user_id: userId }]);
      }

      // Update UI optimistically
      setLikedPosts((prev) => ({
        ...prev,
        [postId]: !hasLiked
      }));
    } catch (error) {
      console.error("Error updating like:", error);
      Alert.alert("Error", "Failed to update like.");
    }
  };
  const checkUserCommunity = async () => {
    try {
      const { data, error } = await supabase
        .from("communities")
        .select()
        .eq("creator_id", userId);

      if (error) throw error;
      setHasCreatedCommunity(data && data.length > 0);
    } catch (error) {
      console.error("Error checking user community:", error);
    }
  };
  const handleCreateCommunity = async () => {
    if (!newCommunityName.trim()) {
      Alert.alert("Error", "Please enter a community name");
      return;
    }

    try {
      const { data, error } = await supabase.from("communities").insert([
        {
          name: newCommunityName.trim(),
          creator_id: userId,
          color: "#" + Math.floor(Math.random() * 16777215).toString(16),
          pfp: `https://ui-avatars.com/api/?name=${newCommunityName
            .trim()
            .charAt(0)}`
        }
      ]);

      if (error) throw error;

      Alert.alert("Success", "Community created successfully!");
      setShowCreateModal(false);
      setNewCommunityName("");
      setHasCreatedCommunity(true);
    } catch (error) {
      console.error("Error creating community:", error);
      Alert.alert("Error", "Failed to create community");
    }
  };
  useEffect(() => {
    const fetchUserData = async () => {
      const userSession = await AsyncStorage.getItem("userSession");
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        const currentUserId = parsedSession?.userId || userSession;
        setUserId(currentUserId);

        try {
          const response = await fetch(
            `https://nityasha.vercel.app/api/v1/users/${currentUserId}`
          );
          const userData = await response.json();

          if (response.ok && userData) {
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
        }
      }
    };

    const fetchPosts = async () => {
      try {
        const { data, error } = await supabase
          .from("posts")
          .select(
            `
            id,
            content,
            created_at,
            user_id,
            likes:likes(user_id),
            comments:comments(*)
          `
          )
          .order("created_at", { ascending: false });

        const postsWithUserDetails = await Promise.all(
          data.map(async (post) => {
            try {
              const response = await fetch(
                `https://nityasha.vercel.app/api/v1/users/${post.user_id}`
              );
              const userData = await response.json();

              // Check if the current user has liked this post
              const hasLiked = post.likes?.some(
                (like) => like.user_id === userId
              );
              setLikedPosts((prev) => ({ ...prev, [post.id]: hasLiked }));

              // Process comments to include user details
              const commentsWithUserDetails = await Promise.all(
                (post.comments || []).map(async (comment) => {
                  const commentUserResponse = await fetch(
                    `https://nityasha.vercel.app/api/v1/users/${comment.user_id}`
                  );
                  const commentUserData = await commentUserResponse.json();
                  return {
                    ...comment,
                    username: commentUserData.username || "Anonymous",
                    user_pfp:
                      commentUserData.pfp ||
                      `https://ui-avatars.com/api/?name=${commentUserData.username?.charAt(0) || "A"
                      }`
                  };
                })
              );

              return {
                ...post,
                likes: post.likes?.length || 0,
                comments: post.comments?.length || 0,
                commentsList: commentsWithUserDetails,
                username: userData.username,
                pfp:
                  userData.pfp ||
                  `https://ui-avatars.com/api/?name=${userData.username?.charAt(0) || "A"
                  }`
              };
            } catch (error) {
              console.error("Error fetching user data:", error);
              return null;
            }
          })
        );
        setPosts(postsWithUserDetails.filter((post) => post !== null));
        setTabData(prev => ({
          ...prev,
          feed: { loaded: true, loading: false, data: postsWithUserDetails }
        }));
      } catch (error) {
        console.error("Error fetching posts:", error);
      } finally {
        setLoading(false);
      }
    };
    // Subscribe to posts changes
    const postsSubscription = supabase
      .channel("public:posts")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "posts" },
        (payload) => {
          console.log("Post change detected:", payload);
          fetchPosts();
        }
      )
      .subscribe();

    // Subscribe to likes changes
    const likesSubscription = supabase
      .channel("public:likes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "likes" },
        (payload) => {
          console.log("Likes change detected:", payload);
          fetchPosts();
        }
      )
      .subscribe();

    // Subscribe to comments changes
    const commentsSubscription = supabase
      .channel("public:comments")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "comments" },
        (payload) => {
          console.log("Comments change detected:", payload);
          fetchPosts();
        }
      )
      .subscribe();

    // Subscribe to community members changes
    const membersSubscription = supabase
      .channel("public:community_members")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "community_members" },
        (payload) => {
          console.log("Community member change detected:", payload);
          fetchJoinedCommunities();
        }
      )
      .subscribe();

    const fetchJoinedCommunities = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (!userSession) return;

        const parsedSession = JSON.parse(userSession);
        const userId = parsedSession?.userId || userSession;

        const { data: memberData, error: memberError } = await supabase
          .from("community_members")
          .select("community_id")
          .eq("user_id", userId);

        if (memberError) throw memberError;

        if (memberData && memberData.length > 0) {
          const communityIds = memberData.map((item) => item.community_id);

          const { data: communities, error: commError } = await supabase
            .from("communities")
            .select("id, name, pfp")
            .in("id", communityIds);

          if (commError) throw commError;

          const formattedCommunities = communities.map((comm) => ({
            id: comm.id,
            name: comm.name,
            image:
              comm.pfp ||
              `https://ui-avatars.com/api/?name=${comm.name.slice(0, 1)}`
          }));

          setJoinedCommunities(formattedCommunities);
        }
      } catch (error) {
        console.error("Error fetching joined communities:", error);
        Alert.alert("Error", "Failed to load joined communities");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
    if (userId) {
      checkUserCommunity();
    }
    fetchJoinedCommunities();
    fetchPosts();

    return () => {
      postsSubscription.unsubscribe();
      likesSubscription.unsubscribe();
      commentsSubscription.unsubscribe();
      membersSubscription.unsubscribe();
    };
  }, []);
  const originalTabs = ["feed", "Updates", "communities"];
  const displayTabs = ["Feed", "Updates", "Community"];

  const TabButton = React.memo(({ tab, index, isActive, onPress }) => {
    const buttonStyle = React.useMemo(
      () => tw`${isActive ? "bg-[#F3F0E8]" : "bg-[#]"} px-4 py-2 rounded-full`,
      [isActive]
    );

    const textStyle = React.useMemo(
      () => [tw`text-black`, { fontFamily: "ProductSans-Medium" }],
      []
    );

    return (
      <TouchableOpacity
        key={index}
        style={buttonStyle}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Text style={textStyle}>{tab}</Text>
      </TouchableOpacity>
    );
  }, (prevProps, nextProps) => prevProps.isActive === nextProps.isActive);

  if (loading) {
    return (
  <ImageBackground source={require('@/assets/screens/screen7th.png')} style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
             
            
            </ImageBackground>
    );
  }
  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1`}>
      <View style={tw`flex-1`}>
        <ScrollView 
          style={tw`flex-1 h-full`}
          onScroll={onScroll}
          scrollEventThrottle={16}
        >
          <View style={tw`h-16 flex-row items-center px-4 mb-5`}>
            <View style={tw`flex-1`}>
              <IconButton
                icon="arrow-left"
                size={24}
                onPress={() => {
                  navigation.dispatch(
                    CommonActions.reset({
                      index: 0,
                      routes: [{ name: "BottomTabs" }]
                    })
                  );
                }}
                style={{
                  backgroundColor: "#CDF0D2",
                  borderRadius: 1000,
                  width: 50,
                  height: 50,
                  justifyContent: "center",
                  alignItems: "center",
                  elevation: 5
                }}
              />
            </View>

            <View style={tw`flex-1 items-center`}>
              <Image
                source={Logo}
                style={[tw`w-12 h-12 rounded-full bg-black`, { tintColor: "#fff" }]}
              />
            </View>

            <View style={tw`flex-1 items-end gap-1 flex-row justify-end`}>
              <View style={tailwind`relative`}>
                <TouchableOpacity>
                  <Bell size={24} color={"#000"} />
                  <View style={tailwind`absolute top-0 right-0 w-3 h-3 bg-red-500 rounded-full border border-white`} />
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                onPress={() => navigation.navigate("CreateCommunity")}
              >
                <EllipsisVertical size={24} color={"#000"} />
              </TouchableOpacity>
            </View>
          </View>
          <View
            style={tw`flex-1 w-full rounded-t-[30px] flex py-2 px-2 overflow-hidden`}
          >
            <View style={tw`flex-row mb-5 flex items-center justify-center`}>
              <View
                style={tw`flex-row items-center gap-x-3 bg-[#CDF0D2] p-1 px-2 rounded-full`}
              >
                {displayTabs.map((tab, index) => (
                  <TouchableOpacity
                    key={tab}
                    onPress={() =>
                      switchTab(
                        ["feed", "updates", "communities", "connects"][index]
                      )
                    }
                    style={[
                      tw`px-4 py-2 rounded-full`,
                      activeTab ===
                      ["feed", "updates", "communities", "connects"][index] &&
                      tw`bg-[#F5FCF5]`
                    ]}
                  >
                    <Text style={[tw`text-black font-medium`,{fontFamily: "ProductSans-Medium"}]}>{tab}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              {activeTab === "communities" ? (
                <CardCommunities navigation={navigation} />
              ) : activeTab === "feed" ? (
                <View>
                  {posts?.map((post) => (
                    <View style={{ padding: 10 }} key={post.id}>
                      <Card style={tw`bg-[#F5FCF5]`}>
                        <Card.Title
                          title={`${post.username}`}
                          left={(props) => (
                            <Avatar.Image {...props} source={{ uri: post.pfp }} />
                          )}
                          right={(props) => (
                            <IconButton
                              iconColor={
                                likedPosts[post.id] ? "#24cfa6" : "#767062"
                              }
                              style={{
                                backgroundColor: "#CDF0D2",
                                borderRadius: 1000,
                                width: 50,
                                height: 50,
                                justifyContent: "center",
                                alignItems: "center",
                                elevation: 5
                              }}
                              {...props}
                              icon="star-outline"
                              onPress={() => handleLike(post.id)}
                            />
                          )}
                        />
                        <Card.Content>
                          <Text variant="bodyMedium" style={{ marginTop: 5 }}>
                            {post.content}
                          </Text>
                        </Card.Content>
                        <View
                          style={tw`w-full items-center justify-between flex-row px-3 my-3`}
                        >
                          <Button
                            mode="contained"
                            onPress={() => {
                              setSelectedComments(post.commentsList || []);
                              setShowComments(true);
                              setSelectedPostid(post.id);
                            }}
                            style={tw`w-[100%]`}
                            buttonColor="#CDF0D2"
                            textColor="#000"
                          >
                            Comment {post.commentsList?.length || 0}
                          </Button>
                        </View>
                      </Card>
                    </View>
                  ))}
                </View>
              ) : activeTab === "connects" ? (
                <View style={tw`w-full h-full`}>
                  <Posts userId={userId} />
                </View>
              ) : (
                <Updates />
              )}
            </ScrollView>
          </View>
        </ScrollView>

        <AnimatedFAB
          icon={'plus'}
          label={'Create Post'}
          extended={isExtended}
          onPress={() => navigation.navigate('CreatePost', { userId })}
          visible={true}
          animateFrom={'right'}
          iconMode={'dynamic'}
          style={[
            tw`absolute bottom-4 right-4`,
            {
              backgroundColor: '#000',
            }
          ]}
          color="white"
        />

        <CommentsSheet
          postId={postId}
          visible={showComments}
          onClose={() => setShowComments(false)}
          comments={selectedComments}
        />
      </View>
    </ImageBackground>
  );
};

export default Community;
