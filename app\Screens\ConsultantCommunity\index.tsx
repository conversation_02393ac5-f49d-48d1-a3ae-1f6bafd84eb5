import { View, Text, ImageBackground, TouchableOpacity, Dimensions, ActivityIndicator, TextInput, Modal, TouchableWithoutFeedback, Platform, Alert, ScrollView, Animated, PanResponder, Keyboard, SafeAreaView, InteractionManager, Image } from 'react-native'
import React, { useState, useCallback, useRef, useEffect } from 'react'
import tw from 'twrnc'
import { ArrowLeft, Heart, MessageCircle, EllipsisVertical, Send, Plus, Camera, Video, FileText, Zap, X, CircleCheck } from 'lucide-react-native'
import { Image as MotiImage, MotiView } from 'moti'
import { FlatList } from 'react-native-gesture-handler'
import * as ImagePicker from 'expo-image-picker'
import <PERSON><PERSON><PERSON>y<PERSON>heckout from 'react-native-razorpay'
import PaymentPopup from '@/components/payment/paymentpopup'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useReanimatedKeyboardAnimation, KeyboardAvoidingView } from 'react-native-keyboard-controller'

interface PollOption {
  text: string;
  votes: number;
}

interface Message {
  id: string;
  type: 'text' | 'poll' | 'image';
  content: string;
  timestamp: string;
  likes: number;
  comments: number;
  pollOptions?: PollOption[]; 
  imageUrl?: string;
}

// Add these new interfaces
interface Poll {
  _id: string;
  channelId: string;
  question: string;
  options: {
    text: string;
    votes: string[];
  }[];
  createdBy: string;
  isActive: boolean;
  createdAt: string;
}

interface PollResults {
  pollId: string;
  question: string;
  totalVotes: number;
  isActive: boolean;
  createdAt: string;
  options: {
    text: string;
    votes: number;
    percentage: number;
  }[];
}

interface ChatMessage {
  _id: string;
  channelId: string;
  content: string;
  senderId: string;
  type: 'text' | 'image' | 'poll' | 'superchat';
  timestamp: string;
  imageUrl?: string;
  amount?: number;
}

interface Follow {
  followerId: string;
  followingId: string;
  createdAt: string;
}

interface Channel {
  _id: string;
  name: string;
  description: string;
  isPaid: boolean;
  price: number;
  ownerId: string;
  createdAt: string;
}

// Add new interface for user info
interface UserInfo {
  id: number;
  username: string;
  pfp?: string; 
  email?: string;
  is_online?: number;
  last_seen?: string;
  role?: string;
  balance?: string;
}

export default function ConsultantCommunity({ route, navigation }: any) {
  const { consultantId, username, pfp, active } = route.params;
  const windowWidth = Dimensions.get('window').width;
  const [selectedPollOptions, setSelectedPollOptions] = useState<Record<string, number>>({});
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});
  const [imageDimensions, setImageDimensions] = useState<Record<string, { width: number, height: number }>>({});
  const [messageText, setMessageText] = useState('');
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [userId, setuserId] = useState<string>('');

  // Add missing state variables for channel access
  const [isPaidChannel, setIsPaidChannel] = useState(false);
  const [channelPrice, setChannelPrice] = useState(0);
  const [hasAccess, setHasAccess] = useState(false);
 
  // Add these new states
  const [polls, setPolls] = useState<Poll[]>([]);
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followersCount, setFollowersCount] = useState(0);
  const [isFreeMessagingEnabled, setIsFreeMessagingEnabled] = useState(true);
  const [preloadedContacts, setPreloadedContacts] = useState<ChatMessage[]>([]);

  // Add these state variables to your component
  const [showCreatePollModal, setShowCreatePollModal] = useState(false);
  const [pollQuestion, setPollQuestion] = useState('');
  const [pollOptions, setPollOptions] = useState(['', '']);
  const [activePoll, setActivePoll] = useState<Poll | null>(null);
  const [pollResults, setPollResults] = useState<PollResults | null>(null);

  // YouTube-style popup states
  const [showYouTubeStylePoll, setShowYouTubeStylePoll] = useState(false);
  const slideAnimation = useRef(new Animated.Value(0)).current;

  // Add this state for super chat
  const [showSuperChat, setShowSuperChat] = useState(false);
  const [superChatAmount, setSuperChatAmount] = useState(0);

  // Add this to your imports
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [plans, setPlans] = useState([]);
  // Use Reanimated Keyboard Animation hook
  const { height: keyboardHeight } = useReanimatedKeyboardAnimation();

  // Smoothly handle keyboard visibility and scrolling
  useEffect(() => {
    if (keyboardHeight.value > 0) {
      // Keyboard is visible
      setKeyboardVisible(true);

      // Smoothly scroll to the bottom after the keyboard animation
      InteractionManager.runAfterInteractions(() => {
        // Scroll to bottom logic removed
      });
    } else {
      // Keyboard is hidden
      setKeyboardVisible(false);
    }
  }, [keyboardHeight.value]);

  // Remove these demo data arrays
  // const demoMessages: ChatMessage[] = [...]
  // const demoPolls: Poll[] = [...]

  // Fetch polls for the channel
  const fetchPolls = useCallback(async () => {
    try {
      const response = await fetch(`https://api-asia-community.nityasha.com/api/channels/${consultantId}/polls`);
      if (!response.ok) throw new Error('Failed to fetch polls');
      const data = await response.json();
      setPolls(data);
    } catch (error) {
    }
  }, [consultantId]);

  // Create a new poll
  const createPoll = async (question: string, options: string[]) => {
    try {
      const response = await fetch(`https://api-asia-community.nityasha.com/api/channels/${consultantId}/polls`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          question,
          options,
          userId: consultantId // assuming this is the owner
        })
      });

      if (!response.ok) throw new Error('Failed to create poll');
      const newPoll = await response.json();
      setPolls(prev => [...prev, newPoll]);
    } catch (error) {
    }
  };
  const getImageDimensions = useCallback((imageWidth: number, imageHeight: number) => {
    const maxWidth = windowWidth * 0.93; // 93% of screen width
    const aspectRatio = imageWidth / imageHeight;
    const calculatedHeight = maxWidth / aspectRatio;
    return { width: maxWidth, height: calculatedHeight };
  }, [windowWidth]);

  // Vote on a poll
  const votePoll = async (pollId: string, optionIndex: number) => {
    try {
      const response = await fetch(`https://api-asia-community.nityasha.com/api/polls/${pollId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          optionIndex
        })
      });

      if (!response.ok) throw new Error('Failed to vote on poll');
      const updatedPoll = await response.json();
      setPolls(prev => prev.map(poll => poll._id === pollId ? updatedPoll : poll));
    } catch (error) {
    }
  };

  // Get poll results
  const getPollResults = async (pollId: string): Promise<PollResults> => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/results`);
      if (!response.ok) throw new Error('Failed to get poll results');
      return await response.json();
    } catch (error) {
      throw error;
    }
  };

  const handleAttachmentPress = async (type: string) => {
    setShowAttachmentMenu(false);

    switch (type) {
      case 'image':
        const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (permissionResult.granted === false) {
          alert("You need to enable permission to access the gallery");
          return;
        }

        const pickerResult = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          quality: 1,
        });

        if (!pickerResult.canceled) {
          // Handle the selected image
          console.log('Selected image:', pickerResult.assets[0].uri);

          // Set loading state for this image
          const tempId = `temp-${Date.now()}`;
          setLoadingImages(prev => ({ ...prev, [tempId]: true }));

          try {
            // First, you would typically upload the image to your server or a cloud storage
            // For this example, we'll assume the image is already accessible via URL
            const imageUrl = pickerResult.assets[0].uri;

            // Send the image message
            const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/messages`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                content: imageUrl,
                userId: userId,
                type: 'image'
              })
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || 'Failed to send image');
            }

            // Refresh chat history to show the new image
            fetchChatHistory();
          } catch (error) {
            console.error('Error sending image:', error);
            Alert.alert('Error', 'Failed to send image. Please try again.');
          } finally {
            setLoadingImages(prev => ({ ...prev, [tempId]: false }));
          }
        }
        break;

      case 'camera':
        const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

        if (cameraPermission.granted === false) {
          alert("You need to enable camera permission");
          return;
        }

        const cameraResult = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          quality: 1,
        });

        if (!cameraResult.canceled) {
          // Handle the captured image
          console.log(cameraResult.assets[0].uri);
          // Add your camera handling logic here
        }
        break;

      case 'video':
        // Add video picker logic
        break;

      case 'document':
        // Add document picker logic
        break;

      case 'poll':
        setShowCreatePollModal(true);
        break;
    }
  };

  // Add state for storing user info
  const [userInfoMap, setUserInfoMap] = useState<Record<string, UserInfo>>({});

  // Add loading state for user info
  const [loadingUserInfo, setLoadingUserInfo] = useState<Record<string, boolean>>({});

  // Function to fetch user info from separate API
  const fetchUserInfo = useCallback(async (userId: string) => {
    if (userInfoMap[userId] || loadingUserInfo[userId]) return; // Skip if already fetched or loading

    try {
      setLoadingUserInfo(prev => ({ ...prev, [userId]: true }));
      const response = await fetch(`https://api.nityasha.com/api/v1/users/${userId}`);
      if (!response.ok) throw new Error('Failed to fetch user info');
      const userInfo = await response.json();
      
      // Update user info with proper data structure
      const formattedUserInfo: UserInfo = {
        id: userInfo.id,
        username: userInfo.username || 'User',
        pfp: userInfo.pfp,
        email: userInfo.email,
        is_online: userInfo.is_online,
        last_seen: userInfo.last_seen,
        role: userInfo.role,
        balance: userInfo.balance
      };

      setUserInfoMap(prev => ({ ...prev, [userId]: formattedUserInfo }));
    } catch (error) {
      console.error('Error fetching user info:', error);
      // Set a default user info on error
      setUserInfoMap(prev => ({
        ...prev,
        [userId]: {
          id: parseInt(userId),
          username: 'User',
          pfp: undefined
        }
      }));
    } finally {
      setLoadingUserInfo(prev => ({ ...prev, [userId]: false }));
    }
  }, [userInfoMap, loadingUserInfo]);

  // Update useEffect to fetch user info for messages
  useEffect(() => {
    const fetchUserInfoForMessages = async () => {
      const uniqueSenders = new Set(chatHistory.map(msg => msg.senderId));
      const fetchPromises = Array.from(uniqueSenders)
        .filter((senderId): senderId is string => typeof senderId === 'string')
        .filter(senderId => !userInfoMap[senderId] && !loadingUserInfo[senderId])
        .map(senderId => fetchUserInfo(senderId));

      if (fetchPromises.length > 0) {
        await Promise.all(fetchPromises);
      }
    };

    fetchUserInfoForMessages();
  }, [chatHistory, userInfoMap, loadingUserInfo, fetchUserInfo]);

  // Add this function to load contacts from AsyncStorage
  const loadContactsFromStorage = async () => {
    try {
      const storedContacts = await AsyncStorage.getItem('chatContacts');
      if (storedContacts) {
        const parsedContacts = JSON.parse(storedContacts);
        setPreloadedContacts(parsedContacts);
        setChatHistory(parsedContacts);
        setIsLoadingHistory(false);
      }
    } catch (error) {
      console.error('Error loading contacts from storage:', error);
    }
  };

  // Modify fetchChatHistory to save contacts to AsyncStorage
  const fetchChatHistory = async () => {
    try {
      setIsLoadingHistory(true);
      console.log('Fetching chat history for consultant ID:', consultantId);

      // Calculate date 15 days ago
      const fifteenDaysAgo = new Date();
      fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);

      const response = await fetch(
        `https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/messages?fromDate=${fifteenDaysAgo.toISOString()}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(`Failed to fetch chat history: ${errorData.error || response.statusText}`);
      }

      const messages = await response.json();
      console.log(`Received ${messages.length} messages from API`);
      
      // Filter messages to ensure they're within 15 days
      const filteredMessages = messages.filter((msg: ChatMessage) => {
        const messageDate = new Date(msg.timestamp);
        return messageDate >= fifteenDaysAgo;
      });

      // Save to AsyncStorage
      await AsyncStorage.setItem('chatContacts', JSON.stringify(filteredMessages));
      
      setChatHistory(filteredMessages);
      setPreloadedContacts(filteredMessages);

    } catch (error) {
      console.error('Error fetching chat history:', error);
      // If fetch fails, try to use preloaded contacts
      if (preloadedContacts.length > 0) {
        setChatHistory(preloadedContacts);
      } else {
        setChatHistory([]);
      }
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Add useEffect to load contacts on component mount
  useEffect(() => {
    loadContactsFromStorage();
  }, []);

  // Update renderMessage to handle loading states
  const renderMessage = useCallback(({ item }: { item: ChatMessage }) => {
    const isOwnMessage = String(item.senderId) === String(userId);
    const senderInfo = userInfoMap[item.senderId];
    const isLoading = loadingUserInfo[item.senderId];

    return (
      <>
        <View
          style={[
            tw`mx-4 my-2 rounded-xl`,
            !['image', 'poll', 'superchat'].includes(item.type) &&
            (isOwnMessage
              ? tw`bg-blue-500 ml-auto`
              : tw`bg-white items-center justify-center w-full`),
            ['image', 'poll'].includes(item.type) &&
            (isOwnMessage
              ? tw`ml-auto`
              : tw`mr-auto w-full`),
            item.type === 'text' && tw`border py-1`,
            { maxWidth: '93%' }
          ]}
        >
          {/* Sender info for all messages */}
        

          {/* Message content */}
          {item.type === 'text' && (
            <Text style={[
              tw`${isOwnMessage ? 'text-white px-3' : 'text-black'} text-base`]}>
              {item.content}
            </Text>
          )}

          {item.type === 'superchat' && (
             <Text style={[
              tw`${isOwnMessage ? 'text-white px-3' : 'text-black'} text-base`,
              { fontFamily: 'Helvetica_bold' }
            ]}>
              {item.content}
            </Text>
          )}
        </View>
      </>
    );
  }, [userId, userInfoMap, loadingUserInfo]);

  const checkBalanceAndSendMessage = async () => {
    if (!messageText.trim()) return;

    // Send message directly without payment check
    sendMessage();
  };

  const sendMessage = async () => {
    if (!messageText.trim()) return;

    try {
      // Create message with proper senderId field
      const newMessage: Partial<ChatMessage> = {
        content: messageText,
        senderId: userId as string,
        type: 'text',
        _id: `temp-${Date.now()}`, // Temporary ID until server responds
        channelId: consultantId,
        timestamp: new Date().toISOString(),
      };

      // Add to local state immediately for better UX
      setChatHistory(prev => [...prev, newMessage as ChatMessage]);

      // Clear input
      setMessageText('');

      // Send to API
      const response = await fetch(`https://api-asia-community.nityasha.com/api/channels/${consultantId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: messageText,
          userId: userId,
          type: 'superchat',
        })
      });

      if (!response.ok) throw new Error('Failed to send message');

      // Scroll to bottom
      setTimeout(() => {
        // Scroll to bottom logic removed 
      }, 100);
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  };

  // Add follow/unfollow functionality
  const toggleFollow = async () => {
    try {
      const method = isFollowing ? 'DELETE' : 'POST';
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/follow/${consultantId}`, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          followerId: userId
        })
      });

      if (!response.ok) throw new Error('Failed to update follow status');

      setIsFollowing(!isFollowing);
      setFollowersCount(prev => isFollowing ? prev - 1 : prev + 1);
    } catch (error) {
      Alert.alert('Error', 'Failed to update follow status');
    }
  };

  // Check channel access
  const checkChannelAccess = async () => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}`);
      const channel = await response.json();

      setIsPaidChannel(channel.isPaid);
      setChannelPrice(channel.price);

      if (!channel.isPaid) {
        setHasAccess(true);
        return;
      }

      // Check if user has paid for access
      const accessResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/access/${userId}`);
      const accessData = await accessResponse.json();
      setHasAccess(accessData.hasAccess);
    } catch (error) {
      console.error('Error checking channel access:', error);
    }
  };

  // Add useEffects
  useEffect(() => {
    const checkFollowStatus = async () => {
      try {
        const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/follow/${consultantId}/${userId}`);
        const data = await response.json();
        setIsFollowing(data.isFollowing);
      } catch (error) {
        console.error('Error checking follow status:', error);
      }
    };

    const getFollowersCount = async () => {
      try {
        const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/followers/${consultantId}`);
        const followers = await response.json();
        setFollowersCount(followers.length);
      } catch (error) {
        console.error('Error getting followers count:', error);
      }
    };

    checkFollowStatus();
    getFollowersCount();
    checkChannelAccess();
  }, [consultantId, userId]);

  useEffect(() => {
    fetchPolls();
  }, [fetchPolls]);

  useEffect(() => {
    fetchChatHistory();
  }, [consultantId]);

  // Update the useEffect to load demo data
  useEffect(() => {
    // Get user ID from AsyncStorage
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          setuserId(parsedSession?.userId || userSession);
        }
      } catch (error) {
        console.error('Error getting user session:', error);
      }
    };

    getUserId();
  }, []);

  // Add this function to create a new poll
  const handleCreatePoll = async () => {
    if (!pollQuestion.trim()) {
      Alert.alert('Error', 'Please enter a poll question');
      return; 
    }

    // Filter out empty options
    const validOptions = pollOptions.filter(option => option.trim() !== '');

    if (validOptions.length < 2) {
      Alert.alert('Error', 'Please enter at least 2 options');
      return;
    }

    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/polls`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          question: pollQuestion,
          options: validOptions,
          userId: userId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create poll');
      }

      const newPoll = await response.json();
      setPolls(prev => [newPoll, ...prev]);

      // Reset form and close modal
      setPollQuestion('');
      setPollOptions(['', '']);
      setShowCreatePollModal(false);

      // Refresh chat history to show the new poll
      fetchChatHistory();

      Alert.alert('Success', 'Poll created successfully');
    } catch (error) {
      console.error('Error creating poll:', error);
      Alert.alert('Error', 'Failed to create poll. Please try again.');
    }
  };

  // Add this function to add a poll option
  const addPollOption = () => {
    setPollOptions([...pollOptions, '']);
  };

  // Add this function to remove a poll option
  const removePollOption = (index: number) => {
    if (pollOptions.length <= 2) {
      Alert.alert('Error', 'A poll must have at least 2 options');
      return;
    }

    const newOptions = [...pollOptions];
    newOptions.splice(index, 1);
    setPollOptions(newOptions);
  };

  // Add this function to handle poll option changes
  const handlePollOptionChange = (text: string, index: number) => {
    const newOptions = [...pollOptions];
    newOptions[index] = text;
    setPollOptions(newOptions);
  };



  // Function to close the YouTube-style popup
  const closeYouTubeStylePoll = () => {
    // Animate the popup sliding down
    Animated.timing(slideAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      // After animation completes, hide the popup
      setShowYouTubeStylePoll(false);
    });
  };

  // Add this function to vote on a poll
  const handleVoteOnPoll = async (pollId: string, optionIndex: number) => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: userId,
          optionIndex: optionIndex
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to vote on poll');
      }

      // Update only this poll's results
      const resultsResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/results`);
      if (!resultsResponse.ok) {
        throw new Error('Failed to fetch updated poll results');
      }
      const results = await resultsResponse.json();
      setPollResults(results);
      setSelectedPollOptions(prev => ({ ...prev, [pollId]: optionIndex }));
    } catch (error) {
      console.error('Error voting on poll:', error);
      Alert.alert('Error', 'Failed to vote on poll. Please try again.');
    }
  };

  const handleSendWithPayment = async (amount: number) => {
    if (!messageText.trim()) {
      Alert.alert('Error', 'Please enter a message');
      return;
    }

    try {
      const userDetailsStr = await AsyncStorage.getItem('userDetails');
      const userSessionStr = await AsyncStorage.getItem('userSession');

      if (!userSessionStr) {
        Alert.alert('Error', 'You must be logged in to send a Super Chat.');
        return;
      }

      const userDetails = userDetailsStr ? JSON.parse(userDetailsStr) : {};
      const parsedSession = JSON.parse(userSessionStr);

      // Create order only if not cancelled
      const orderResponse = await fetch('https://api.nityasha.com/api/v1/paymemt/2');
      const orderData = await orderResponse.json();

      if (!orderData || !orderData.api) {
        Alert.alert('Service Unavailable', 'Payment service is temporarily unavailable. Please try again later.');
        return;
      }

      const options = {
        description: 'Super Chat Message',
        image: 'https://nityasha.com/logo-dark.svg',
        currency: 'INR',
        key: orderData.api,
        amount: amount * 100,
        name: 'Nityasha',
        order_id: orderData.order_id,
        prefill: {
          contact: userDetails.email || '',
          name: userDetails.name || parsedSession.username || 'User',
        },
        theme: { color: '#000000' },
      };

      try {
        const data = await RazorpayCheckout.open(options);

        // Payment successful
        if (data.razorpay_payment_id) {
          // Send message with payment details
          const messageResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/messages`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              content: messageText,
              userId: parsedSession.userId,
              type: 'superchat',
              amount: amount
            }),
          });

          if (!messageResponse.ok) {
            throw new Error('Failed to send message');
          }

          setMessageText('');
          setShowSuperChat(false);
          fetchChatHistory();
        }
      } catch (error: any) {
        // Handle specific payment errors
        if (error.code === 'PAYMENT_CANCELLED') {
          Alert.alert('Payment Cancelled', 'Transaction was cancelled.');
          setShowSuperChat(false); // Close the superchat panel
          return; // Exit the function
        }
        throw error; // Re-throw other errors
      }
    } catch (error: any) {
      console.error('Payment Error:', error);
      if (error.code === 'USER_CANCELLED') {
        Alert.alert('Payment Cancelled', 'You cancelled the payment.');
        setShowSuperChat(false); // Close the superchat panel
      } else {
        Alert.alert('Error', error.message || 'Something went wrong. Please try again.');
      }
    }
  };

  // Update the Super Chat Panel to handle cancellation
  const handleSuperChatClose = () => {
    setShowSuperChat(false);
    setMessageText(''); // Clear message text when closing
  };

  // FlatList ref for scrolling to bottom
  const flatListRef = useRef<any>(null);

  // Function to scroll to bottom
  const scrollToBottom = useCallback(() => {
    if (flatListRef.current && chatHistory.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [chatHistory.length]);

  // Scroll to bottom when chatHistory changes
  useEffect(() => {
    if (chatHistory.length > 0) {
      setTimeout(() => {
        scrollToBottom();
      }, 300);
    }
  }, [chatHistory, scrollToBottom]);

  // Add this useEffect to ensure it runs properly
  useEffect(() => {
    if (chatHistory.length > 0) {
      // Delay scrolling slightly to ensure layout is complete
      setTimeout(() => {
        scrollToBottom();
      }, 1);
    }
  }, [chatHistory]);

  // Add this to ensure scrolling when keyboard appears
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        // Scroll to bottom when keyboard appears
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  // Enhance keyboard handling
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        // Multiple scroll attempts when keyboard appears
        setTimeout(() => {
          scrollToBottom();
        }, 100);

        setTimeout(() => {
          scrollToBottom();
        }, 100);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  const handleSuperChatSelect = (amount: number) => {
    setSuperChatAmount(amount);
    setShowSuperChat(false); // Close the Super Chat panel after selection
  };

  const [shouldScrollOnLoad, setShouldScrollOnLoad] = useState(true);

  // Add renderContent function
  const renderContent = () => {
    if (isLoadingHistory && preloadedContacts.length === 0) {
      return (
        <View style={tw`flex-1 justify-center items-center`}> 
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={tw`mt-2 text-gray-500`}>Loading messages...</Text>
        </View>
      );
    }

    return (
      <FlatList
        ref={flatListRef}
        data={chatHistory}
        renderItem={renderMessage}
        keyExtractor={(item, index) => item._id || `message-${index}`}
        contentContainerStyle={tw`pb-20 pt-2`}
        ListEmptyComponent={
          <View style={tw`flex-1 justify-center items-center p-4`}>
            <Text style={tw`text-gray-500 text-center`}>No messages yet. Start the conversation!</Text>
          </View>
        }
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={7}
        removeClippedSubviews={true}
        getItemLayout={(data, index) => ({
          length: 90,
          offset: 90 * index,
          index,
        })}
        onContentSizeChange={() => {
          if (shouldScrollOnLoad) {
            scrollToBottom();
            setShouldScrollOnLoad(false);
          }
        }}
        onLayout={() => {
          if (shouldScrollOnLoad) {
            setTimeout(() => {
              scrollToBottom();
              setShouldScrollOnLoad(false);
            }, 0);
          }
        }}
      />
    );
  };

  // Reply to a message
  const replyToMessage = async (messageId: string, content: string) => {
    try {
      const response = await fetch(`https://api-asia-community.nityasha.com/api/channels/${consultantId}/messages/${messageId}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content,
          userId
        })
      });
  
      if (!response.ok) throw new Error('Failed to reply to message');
      const reply = await response.json();
      setChatHistory(prev => [...prev, reply]);
    } catch (error) {
    }
  };
  useEffect(() => {
    fetch('https://api.php.nityasha.com/superchat.json')
      .then(res => res.json())
      .then(data => setPlans(data.plans));
  }, []);
  // Edit a message
  const editMessage = async (messageId: string, content: string) => {
    try {
      const response = await fetch(`https://api-asia-community.nityasha.com/api/messages/${messageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content,
          userId
        })
      });

      if (!response.ok) throw new Error('Failed to edit message');
      const updatedMessage = await response.json();
      setChatHistory(prev => prev.map(msg => msg._id === messageId ? updatedMessage : msg));
    } catch (error) {
    }
  };

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 bg-white`}>
      {/* Header */}
      <View style={tw`h-[54px] border-b border-[#E4E4E4] flex-row justify-between items-center px-3`}>
        <View style={tw`flex-row gap-3 items-center`}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <ArrowLeft size={24} color="#000" />
          </TouchableOpacity>
          <View style={tw`w-[40px] h-[40px] rounded-[14.5px] overflow-hidden`}>
            <MotiImage style={tw`w-full h-full`} source={{ uri: pfp }} />
          </View>
          <View>
            <Text style={[tw``, { fontFamily: 'Helvetica_bold' }]}>{username}</Text>
            <Text style={[tw`text-xs`, { fontFamily: 'Helvetica_bold' }]}>{active === 1 ? 'Online' : 'Offline'}</Text>
          </View>
        </View>
        <View style={tw`flex-row items-center gap-2`}>
          <TouchableOpacity style={tw`border-2 px-4 py-1 rounded-full`}>
            <Text style={[tw`font-bold`, { fontFamily: 'Helvetica_bold' }]}>Join</Text>
          </TouchableOpacity>
          <TouchableOpacity>
            <EllipsisVertical size={24} color="#000" />
          </TouchableOpacity>
        </View>
      </View>

      {/* KeyboardAvoidingView wraps the main content */}
      <KeyboardAvoidingView style={tw`flex-1`}>
        {/* Message List */}
        {renderContent()}

        {/* Input field - only shown when user is online */}
        <View style={[
          tw`absolute bottom-0 left-0 right-0 px-2 py-2 bg-white`,
        ]}>


          {/* Message Input */}
          <View style={tw`flex-row items-center justify-between`}>
            <View style={tw`bg-gray-100 w-[88%] rounded-full flex-row items-center py-1.5 px-3`}>
              <TextInput
                placeholder="Type your message..."
                style={[
                  tw`flex-1 rounded-full px-4 py-2`,
                  { fontFamily: 'Helvetica' }
                ]}
                multiline
                value={messageText}
                onChangeText={setMessageText}
                onFocus={() => {
                  // Delay scrolling to ensure keyboard is fully shown
                  setTimeout(scrollToBottom, 100);
                }}
                onContentSizeChange={scrollToBottom} // Add this to handle multiline input
              />
            </View>
            <TouchableOpacity
              style={tw`w-12 h-12 bg-blue-500 rounded-full items-center justify-center`}
              onPress={() => setShowSuperChat(true)}
            >
              <Send size={24} color="#FFF" />
            </TouchableOpacity>
          </View>
        </View>


        {/* Super Chat Panel */}
        {showSuperChat && (
          <MotiView
            from={{ translateY: 300 }}
            animate={{ translateY: 0 }}
            transition={{ type: 'spring', damping: 15 }}
            style={tw`absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-lg p-4 z-50`}
          >
            <View style={tw`flex-row justify-between items-center mb-4`}>
              <Text style={[tw`text-xl font-bold`, { fontFamily: 'Helvetica_bold' }]}>Super Chat</Text>
              <TouchableOpacity onPress={handleSuperChatClose}>
                <X size={24} color="#000" />
              </TouchableOpacity>
            </View>

            <Text style={[tw`mb-4`, { fontFamily: 'Helvetica' }]}>
              Highlight your message with a Super Chat:
            </Text>

            <View style={tw`flex-row flex-wrap justify-between mb-4`}>
            {plans.map((plan) => (
          <TouchableOpacity
            key={plan.amount}
            onPress={() => handleSendWithPayment(plan.amount)}
            style={[
              tw`w-[30%] h-20 mb-4 rounded-xl items-center justify-center shadow-sm`,
              tw`bg-${plan.color}`
            ]}
          >
            <Text style={[tw`text-lg font-bold`, { fontFamily: 'Helvetica_bold' }]}>
              ₹{plan.amount}
            </Text>
            <Text style={tw`text-xs text-gray-500`}>{plan.label}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[tw`text-xs text-gray-500 text-center mb-2`, { fontFamily: 'Helvetica' }]}>
        Your message will be highlighted and more visible to everyone
      </Text>
          
          </MotiView>
        )}
      </KeyboardAvoidingView>
    </ImageBackground>
  );
}
