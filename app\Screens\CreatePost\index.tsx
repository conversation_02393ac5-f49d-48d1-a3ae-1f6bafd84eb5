import { View, Text, Image, TouchableOpacity, ImageBackground, SafeAreaView, Keyboard } from 'react-native';
import React, { useState, useEffect, useRef } from 'react';
import tw from 'twrnc';
import { TextInput } from 'react-native-paper';
import { useNavigation, NavigationProp, ParamListBase } from '@react-navigation/native';
import { MotiView } from 'moti';
import Animated, { useSharedValue, useAnimatedStyle, withSpring, withTiming } from 'react-native-reanimated';
import axios from 'axios';
import AsyncStorage from "@react-native-async-storage/async-storage";

const MAX_CHARS = 100; // X.com's character limit

// Define types for route params
interface RouteParams {
  communityId: number | string;
}

export default function CreatePost({ route }: { route: { params: RouteParams } }) {
  console.log('CreatePost component mounted');

  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const [post, setPost] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [userId, setUserId] = useState<string | number | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const remainingChars = MAX_CHARS - post.length;
  const { communityId } = route.params;
  const textInputRef = useRef<any>(null);

  console.log('Initial State:', { post, loading, userId, errorMessage, remainingChars, communityId });

  // Animation values
  const fadeIn = useSharedValue(0);
  const scale = useSharedValue(0.95);
  const buttonScale = useSharedValue(1);

  // Initialize animations when component mounts
  useEffect(() => {
    console.log('Running initial animations');
    fadeIn.value = withTiming(1, { duration: 400 });
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
    textInputRef.current?.focus();
  }, []);

  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        if (navigation.isFocused()) {
          navigation.goBack();
        }
      }
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, [navigation]);

  useEffect(() => {
    console.log('Fetching user ID from AsyncStorage');
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        console.log('User Session:', userSession);
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          console.log('Parsed User Session:', parsedSession);
          setUserId(parsedSession?.userId || userSession);
        }
      } catch (error) {
        console.error("Error getting user session:", error);
      }
    };
    getUserId();
  }, []);

  // Animated styles
  const contentAnimatedStyle = useAnimatedStyle(() => {
    console.log('Applying content animated style');
    return {
      opacity: fadeIn.value,
      transform: [{ scale: scale.value }],
    };
  });

  const buttonAnimatedStyle = useAnimatedStyle(() => {
    console.log('Applying button animated style');
    return {
      transform: [{ scale: buttonScale.value }],
    };
  });

  // Handle post submission
  const handlePostSubmit = async () => {
    console.log('Submitting post:', { post, communityId, userId });
    if (!post.trim()) return;
    setLoading(true);
    setErrorMessage('');

    try {
      const response = await axios.post('https://posts-api.nityasha.com/api/posts', {
        channel_id: communityId,
        user_id: userId,
        content: post.trim(),
      });
      console.log('Post submitted successfully:', response.data);
      setPost('');
      navigation.goBack();
    } catch (error: any) {
      console.error('Error creating post:', error);
      setErrorMessage(
        error.response?.data?.message || 'Failed to create post. Please try again.'
      );
    } finally {
      console.log('Post submission complete');
      setLoading(false);
    }
  };

  // Handle text change with button scale animation
  const handlePostChange = (text: string) => {
    console.log('Post content changed:', text);
    if (text.length <= MAX_CHARS) {
      setPost(text);
      if (text.trim() && !post.trim()) {
        console.log('Animating button on first text entry');
        buttonScale.value = withSpring(1.05, { damping: 10 }, () => {
          buttonScale.value = withSpring(1);
        });
      }
    }
  };

  return (
    <SafeAreaView style={tw`flex-1`}>
      <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 px-4 pt-2`} resizeMode="cover">
        <View style={tw`flex-row justify-between items-center mb-2`}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={[tw`text-black text-base`, { fontFamily: 'Helvetica_bold' }]}>Cancel</Text>
          </TouchableOpacity>
          <Animated.View style={buttonAnimatedStyle}>
            <TouchableOpacity onPress={handlePostSubmit} disabled={!post.trim() || loading} style={[tw`rounded-full py-1.5 px-4`, { backgroundColor: post.trim() ? '#1D9BF0' : '#8ECDF8' }]}>              <Text style={[tw`text-base`, { color: 'white', fontFamily: 'Helvetica_bold' }]}>{loading ? 'Posting...' : 'Post'}</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>

        {errorMessage ? (
          <Text style={[tw`text-red-500 text-sm mb-2`, { fontFamily: 'Helvetica_bold' }]}>{errorMessage}</Text>
        ) : null}

        <View style={tw`h-[0.5px] bg-gray-200 mb-3`} />

        <Animated.View style={[tw`flex-row items-start`, contentAnimatedStyle]}>
          <MotiView from={{ opacity: 0, scale: 0.8 }} animate={{ opacity: 1, scale: 1 }} transition={{ type: 'timing', duration: 500, delay: 100 }}>
            <Image source={{ uri: 'https://ui-avatars.com/api/?name=Anonymous?format=png' }} style={tw`w-10 h-10 rounded-full mr-3`} />
          </MotiView>
          <View style={tw`flex-1`}>
            <TextInput ref={textInputRef} value={post} onChangeText={handlePostChange} placeholder="What's happening?" multiline style={tw`flex-1 text-base bg-transparent p-0 m-0 min-h-[400px]`} underlineColor="transparent" activeUnderlineColor="transparent" theme={{ colors: { text: '#000', placeholder: 'gray' } }} maxLength={MAX_CHARS} />
          </View>
        </Animated.View>
      </ImageBackground>
    </SafeAreaView>
  );
}
