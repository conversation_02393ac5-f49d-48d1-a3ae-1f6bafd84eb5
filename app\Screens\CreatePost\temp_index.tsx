import { View, Text, Image, TouchableOpacity, ImageBackground, SafeAreaView, Platform } from 'react-native';
import React, { useState } from 'react';
import tw from 'twrnc';
import { TextInput } from 'react-native-paper';
import { Feather } from '@expo/vector-icons';

const MAX_CHARS = 280; // X.com's character limit

export default function CreatePost() {
  const [post, setPost] = useState('');
  const remainingChars = MAX_CHARS - post.length;
  
  const handlePostChange = (text) => {
    if (text.length <= MAX_CHARS) {
      setPost(text);
    }
  };

  return (
    <SafeAreaView style={tw`flex-1`}>
      <ImageBackground
        source={require('@/assets/screens/screen7th.png')}
        style={tw`flex-1 px-4 pt-2`}
        resizeMode="cover"
      >
        {/* Header */}
        <View style={tw`flex-row justify-between items-center mb-2`}>
          <TouchableOpacity>
            <Text style={[tw`text-black text-base`, { fontFamily: 'Helvetica_bold' }]}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            disabled={!post.trim()}
            style={[
              tw`rounded-full py-1.5 px-4`,
              { backgroundColor: post.trim() ? '#1D9BF0' : '#8ECDF8' }
            ]}
          >
            <Text
              style={[
                tw`text-base`,
                { color: 'white', fontFamily: 'Helvetica_bold' },
              ]}
            >
              Post
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Divider */}
        <View style={tw`h-[0.5px] bg-gray-200 mb-3`} />

        {/* Avatar + Input */}
        <View style={tw`flex-row items-start`}>
          <Image
            source={{ uri: 'https://ui-avatars.com/api/?name=Anonymous' }}
            style={tw`w-10 h-10 rounded-full mr-3`}
          />
          <View style={tw`flex-1`}>
            <TextInput
              value={post}
              onChangeText={handlePostChange}
              placeholder="What's happening?"
              multiline
              style={tw`flex-1 text-base bg-transparent p-0 m-0 min-h-[100px]`}
              underlineColor="transparent"
              activeUnderlineColor="transparent"
              theme={{ colors: { text: '#000', placeholder: 'gray' } }}
              maxLength={MAX_CHARS}
            />
            
            {/* Attachment options */}
            <View style={tw`flex-row mt-4 border-t border-gray-200 pt-3`}>
              <TouchableOpacity style={tw`mr-6`}>
                <Feather name="image" size={20} color="#1D9BF0" />
              </TouchableOpacity>
              <TouchableOpacity style={tw`mr-6`}>
                <Feather name="bar-chart-2" size={20} color="#1D9BF0" />
              </TouchableOpacity>
              <TouchableOpacity style={tw`mr-6`}>
                <Feather name="smile" size={20} color="#1D9BF0" />
              </TouchableOpacity>
              <TouchableOpacity style={tw`mr-6`}>
                <Feather name="calendar" size={20} color="#1D9BF0" />
              </TouchableOpacity>
              <TouchableOpacity style={tw`mr-6`}>
                <Feather name="map-pin" size={20} color="#1D9BF0" />
              </TouchableOpacity>
            </View>
            
            {/* Character count */}
            <View style={tw`flex-row justify-end items-center mt-3`}>
              {remainingChars <= 20 && (
                <Text 
                  style={[
                    tw`text-sm mr-2`, 
                    { color: remainingChars <= 0 ? 'red' : '#1D9BF0' }
                  ]}
                >
                  {remainingChars}
                </Text>
              )}
              <View 
                style={[
                  tw`w-6 h-6 rounded-full border-2 justify-center items-center`,
                  { 
                    borderColor: remainingChars <= 20 
                      ? (remainingChars <= 0 ? 'red' : '#1D9BF0') 
                      : 'transparent' 
                  }
                ]}
              >
                {remainingChars <= 20 && (
                  <View 
                    style={[
                      tw`bg-${remainingChars <= 0 ? 'red' : '[#1D9BF0]'} rounded-full`,
                      { 
                        width: `${Math.min(100, (MAX_CHARS - remainingChars) / MAX_CHARS * 100)}%`,
                        height: `${Math.min(100, (MAX_CHARS - remainingChars) / MAX_CHARS * 100)}%`
                      }
                    ]}
                  />
                )}
              </View>
            </View>
          </View>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}
