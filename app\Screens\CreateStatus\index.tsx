import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  TextInput
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';

export default function CreateStatus() {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<string | null>(null);
  const [body, setBody] = useState('');

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [9, 16],
        quality: 0.8,
      });

      console.log('Image picker result:', result); // Add this log

      if (!result.canceled && result.assets && result.assets.length > 0) {
        console.log('Selected media URI:', result.assets[0].uri); // Add this log
        setSelectedMedia(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const takePhoto = async () => {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

      if (cameraPermission.status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [9, 16],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const uploadStatus = async () => {
    if (!selectedMedia) {
      Alert.alert('Error', 'Please select a media file first.');
      return;
    }

    setLoading(true);

    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) {
        Alert.alert('Error', 'You must be logged in to upload a status.');
        setLoading(false);
        return;
      }

      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession.userId;

      const formData = new FormData();

      const isVideo = selectedMedia.endsWith('.mp4') || selectedMedia.endsWith('.mov');

      formData.append('media', {
        uri: selectedMedia,
        type: isVideo ? 'video/mp4' : 'image/jpeg',
        name: `status_${Date.now()}.${isVideo ? 'mp4' : 'jpg'}`,
      } as any);

      formData.append('userId', userId);
      formData.append('body', body);

      const response = await fetch('https://status.api.nityasha.com/upload-status', {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to upload status');
      }

      Alert.alert('Success', 'Your status has been uploaded!');
      navigation.goBack();
    } catch (error) {
      console.error('Error uploading status:', error);
      Alert.alert('Error', 'Failed to upload status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={tw`flex-1 bg-black`}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      {/* Header */}
      <View style={tw`flex-row items-center justify-between p-4`}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="close" size={28} color="white" />
        </TouchableOpacity>
        <Text style={tw`text-white font-bold text-lg`}>Create Status</Text>
        <View style={tw`w-7`} />
      </View>

      {/* Preview Area */}
      <View style={tw`flex-1 justify-center items-center p-4`}>
        {selectedMedia ? (
          <Image
            source={{ uri: selectedMedia }}
            style={tw`w-full h-full rounded-lg`}
            resizeMode="contain"
            onError={(error) => console.error('Image loading error:', error)}
          />
        ) : (
          <View style={tw`items-center`}>
            <Ionicons name="images-outline" size={80} color="#555" />
            <Text style={tw`text-white text-lg mt-4`}>Select an image or video</Text>
          </View>
        )}
      </View>

      {/* Caption Input */}
      {selectedMedia && (
        <View style={tw`px-4 mb-2`}>
          <TextInput
            style={tw`bg-gray-800 text-white p-3 rounded-lg`}
            placeholder="Add a caption..."
            placeholderTextColor="#666"
            value={body}
            onChangeText={setBody}
            multiline
          />
        </View>
      )}

      {/* Action Buttons */}
      <View style={tw`p-4`}>
        {selectedMedia ? (
          <TouchableOpacity
            style={tw`bg-green-600 py-3 rounded-full items-center`}
            onPress={uploadStatus}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={tw`text-white font-bold text-lg`}>Upload Status</Text>
            )}
          </TouchableOpacity>
        ) : (
          <View style={tw`flex-row justify-center space-x-4`}>
            <TouchableOpacity
              style={tw`bg-gray-700 p-4 rounded-full items-center justify-center`}
              onPress={pickImage}
            >
              <Ionicons name="images" size={28} color="white" />
              <Text style={tw`text-white mt-1`}>Gallery</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={tw`bg-gray-700 p-4 rounded-full items-center justify-center`}
              onPress={takePhoto}
            >
              <Ionicons name="camera" size={28} color="white" />
              <Text style={tw`text-white mt-1`}>Camera</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}





