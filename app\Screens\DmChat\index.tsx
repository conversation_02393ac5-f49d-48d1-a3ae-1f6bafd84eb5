"use client"

import React, { useEffect, useRef, useState } from "react"
import {
  View,
  TextInput,
  FlatList,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  Dimensions,
  Animated,
  Pressable,
} from "react-native"
import { db } from "@/lib/Firebase"
import { ref, onValue, set, serverTimestamp, update } from "firebase/database"
import {
  Send,
  Paperclip,
  Mic,
  ArrowLeft,
  MoreVertical,
  Check,
  CheckCheck,
  Play,
  Pause,
  Image as ImageIcon,
} from "lucide-react-native"
import { useNavigation, useRoute } from "@react-navigation/native"
import { SafeAreaView } from "react-native-safe-area-context"
import * as ImagePicker from "expo-image-picker"
import { supabase } from "@/utils/supabase"
import { Audio } from "expo-av"
import { theme } from "@/app/theme"
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Message {
  id: number
  text: string
  senderId: string
  receiverId: string
  timestamp: number
  status: "sent" | "delivered" | "read"
  type: "text" | "image" | "audio"
  mediaUrl?: string
}

interface ChatParams {
  otherUserId: string
  otherUserName: string
  otherUserAvatar: string
}

interface AudioRecording {
  recording: Audio.Recording | null
  sound: Audio.Sound | null
}

const { width } = Dimensions.get("window")

export default function DmChat() {
  const navigation = useNavigation()
  const route = useRoute()
  const { otherUserId, otherUserName, otherUserAvatar } = route.params as ChatParams

  const [messages, setMessages] = useState<Message[]>([])
  const [messageText, setMessageText] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isOtherUserOnline, setIsOtherUserOnline] = useState(false)
  const [isOtherUserTyping, setIsOtherUserTyping] = useState(false)
  const [lastSeen, setLastSeen] = useState<number | null>(null)
  const [isRecording, setIsRecording] = useState(false)
  const [recordingDuration, setRecordingDuration] = useState(0)
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false)
  const [recipientPushToken, setRecipientPushToken] = useState(null)

  const flatListRef = useRef<FlatList>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()
  const recordingTimerRef = useRef<NodeJS.Timeout>()
  const pulseAnim = useRef(new Animated.Value(1)).current
  const userId = "currentUserId" // Replace with actual user ID from auth
  const recordingRef = useRef<Audio.Recording | null>(null)
  const inputRef = useRef<TextInput>(null)

  // Animation for recording pulse
  useEffect(() => {
    if (isRecording) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ]),
      ).start()
    } else {
      pulseAnim.setValue(1)
    }
  }, [isRecording, pulseAnim])

  // Listen for messages and user status
  useEffect(() => {
    const chatId = [userId, otherUserId].sort().join("_")
    const messagesRef = ref(db, `dm_messages/${chatId}`)
    const userStatusRef = ref(db, `users/${otherUserId}/status`)

    // Listen for messages
    const messageUnsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val()
      if (data) {
        const messageArray = Object.values(data) as Message[]
        // Sort messages by timestamp in ascending order (oldest to newest)
        setMessages(messageArray.sort((a, b) => a.timestamp - b.timestamp))

        // Mark messages as read
        messageArray
          .filter((msg) => msg.senderId === otherUserId && msg.status !== "read")
          .forEach((msg) => {
            update(ref(db, `dm_messages/${chatId}/${msg.id}`), {
              status: "read",
            })
          })
      }
      setIsLoading(false)
    })

    // Listen for user status
    const statusUnsubscribe = onValue(userStatusRef, (snapshot) => {
      const status = snapshot.val()
      if (status) {
        setIsOtherUserOnline(status.state === "online")
        setLastSeen(status.lastSeen)
        // Only show typing indicator if they're typing to this user
        setIsOtherUserTyping(status.isTyping && status.typingInChatWith === userId)
      }
    })

    return () => {
      messageUnsubscribe()
      statusUnsubscribe()
    }
  }, [userId, otherUserId])

  // Add this effect to scroll to bottom on initial load
  useEffect(() => {
    if (!isLoading && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 100);
    }
  }, [isLoading]);

  // Update user typing status
  const updateTypingStatus = (isTyping: boolean) => {
    const userStatusRef = ref(db, `users/${userId}/status`)
    update(userStatusRef, {
      isTyping,
      timestamp: serverTimestamp(),
      typingInChatWith: isTyping ? otherUserId : null // Add the recipient ID
    })
  }

  const handleTextChange = (text: string) => {
    setMessageText(text)

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    updateTypingStatus(true)
    typingTimeoutRef.current = setTimeout(() => {
      updateTypingStatus(false)
    }, 1500)
  }

  const sendPushNotification = async (messageText: string) => {
    if (!recipientPushToken) return;

    try {
      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: recipientPushToken,
          sound: 'default',
          title: 'New Message',
          body: messageText,
          data: { screen: 'DmChat', senderId: userId },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send push notification');
      }
    } catch (error) {
      console.error('Error sending push notification:', error);
    }
  };

  const sendMessage = async () => {
    if (messageText.trim()) {
      const chatId = [userId, otherUserId].sort().join("_")
      const newMessage: Message = {
        id: Date.now(),
        text: messageText.trim(),
        senderId: userId,
        receiverId: otherUserId,
        timestamp: Date.now(),
        status: "sent",
        type: "text",
      }

      set(ref(db, `dm_messages/${chatId}/${newMessage.id}`), newMessage)
      setMessageText("")
      updateTypingStatus(false)

      // Send push notification
      await sendPushNotification(messageText);

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true })
      }, 100)
    }
  }

  const pickImage = async () => {
    setShowAttachmentOptions(false)
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()

    if (!permissionResult.granted) {
      alert("Permission to access gallery is required!")
      return
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 1,
    })

    if (!result.canceled) {
      await uploadAndSendImage(result.assets[0].uri)
    }
  }

  const uploadAndSendImage = async (uri: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(uri)
      const blob = await response.blob()

      const fileExt = uri.split(".").pop()
      const fileName = `${Date.now()}.${fileExt}`
      const filePath = `chat-images/${fileName}`

      const { data, error } = await supabase.storage.from("chat-media").upload(filePath, blob, {
        contentType: `image/${fileExt}`,
      })

      if (error) throw error

      const { data: publicUrl } = supabase.storage.from("chat-media").getPublicUrl(filePath)

      const chatId = [userId, otherUserId].sort().join("_")
      const newMessage: Message = {
        id: Date.now(),
        text: "",
        senderId: userId,
        receiverId: otherUserId,
        timestamp: Date.now(),
        status: "sent",
        type: "image",
        mediaUrl: publicUrl.publicUrl,
      }

      await set(ref(db, `dm_messages/${chatId}/${newMessage.id}`), newMessage)

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true })
      }, 100)
    } catch (error) {
      console.error("Error uploading image:", error)
      alert("Failed to send image")
    } finally {
      setIsLoading(false)
    }
  }

  // Audio recording functions
  const [isPlayingAudioMap, setIsPlayingAudioMap] = useState<{ [key: string]: boolean }>({})
  const [audioDurationMap, setAudioDurationMap] = useState<{ [key: string]: number }>({})
  const [audioProgressMap, setAudioProgressMap] = useState<{ [key: string]: number }>({})
  const soundRefs = useRef<{ [key: string]: Audio.Sound }>({})

  const loadAudio = async (messageId: number, audioUrl: string) => {
    try {
      const soundObject = new Audio.Sound()
      await soundObject.loadAsync({ uri: audioUrl })

      // Get duration
      const status = await soundObject.getStatusAsync()
      if (status.isLoaded) {
        setAudioDurationMap((prev) => ({
          ...prev,
          [messageId]: status.durationMillis ? Math.floor(status.durationMillis / 1000) : 0,
        }))
      }

      soundRefs.current[messageId] = soundObject
      return soundObject
    } catch (error) {
      console.error("Error loading audio:", error)
      return null
    }
  }

  const handleAudioPress = async (messageId: number, audioUrl: string) => {
    if (isPlayingAudioMap[messageId]) {
      if (soundRefs.current[messageId]) {
        await soundRefs.current[messageId].stopAsync()
        await soundRefs.current[messageId].unloadAsync()
        delete soundRefs.current[messageId]
      }
      setIsPlayingAudioMap((prev) => ({ ...prev, [messageId]: false }))
      setAudioProgressMap((prev) => ({ ...prev, [messageId]: 0 }))
      return
    }

    setIsPlayingAudioMap((prev) => ({ ...prev, [messageId]: true }))
    try {
      const sound = await loadAudio(messageId, audioUrl)
      if (!sound) throw new Error("Failed to load audio")

      sound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded) {
          if (status.didJustFinish) {
            setIsPlayingAudioMap((prev) => ({ ...prev, [messageId]: false }))
            setAudioProgressMap((prev) => ({ ...prev, [messageId]: 0 }))
          } else if (status.isPlaying) {
            const progress = status.positionMillis / status.durationMillis
            setAudioProgressMap((prev) => ({ ...prev, [messageId]: progress }))
          }
        }
      })

      await sound.playAsync()
    } catch (error) {
      console.error("Error playing audio:", error)
      alert("Failed to play audio message")
      setIsPlayingAudioMap((prev) => ({ ...prev, [messageId]: false }))
    }
  }

  useEffect(() => {
    return () => {
      for (const key in soundRefs.current) {
        soundRefs.current[key].unloadAsync()
        delete soundRefs.current[key]
      }
    }
  }, [])

  const startRecording = async () => {
    try {
      // First, unload any existing recording
      if (recordingRef.current) {
        await recordingRef.current.stopAndUnloadAsync()
        recordingRef.current = null
      }

      await Audio.requestPermissionsAsync()
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        playThroughEarpieceAndroid: false,
        interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
        interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
      })

      const { recording } = await Audio.Recording.createAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY)

      recordingRef.current = recording
      setIsRecording(true)
      setRecordingDuration(0)

      // Start timer for recording duration
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration((prev) => prev + 1)
      }, 1000)
    } catch (err) {
      console.error("Failed to start recording", err)
      alert("Failed to start recording")
    }
  }

  const stopRecording = async () => {
    try {
      if (!recordingRef.current) return

      setIsRecording(false)
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
      }

      await recordingRef.current.stopAndUnloadAsync()
      const uri = recordingRef.current.getURI()

      if (uri) {
        await uploadAndSendAudio(uri)
      }

      recordingRef.current = null
    } catch (err) {
      console.error("Failed to stop recording", err)
      alert("Failed to stop recording")
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`
  }

  const uploadAndSendAudio = async (uri: string) => {
    try {
      // Show loading indicator
      setIsLoading(true)

      // Create blob from file
      const response = await fetch(uri)
      if (!response.ok) {
        throw new Error("Failed to fetch audio file")
      }

      const blob = await response.blob()
      const fileName = `audio_${Date.now()}.m4a`
      const filePath = `chat-audio/${fileName}`

      // Upload to Supabase
      const { data, error } = await supabase.storage.from("chat-media").upload(filePath, blob, {
        contentType: "audio/m4a",
        cacheControl: "3600",
      })

      if (error) {
        throw error
      }

      // Get public URL
      const { data: publicUrlData } = supabase.storage.from("chat-media").getPublicUrl(filePath)

      if (!publicUrlData.publicUrl) {
        throw new Error("Failed to get public URL")
      }

      // Create message
      const chatId = [userId, otherUserId].sort().join("_")
      const newMessage: Message = {
        id: Date.now(),
        text: "",
        senderId: userId,
        receiverId: otherUserId,
        timestamp: Date.now(),
        status: "sent",
        type: "audio",
        mediaUrl: publicUrlData.publicUrl,
      }

      // Save to Firebase
      await set(ref(db, `dm_messages/${chatId}/${newMessage.id}`), newMessage)

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true })
      }, 100)
    } catch (error) {
      console.error("Error uploading audio:", error)
      alert("Failed to send audio message. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
        <ArrowLeft color={theme.colors.primary} size={24} />
      </TouchableOpacity>

      <View style={styles.headerProfile}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: otherUserAvatar }} style={styles.avatar} />
          {isOtherUserOnline && <View style={styles.onlineIndicator} />}
        </View>
        <View style={styles.headerInfo}>
          <Text style={styles.headerName}>{otherUserName}</Text>
          <Text style={styles.headerStatus}>
            {isOtherUserTyping
              ? "typing..."
              : isOtherUserOnline
                ? "online"
                : lastSeen
                  ? `last seen ${new Date(lastSeen).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`
                  : ""}
          </Text>
        </View>
      </View>

      <TouchableOpacity style={styles.moreButton}>
        <MoreVertical color={theme.colors.primary} size={24} />
      </TouchableOpacity>
    </View>
  )

  // Create a new MessageItem component
  const MessageItem = React.memo(
    ({
      message,
      userId,
    }: {
      message: Message
      userId: string
    }) => {
      const isMyMessage = message.senderId === userId
      const isPlayingAudio = isPlayingAudioMap[message.id] || false
      const audioDuration = audioDurationMap[message.id] || 0
      const audioProgress = audioProgressMap[message.id] || 0

      const handleAudioPressWrapper = () => {
        if (message.mediaUrl) {
          handleAudioPress(message.id, message.mediaUrl)
        }
      }

      const renderMessageStatus = () => {
        if (isMyMessage) {
          switch (message.status) {
            case "sent":
              return <Check size={14} color={theme.colors.onPrimaryContainer} style={styles.statusIcon} />
            case "delivered":
              return <CheckCheck size={14} color={theme.colors.onPrimaryContainer} style={styles.statusIcon} />
            case "read":
              return <CheckCheck size={14} color={theme.colors.primary} style={styles.statusIcon} />
            default:
              return null
          }
        }
        return null
      }

      return (
        <View style={[styles.messageContainer, isMyMessage ? styles.myMessage : styles.theirMessage]}>
          {message.type === "image" && (
            <View style={styles.imageWrapper}>
              <Image source={{ uri: message.mediaUrl }} style={styles.messageImage} resizeMode="cover" />
            </View>
          )}
          {message.type === "audio" && (
            <TouchableOpacity
              style={[styles.audioContainer, isMyMessage ? styles.myAudioContainer : styles.theirAudioContainer]}
              onPress={handleAudioPressWrapper}
              activeOpacity={0.7}
            >
              <View
                style={[
                  styles.audioIconContainer,
                  isMyMessage ? styles.myAudioIconContainer : styles.theirAudioIconContainer,
                ]}
              >
                {isPlayingAudio ? (
                  <Pause size={18} color={isMyMessage ? theme.colors.onPrimary : theme.colors.primary} />
                ) : (
                  <Play size={18} color={isMyMessage ? theme.colors.onPrimary : theme.colors.primary} />
                )}
              </View>

              <View style={styles.audioProgressContainer}>
                <View
                  style={[
                    styles.audioProgressBar,
                    { width: `${audioProgress * 100}%` },
                    isMyMessage ? styles.myAudioProgress : styles.theirAudioProgress,
                  ]}
                />
              </View>

              <Text style={[styles.audioDuration, isMyMessage ? styles.myAudioText : styles.theirAudioText]}>
                {formatDuration(audioDuration)}
              </Text>
            </TouchableOpacity>
          )}
          {message.type === "text" && (
            <Text style={[styles.messageText, isMyMessage ? styles.myMessageText : styles.theirMessageText]}>
              {message.text}
            </Text>
          )}
          <View style={styles.messageFooter}>
            <Text style={[styles.timestamp, isMyMessage ? styles.myTimestamp : styles.theirTimestamp]}>
              {new Date(message.timestamp).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
            </Text>
            {renderMessageStatus()}
          </View>
        </View>
      )
    },
  )

  const renderMessage = ({ item }: { item: Message }) => <MessageItem message={item} userId={userId} />

  const renderRecordingOverlay = () => {
    if (!isRecording) return null

    return (
      <View style={styles.recordingOverlay}>
        <Animated.View style={[styles.recordingPulse, { transform: [{ scale: pulseAnim }] }]}>
          <View style={styles.recordingIndicator}>
            <Mic size={24} color={theme.colors.onPrimary} />
          </View>
        </Animated.View>
        <Text style={styles.recordingTimer}>{formatDuration(recordingDuration)}</Text>
        <Text style={styles.recordingHint}>Release to send, swipe up to cancel</Text>
      </View>
    )
  }

  const renderAttachmentOptions = () => {
    if (!showAttachmentOptions) return null

    return (
      <View style={styles.attachmentOptionsContainer}>
        <TouchableOpacity style={styles.attachmentOption} onPress={pickImage}>
          <View style={styles.attachmentIconContainer}>
            <ImageIcon size={24} color={theme.colors.onPrimary} />
          </View>
          <Text style={styles.attachmentText}>Gallery</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.attachmentOption}
          onPress={() => {
            setShowAttachmentOptions(false)
            // Add camera functionality here
          }}
        >
          <View style={styles.attachmentIconContainer}>
            <ImageIcon size={24} color={theme.colors.onPrimary} />
          </View>
          <Text style={styles.attachmentText}>Camera</Text>
        </TouchableOpacity>
      </View>
    )
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    )
  }

  return (
    <View style={styles.container} edges={["top"]}>
      {renderHeader()}
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={Platform.OS === "ios" ? 60 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={[
            styles.messagesList,
            // Add this to make messages start from bottom when there are few messages
            { flexGrow: 1, justifyContent: 'flex-end' }
          ]}
          inverted={false}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => {
            // Scroll to bottom when content size changes (new message)
            flatListRef.current?.scrollToEnd({ animated: true });
          }}
        />

        {renderAttachmentOptions()}

        <View style={styles.inputContainer}>
          <TouchableOpacity
            style={styles.attachButton}
            onPress={() => setShowAttachmentOptions(!showAttachmentOptions)}
          >
            <Paperclip size={22} color={theme.colors.secondary} />
          </TouchableOpacity>

          <TextInput
            ref={inputRef}
            style={styles.input}
            value={messageText}
            onChangeText={handleTextChange}
            placeholder="Message"
            placeholderTextColor={theme.colors.secondary}
            multiline
            maxLength={1000}
          />

          {messageText.trim() ? (
            <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
              <Send size={18} color={theme.colors.onPrimary} />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
            <Send size={18} color={theme.colors.onPrimary} />
          </TouchableOpacity>
          )}
        </View>
      </KeyboardAvoidingView>
      {renderRecordingOverlay()}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    backgroundColor: theme.colors.primaryContainer,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
    height: 60,
  },
  backButton: {
    padding: 8,
  },
  moreButton: {
    padding: 8,
  },
  headerProfile: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 8,
  },
  avatarContainer: {
    position: "relative",
  },
  headerInfo: {
    marginLeft: 12,
  },
  headerName: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "GoogleSans-Bold",
    color: theme.colors.primary,
  },
  headerStatus: {
    fontSize: 12,
    fontFamily: "GoogleSans-Regular",
    color: theme.colors.secondary,
    marginTop: 2,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#34C759",
    borderWidth: 2,
    borderColor: theme.colors.primaryContainer,
  },
  messagesList: {
    paddingHorizontal: 12,
    paddingVertical: 16,
    flexGrow: 1, // Add this
  },
  messageContainer: {
    maxWidth: width * 0.75,
    marginVertical: 4,
    padding: 12,
    borderRadius: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  myMessage: {
    alignSelf: "flex-end",
    backgroundColor: theme.colors.primary,
    borderBottomRightRadius: 4,
  },
  theirMessage: {
    alignSelf: "flex-start",
    backgroundColor: theme.colors.primaryContainer,
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    fontFamily: "GoogleSans-Regular",
  },
  myMessageText: {
    color: theme.colors.onPrimary,
  },
  theirMessageText: {
    color: theme.colors.onPrimaryContainer,
  },
  imageWrapper: {
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: 4,
  },
  messageImage: {
    width: width * 0.6,
    height: width * 0.6,
  },
  messageFooter: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginTop: 4,
  },
  timestamp: {
    fontSize: 11,
    marginRight: 4,
    fontFamily: "GoogleSans-Regular",
  },
  myTimestamp: {
    color: "rgba(255,255,255,0.7)",
  },
  theirTimestamp: {
    color: theme.colors.secondary,
  },
  statusIcon: {
    marginLeft: 2,
  },
  inputContainer: {
    flexDirection: "row",
    padding: 8,
    paddingHorizontal: 12,
    backgroundColor: theme.colors.primaryContainer,
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.05)",
  },
  attachButton: {
    padding: 8,
    marginRight: 4,
  },
  input: {
    flex: 1,
    backgroundColor: theme.colors.background,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 8,
    maxHeight: 120,
    fontSize: 16,
    fontFamily: "GoogleSans-Regular",
    color: theme.colors.primary,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  micButton: {
    padding: 8,
  },
  audioContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
    borderRadius: 16,
    minWidth: 120,
    height: 48,
  },
  myAudioContainer: {
    backgroundColor: "transparent",
  },
  theirAudioContainer: {
    backgroundColor: "transparent",
  },
  audioIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  myAudioIconContainer: {
    backgroundColor: theme.colors.onPrimary,
  },
  theirAudioIconContainer: {
    backgroundColor: theme.colors.primary,
  },
  audioProgressContainer: {
    flex: 1,
    height: 3,
    backgroundColor: "rgba(0,0,0,0.1)",
    borderRadius: 2,
    marginHorizontal: 8,
  },
  audioProgressBar: {
    height: "100%",
    borderRadius: 2,
  },
  myAudioProgress: {
    backgroundColor: theme.colors.onPrimary,
  },
  theirAudioProgress: {
    backgroundColor: theme.colors.primary,
  },
  audioDuration: {
    fontSize: 12,
    marginLeft: 4,
    fontFamily: "GoogleSans-Regular",
  },
  myAudioText: {
    color: theme.colors.onPrimary,
  },
  theirAudioText: {
    color: theme.colors.onPrimaryContainer,
  },
  recordingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.7)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  recordingPulse: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(35,31,6,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  recordingIndicator: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  recordingTimer: {
    color: theme.colors.onPrimary,
    fontSize: 24,
    fontWeight: "bold",
    fontFamily: "GoogleSans-Bold",
    marginTop: 20,
  },
  recordingHint: {
    color: theme.colors.onPrimary,
    fontSize: 14,
    marginTop: 12,
    opacity: 0.8,
    fontFamily: "GoogleSans-Regular",
  },
  attachmentOptionsContainer: {
    position: "absolute",
    bottom: 70,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.primaryContainer,
    padding: 16,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    flexDirection: "row",
    justifyContent: "space-around",
    borderTopWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  attachmentOption: {
    alignItems: "center",
    justifyContent: "center",
    padding: 8,
  },
  attachmentIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  attachmentText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontFamily: "GoogleSans-Medium",
  },
})

