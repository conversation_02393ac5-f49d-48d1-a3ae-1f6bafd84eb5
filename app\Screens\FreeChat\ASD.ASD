import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  FlatList,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Image,
  Keyboard,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase'; // Adjust the import path as necessary
import { ref, onValue, set } from 'firebase/database';
import tw from 'twrnc';
import { useNavigation, useNavigationSetOptions } from '@react-navigation/native';
import { Send } from 'lucide-react-native';
import Backgroundchat from '@/assets/images/backgroundchat.png';
import DashedLine from '@/components/icons/DashedLine';
import Ab from '@/components/usersAv'
import * as NavigationBar from 'expo-navigation-bar';
import { BackHandler } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { useToast } from '../../../context/ToastContext';

const generateUsername = (userId) => {
  return `User_${userId}`;
};

const generateRoomName = () => {
  return Math.random().toString(36).substring(2, 12); // Generates a random 10-letter string
};

const ChatScreen = ({ route }) => {
  const { consultantId, balance, chatDuration, totalCost, isFreeChat, astrologer } = route.params;
  const navigation = useNavigation();
  const { showToast } = useToast();
  NavigationBar.setVisibilityAsync('hidden');

  useEffect(() => {
    navigation.setOptions({
      headerShown: false
    });
  }, []);
  console.log(balance)
  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');
  const [roomName] = useState(generateRoomName());
  const [userSession, setUserSession] = useState(null);
  const flatListRef = useRef(null);
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dataSent, setDataSent] = useState(false);
  const [isApproved, setIsApproved] = useState(false);
  const [approvalChecked, setApprovalChecked] = useState(false);
  const inputRef = useRef(null);
  const [remainingTime, setRemainingTime] = useState(chatDuration * 60);
  const [balanceDeducted, setBalanceDeducted] = useState(false); // State to track balance deduction

  useEffect(() => {
    let interval;
    if (isApproved && remainingTime > 0) {
      // Deduct balance when chat is approved
      const deductBalance = async () => {
        if (!isFreeChat && !balanceDeducted) {
          const userSession = await AsyncStorage.getItem('userSession');
          const userId = JSON.parse(userSession).userId;
          const newBalance = balance - totalCost;

          try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ balance: newBalance }),
            });

            if (response.ok) {
              setBalanceDeducted(true);
            } else {
              navigation.goBack();
            }
          } catch (error) {
            console.error('Error updating balance:', error);
            navigation.goBack();
          }
        }
      };

      deductBalance();

      interval = setInterval(() => {
        setRemainingTime((prevTime) => prevTime - 1);
      }, 1000);
    }

    if (remainingTime === 0) {
      navigation.goBack(); // Go back when time runs out
    }

    return () => clearInterval(interval); // Cleanup interval on unmount or if the approval changes
  }, [isApproved, remainingTime, navigation]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (!isApproved) {
        fetchApprovalStatus(); // Keep checking approval status until approved
      } else {
        clearInterval(intervalId); // Clear the interval once approved
      }
    }, 10000);

    return () => clearInterval(intervalId); // Cleanup the interval on unmount
  }, [isApproved]);

  // Updated code block to also send a Firebase message with profile1 details:
  useEffect(() => {
    const checkAstrologerProfile = async () => {
      if (astrologer) {
        try {
          const profileData = await AsyncStorage.getItem('userProfile');
          const parsedProfile = profileData ? JSON.parse(profileData) : null;
          console.log('Astrologer Profile:', parsedProfile);
          if (parsedProfile && parsedProfile.profile1) {
            const details = parsedProfile.profile1;
            // Construct the message text with details from userProfile.profile1
            const messageText = `Hi,
Below are my details:
Name: ${details.name}
Gender: ${details.gender}
DOB: ${details.dob}
TOB: ${details.tob}
POB: ${details.pob}`;
            const firebaseMessage = {
              id: Date.now(),
              text: messageText,
              username: generateUsername('astrologer'),
              roomName: roomName,
            };
            // Send the message to Firebase
            await set(ref(db, 'messages/' + firebaseMessage.id), firebaseMessage);
          }
        } catch (error) {
          console.error('Error fetching or sending astrologer profile message:', error);
        }
      }
    };

    checkAstrologerProfile();
  }, [astrologer, roomName]);

  const user1 = generateUsername('1');
  const user2 = generateUsername('2');

  useEffect(() => {
    const loadUserSession = async () => {
      const session = await AsyncStorage.getItem('userSession');
      setUserSession(session);
    };

    loadUserSession();
  }, []);

  const fetchApprovalStatus = async () => {
    setIsApproved(null);
    setLoading(true);
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      const response = await fetch(`https://nityasha.vercel.app/api/${userId}`);

      if (!response.ok) {
        showToast('Failed to check approval status', 'error');
        throw new Error('Network response was not ok');
      }

      const data = await response.json();
      const consultantData = data.find(item => item.consultantId === consultantId);
      setIsApproved(consultantData?.status === 'approved' || false);
    } catch (error) {
      console.error('Error fetching approval status:', error);
      showToast('Error checking approval status', 'error');
      setIsApproved(false);
    } finally {
      setLoading(false);
    }
  };

  const sendRoomData = async () => {
    if (dataSent) {
      return;
    }

    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      const roomDetails = {
        userId: String(userId),
        consultantId: String(consultantId),
        bel: String(balance),
        time: String(chatDuration),
        RoomN: roomName,
      };

      const response = await fetch('https://nityasha.vercel.app/api/v1/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(roomDetails),
      });

      if (!response.ok) {
        showToast('Failed to initialize chat room', 'error');
        throw new Error(`Failed to send room data: ${response.statusText}`);
      }

      setDataSent(true);
    } catch (error) {
      console.error('Error sending room data:', error);
      showToast('Error setting up chat room', 'error');
    }
  };

  useEffect(() => {
    const messagesRef = ref(db, 'messages');
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messageArray = Object.values(data).filter(
          (message) => message.roomName === roomName
        );
        setMessages(messageArray);
      }
    });

    return () => unsubscribe();
  }, [roomName]);

  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  useEffect(() => {
    const handleDataFetch = async () => {
      if (userSession && consultantId && !approvalChecked) {
        try {
          setLoading(true);
          await sendRoomData();
          setApprovalChecked(true);
        } catch (error) {
          console.error('Error in sending room data or fetching approval status:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    handleDataFetch();
  }, [userSession, consultantId, approvalChecked]);

  // Assuming the Expo push token is stored as `expoToken` for each consultant
  const fetchConsultants = async () => {
    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/consultants/${consultantId}`);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const data = await response.json();
  
      setConsultants([data]);
  
      const expoToken = data.push_token; // Assuming the actual field for push_token is correct
      if (expoToken && expoToken !== '0') {
        sendExpoPushToken(expoToken);
      } else {
        console.warn('Invalid or missing expo token:', expoToken);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const sendExpoPushToken = async (expoToken) => {
    try {
      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: expoToken,  // Using 'to' as per your example
          sound: 'default',
          title: 'New Chat Request',
          body: 'You have a new Chat Request from a user.',
        }),
      });
  
      console.log('Response status:', {
        to: expoToken,  // Using 'to' as per your example
        sound: 'default',
        title: 'New Chat Request',
        body: 'You have a new Chat Requesst from a user.',
      });
      
      console.log('Expo Token:', expoToken);
  
      if (response.ok) {
        console.log('Notification sent successfully!');
      } else {
        throw new Error('Failed to send notification');
      }
    } catch (error) {
      console.error('Error sending Expo push notification:', error);
    }
  };

  useEffect(() => {
    fetchConsultants();
  }, []);

  const sendMessage = (user) => {
    if (messageText) {
      const newMessage = {
        id: Date.now(),
        text: messageText,
        username: user,
        roomName: roomName,
      };
      set(ref(db, 'messages/' + newMessage.id), newMessage);
      setMessageText('');
    }
  };

  const formatTime = (time) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;

    const hoursFormatted = hours > 0 ? String(hours) : '';  // Show hours only if > 0
    const minutesFormatted = minutes > 0 || hours > 0 ? String(minutes).padStart(2, '0') : String(minutes);  // Show minutes only if > 0 or hours > 0
    const secondsFormatted = String(seconds).padStart(2, '0'); // Always pad seconds to two digits

    // Only include colons where necessary
    if (hoursFormatted && minutesFormatted) {
      return `${hoursFormatted}:${minutesFormatted}:${secondsFormatted}`;
    } else if (minutesFormatted) {
      return `${minutesFormatted}:${secondsFormatted}`;
    } else {
      return `${secondsFormatted}`;
    }
  };

  const handleCloseChat = async () => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      // Send system message about user leaving
      const leftMessage = {
        id: Date.now(),
        text: "User has left the chat",
        username: "system",
        roomName: roomName,
      };
      await set(ref(db, 'messages/' + leftMessage.id), leftMessage);

      if (!isApproved) {
        const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ balance: balance }),
        });

        if (!response.ok) {
          showToast('Failed to update user status', 'error');
          throw new Error('Failed to update user status');
        }

        navigation.reset({
          index: 0,
          routes: [{ name: 'BottomTabs' }],
        });
        return;
      }

      const usedTime = chatDuration * 60 - remainingTime;
      const balanceUsed = (usedTime / (chatDuration * 60)) * balance;
      const remainingBalance = Math.max(0, parseFloat((balance - balanceUsed).toFixed(2)));

      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ balance: remainingBalance }),
      });

      if (!response.ok) {
        throw new Error('Failed to update balance');
      }

      navigation.reset({
        index: 0,
        routes: [{ name: 'BottomTabs' }],
      });
    } catch (error) {
      console.error('Error when closing the chat:', error);
      showToast('Error ending chat session', 'error');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const updateUserBalance = async (newBalance) => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ balance: newBalance }),
      });

      if (!response.ok) {
        throw new Error('Failed to update balance');
      }

      return true;
    } catch (error) {
      console.error('Error updating balance:', error);
      throw error;
    }
  };

  const renderMessageItem = ({ item }) => (
    <View style={tw`p-2 my-1 ${item.username === user2 ? 'bg-[#0E40EF] self-end text-white mr-2 rounded-lg rounded-tr-none' : 'bg-black text-white ml-2 self-start rounded-lg rounded-tl-none'}`}
    >
      <Text style={[tw`text-white`, { fontFamily: 'Geist-Medium' }]}> {item.text} </Text>
    </View>
  );

  useEffect(() => {
    if (remainingTime === 300) { // 5 minutes remaining
    } else if (remainingTime === 60) { // 1 minute remaining
    }
  }, [remainingTime]);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Return true to prevent default back action
        return true;
      };

      // Add the event listener
      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      // Cleanup the event listener on screen unfocus
      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
      };
    }, [])
  );

  return (
    <View style={[tw``, styles.container]}>
      <>
        <View style={tw`flex px-5 bg-white flex-row items-center justify-between pb-2 mb-2`}>
          <StatusBar backgroundColor={'white'} />
          <View style={tw`flex h-full`}>
            {
              Array.isArray(consultants) && consultants.length > 0 ? (
                consultants.map((consultant, index) => (
                  <TouchableOpacity key={index} style={tw`flex flex-row gap-1 items-center justify-center`} >
                    <Image source={{ uri: consultant.pfp }} style={tw`w-12 h-12 rounded-full`}
                    />
                    <View style={tw`flex`}>
                      <Text style={[tw``, { fontFamily: 'Geist-SemiBold' }]}>{consultant.name}</Text>
                      <Text style={[tw``, { fontFamily: 'Geist-ExtraLight' }]}>{formatTime(remainingTime)}</Text>
                    </View>
                  </TouchableOpacity>
                ))
              ) : (
                <Text>No consultants available </Text>
              )}
          </View>
          <View style={tw`flex items-center justify-center flex-row gap-1`}>
            <TouchableOpacity onPress={handleCloseChat} style={tw`py-1 rounded-lg border-red-600 border px-1`}>
              <Text style={[tw`text-black`, { fontFamily: 'Geist-ExtraLight' }]}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>

        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessageItem}
          keyExtractor={(item) => item.id.toString()}
        />
        {
          loading ? (
            <View style={tw`flex w-full items-center justify-center bg-white py-5 flex-row gap-2`}>
              <Text style={[tw`text-gray-600`, { fontFamily: 'Geist-Bold' }]}>Please Wait</Text>
              <ActivityIndicator size="small" color="#10b981" />
            </View>
          ) : (
            <>
              {isApproved === false && (
                <View style={tw`flex w-full items-center justify-center bg-white py-5 flex-row gap-2`}>
                  <Text style={[tw`text-gray-600`, { fontFamily: 'Geist-Bold' }]}>Please Wait</Text>
                  <ActivityIndicator size="small" color="#10b981" />
                </View>
              )}
              {isApproved === true && (
                <>

                  <View style={tw`px-1 py-2`}>
                    <View style={tw`flex items-center justify-center flex-row w-full rounded-full gap-1`}>
                      <TextInput
                        ref={inputRef}
                        value={messageText}
                        onChangeText={setMessageText}
                        placeholder="Type your message"
                        style={[tw`flex w-[86%] border py-2 rounded-full pl-3 bg-white`, { fontFamily: 'Helvetica_bold' }]}
                        onSubmitEditing={sendMessage}
                      />
                      <TouchableOpacity onPress={() => sendMessage(user2)} style={[tw`flex rounded-full items-center justify-center p-2 bg-emerald-500`, { fontFamily: 'Helvetica_bold' }]} >
                        <Send size={26} color={"white"} />
                      </TouchableOpacity>
                    </View>
                  </View>
                </>
              )}
            </>
          )}
      </>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
});

export default ChatScreen;
