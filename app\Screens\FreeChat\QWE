"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  View,
  TextInput,
  FlatList,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Keyboard,
  ActivityIndicator,
  StatusBar,
  useWindowDimensions,
  Platform,
  KeyboardAvoidingView,
  Dimensions,
  ScrollView,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { db } from "@/lib/Firebase"; // Adjust the import path as necessary
import { ref, onValue, set } from "firebase/database";
import { useNavigation } from "@react-navigation/native";
import { Send, X, Clock } from "lucide-react-native";
import * as NavigationBar from "expo-navigation-bar";
import { BackHandler } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import { useToast } from "@/context/ToastContext";
import type { RouteProp } from "@react-navigation/native";
import type { RootStackParamList } from "@/navigation/types";
import SkeletonLoader from "@/components/ui/SkeletonLoader";
import { theme } from "../../theme"; // Import the theme
import { Appbar } from "react-native-paper";
import { SafeAreaView } from "moti";
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

type ChatScreenRouteProp = RouteProp<RootStackParamList, "Chat">;

interface ChatScreenProps {
  route: {
    params: {
      consultantId: string;
      balance: number;
      chatDuration: number;
      totalCost?: number;
      isFreeChat?: boolean;
      astrologer?: boolean;
    };
  };
}

interface Message {
  id: number;
  text: string;
  username: string;
  roomName: string;
}

interface Consultant {
  pfp: string;
  name: string;
  push_token?: string;
  per_minute_rate?: number;
}

const generateUsername = (userId: string): string => {
  return `User_${userId}`;
};

const generateRoomName = () => {
  return Math.random().toString(36).substring(2, 12);
};

const ChatScreen = ({ route }: ChatScreenProps) => {
  const {
    consultantId,
    balance: balanceParam,
    chatDuration,
    totalCost,
    isFreeChat,
    astrologer,
  } = route.params;
  const navigation = useNavigation();
  const { showToast } = useToast();
  const { width, height } = useWindowDimensions();
  const isSmallScreen = width < 375;
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [listHeight, setListHeight] = useState(0);
  const windowHeight = Dimensions.get("window").height;

  // Hide navigation bar on Android
  useEffect(() => {
    if (Platform.OS === "android") {
      NavigationBar.setVisibilityAsync("hidden");
    }
  }, []);

  // Keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  // Parse the balance properly, ensuring it's a number
  const [balance, setBalance] = useState(() => {
    const parsedBalance =
      typeof balanceParam === "string"
        ? Number.parseFloat(balanceParam)
        : balanceParam;
    return isNaN(parsedBalance) ||
      parsedBalance === null ||
      parsedBalance === undefined
      ? 0
      : parsedBalance;
  });

  const [messages, setMessages] = useState<Message[]>([]);
  const [messageText, setMessageText] = useState("");
  const [roomName] = useState(generateRoomName());
  const [userSession, setUserSession] = useState(null);
  const flatListRef = useRef(null);
  const [consultants, setConsultants] = useState<Consultant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dataSent, setDataSent] = useState(false);
  const [isApproved, setIsApproved] = useState(false);
  const [approvalChecked, setApprovalChecked] = useState(false);
  const inputRef = useRef(null);
  const [remainingTime, setRemainingTime] = useState(chatDuration * 60);
  const [balanceDeducted, setBalanceDeducted] = useState(false);
  const scrollViewRef = useRef(null); // Ref for ScrollView

  // Use astrologer with null check
  const isAstrologer = astrologer ?? false;

  const user1 = generateUsername("1");
  const user2 = generateUsername("2");

  // Timer and balance deduction effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isApproved && remainingTime > 0) {
      // Deduct balance when chat is approved
      const deductBalance = async () => {
        if (!isFreeChat && !balanceDeducted) {
          try {
            const userSession = await AsyncStorage.getItem("userSession");
            if (!userSession) throw new Error("No user session found");
            const parsedSession = JSON.parse(userSession);
            const userId = parsedSession.userId;

            // Get current chat details if available
            const chatDetails = await AsyncStorage.getItem(
              "currentChatDetails"
            );
            let currentBalance = balance;

            // If we have chat details with a balance, use that
            if (chatDetails) {
              const parsedDetails = JSON.parse(chatDetails);
              if (
                parsedDetails.balance !== undefined &&
                !isNaN(Number.parseFloat(parsedDetails.balance))
              ) {
                currentBalance = Number.parseFloat(parsedDetails.balance);
              }
            }

            // Calculate new balance
            const calculatedCost =
              totalCost ||
              ((chatDuration * 30 - remainingTime) / 30) *
                (consultants[0]?.per_minute_rate || 0);
            const newBalance = Math.max(0, currentBalance - calculatedCost);

            const response = await fetch(
              `https://nityasha.vercel.app/api/v1/users/${userId}`,
              {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ balance: newBalance.toString() }),
              }
            );

            if (response.ok) {
              setBalance(newBalance);
              setBalanceDeducted(true);

              // Update user session with new balance
              parsedSession.balance = newBalance;
              await AsyncStorage.setItem(
                "userSession",
                JSON.stringify(parsedSession)
              );
            } else {
              showToast("Failed to update balance. Please try again.");
              navigation.goBack();
            }
          } catch (error) {
            console.error("Error updating balance:", error);
            showToast("Error updating balance. Please try again.");
            navigation.goBack();
          }
        }
      };

      deductBalance();

      interval = setInterval(() => {
        setRemainingTime((prevTime) => prevTime - 1);
      }, 1000);
    }

    if (remainingTime === 0) {
      navigation.goBack(); // Go back when time runs out
    }

    return () => clearInterval(interval);
  }, [
    isApproved,
    remainingTime,
    navigation,
    isFreeChat,
    balanceDeducted,
    totalCost,
    chatDuration,
    consultants,
    balance,
    showToast,
  ]);

  // Check approval status periodically
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (!isApproved) {
        fetchApprovalStatus();
      } else {
        clearInterval(intervalId);
      }
    }, 10000);

    return () => clearInterval(intervalId);
  }, [isApproved]);

  // Send astrologer profile details if applicable
  useEffect(() => {
    const sendAstrologerDetails = async () => {
      if (isAstrologer) {
        try {
          const profileData = await AsyncStorage.getItem("userProfile");
          const parsedProfile = profileData ? JSON.parse(profileData) : null;

          if (parsedProfile?.profiles?.profile1) {
            const details = parsedProfile.profiles.profile1;
            const messageText = `Hi,
Below are my details:
Name: ${details.Name}
Gender: ${details.gender}
DOB: ${new Date(details.dob)
              .toLocaleDateString("en-IN", {
                year: "numeric",
                month: "long",
                day: "numeric",
                timeZone: "UTC",
              })
              .replace(/\s/g, "-")}
TOB: ${
              details.userbirthtime
                ? new Date(details.userbirthtime).toLocaleTimeString("en-US", {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true,
                  })
                : "N/A"
            }
POB: ${details.birthplace}`;

            const firebaseMessage = {
              id: Date.now(),
              text: messageText,
              username: "system",
              roomName: roomName,
            };

            await set(
              ref(db, "messages/" + firebaseMessage.id),
              firebaseMessage
            );
          }
        } catch (error) {
          console.error(
            "Error fetching or sending astrologer profile message:",
            error
          );
        }
      }
    };

    sendAstrologerDetails();
  }, [isAstrologer, roomName]);

  // Load user session
  useEffect(() => {
    const loadUserSession = async () => {
      const session = await AsyncStorage.getItem("userSession");
      setUserSession(session);
    };

    loadUserSession();
  }, []);

  // Fetch approval status
  const fetchApprovalStatus = async () => {
    setLoading(true);
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (!userSession) throw new Error("No user session found");
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession.userId;

      const response = await fetch(`https://nityasha.vercel.app/api/${userId}`);
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const data = await response.json();
      const consultantData = data.find(
        (item) => item.consultantId === consultantId
      );
      setIsApproved(consultantData?.status === "approved" || false);
    } catch (error) {
      console.error("Error fetching approval status:", error);
      showToast("Error checking approval status", "error");
      setIsApproved(false);
    } finally {
      setLoading(false);
    }
  };

  // Send room data to server
  const sendRoomData = useCallback(async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (!userSession) throw new Error("No user session found");
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession.userId;

      const roomDetails = {
        userId: String(userId),
        consultantId: String(consultantId),
        bel: String(balance),
        time: String(chatDuration),
        RoomN: roomName,
      };

      const response = await fetch("https://nityasha.vercel.app/api/v1/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(roomDetails),
      });

      if (!response.ok) {
        showToast("Failed to initialize chat room", "error");
        throw new Error(`Failed to send room data: ${response.statusText}`);
      }

      setDataSent(true);
    } catch (error) {
      console.error("Error sending room data:", error);
      showToast("Error setting up chat room", "error");
    }
  }, [balance, chatDuration, consultantId, roomName, showToast]);

  // Listen for messages from Firebase
  useEffect(() => {
    const messagesRef = ref(db, "messages");
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messageArray = Object.values(data).filter(
          (message) => message.roomName === roomName
        );
        setMessages(messageArray as Message[]);
      }
    });

    return () => unsubscribe();
  }, [roomName]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  // Initialize chat room
  useEffect(() => {
    const handleDataFetch = async () => {
      if (userSession && consultantId && !approvalChecked) {
        try {
          setLoading(true);
          await sendRoomData();
          setApprovalChecked(true);
        } catch (error) {
          console.error(
            "Error in sending room data or fetching approval status:",
            error
          );
        } finally {
          setLoading(false);
        }
      }
    };

    handleDataFetch();
  }, [userSession, consultantId, approvalChecked, sendRoomData]);

  // Fetch consultant data
  const fetchConsultants = useCallback(async () => {
    try {
      const response = await fetch(
        `https://nityasha.vercel.app/api/v1/consultants/${consultantId}`
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();

      setConsultants([data]);

      const expoToken = data.push_token;
      if (expoToken && expoToken !== "0") {
        sendExpoPushToken(expoToken);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [consultantId]);

  // Send push notification to consultant
  const sendExpoPushToken = async (expoToken: string) => {
    try {
      const response = await fetch("https://exp.host/--/api/v2/push/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          to: expoToken,
          sound: "sound.wav",
          title: "New chat request",
          body: "You have received a new chat request",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error sending Expo push notification:", error);
    }
  };

  useEffect(() => {
    fetchConsultants();
  }, [fetchConsultants]);

  // Send message function
  const sendMessage = (user: string) => {
    if (messageText.trim()) {
      const newMessage = {
        id: Date.now(),
        text: messageText.trim(),
        username: user,
        roomName: roomName,
      };
      set(ref(db, "messages/" + newMessage.id), newMessage);
      setMessageText("");

      // Focus back on input after sending
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  };

  // Format time display
  const formatTime = (time: number): string => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;

    const hoursFormatted = hours > 0 ? String(hours) : "";
    const minutesFormatted =
      minutes > 0 || hours > 0 ? String(minutes).padStart(2, "0") : "";
    const secondsFormatted = String(seconds).padStart(2, "0");

    if (hoursFormatted) {
      return `${hoursFormatted}:${minutesFormatted}:${secondsFormatted}`;
    } else if (minutesFormatted) {
      return `${minutesFormatted}:${secondsFormatted}`;
    } else {
      return `${secondsFormatted}`;
    }
  };

  // Handle closing the chat
  const handleCloseChat = useCallback(async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (!userSession) throw new Error("No user session found");
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession.userId;

      // Send system message about user leaving
      const leftMessage = {
        id: Date.now(),
        text: "User has left the chat",
        username: "system",
        roomName: roomName,
      };
      await set(ref(db, "messages/" + leftMessage.id), leftMessage);

      if (!isApproved) {
        const response = await fetch(
          `https://nityasha.vercel.app/api/v1/users/${userId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ balance: balance }),
          }
        );

        if (!response.ok) {
          showToast("Failed to update user status", "error");
          throw new Error("Failed to update user status");
        }

        navigation.reset({
          index: 0,
          routes: [{ name: "BottomTabs" }],
        });
        return;
      }

      const usedTime = chatDuration * 30 - remainingTime;
      const balanceUsed = (usedTime / (chatDuration * 30)) * balance;
      const remainingBalance = Math.max(
        0,
        Number.parseFloat((balance - balanceUsed).toFixed(2))
      );

      const response = await fetch(
        `https://nityasha.vercel.app/api/v1/users/${userId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ balance: remainingBalance }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update balance");
      }

      navigation.reset({
        index: 0,
        routes: [{ name: "BottomTabs" }],
      });
    } catch (error) {
      console.error("Error when closing the chat:", error);
      showToast("Error ending chat session", "error");
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  }, [
    balance,
    chatDuration,
    isApproved,
    navigation,
    remainingTime,
    roomName,
    showToast,
  ]);
  useEffect(() => {
    if (scrollViewRef.current && messages.length > 0) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);
  // Render message item
  const renderMessageItem = (item: Message) => (
    <View
      key={item.id.toString()}
      style={[
        styles.messageContainer,
        item.username === user2 ? styles.userMessage : styles.otherMessage,
        item.username === "system" && styles.systemMessage,
      ]}
    >
      <Text
        style={[
          styles.messageText,
          item.username === "system" && styles.systemMessageText,
        ]}
      >
        {item.text}
      </Text>
    </View>
  );

  // Handle back button
  const handleBackPress = React.useCallback(() => {
    handleCloseChat();
    return true;
  }, [handleCloseChat]);

  useFocusEffect(
    React.useCallback(() => {
      BackHandler.addEventListener("hardwareBackPress", handleBackPress);
      return () =>
        BackHandler.removeEventListener("hardwareBackPress", handleBackPress);
    }, [handleBackPress])
  );

  return (
    <KeyboardAwareScrollView
    behavior={Platform.OS === "ios" ? "padding" : "height"}
    style={{ flex: 1 }}
  >
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <StatusBar
          backgroundColor={theme.colors.primaryContainer}
          barStyle="dark-content"
        />
        <View style={styles.consultantInfo}>
          {Array.isArray(consultants) && consultants.length > 0 ? (
            <View style={styles.consultantRow}>
              <Image source={{ uri: consultants[0].pfp }} style={styles.avatar} />
              <View>
                <Text style={styles.consultantName}>{consultants[0].name}</Text>
                <View style={styles.timeRow}>
                  <Clock
                    size={isSmallScreen ? 12 : 14}
                    color={theme.colors.primary}
                  />
                  <Text style={styles.timeText}>
                    {formatTime(remainingTime)}
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <SkeletonLoader height={40} width={120} borderRadius={8} />
          )}
        </View>
        <TouchableOpacity onPress={handleCloseChat} style={styles.closeButton}>
          <X size={isSmallScreen ? 14 : 16} color="white" />
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
  
      <View style={{ flex: 1, height: "100%", backgroundColor: theme.colors.background }}>
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={{ flexGrow: 1, justifyContent: "flex-end", height: "100%" }}
        >
          {messages.map((message) => renderMessageItem(message))}
        </ScrollView>
      </View>
  
      {loading ? (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Please Wait</Text>
          <ActivityIndicator size="small" color={theme.colors.primary} />
        </View>
      ) : (
        <>
          {isApproved === false && (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Waiting for approval</Text>
              <ActivityIndicator size="small" color={theme.colors.primary} />
            </View>
          )}
          {isApproved === true && (
            <View style={styles.inputContainer}>
              <View style={styles.inputRow}>
                <TextInput
                  ref={inputRef}
                  value={messageText}
                  onChangeText={setMessageText}
                  placeholder="Type your message"
                  style={styles.textInput}
                  onSubmitEditing={() => sendMessage(user2)}
                  multiline={Platform.OS === "ios"}
                  maxLength={500}
                />
                <TouchableOpacity
                  onPress={() => sendMessage(user2)}
                  style={[
                    styles.sendButton,
                    !messageText.trim() && styles.disabledSendButton,
                  ]}
                  disabled={!messageText.trim()}
                >
                  <Send size={20} color="white" />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </>
      )}
    </SafeAreaView>
  </KeyboardAwareScrollView>
  
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.primaryContainer,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
    zIndex: 10, // Ensure header stays on top
    
  },
  consultantInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  consultantRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
  },
  consultantName: {
    fontSize: 16,
    fontFamily: "GoogleSans-Medium",
    color: theme.colors.primary,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeText: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: "GoogleSans-Regular",
    color: theme.colors.primary,
  },
  closeButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#e53935",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  closeButtonText: {
    marginLeft: 4,
    color: "white",
    fontFamily: "GoogleSans-Medium",
    fontSize: 14,
  },
  keyboardAvoidView: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
  },

  messageContainer: {
    padding: 8,
    marginVertical: 4,
    maxWidth: "80%",
    borderRadius: 12,
  },
  userMessage: {
    backgroundColor: "#0E40EF",
    alignSelf: "flex-end",
    marginRight: 8,
    borderTopRightRadius: 0,
  },
  otherMessage: {
    backgroundColor: theme.colors.primary,
    alignSelf: "flex-start",
    marginLeft: 8,
    borderTopLeftRadius: 0,
  },
  systemMessage: {
    backgroundColor: theme.colors.secondaryContainer,
    alignSelf: "center",
    borderRadius: 8,
  },
  messageText: {
    color: "white",
    fontFamily: "GoogleSans-Regular",
  },
  systemMessageText: {
    color: theme.colors.primary,
  },
  loadingContainer: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: theme.colors.primaryContainer,
    paddingVertical: 16,
    gap: 8,
  },
  loadingText: {
    fontFamily: "GoogleSans-Bold",
    color: theme.colors.primary,
  },
  inputContainer: {
    padding: 8,
    backgroundColor: theme.colors.primaryContainer,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.05)",
  },
  inputRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  textInput: {
    flex: 1,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontFamily: "GoogleSans-Regular",
    fontSize: 15,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#0E40EF",
    alignItems: "center",
    justifyContent: "center",
  },
  disabledSendButton: {
    opacity: 0.6,
  },
});

export default ChatScreen;







