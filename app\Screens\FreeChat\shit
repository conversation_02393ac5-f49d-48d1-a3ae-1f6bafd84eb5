import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  FlatList,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Image,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase'; // Adjust the import path as necessary
import { ref, onValue, set } from 'firebase/database';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { Send } from 'lucide-react-native';
import Backgroundchat from '@/assets/images/backgroundchat.png';

const generateUsername = (userId) => {
  return `User_${userId}`;
};

const generateRoomName = () => {
  return Math.random().toString(36).substring(2, 12); // Generates a random 10-letter string
};

const ChatScreen = ({ route }) => {
  const { consultantId, balance, chatDuration } = route.params;

  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');
  const [roomName] = useState(generateRoomName());
  const [userSession, setUserSession] = useState(null);
  const flatListRef = useRef(null);
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dataSent, setDataSent] = useState(false);
  const [isApproved, setIsApproved] = useState(null); // State for approval status
  const [approvalChecked, setApprovalChecked] = useState(false); // State to track if approval has been checked

  const user1 = generateUsername('1'); // First user
  const user2 = generateUsername('2'); // Second user

  useEffect(() => {
    const loadUserSession = async () => {
      const session = await AsyncStorage.getItem('userSession');
      setUserSession(session);
    };

    loadUserSession();
  }, []);

  const fetchApprovalStatus = async () => {
    setIsApproved(null); // Reset the approval status while loading
    setLoading(true); // Set loading to true
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const parsedSession = JSON.parse(userSession); // Parse the session if needed
      const userId = parsedSession?.userId || userSession; // Use parsed userId or raw session

      const response = await fetch(`https://nityasha.vercel.app/api/${userId}`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error('Network response was not ok');
      }

      const data = await response.json();
      console.log('API Response:', data);

      const consultantData = data.find(item => item.consultantId === consultantId); // Use dynamic consultantId
      setIsApproved(consultantData?.status === 'approved' || false);
    } catch (error) {
      console.error('Error fetching approval status:', error);
      setIsApproved(false); // Handle error by setting approval to false
    } finally {
      setLoading(false); // Set loading to false after the fetch is complete
    }
  };

  const sendRoomData = async () => {
    if (dataSent) {
      console.log('Room data has already been sent. Skipping...');
      return;
    }

    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession; // Use userId if present, else fallback to raw session

      const roomDetails = {
        userId: String(userId), // Ensure this is a plain string
        consultantId: String(consultantId),
        bel: String(balance),
        time: String(chatDuration),
        RoomN: roomName,
      };

      console.log('Sending Room Data:', JSON.stringify(roomDetails, null, 2));

      const response = await fetch('https://nityasha.vercel.app/api/v1/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(roomDetails),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error data:', errorData);
        throw new Error(`Failed to send room data: ${errorData.message || response.statusText}`);
      }

      const responseData = await response.json();
      console.log('Room data sent successfully:', responseData);
      setDataSent(true);
    } catch (error) {
      console.error('Error sending room data:', error);
    }
  };

  useEffect(() => {
    const messagesRef = ref(db, 'messages');
    const unsubscribe = onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const messageArray = Object.values(data).filter(
          (message) => message.roomName === roomName
        );
        setMessages(messageArray);
      }
    });

    return () => unsubscribe();
  }, [roomName]);

  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  useEffect(() => {
    const handleDataFetch = async () => {
      if (userSession && consultantId && !approvalChecked) {
        try {
          setLoading(true); // Set loading to true while sending room data and fetching approval
          await sendRoomData(); // Await sending room data
          setApprovalChecked(true); // Mark that approval has been checked
        } catch (error) {
          console.error('Error in sending room data or fetching approval status:', error);
        } finally {
          setLoading(false); // Set loading to false once the fetches are done
        }
      }
    };

    handleDataFetch(); // Call the data fetch function
  }, [userSession, consultantId, approvalChecked]); // Effect runs when userSession, consultantId changes, or approvalChecked changes
  useEffect(() => {
    fetchApprovalStatus(); // Fetch on component mount

    const intervalId = setInterval(() => {
        fetchApprovalStatus(); // Fetch every 10 seconds
    }, 10000); // 10000 milliseconds = 10 seconds

    // Cleanup the interval on component unmount
    return () => clearInterval(intervalId);
}, []);

  const fetchConsultants = async () => {
    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/consultants/${consultantId}`);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const data = await response.json();
      console.log('Fetched data:', data);
      setConsultants([data]);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConsultants();
  }, []);

  const sendMessage = (user) => {
    if (messageText) {
      const newMessage = {
        id: Date.now(),
        text: messageText,
        username: user,
        roomName: roomName,
      };
      set(ref(db, 'messages/' + newMessage.id), newMessage);
      setMessageText('');
      Keyboard.dismiss();
    }
  };

  const renderMessageItem = ({ item }) => (
    <View style={tw`p-2 my-1 ${item.username === user2 ? 'bg-orange-500 self-end text-white mr-2 rounded-lg rounded-tr-none' : 'bg-orange-500 ml-2 self-start rounded-lg rounded-tl-none'}`}>
      <Text style={[tw`text-white`, { fontFamily: 'Helvetica_bold' }]}>{item.text}</Text>
    </View>
  );

  return (
    <ImageBackground source={Backgroundchat} style={tw`flex h-full`}>
      <View style={styles.container}>
        {/* Check if isApproved is null or false */}
        {isApproved === null ? (
          !approvalChecked ? ( // Only show loading if approval hasn't been checked yet
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#0000ff" />
              <Text>Loading Approval Status...</Text>
            </View>
          ) : null // Do not display anything if approval is already checked
        ) : !isApproved ? (
          <View style={styles.approvalContainer}>
            <Text style={styles.approvalText}>Waiting for Approval...</Text>
          </View>
        ) : (
          <>
            {/* Header with Consultant Information */}
            <View style={tw`flex px-5 bg-white flex-row items-center justify-between pb-2`}>
              <View style={tw`flex h-full`}>
                {Array.isArray(consultants) && consultants.length > 0 ? (
                  consultants.map((consultant, index) => (
                    <TouchableOpacity key={index} style={tw`flex flex-row gap-3 items-center justify-center`}>
                      <Image source={{ uri: consultant.pfp }} style={tw`w-12 h-12 rounded-full`} />
                      <Text style={tw`font-bold`}>{consultant.name}</Text>
                    </TouchableOpacity>
                  ))
                ) : (
                  <Text>No consultants available</Text>
                )}
              </View>
            </View>

            {/* Chat Messages */}
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessageItem}
              keyExtractor={(item) => item.id.toString()}
            />

            {/* Input Field and Send Button */}
            <View style={tw`px-1 py-2`}>
              <View style={tw`flex items-center justify-center flex-row w-full rounded-full gap-1`}>
                <TextInput
                  value={messageText}
                  onChangeText={setMessageText}
                  placeholder="Type your message"
                  style={[tw`flex w-[86%] border py-2 rounded-full pl-3 bg-white`, { fontFamily: 'Helvetica_bold' }]}
                />
                <TouchableOpacity onPress={() => sendMessage(user2)} style={[tw`flex rounded-full items-center justify-center p-2 bg-emerald-500`, { fontFamily: 'Helvetica_bold' }]}>
                  <Send size={26} color={"white"} />
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  approvalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  approvalText: {
    fontSize: 20,
    color: 'orange',
  },
});

export default ChatScreen;

