import React, { useState, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// Together AI API configuration
const TOGETHER_API_KEY = "728122776d61611edc84ceb621686b3bb9258ed391a84f9d13d0412ab0874e1b";
const TOGETHER_API_URL = "https://api.together.xyz/v1/chat/completions";

import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  FlatList,
  Image,
  Alert,
  BackHandler,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import tw from 'twrnc';
import { MotiView } from 'moti';
import PaymentPopup from "@/components/payment/paymentpopup";
// Define types
interface Message {
  id: string;
  text: string;
  isUser: boolean;
}



interface RouteParams {
  name: string;
  pfp: string;
  selectedRate: number;
}

interface RouteProps {
  route: {
    params: RouteParams;
  };
}



export default function App({route}: RouteProps) {
  const { name, pfp , selectedRate  } = route.params;
  const navigation = useNavigation<StackNavigationProp<any>>();

  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [inputText, setInputText] = useState('');
  const [userMessageCount, setUserMessageCount] = useState(0);
  const [showPaymentPopup, setShowPaymentPopup] = useState(false);

  // Function to get response from Together AI API
  const getAIResponse = async (userMessage: string) => {
    try {
      console.log('Sending to Together AI API:', userMessage);

      const response = await fetch(TOGETHER_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${TOGETHER_API_KEY}`
        },
        body: JSON.stringify({
          model: 'mistralai/Mixtral-8x7B-Instruct-v0.1',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that provides concise and informative responses. Always respond in Hindi language only, regardless of the language the user uses to ask questions. Use casual, conversational Hindi that sounds natural. Use Hinglish (mix of Hindi and English) when appropriate for technical terms.'
            },
            {
              role: 'user',
              content: userMessage
            }
          ],
          temperature: 0.7,
          max_tokens: 800
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error: any) {
      console.error('Error calling Together AI API:', error);
      // Show error alert in development
      if (__DEV__) {
        Alert.alert('API Error', error.message || 'Unknown error occurred');
      }
      return "Sorry, I couldn't process your request at the moment. Please try again later.";
    }
  };

  // Function to handle sending a new message
  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    // If user has already sent 2 messages, don't allow more messages
    if (userMessageCount >= 2) {
      setShowPaymentPopup(true);
      return;
    }

    // Check if user has reached message limit
    const newCount = userMessageCount + 1;
    setUserMessageCount(newCount);

    // Add user message
    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
    };

    setMessages(prevMessages => [...prevMessages, newMessage]);
    setInputText(''); // Clear input field

    // Show payment popup after second message
    if (newCount === 2) {
      // Still show the typing indicator briefly before showing popup
      setIsTyping(true);
      setTimeout(() => {
        setIsTyping(false);
        setShowPaymentPopup(true);
      }, 1000);
      return;
    }

    // Show typing indicator after user sends a message
    setIsTyping(true);

    // Get response from Together AI API
    const aiResponse = await getAIResponse(newMessage.text);

    // Hide typing indicator and add AI response
    setIsTyping(false);
    const responseMessage: Message = {
      id: (Date.now() + 1).toString(),
      text: aiResponse,
      isUser: false,
    };

    setMessages(prevMessages => [...prevMessages, responseMessage]);
  };

  useEffect(() => {
    // Show typing animation first
    setIsTyping(true);

    // After 2 seconds, hide typing animation and show the message
    const typingTimer = setTimeout(() => {
      setIsTyping(false);
      setMessages([
        {
          id: Date.now().toString(),
          text: "नमस्ते! मैं आपकी बात सुनने के लिए यहां हूं। आप मुझे कोई भी सवाल पूछ सकते हैं या अपनी समस्या बता सकते हैं।",
          isUser: false,
        }
      ]);
    }, 2000);

    return () => clearTimeout(typingTimer);
  }, []);

  // Handle back button press for the entire screen
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // If payment popup is showing, prevent going back regardless of message count
      if (showPaymentPopup) {
        return true; // Prevent default back button behavior
      }
      // Navigate back when back button is pressed
      navigation.goBack();
      return true; // We handled the back button press
    });

    return () => backHandler.remove();
  }, [showPaymentPopup, navigation]);

  // Handle keyboard submit
  const handleKeyboardSubmit = () => {
    if (inputText.trim() && userMessageCount < 2) {
      handleSendMessage();
    } else if (userMessageCount >= 2) {
      setShowPaymentPopup(true);
    }
  };

  // Typing indicator component
  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    return (
      <View style={styles.typingIndicatorContainer}>
        <View style={styles.typingBubble}>
          <MotiView
            from={{ opacity: 0.4 }}
            animate={{ opacity: 1 }}
            transition={{ type: 'timing', duration: 500, loop: true }}
            style={styles.typingDot}
          />
          <MotiView
            from={{ opacity: 0.4 }}
            animate={{ opacity: 1 }}
            transition={{ type: 'timing', duration: 500, loop: true, delay: 200 }}
            style={styles.typingDot}
          />
          <MotiView
            from={{ opacity: 0.4 }}
            animate={{ opacity: 1 }}
            transition={{ type: 'timing', duration: 500, loop: true, delay: 400 }}
            style={styles.typingDot}
          />
        </View>
      </View>
    );
  };

  const renderContent = () => {
    return (
      <>
        <FlatList
          data={[...messages].reverse()}
          renderItem={renderMessage}
          keyExtractor={item => String(item.id)}
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'flex-end',
            padding: 15
          }}
          inverted
          showsVerticalScrollIndicator={false}
        />
        {renderTypingIndicator()}
      </>
    );
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <View
      style={[
        styles.messageContainer,
        item.isUser ? styles.userMessage : styles.otherMessage
      ]}
    >
      <View style={styles.messageContentContainer}>
        <Text style={styles.messageText}>{item.text}</Text>
        {item.isUser && (
          <View style={styles.messageStatusContainer}>
            <Feather name="check-circle" size={14} color="#4CAF50" style={styles.readIcon} />
          </View>
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.logoContainer}>
            <Image source={{ uri: pfp }} style={tw`w-full h-full`} />
          </View>
          <View>
            <Text style={styles.headerTitle}>{name}</Text>
            <View style={styles.headerSubtitleContainer}>
              <Text style={styles.headerSubtitle}>Online</Text>
            </View>
          </View>
        </View>
        <View style={styles.headerRight}>

        </View>
      </View>

      {/* Chat Messages */}
      {renderContent()}

      {/* Message Input */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={[styles.inputWrapper, userMessageCount >= 2 && styles.disabledInputWrapper]}>
          <TouchableOpacity
            style={styles.plusButton}
            onPress={() => userMessageCount >= 2 && setShowPaymentPopup(true)}
          >
            <Feather name="plus" size={24} color={userMessageCount >= 2 ? "#999" : "#4CAF50"} />
          </TouchableOpacity>
          <TextInput
            style={[styles.input, userMessageCount >= 2 && styles.disabledInput]}
            placeholder={userMessageCount >= 2 ? "Please subscribe to continue chatting" : "Type a message..."}
            placeholderTextColor={userMessageCount >= 2 ? "#FF6B6B" : "#999"}
            value={inputText}
            onChangeText={setInputText}
            onSubmitEditing={handleKeyboardSubmit}
            returnKeyType="send"
            editable={userMessageCount < 2}
          />
        </View>
        <TouchableOpacity
          style={[styles.sendButton, (!inputText.trim() || userMessageCount >= 2) && styles.sendButtonDisabled]}
          onPress={handleSendMessage}
          disabled={!inputText.trim() || userMessageCount >= 2}
        >
          <Feather name="send" size={24} color="white" />
        </TouchableOpacity>
      </KeyboardAvoidingView>

            <PaymentPopup
              visible={showPaymentPopup}
              onClose={() => {
                // Only allow closing the popup if user hasn't sent 2 messages yet
                if (userMessageCount < 2) {
                  setShowPaymentPopup(false);
                }
                // If user has sent 2 messages, the popup cannot be closed by the back button
                // It can only be closed via the X button or payment completion
              }}
              rate={selectedRate}
              username={name}
            />

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c5e8c8',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBlockColor: '#B0DEBA',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 45,
    backgroundColor: '#fff',
    height: 45,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    overflow: 'hidden',
  },
  headerTitle: {
    fontSize: 15,
    fontFamily: 'Helvetica_bold',
    color: '#2c5e2e',
  },
  headerSubtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  headerSubtitle: {
    fontSize: 13,
    color: '#2c5e2e',
    fontFamily: 'Helvetica_bold',
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 20,
    marginVertical: 4,
  },
  userMessage: {
    backgroundColor: '#DCF8C6',
    alignSelf: 'flex-end',
    marginLeft: '20%',
  },
  otherMessage: {
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    marginRight: '20%',
  },
  messageText: {
    fontSize: 16,
  },
  messageContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  messageStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginLeft: 8,
  },
  readIcon: {
    marginLeft: 4,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  badgeContainer: {
    backgroundColor: '#FF5252',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    paddingHorizontal: 6,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  menuContainer: {
    position: 'absolute',
    top: 30,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
    minWidth: 180,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  menuIcon: {
    marginRight: 10,
  },
  menuItemText: {
    fontSize: 14,
    color: '#333',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 10,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    marginRight: 10,
  },
  plusButton: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    paddingVertical: 10,
    paddingRight: 15,
    color: '#333',
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#A5D6A7', // Lighter green when disabled
    opacity: 0.7,
  },
  messageList: {
    flexGrow: 1,
    justifyContent: 'flex-end',
    padding: 15,
  },
  typingIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
    marginBottom: 10,
  },
  typingBubble: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginHorizontal: 3,
  },
  typingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  paymentPopup: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    width: '80%',
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  paymentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c5e2e',
    marginBottom: 15,
    textAlign: 'center',
  },
  paymentText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 20,
  },
  paymentButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  paymentButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 10,
    padding: 12,
    flex: 1,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  paymentButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#333',
    fontWeight: 'bold',
    fontSize: 14,
  },
  disabledInput: {
    backgroundColor: '#f5f5f5',
    color: '#999',
  },
  disabledInputWrapper: {
    backgroundColor: '#f5f5f5',
    borderColor: '#ddd',
    borderWidth: 1,
  },
});

