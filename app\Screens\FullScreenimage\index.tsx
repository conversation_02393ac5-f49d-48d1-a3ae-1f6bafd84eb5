import React, { useRef } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Dimensions,
  PanResponder,
  Animated,
} from 'react-native';

export default function ImageView({ route }: { route: any }) {
  const { imageUrl } = route.params ?? {};

  const scale = useRef(new Animated.Value(1)).current;
  const lastScale = useRef(1);
  const pan = useRef(new Animated.ValueXY()).current;

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) =>
        gestureState.dx !== 0 || gestureState.dy !== 0 || gestureState.numberActiveTouches === 2,
      onPanResponderMove: (e, gestureState) => {
        if (gestureState.numberActiveTouches === 2) {
          const touches = e.nativeEvent.touches;
          if (touches.length == 2) {
            const dx = touches[0].pageX - touches[1].pageX;
            const dy = touches[0].pageY - touches[1].pageY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (lastScale.currentDistance) {
              const scaleFactor = distance / lastScale.currentDistance;
              scale.setValue(lastScale.current * scaleFactor);
            } else {
              lastScale.currentDistance = distance;
            }
          }
        } else if (gestureState.numberActiveTouches === 1) {
          pan.setValue({ x: gestureState.dx, y: gestureState.dy });
        }
      },
      onPanResponderRelease: () => {
        lastScale.current = scale._value;
        lastScale.currentDistance = null;
      },
      onPanResponderTerminate: () => {
        lastScale.current = scale._value;
        lastScale.currentDistance = null;
      },
    })
  ).current;

  return (
    <View style={styles.container}>
      <Animated.View
        {...panResponder.panHandlers}
        style={[
          {
            transform: [
              { scale: scale },
              { translateX: pan.x },
              { translateY: pan.y },
            ],
          },
        ]}
      >
        <Image
          source={{ uri: imageUrl }}
          style={styles.image}
          resizeMode="contain"
        />
      </Animated.View>
    </View>
  );
}

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  image: {
    width,
    height,
  },
});