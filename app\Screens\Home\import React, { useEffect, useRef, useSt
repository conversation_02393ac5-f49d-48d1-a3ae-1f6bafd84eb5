import { TouchableOpacity, View, Alert, Image } from "react-native";
import React, { useState } from "react";
import tw from "twrnc";
import { StatusBar } from "expo-status-bar";
import { Searchbar, Text, ActivityIndicator } from "react-native-paper";
import { Bell } from "lucide-react-native";
import { ScrollView } from "react-native";
import { supabase } from "@/utils/supabase";
import CommunityCard from "@/components/Home/Community";
import Posts from "@/components/Home/Posts";
import Logo from "@/assets/images/image.png";
import useTokenPush from "@/hooks/tokenpush";
import AuthProvider from "@/hooks/auth-provider";
import Wallet from "@/components/Wallet";

export default function Home({ navigation }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState({
    communities: [],
    posts: []
  });
  useTokenPush();

  const handleSearch = async (query) => {
    if (!query.trim()) {
      setSearchResults({ communities: [], posts: [] });
      return;
    }
  
    setLoading(true);
    try {
      const response = await fetch(`https://9cc3-49-36-25-95.ngrok-free.app/search?query=${query}`);
      const data = await response.json();
  
      if (!response.ok) throw new Error(data.error || "Failed to fetch");
  
      setSearchResults({
        communities: data.communities,
        posts: data.posts,
      });
    } catch (error) {
      console.error("Search error:", error);
      Alert.alert("Error", "Failed to search");
    } finally {
      setLoading(false);
    }
  };
  

  return (
    <AuthProvider>
    <ScrollView style={[tw`h-full flex-1 bg-[#FFF8E1]`]}>
      <StatusBar backgroundColor="#FFF8E1" />
      <View style={tw`bg-[#FFF8E1] h-30 pt-3 px-3`}>
        <View style={tw`items-center justify-between flex-row mb-3`}>
          <View style={tw`flex-row items-center justify-center`}>
            <Image
              source={Logo}
              style={[tw`w-12 h-12 `, { tintColor: "#000" }]}
            />
            <Text style={[tw`text-xl`, { fontFamily: "GoogleSans-Bold" }]}>
              Nityasha
            </Text>
          </View>
         <View style={tw` flex-row gap-0  items-center justify-center`}>
         <Wallet />
          <TouchableOpacity onPress={() => navigation.navigate("Notification")}>
            <Bell size={24} color={"#000"} />
          </TouchableOpacity>
         </View>
        </View>
        <Searchbar
          placeholder="Search communities and posts"
          style={tw`bg-[#F7F4E7]`}
          value={searchQuery}
          onChangeText={(text) => {
            setSearchQuery(text);
            handleSearch(text);
          }}
        />
      </View>

      {loading ? (
        <View style={tw`items-center justify-center py-4`}>
          <ActivityIndicator size="large" color="#000" />
        </View>
      ) : searchQuery ? (
        <View style={tw`px-3 mt-5`}>
          {searchResults.communities.length > 0 && (
            <>
              <Text
                style={[tw`text-xl mb-3`, { fontFamily: "GoogleSans-Bold" }]}
              >
                Communities
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={tw`w-full`}
                contentContainerStyle={tw`gap-3`}
              >
                {searchResults.communities.map((community) => (
                  <CommunityCard key={community.id} community={community} />
                ))}
              </ScrollView>
            </>
          )}

          {searchResults.posts.length > 0 && (
            <>
              <Text
                style={[
                  tw`text-xl mt-5 mb-3`,
                  { fontFamily: "GoogleSans-Bold" }
                ]}
              >
                Posts
              </Text>
              <View style={tw`gap-5 mb-5`}>
                {searchResults.posts.map((post) => (
                  <Posts key={post.id} post={post} />
                ))}
              </View>
            </>
          )}

          {searchResults.communities.length === 0 &&
            searchResults.posts.length === 0 && (
              <Text style={tw`text-center text-gray-600 mt-4`}>
                No results found
              </Text>
            )}
        </View>
      ) : (
        <>
          <View
            style={tw`flex items-center justify-between flex-row px-3 mt-5`}
          >
            <Text style={[tw`text-xl`, { fontFamily: "GoogleSans-Bold" }]}>
              Top Community
            </Text>
            <Text style={[tw`text-[#AAAAAC]`, { fontFamily: "" }]}>
              View all
            </Text>
          </View>
          <View>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={tw`w-full mt-3 px-3`}
              contentContainerStyle={tw`gap-3`}
            >
              <CommunityCard />
            </ScrollView>
          </View>
          <Text
            style={[tw`mt-3 pl-3 text-xl`, { fontFamily: "GoogleSans-Bold" }]}
          >
            Trending
          </Text>
          <View style={tw`px-3 mt-5 h-auto gap-5 mb-5`}>
            <Posts />
          </View>
        </>
      )}
    </ScrollView>
    </AuthProvider>
  );
}
