import { TouchableOpacity, View, Alert, Image, FlatList } from "react-native";
import React, { useState, useEffect } from "react";
import tw from "twrnc";
import { StatusBar } from "expo-status-bar";
import { Searchbar, Text, ActivityIndicator } from "react-native-paper";
import { Bell } from "lucide-react-native";
import { KeyboardAvoidingView, Platform, SafeAreaView } from "react-native";
import { supabase } from "@/utils/supabase";
import CommunityCard from "@/components/Home/Community";
import Posts from "@/components/Home/Posts";
import Logo from "@/assets/images/image.png";
import useTokenPush from "@/hooks/tokenpush";
import AuthProvider from "@/hooks/auth-provider";
import Wallet from "@/components/Wallet";
import Profile from "@/components/Home/Profile";

export default function Home({ navigation }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState({ communities: [], posts: [] });

  useTokenPush();



  const defaultPosts = [
    { id: "1", title: "Trending Post 1", content: "This is a trending post" },
    { id: "2", title: "Trending Post 2", content: "This is another trending post" },
  ];

  if (loading) {
    return <ActivityIndicator size="large" />
  }

  return (
    <AuthProvider>
      <SafeAreaView style={tw`flex-1 bg-[#FFF8E1] px-3`}>
        <StatusBar backgroundColor="#FFF8E1" />
        <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={tw`flex-1`}>
          <FlatList
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
            data={searchQuery ? searchResults.posts : defaultPosts}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={{ paddingBottom: 20 }} // Ensures full scrollability
            ListHeaderComponent={
              <View style={tw`bg-[#FFF8E1]`}>
                <View style={tw`bg-[#FFF8E1] h-15 pt-3 px-`}>
                  <View style={tw`flex-row items-center justify-between`}>
                    <View style={tw`flex-row items-center`}>
                      <Image source={Logo} style={[tw`w-12 h-12`, { tintColor: "#000" }]} />
                      <Text style={[tw`text-xl`, { fontFamily: "GoogleSans-Bold" }]}>
                        Nityasha
                      </Text>
                    </View>
                    <View style={tw`flex-row items-center`}>
                      <Wallet />
                      <Profile />
                    </View>
                  </View>
                </View>
                {searchQuery && !loading && (
                  <View style={tw`px-3 mt-5`}>
                    {searchResults.communities.length > 0 && (
                      <>
                        <Text style={[tw`text-xl mb-3`, { fontFamily: "GoogleSans-Bold" }]}>
                          Communities
                        </Text>
                        <FlatList
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          data={searchResults.communities}
                          keyExtractor={(item) => item.id.toString()}
                          contentContainerStyle={tw`gap-3`}
                          renderItem={({ item }) => <CommunityCard community={item} />}
                        />
                      </>
                    )}

                    {searchResults.posts.length === 0 && searchResults.communities.length === 0 && (
                      <Text style={tw`text-center text-gray-600 mt-4`}>
                        No results found
                      </Text>
                    )}
                  </View>
                )}

                {!searchQuery && (
                  <>
                    <FlatList
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      data={[{ id: 1 }]} // Replace with actual community data
                      keyExtractor={(item) => item.id.toString()}
                      contentContainerStyle={tw`px-0 gap-3 mt-6`}
                      renderItem={() => <CommunityCard />}
                    />
                    
                  </>
                )}
              </View>
            }
            renderItem={({ item }) => <Posts post={item} />}
            ListEmptyComponent={<Text style={tw`text-center text-gray-600 mt-4`}>No posts found</Text>}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </AuthProvider>
  );
}
