import React, { useEffect, useRef, useState } from 'react';
import { View, FlatList, Text, StyleSheet, StatusBar } from 'react-native';
import { ref, onValue } from 'firebase/database';
import { db } from '@/lib/Firebase';

const InboxChat = ({ route }) => {
    const { roomN } = route.params;
    const [messages, setMessages] = useState([]);
    const flatListRef = useRef(null);

    // Fetch messages from Firebase
    useEffect(() => {
        const messagesRef = ref(db, `messages/${roomN}`);
        const unsubscribe = onValue(messagesRef, (snapshot) => {
            const data = snapshot.val();
            if (data) {
                const messageArray = Object.values(data).reduce((acc, item) => {
                    if (item && item.id && item.text && item.username) {
                        acc.push(item);
                    }
                    return acc;
                }, []);
                setMessages(messageArray);
            } else {
                setMessages([]);
            }
        });

        return () => unsubscribe();
    }, [roomN]);

    // Scroll to the latest message
    useEffect(() => {
        if (flatListRef.current && messages.length > 0) {
            flatListRef.current.scrollToEnd({ animated: true });
        }
    }, [messages]);

    // Render each message in the chat
    const renderMessageItem = ({ item }) => (
        <View
            style={[
                styles.messageBubble,
                item.username === 'current_user' // Replace 'current_user' with dynamic username
                    ? styles.sentMessage
                    : styles.receivedMessage,
            ]}
        >
            <Text style={styles.messageText}>{item.text}</Text>
            <Text style={styles.messageSender}>{item.username}</Text>
        </View>
    );

    return (
        <View style={styles.container}>
            <StatusBar barStyle="dark-content" backgroundColor="#fff" />
            <FlatList
                ref={flatListRef}
                data={messages}
                keyExtractor={(item, index) => item.id}
                renderItem={renderMessageItem}
                ListEmptyComponent={() => <Text style={styles.noMessages}>No messages to display</Text>}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f9f9f9',
        padding: 10,
    },
    messageBubble: {
        padding: 10,
        borderRadius: 10,
        marginVertical: 5,
        maxWidth: '75%',
    },
    sentMessage: {
        alignSelf: 'flex-end',
        backgroundColor: '#dcf8c6',
    },
    receivedMessage: {
        alignSelf: 'flex-start',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ddd',
    },
    messageText: {
        fontSize: 16,
        color: '#333',
    },
    messageSender: {
        fontSize: 12,
        color: '#888',
        marginTop: 5,
    },
    noMessages: {
        textAlign: 'center',
        marginTop: 20,
        fontSize: 16,
        color: '#666',
    },
});

export default InboxChat;
