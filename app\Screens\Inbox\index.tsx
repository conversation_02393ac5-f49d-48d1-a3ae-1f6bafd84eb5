import React, { useEffect, useState } from 'react';
import { View, FlatList, TouchableOpacity, Text, Image, StyleSheet } from 'react-native';
import { ref, onValue, query, orderByChild } from 'firebase/database';
import { db } from '@/lib/Firebase';
import { useNavigation } from '@react-navigation/native';
import { theme } from '@/app/theme';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ChatPreview {
  chatId: string;
  otherUserId: string;
  lastMessage: string;
  timestamp: number;
  unreadCount: number;
  otherUserName: string;
  otherUserAvatar: string;
  lastMessageStatus: 'sent' | 'delivered' | 'read';
}

export default function Inbox() {
  const [chats, setChats] = useState<ChatPreview[]>([]);
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const navigation = useNavigation();

  // Fetch userId from AsyncStorage
  useEffect(() => {
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          setUserId(parsedSession.userId);
        }
      } catch (error) {
        console.error('Error fetching user session:', error);
      }
    };

    getUserId();
  }, []);

  useEffect(() => {
    if (!userId) return; // Only proceed if userId is available

    const fetchChats = () => {
      const userChatsRef = ref(db, 'dm_messages');
      
      onValue(userChatsRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
          const chatPreviews: ChatPreview[] = [];
          
          Object.entries(data).forEach(([chatId, messages]: [string, any]) => {
            if (chatId.includes(userId)) {
              const messageArray = Object.values(messages);
              const lastMessage = messageArray[messageArray.length - 1];
              const otherUserId = chatId.split('_').find(id => id !== userId) || '';
              
              chatPreviews.push({
                chatId,
                otherUserId,
                lastMessage: lastMessage.text || 'Media message',
                timestamp: lastMessage.timestamp,
                unreadCount: messageArray.filter((msg: any) => 
                  msg.senderId !== userId && msg.status !== 'read'
                ).length,
                otherUserName: 'User',
                otherUserAvatar: 'https://via.placeholder.com/50',
                lastMessageStatus: lastMessage.status
              });
            }
          });
          
          chatPreviews.sort((a, b) => b.timestamp - a.timestamp);
          setChats(chatPreviews);
        }
        setLoading(false);
      });
    };

    fetchChats();
  }, [userId]); // Add userId as dependency

  const renderChatItem = ({ item }: { item: ChatPreview }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => navigation.navigate('DmChat', { 
        otherUserId: item.otherUserId,
        userName: item.otherUserName 
      })}
    >
      <Image source={{ uri: item.otherUserAvatar }} style={styles.avatar} />
      
      <View style={styles.chatInfo}>
        <View style={styles.chatHeader}>
          <Text style={styles.userName}>{item.otherUserName}</Text>
          <Text style={styles.timestamp}>
            {new Date(item.timestamp).toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </Text>
        </View>
        
        <View style={styles.lastMessageContainer}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage}
          </Text>
          {item.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadCount}>{item.unreadCount}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={chats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.chatId}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  listContainer: {
    padding: 16,
  },
  chatItem: {
    flexDirection: 'row',
    padding: 12,
    marginBottom: 8,
    backgroundColor: theme.colors.primaryContainer,
    borderRadius: 12,
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
    fontFamily: 'GoogleSans-Bold',
  },
  timestamp: {
    fontSize: 12,
    color: theme.colors.secondary,
    fontFamily: 'GoogleSans-Regular',
  },
  lastMessageContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.secondary,
    fontFamily: 'GoogleSans-Regular',
  },
  unreadBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    paddingHorizontal: 8,
  },
  unreadCount: {
    color: theme.colors.onPrimary,
    fontSize: 12,
    fontWeight: '600',
    fontFamily: 'GoogleSans-Bold',
  },
});
