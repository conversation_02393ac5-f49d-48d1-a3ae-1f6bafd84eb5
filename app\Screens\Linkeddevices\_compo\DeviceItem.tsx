import { TouchableRipple } from 'react-native-paper';
import { View, Text } from 'react-native';
import tw from 'twrnc';
import { Chrome } from 'lucide-react-native'; // ✅ Import Chrome icon

const DeviceItem = ({ title, time }) => (
  <TouchableRipple onPress={() => {}}>
    <View style={tw`flex-row items-center py-3 px-4`}>
      {/* Chrome Icon with background */}
      <View style={tw`w-12 h-12 rounded-full mr-4 items-center justify-center bg-red-500`}>
        <Chrome color="white" size={26} />
      </View>

      {/* Device Info */}
      <View style={tw`flex-1`}>
        <Text style={tw`text-gray-800 font-medium text-base`}>
          {title}
        </Text>
        <Text style={tw`text-gray-500 text-sm`}>
          {time}
        </Text>
      </View>
    </View>
  </TouchableRipple>
);

export default DeviceItem;
