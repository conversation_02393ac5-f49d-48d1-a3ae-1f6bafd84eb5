import { View, Text, ImageBackground, TouchableOpacity, ScrollView, Image } from 'react-native'
import React from 'react'
import tw from 'twrnc'
import { Ionicons } from '@expo/vector-icons'
import DeviceItem from './_compo/DeviceItem';


export default function LinkedDevices() {
    return (
        <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1`}>
            <ScrollView style={tw`flex-1 pt-5`} showsVerticalScrollIndicator={false}>
                {/* Header */}
                <View style={tw`flex-row items-center px-4 pb-6`}>
                    <TouchableOpacity style={tw`mr-4`}>
                        <Ionicons name="arrow-back" size={24} color="#000" />
                    </TouchableOpacity>
                    <Text style={tw`text-xl font-semibold text-black`}>Linked devices</Text>
                </View>

                {/* Illustration Section */}
                <View style={tw`items-center px-8 py-8`}>
                    <View style={tw`flex-row items-center justify-center mb-6`}>
                        {/* Phone */}
                        <View style={tw`w-16 h-24 bg-green-200 rounded-lg border-2 border-black mr-4 relative`}>
                            <View style={tw`w-2 h-2 bg-black rounded-full absolute top-2 left-2`} />
                            <View style={tw`w-8 h-6 bg-white rounded border border-black absolute top-6 left-2`}>
                                <View style={tw`flex-1 justify-center items-center`}>
                                    <View style={tw`w-4 h-0.5 bg-black mb-1`} />
                                    <View style={tw`w-4 h-0.5 bg-black mb-1`} />
                                    <View style={tw`w-4 h-0.5 bg-black`} />
                                </View>
                            </View>
                            <View style={tw`w-4 h-4 bg-black rounded-full absolute bottom-2 left-6`} />
                        </View>

                        {/* Connection Icons */}
                        <View style={tw`flex-row items-center mx-2`}>
                            <View style={tw`w-8 h-8 bg-white rounded-full border-2 border-black items-center justify-center mr-2`}>
                                <View style={tw`w-4 h-3 bg-black rounded`} />
                            </View>
                            <View style={tw`w-8 h-8 bg-white rounded-full border-2 border-black items-center justify-center mr-2`}>
                                <Ionicons name="heart" size={16} color="#22c55e" />
                            </View>
                            <View style={tw`w-8 h-8 bg-white rounded-full border-2 border-black items-center justify-center`}>
                                <Text style={tw`text-xs font-bold`}>a~</Text>
                            </View>
                        </View>

                        {/* Laptop */}
                        <View style={tw`ml-4`}>
                            <View style={tw`w-20 h-12 bg-green-400 rounded-t-lg border-2 border-black relative`}>
                                <View style={tw`absolute inset-2 bg-black rounded grid grid-cols-4 gap-0.5 p-1`}>
                                    {[...Array(16)].map((_, i) => (
                                        <View key={i} style={tw`bg-green-400 rounded-sm`} />
                                    ))}
                                </View>
                            </View>
                            <View style={tw`w-24 h-2 bg-green-400 rounded-b-lg border-2 border-black border-t-0 -mx-1`} />
                        </View>
                    </View>

                    {/* Description Text */}
                    <Text style={tw`text-gray-600 text-center mb-2`}>
                        You can link other devices to this account.{' '}
                    </Text>
                </View>

                {/* Link Device Button */}
                <View style={tw`px-6 mb-8`}>
                    <TouchableOpacity style={tw`bg-green-500 py-3 rounded-full border-2`} onPress={() => navigation.navigate('Linkeddevics')}>
                        <Text style={tw`text-black text-center font-semibold text-sm`}>
                            Link a device
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Device Status Section */}
                <View style={tw``}>
                    <View style={tw`px-4`}>
                        <Text style={tw`text-lg font-semibold text-gray-800 mb-2`}>Device Status</Text>
                        <Text style={tw`text-gray-600 mb-6`}>Tap a device to log out.</Text>

                    </View>
                    {/* Device List */}
                    <View style={tw``}>
                        {/* First Chrome Device */}
                        <DeviceItem title="Google Chrome (Windows)" time="Last active today at 5:12 PM" />


                    </View>
                </View>

                {/* Footer Message */}
                <View style={tw`px-6 py-8 items-center`}>
                    <View style={tw`flex-row items-center`}>
                        <Text style={tw`text-gray-600 text-center text-sm`}>
                            Your personal messages are{' '}
                            <Text style={tw`text-green-600 font-medium`}>end-to-end encrypted</Text>
                            {' '}on all your devices.
                        </Text>
                    </View>
                </View>
            </ScrollView>
        </ImageBackground>
    )
}