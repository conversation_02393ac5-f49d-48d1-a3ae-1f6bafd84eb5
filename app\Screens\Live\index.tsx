import React from 'react';
import { View, Text, FlatList, Dimensions, StatusBar } from 'react-native';
import tw from 'twrnc';

const { height, width } = Dimensions.get('window'); // Get screen height and width

const data = [
  { id: '1', content: 'Video 1' },
  { id: '2', content: 'Video 2' },
  { id: '3', content: 'Video 3' },
  { id: '4', content: 'Video 4' },
  // Add more content or video items here...
];

export default function Index({ route }) {
  const { id } = route.params; // Category ID passed from the route

  return (
    <View style={[tw`flex-1 bg-black`]}>
      <StatusBar hidden />
      <FlatList
        data={data}
        renderItem={({ item }) => (
          <View style={[tw`w-full`, { height, width }]}>
            <View style={tw`flex-1 justify-center items-center bg-black`}>
              <Text style={tw`text-white text-2xl`}>{item.content}</Text>
            </View>
          </View>
        )}
        keyExtractor={(item) => item.id}
        horizontal={false}
        pagingEnabled={true}
        snapToInterval={height}
        decelerationRate={'normal'}
      />
    </View>
  );
}
