import { View, Text, SafeAreaView, TouchableOpacity, TextInput, StatusBar, Image, ScrollView, ImageBackground, Keyboard } from 'react-native';
import React, { useState, useEffect } from 'react';
import tw from 'twrnc';
import { ChevronLeft, X, Eye, EyeOff } from 'lucide-react-native';
import { useFonts } from 'expo-font';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useToast } from '../../../context/ToastContext';
import { useKeyboardAnimation } from 'react-native-keyboard-controller';
import { ActivityIndicator } from 'react-native';

export default function Login({ navigation }) {
  const [loaded] = useFonts({
    'Satoshi-Variable': require('@/assets/fonts/Satoshi-Medium.otf'),
  });

  const { showToast } = useToast();
  const { height } = useKeyboardAnimation();

  // State for email and password
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [passwordVisible, setPasswordVisible] = useState(false); // State to toggle password visibility
  const [loading, setLoading] = useState(false);

  const isFormValid = email.trim() !== '' && password.trim() !== '';

  // Function to handle regular Login
  const handleLogin = async () => {
    if (!email || !password) {
      showToast('Please enter both email and password.', 'error');
      return;
    }

    setLoading(true); // Start loading

    try {
      const response = await fetch('https://nityasha.vercel.app/api/v1/users/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok) {
        if (!data.userId) {
          showToast('User ID not received from the server.', 'error');
          return;
        }

        await AsyncStorage.setItem('userSession', JSON.stringify({ userId: data.userId }));
        await AsyncStorage.setItem('userDetails', JSON.stringify(data.user));
        navigation.navigate('BottomTabs');
      } else {
        showToast(data.message, 'error');
      }
    } catch (error) {
      console.error('Error logging in:', error);
      showToast('Something went wrong. Please try again later.', 'error');
    } finally {
      setLoading(false); // Stop loading
    }
  };


  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 px-5 flex items-center justify-between`}>
      <StatusBar barStyle={'dark-content'} translucent />
      <ScrollView
        style={tw`flex-1 w-full`}
        contentContainerStyle={tw`flex-1`}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
      >
        <View style={tw`w-full`}>
          <View style={tw`flex items-center flex-row gap-5`}>
            <TouchableOpacity onPress={() => navigation.goBack()} style={tw`flex items-start justify-start pt-10`}>
              <X size={30} color={"#000"} />
            </TouchableOpacity>
          </View>
          <View style={tw`flex mt-10 gap-5`}>
            <View style={tw`bg-[#222222] rounded-full w-15 h-15 overflow-hidden`}>
              <Image source={require('@/assets/images/favicon.png')} style={tw`w-full h-full`} />
            </View>
            <Text style={[tw`w-full flex items-center justify-center text-2xl text-[#0E0E0E]`, { fontFamily: 'Geist-ExtraBold' }]}>Welcome To Nityasha</Text>
            <Text style={[tw`w-full flex items-center justify-center`, { fontFamily: 'Geist-Regular' }]}>
              Please enter your login number and password.
            </Text>
            <TextInput
              placeholder='Your Phone Number'
              keyboardType='number-pad'
              value={email}
              onChangeText={setEmail}
              style={[tw`w-full px-5 py-4 rounded-[20px] bg-white`, { fontFamily: 'Geist-Regular' }]}
            />
            <View style={tw`relative w-full`}>
              <TextInput
                placeholder='Your Password'
                secureTextEntry={!passwordVisible}
                value={password}
                onChangeText={setPassword}
                style={[tw`w-full px-5 py-4 rounded-[20px] bg-white`, { fontFamily: 'Geist-Regular' }]}
              />
              <TouchableOpacity
                style={tw`absolute right-3 top-3`}
                onPress={() => setPasswordVisible(!passwordVisible)}
              >
                {passwordVisible ? <EyeOff size={24} color="#666" /> : <Eye size={24} color="#666" />}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
      <TouchableOpacity
        onPress={() => {
          if (!isFormValid || loading) return;
          Keyboard.dismiss();
          handleLogin();
        }}
        style={[
          tw`w-full rounded-full flex items-center justify-center py-4 mb-5`,
          isFormValid && !loading ? tw`bg-black` : tw`bg-gray-400`
        ]}
        disabled={!isFormValid || loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={[tw`text-[16px] text-white text-center`, { fontFamily: 'Geist-SemiBold' }]}>
            Login
          </Text>
        )}
      </TouchableOpacity>

    </ImageBackground>
  );
}
