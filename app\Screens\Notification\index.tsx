import { View, Text, ScrollView, TouchableOpacity } from 'react-native'
import React from 'react'
import tw from 'twrnc';
import { ChevronLeft } from 'lucide-react-native';
import Bell from '@/components/icons/Bell';
import Svg, { Path } from 'react-native-svg';
import NotificationCard from '@/components/NotificationCard';

export default function Notification({ navigation }) {
  return (
    <ScrollView style={tw`h-full bg-white px-5 pt-5`}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={tw`flex items-start justify-between w-full flex-row gap-1`}>
        <View style={tw`flex flex-row gap-2 items-center justify-center`}>
          <ChevronLeft size={20} color={"#000"} />
          <Text style={[tw`font-medium text-base h-full flex items-center justify-center`, { fontFamily: 'Satoshi-Variable' }]}>Notification</Text>
        </View>
        <View style={tw`flex flex-row bg-[#F5F5F5] w-[50px] rounded-full`}>
          <View style={tw`w-[24px] h-[24px] rounded-full flex items-center justify-center`}>
            <Svg width="12" height="12" viewBox="0 0 12 12" fill="none">
              <Path
                d="M9.29279 10H1.99979C1.95336 10 1.90785 9.9871 1.86836 9.9627C1.82886 9.93829 1.79695 9.90336 1.77618 9.86184C1.75542 9.82031 1.74663 9.77382 1.7508 9.72758C1.75497 9.68134 1.77193 9.63718 1.79979 9.60003L1.99979 9.33353V5.00003C1.99979 4.33503 2.16179 3.70803 2.44929 3.15653L0.696289 1.40403L1.40379 0.696533L11.3033 10.5965L10.5958 11.3035L9.29279 10ZM3.20379 3.91103C3.0686 4.25817 2.99941 4.6275 2.99979 5.00003V9.00003H8.29279L3.20379 3.91103ZM9.99979 7.89303L8.99979 6.89303V5.00003C8.99988 4.47387 8.86159 3.95693 8.59879 3.50109C8.33599 3.04526 7.95792 2.66656 7.50252 2.40299C7.04713 2.13943 6.53042 2.00028 6.00425 1.99949C5.47809 1.99871 4.96097 2.13633 4.50479 2.39853L3.77979 1.67253C4.38216 1.27064 5.08232 1.03981 5.8056 1.00465C6.52887 0.969496 7.24813 1.13134 7.88663 1.47292C8.52513 1.8145 9.05894 2.323 9.43109 2.94418C9.80325 3.56535 9.99981 4.2759 9.99979 5.00003V7.89303ZM4.74979 10.5H7.24979C7.24979 10.8316 7.11809 11.1495 6.88367 11.3839C6.64925 11.6183 6.33131 11.75 5.99979 11.75C5.66827 11.75 5.35033 11.6183 5.11591 11.3839C4.88149 11.1495 4.74979 10.8316 4.74979 10.5V10.5Z"
                fill="#9CA1A7"
              />
            </Svg>
          </View>
          <View style={tw`w-[24px] h-[24px] bg-[#000] rounded-full flex items-center justify-center`}>
            <Svg width={12} height={12} viewBox="0 0 12 16" fill="none">
              <Path
                d="M10 6.16634C10 5.10548 9.57858 4.08806 8.82844 3.33791C8.07829 2.58777 7.06088 2.16634 6.00001 2.16634C4.93914 2.16634 3.92173 2.58777 3.17158 3.33791C2.42144 4.08806 2.00001 5.10548 2.00001 6.16634V11.4997H10V6.16634ZM11.3333 11.9443L11.6 12.2997C11.6372 12.3492 11.6598 12.4081 11.6653 12.4697C11.6709 12.5314 11.6592 12.5934 11.6315 12.6487C11.6038 12.7041 11.5612 12.7507 11.5086 12.7832C11.4559 12.8158 11.3952 12.833 11.3333 12.833H0.666677C0.604773 12.833 0.544092 12.8158 0.491433 12.7832C0.438775 12.7507 0.396219 12.7041 0.368535 12.6487C0.34085 12.5934 0.329131 12.5314 0.334691 12.4697C0.34025 12.4081 0.362868 12.3492 0.40001 12.2997L0.666677 11.9443V6.16634C0.666677 4.75185 1.22858 3.3953 2.22877 2.39511C3.22897 1.39491 4.58552 0.833008 6.00001 0.833008C7.4145 0.833008 8.77105 1.39491 9.77125 2.39511C10.7714 3.3953 11.3333 4.75185 11.3333 6.16634V11.9443ZM4.33334 13.4997H7.66668C7.66668 13.9417 7.49108 14.3656 7.17852 14.6782C6.86596 14.9907 6.44204 15.1663 6.00001 15.1663C5.55798 15.1663 5.13406 14.9907 4.8215 14.6782C4.50894 14.3656 4.33334 13.9417 4.33334 13.4997V13.4997Z"
                fill="#fff"  // Use color prop here
              />
            </Svg>
          </View>
        </View>
      </TouchableOpacity>
      <View style={tw`flex items-center justify-center gap-5 py-5`}>
        <NotificationCard navigation={undefined} />
      </View>
    </ScrollView>
  )
}