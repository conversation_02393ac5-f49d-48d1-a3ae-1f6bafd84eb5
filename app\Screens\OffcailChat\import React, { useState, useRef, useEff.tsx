import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  Platform,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  Alert,
  Modal,
  
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import tw from 'twrnc';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase';
import { ref, onValue, set, update, onDisconnect } from 'firebase/database';
import { useReanimatedKeyboardAnimation , KeyboardAvoidingView} from 'react-native-keyboard-controller';

const API_BASE = 'https://api.search.nityasha.com';

// Firebase chat collection prefixes
const FIREBASE_OFFICIAL_CHAT_PREFIX = 'official_chats';
const FIREBASE_OFFICIAL_MESSAGES_PREFIX = 'official_messages';

// Move generateUUID outside the component
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Define message types
interface ChatMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  isUser?: boolean; // For UI rendering
  status?: 'sent' | 'delivered' | 'read' | 'error';
  isRead?: boolean;
  readAt?: number;
  isAutomated?: boolean; // Flag for automated messages
}

interface FirebaseMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  status?: 'sent' | 'delivered' | 'read' | 'error';
  isRead?: boolean;
  readAt?: number;
  isAutomated?: boolean; // Flag for automated messages
}

// Define user status interface for type checking
interface UserStatus {
  state: 'online' | 'offline';
  lastSeen: number;
  isTyping?: boolean;
  typingInChatWith?: string; // ID of the user they're typing to
}

// Function to fetch chat history from Firebase
const fetchChatHistory = (userId: string, officialId: string, setMessages: React.Dispatch<React.SetStateAction<ChatMessage[]>>, setLoadError: React.Dispatch<React.SetStateAction<string | null>>, setIsLoadingHistory: React.Dispatch<React.SetStateAction<boolean>>) => {
  try {
    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, officialId].sort().join('_');
    const chatRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}`);

    // Listen for messages in real-time
    const unsubscribe = onValue(chatRef, (snapshot) => {
      setIsLoadingHistory(false);
      const data = snapshot.val();

      if (data) {
        // Convert object to array and sort by timestamp
        const messageArray = Object.values(data)
          .filter((msg: any) => msg.text) // Filter out messages without content
          .map((msg: any) => ({
            id: msg.id,
            text: msg.text,
            timestamp: msg.timestamp,
            senderId: msg.senderId,
            receiverId: msg.receiverId,
            isUser: msg.senderId === userId,
            status: msg.status || 'sent',
            isRead: msg.isRead || false,
            readAt: msg.readAt || null,
            isAutomated: msg.isAutomated || false
          }));

        // Sort messages by timestamp (newest last)
        const sortedMessages = messageArray.sort((a: any, b: any) => a.timestamp - b.timestamp);
        setMessages(sortedMessages);

        // Mark messages as read
        markMessagesAsRead(chatRoomId, userId, officialId);
      } else {
        setMessages([]);
      }
    }, (error) => {
      console.error('Error fetching chat history:', error);
      setLoadError('Failed to load chat history');
      setIsLoadingHistory(false);
    });

    // Return unsubscribe function
    return unsubscribe;
  } catch (error) {
    console.error('Failed to set up chat history listener:', error);
    setLoadError('Failed to load chat history');
    setIsLoadingHistory(false);
    return () => {};
  }
};

// Function to mark messages as read
const markMessagesAsRead = (chatRoomId: string, _userId: string, officialId: string) => {
  try {
    const chatRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}`);

    // Get all messages in the chat room
    onValue(chatRef, (snapshot) => {
      const data = snapshot.val();
      if (!data) return;

      // Find unread messages sent by the official account
      Object.entries(data).forEach(([key, value]: [string, any]) => {
        if (value.senderId === officialId && (!value.isRead || value.status !== 'read')) {
          // Mark as read
          const messageRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${key}`);
          update(messageRef, {
            isRead: true,
            readAt: Date.now(),
            status: 'read'
          });
        }
      });
    }, { onlyOnce: true }); // Only run once to avoid infinite loops
  } catch (error) {
    console.error('Error marking messages as read:', error);
  }
};

// Define route params interface
interface RouteParams {
  accountData?: {
    id: string;
    name: string;
    logo: string;
    online: boolean;
    verified: boolean;
  };
}

interface NavigationProps {
  navigation: any;
  route: { params?: RouteParams };
}

export default function App({ navigation, route }: NavigationProps) {
  const [userId, setUserId] = useState<string | null>(null);
  const { accountData } = route.params || {};
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const flatListRef = useRef<FlatList | null>(null);
  const messageListenerRef = useRef<(() => void) | null>(null);
  const userStatusRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Use Reanimated Keyboard Animation hook
  const { height: keyboardHeight } = useReanimatedKeyboardAnimation();

  // Get userId from AsyncStorage and set up user status
  useEffect(() => {
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          const id = parsedSession?.userId || userSession;
          setUserId(id);

          // Set up user online status
          if (id) {
            userStatusRef.current = ref(db, `users/${id}/status`);

            // Set user as online
            const userStatus: UserStatus = {
              state: 'online',
              lastSeen: Date.now(),
              isTyping: false
            };
            update(userStatusRef.current, userStatus);

            // Set up disconnect handler
            const offlineStatus: UserStatus = {
              state: 'offline',
              lastSeen: Date.now(), // Using Date.now() as serverTimestamp() returns a placeholder
              isTyping: false
            };
            onDisconnect(userStatusRef.current).update(offlineStatus);
          }
        } else {
          console.error('No user session found');
          Alert.alert('Error', 'Please login again');
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error getting user session:', error);
        Alert.alert('Error', 'Failed to get user information');
        navigation.goBack();
      }
    };

    getUserId();

    // Cleanup function
    return () => {
      if (userStatusRef.current) {
        const offlineStatus: UserStatus = {
          state: 'offline',
          lastSeen: Date.now(),
          isTyping: false
        };
        update(userStatusRef.current, offlineStatus);
      }
    };
  }, []);

  // Set up Firebase listener for messages
  const setupMessageListener = useCallback(() => {
    if (!userId || !accountData?.id) return;

    // Clear any existing listener
    if (messageListenerRef.current) {
      messageListenerRef.current();
    }

    // Set up new listener
    messageListenerRef.current = fetchChatHistory(
      userId,
      accountData.id,
      setMessages,
      setLoadError,
      setIsLoadingHistory
    );

    return () => {
      if (messageListenerRef.current) {
        messageListenerRef.current();
      }
    };
  }, [userId, accountData?.id]);

  // Initialize message listener
  useEffect(() => {
    if (userId && accountData?.id) {
      setupMessageListener();
    }

    return () => {
      if (messageListenerRef.current) {
        messageListenerRef.current();
      }
    };
  }, [userId, accountData?.id, setupMessageListener]);

  // Listen for official account online status and typing indicator
  useEffect(() => {
    if (!accountData?.id) return;

    const officialStatusRef = ref(db, `users/${accountData.id}/status`);

    const unsubscribe = onValue(officialStatusRef, (snapshot) => {
      const status = snapshot.val();
      if (status && route.params?.accountData) {
        // Update the accountData with the latest online status
        route.params.accountData.online = status.state === 'online';

        // Update typing indicator - only if they're typing to this user
        setOtherUserTyping(!!status.isTyping && status.typingInChatWith === userId);
      }
    });

    return () => unsubscribe();
  }, [accountData?.id, route.params]);

  const fetchMessages = useCallback(() => {
    if (userId && accountData?.id) {
      setIsLoadingHistory(true);
      // Re-initialize the Firebase listener
      if (messageListenerRef.current) {
        messageListenerRef.current();
      }
      messageListenerRef.current = fetchChatHistory(userId, accountData.id, setMessages, setLoadError, setIsLoadingHistory);
    }
  }, [userId, accountData?.id]);

  // Function to clear chat history
  const clearChatHistory = useCallback(async () => {
    if (!userId || !accountData?.id) {
      Alert.alert('Error', 'Cannot clear chat history at this time');
      return;
      }

    try {
      // Create a chat room ID
      const chatRoomId = [userId, accountData.id].sort().join('_');

      // Clear messages in official_chats collection
      const chatRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}`);
      await set(chatRef, null);

      // Clear messages in official_messages collection
      const messagesRef = ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}`);
      await set(messagesRef, null);

      // Clear local messages state
      setMessages([]);

      // Close menu
      setMenuVisible(false);

      // Show success message
      Alert.alert('Success', 'Chat history has been cleared');
    } catch (error) {
      console.error('Error clearing chat history:', error);
      Alert.alert('Error', 'Failed to clear chat history. Please try again.');
    }
  }, [userId, accountData?.id]);

  const renderContent = () => {
    if (!userId || !accountData?.id) {
      return (
        <View style={tw`flex-1 items-center justify-center`}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={tw`text-gray-500 text-center mt-4`}>Loading chat...</Text>
        </View>
      );
    }

    return (
      <>
        {isLoadingHistory ? (
          <View style={tw`flex-1 items-center justify-center`}>
            <ActivityIndicator size="large" color="#0000ff" />
            <Text style={tw`text-gray-500 text-center mt-4`}>Loading messages...</Text>
          </View>
        ) : loadError ? (
          <View style={tw`flex-1 items-center justify-center p-4`}>
            <Text style={tw`text-red-500 text-center`}>{loadError}</Text>
            <TouchableOpacity
              style={tw`mt-4 bg-blue-500 px-4 py-2 rounded-full`}
              onPress={fetchMessages}
            >
              <Text style={tw`text-white`}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={[...messages].reverse()}
            renderItem={renderMessage}
            keyExtractor={item => String(item.id)} // Ensure key is always a string
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'flex-end',
              padding: 15
            }}
            inverted
            showsVerticalScrollIndicator={false}
            onRefresh={fetchMessages}
            refreshing={isLoadingHistory}
            removeClippedSubviews={false} // Add this to prevent rendering issues
          />
        )}
      </>
    );
  };

  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    }
  };

  // Function to send automated offline response
  const sendAutomatedOfflineResponse = async (_userMessageId: string, _userMessageText: string) => {
    if (!userId || !accountData?.id) return;

    const responseId = generateUUID();
    const timestamp = Date.now() + 1000; // Add 1 second to ensure it appears after user message

    const automatedMsg: FirebaseMessage = {
      id: responseId,
      text: `Thank you for your message. The official account is currently offline. We will respond to your query within 5-24 hours.`,
      senderId: accountData.id,
      receiverId: userId,
      timestamp: timestamp,
      status: 'sent',
      isRead: true,
      isAutomated: true // Mark as automated message
    };

    try {
      // Create a chat room ID
      const chatRoomId = [userId, accountData.id].sort().join('_');

      // Add to UI
      setMessages(prev => [...prev, {...automatedMsg, isUser: false}]);

      // Save to Firebase
      await set(ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${responseId}`), automatedMsg);
      await set(ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}/${responseId}`), automatedMsg);

      // Scroll to bottom to show the automated message
      scrollToBottom();
    } catch (error) {
      console.error('Failed to send automated response:', error);
    }
  };

  const sendMessage = async () => {
    if (message.trim() === '') return;

    const messageId = generateUUID();
    const timestamp = Date.now();

    const newMsg: FirebaseMessage = {
      id: messageId,
      text: message.trim(),
      senderId: userId || '',
      receiverId: accountData?.id || '',
      timestamp: timestamp,
      status: 'sent',
      isRead: false
    };

    try {
      // Optimistically add message to UI
      setMessages(prev => [...prev, {...newMsg, isUser: true}]);
      setMessage('');
      scrollToBottom();

      if (!accountData?.id || !userId) {
        throw new Error('Account data or user ID is missing');
      }

      // Create a chat room ID by sorting and joining the user IDs
      const chatRoomId = [userId, accountData.id].sort().join('_');

      // Save message to Firebase
      await set(ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
      await set(ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), newMsg);

      // Send automated response if official account is offline
      if (!accountData.online) {
        // Wait a moment before sending the automated response
        setTimeout(() => {
          sendAutomatedOfflineResponse(messageId, message.trim());
        }, 500);
      }

      // Also send to the API for backward compatibility if needed
      try {
        await axios.post(`${API_BASE}/chat/${userId}/${accountData.id}`, {
          content: message.trim(),
          senderId: userId,
          receiverId: accountData.id
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
      } catch (apiError) {
        console.warn('API fallback failed, but Firebase message was sent:', apiError);
        // Continue since Firebase message was sent successfully
      }
    } catch (err) {
      console.error('Sending message failed:', err);
      // Remove the optimistically added message
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      setMessage(message);
      Alert.alert(
        'Error',
        'Failed to send message. Please try again.'
      );
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    // Add special styling for automated messages
    <>
      <View
        style={[
          styles.messageContainer,
          item.isUser ? styles.userMessage : styles.otherMessage,
          item.isAutomated && styles.automatedMessage
        ]}
      >
        <Text style={[styles.messageText, item.isAutomated && styles.automatedMessageText]}>{item.text}</Text>
        {item.isAutomated && (
          <Text style={styles.automatedLabel}>Automated Response</Text>
        )}
      </View>
    </>
  );

  return (
    <KeyboardAvoidingView
      keyboardVerticalOffset={100}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />

        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={styles.logoContainer}>
              <Image source={{ uri: accountData?.logo || 'https://via.placeholder.com/150' }} style={tw`w-full h-full`} />
            </View>
            <View>
              <Text style={styles.headerTitle}>{accountData?.name || 'User'}</Text>
              <Text style={styles.headerSubtitle}>
                {accountData?.online ? 'Online' : 'Offline'}{accountData?.verified && ' ✓'}
              </Text>
            </View>
          </View>
          <TouchableOpacity onPress={() => setMenuVisible(true)}>
            <Feather name="more-horizontal" size={24} color="black" />
          </TouchableOpacity>

          {/* Menu Modal */}
          <Modal
            visible={menuVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setMenuVisible(false)}
          >
            <TouchableOpacity
              style={styles.modalOverlay}
              activeOpacity={1}
              onPress={() => setMenuVisible(false)}
            >
              <View style={styles.menuContainer}>
                {accountData?.online && (
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={clearChatHistory}
                  >
                    <Feather name="trash-2" size={20} color="#FF3B30" />
                    <Text style={styles.menuItemText}>Clear Chat History</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => setMenuVisible(false)}
                >
                  <Feather name="x" size={20} color="#007AFF" />
                  <Text style={styles.menuItemText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </Modal>
        </View>

        {/* Chat Messages */}
        {renderContent()}

        {/* Typing Indicator */}
        {otherUserTyping && accountData?.online && (
          <View style={styles.typingIndicator}>
            <Text style={styles.typingText}>{accountData?.name || 'User'} is typing...</Text>
          </View>
        )}

        {/* Message Input */}
        <View
          style={[
            styles.inputContainer,
            { marginBottom: keyboardHeight }, // Adjust for keyboard height
          ]}
        >
          <View style={styles.inputWrapper}>
            <TouchableOpacity style={styles.plusButton}>
              <Feather name="plus" size={24} color="#4CAF50" />
            </TouchableOpacity>
            <TextInput
              style={styles.input}
              placeholder="Type a message..."
              placeholderTextColor="#999"
              value={message}
              onChangeText={(text) => {
                setMessage(text);

                // Update typing status
                if (!isTyping && text.trim().length > 0) {
                  setIsTyping(true);
                  if (userStatusRef.current) {
                    update(userStatusRef.current, {
                      isTyping: true,
                      typingInChatWith: accountData?.id // Add the recipient ID
                    });
                  }
                } else if (isTyping && text.trim().length === 0) {
                  setIsTyping(false);
                  if (userStatusRef.current) {
                    update(userStatusRef.current, {
                      isTyping: false,
                      typingInChatWith: null
                    });
                  }
                }

                // Clear any existing timeout
                if (typingTimeoutRef.current) {
                  clearTimeout(typingTimeoutRef.current);
                }

                // Set a timeout to stop typing indicator after 3 seconds of inactivity
                typingTimeoutRef.current = setTimeout(() => {
                  if (isTyping) {
                    setIsTyping(false);
                    if (userStatusRef.current) {
                      update(userStatusRef.current, {
                        isTyping: false,
                        typingInChatWith: null
                      });
                    }
                  }
                }, 3000);
              }}
              onFocus={scrollToBottom}
            />
          </View>
          <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
            <Feather name="send" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c5e8c8',
  },
  typingIndicator: {
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 10,
    marginHorizontal: 15,
    marginBottom: 5,
  },
  typingText: {
    fontSize: 12,
    color: '#2c5e2e',
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingRight: 10,
    paddingTop: 60,
  },
  menuContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 5,
    width: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
  },
  menuItemText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#333',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBlockColor: '#B0DEBA',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 45,
    height: 45,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    overflow: 'hidden',
  },
  headerTitle: {
    fontSize: 15,
    fontFamily: 'Helvetica_bold',
    color: '#2c5e2e',
  },
  headerSubtitle: {
    fontSize: 13,
    color: '#2c5e2e',
    fontFamily: 'Helvetica_bold',
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 20,
    marginVertical: 4,
  },
  userMessage: {
    backgroundColor: '#DCF8C6',
    alignSelf: 'flex-end',
    marginLeft: '20%',
  },
  otherMessage: {
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    marginRight: '20%',
  },
  messageText: {
    fontSize: 16,
  },
  automatedMessage: {
    backgroundColor: '#F0F8FF', // Light blue background for automated messages
    borderWidth: 1,
    borderColor: '#ADD8E6',
  },
  automatedMessageText: {
    color: '#4682B4', // Steel blue color for text
  },
  automatedLabel: {
    fontSize: 10,
    color: '#778899', // Slate gray for the label
    marginTop: 4,
    fontStyle: 'italic',
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 10,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    marginRight: 10,
  },
  plusButton: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    paddingVertical: 10,
    paddingRight: 15,
    color: '#333',
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageList: {
    flexGrow: 1,
    justifyContent: 'flex-end',
    padding: 15,
  },
});
