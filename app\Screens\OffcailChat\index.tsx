import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  Platform,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  Alert,
  Modal,
  
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import tw from 'twrnc';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase';
import { ref, onValue, set, update, onDisconnect } from 'firebase/database';
import { getStorage, ref as storageRef, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useReanimatedKeyboardAnimation , KeyboardAvoidingView} from 'react-native-keyboard-controller';
import * as ImagePicker from 'expo-image-picker';

const API_BASE = 'https://api.search.nityasha.com';

// Firebase chat collection prefixes
const FIREBASE_OFFICIAL_CHAT_PREFIX = 'official_chats';
const FIREBASE_OFFICIAL_MESSAGES_PREFIX = 'official_messages';

// Move generateUUID outside the component
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Define message types
interface ChatButton {
  text: string;
  value: string; // Value to send back when button is tapped
}

interface ChatMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  isUser?: boolean; // For UI rendering
  status?: 'sent' | 'delivered' | 'read' | 'error';
  isRead?: boolean;
  readAt?: number;
  isAutomated?: boolean; // Flag for automated messages
  buttons?: ChatButton[]; // Add buttons property here
  isLocationRequest?: boolean; // Add flag for location request messages
  type?: string; // Add type property
  imageUrl?: string; // Add imageUrl property
}

interface FirebaseMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  status?: 'sent' | 'delivered' | 'read' | 'error';
  isRead?: boolean;
  readAt?: number;
  isAutomated?: boolean; // Flag for automated messages
  buttons?: ChatButton[]; // Add buttons property here
  isLocationRequest?: boolean; // Add flag for location request messages
}

// Define user status interface for type checking
interface UserStatus {
  state: 'online' | 'offline';
  lastSeen: number;
  isTyping?: boolean;
  typingInChatWith?: string; // ID of the user they're typing to
}

// Get Firebase Storage instance
const storage = getStorage();

// Function to fetch chat history from Firebase
const fetchChatHistory = (userId: string, officialId: string, setMessages: React.Dispatch<React.SetStateAction<ChatMessage[]>>, setLoadError: React.Dispatch<React.SetStateAction<string | null>>, setIsLoadingHistory: React.Dispatch<React.SetStateAction<boolean>>) => {
  try {
    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, officialId].sort().join('_');
    const chatRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}`);

    // Listen for messages in real-time
    const unsubscribe = onValue(chatRef, (snapshot) => {
      setIsLoadingHistory(false);
      const data = snapshot.val();

      if (data) {
        // Convert object to array and sort by timestamp
        const messageArray = Object.values(data)
           // Modified filter to include messages with text OR image messages
          .filter((msg: any) => msg.text || (msg.type === 'image' && msg.imageUrl))
          .map((msg: any) => ({
            id: msg.id,
            text: msg.text || '', // Ensure text is at least an empty string for type safety
            timestamp: msg.timestamp,
            senderId: msg.senderId,
            receiverId: msg.receiverId,
            isUser: msg.senderId === userId,
            status: msg.status || 'sent',
            isRead: msg.isRead || false,
            readAt: msg.readAt || null,
            isAutomated: msg.isAutomated || false,
            isLocationRequest: msg.isLocationRequest || false,
            buttons: msg.buttons || undefined,
            type: msg.type || 'text', // Default to 'text' if type is not specified
            imageUrl: msg.imageUrl || undefined, // Include imageUrl
          }));

        // Sort messages by timestamp (newest last)
        const sortedMessages = messageArray.sort((a: any, b: any) => a.timestamp - b.timestamp);
        setMessages(sortedMessages);
        console.log('fetchChatHistory: Fetched and processed messages:', sortedMessages); // Log the fetched messages

        // Mark messages as read
        markMessagesAsRead(chatRoomId, userId, officialId);
      } else {
        setMessages([]);
        console.log('fetchChatHistory: No messages found.');
      }
    }, (error) => {
      console.error('Error fetching chat history:', error);
      setLoadError('Failed to load chat history');
      setIsLoadingHistory(false);
    });

    // Return unsubscribe function
    return unsubscribe;
  } catch (error) {
    console.error('Failed to set up chat history listener:', error);
    setLoadError('Failed to load chat history');
    setIsLoadingHistory(false);
    return () => {};
  }
};

// Function to mark messages as read
const markMessagesAsRead = (chatRoomId: string, _userId: string, officialId: string) => {
  try {
    const chatRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}`);

    // Get all messages in the chat room
    onValue(chatRef, (snapshot) => {
      const data = snapshot.val();
      if (!data) return;

      // Find unread messages sent by the official account
      Object.entries(data).forEach(([key, value]: [string, any]) => {
        if (value.senderId === officialId && (!value.isRead || value.status !== 'read')) {
          // Mark as read
          const messageRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${key}`);
          update(messageRef, {
            isRead: true,
            readAt: Date.now(),
            status: 'read'
          });
        }
      });
    }, { onlyOnce: true }); // Only run once to avoid infinite loops
  } catch (error) {
    console.error('Error marking messages as read:', error);
  }
};

// Define route params interface
interface RouteParams {
  accountData?: {
    id: string;
    name: string;
    logo: string;
    online: boolean;
    verified: boolean;
  };
}

interface NavigationProps {
  navigation: any;
  route: { params?: RouteParams };
}

export default function App({ navigation, route }: NavigationProps) {
  const [userId, setUserId] = useState<string | null>(null);
  const { accountData } = route.params || {};
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const flatListRef = useRef<FlatList | null>(null);
  const messageListenerRef = useRef<(() => void) | null>(null);
  const userStatusRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [locationRequestModalVisible, setLocationRequestModalVisible] = useState(false); // State for the location popup
  const [currentLocationRequestMessageId, setCurrentLocationRequestMessageId] = useState<string | null>(null); // To store the ID of the message requesting location
  const [isMessageTypePopupVisible, setIsMessageTypePopupVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Use Reanimated Keyboard Animation hook
  const { height: keyboardHeight } = useReanimatedKeyboardAnimation();

  // Get userId from AsyncStorage and set up user status
  useEffect(() => {
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          const id = parsedSession?.userId || userSession;
          setUserId(id);

          // Set up user online status
          if (id) {
            userStatusRef.current = ref(db, `users/${id}/status`);

            // Set user as online
            const userStatus: UserStatus = {
              state: 'online',
              lastSeen: Date.now(),
              isTyping: false
            };
            update(userStatusRef.current, userStatus);

            // Set up disconnect handler
            const offlineStatus: UserStatus = {
              state: 'offline',
              lastSeen: Date.now(), // Using Date.now() as serverTimestamp() returns a placeholder
              isTyping: false
            };
            onDisconnect(userStatusRef.current).update(offlineStatus);
          }
        } else {
          console.error('No user session found');
          Alert.alert('Error', 'Please login again');
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error getting user session:', error);
        Alert.alert('Error', 'Failed to get user information');
        navigation.goBack();
      }
    };

    getUserId();

    // Cleanup function
    return () => {
      if (userStatusRef.current) {
        const offlineStatus: UserStatus = {
          state: 'offline',
          lastSeen: Date.now(),
          isTyping: false
        };
        update(userStatusRef.current, offlineStatus);
      }
    };
  }, []);

  // Set up Firebase listener for messages
  const setupMessageListener = useCallback(() => {
    if (!userId || !accountData?.id) return;

    // Clear any existing listener
    if (messageListenerRef.current) {
      messageListenerRef.current();
    }

    // Set up new listener
    messageListenerRef.current = fetchChatHistory(
      userId,
      accountData.id,
      setMessages,
      setLoadError,
      setIsLoadingHistory
    );

    return () => {
      if (messageListenerRef.current) {
        messageListenerRef.current();
      }
    };
  }, [userId, accountData?.id]);

  // Initialize message listener
  useEffect(() => {
    if (userId && accountData?.id) {
      setupMessageListener();
    }

    return () => {
      if (messageListenerRef.current) {
        messageListenerRef.current();
      }
    };
  }, [userId, accountData?.id, setupMessageListener]);

  // Listen for official account online status and typing indicator
  useEffect(() => {
    if (!accountData?.id) return;

    const officialStatusRef = ref(db, `users/${accountData.id}/status`);

    const unsubscribe = onValue(officialStatusRef, (snapshot) => {
      const status = snapshot.val();
      if (status && route.params?.accountData) {
        // Update the accountData with the latest online status
        route.params.accountData.online = status.state === 'online';

        // Update typing indicator - only if they're typing to this user
        setOtherUserTyping(!!status.isTyping && status.typingInChatWith === userId);
      }
    });

    return () => unsubscribe();
  }, [accountData?.id, route.params]);

  const fetchMessages = useCallback(() => {
    if (userId && accountData?.id) {
      setIsLoadingHistory(true);
      // Re-initialize the Firebase listener
      if (messageListenerRef.current) {
        messageListenerRef.current();
      }
      messageListenerRef.current = fetchChatHistory(userId, accountData.id, setMessages, setLoadError, setIsLoadingHistory);
    }
  }, [userId, accountData?.id]);

  // Function to clear chat history
  const clearChatHistory = useCallback(async () => {
    if (!userId || !accountData?.id) {
      Alert.alert('Error', 'Cannot clear chat history at this time');
      return;
      }

    try {
      // Create a chat room ID
      const chatRoomId = [userId, accountData.id].sort().join('_');

      // Clear messages in official_chats collection
      const chatRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}`);
      await set(chatRef, null);

      // Clear messages in official_messages collection
      const messagesRef = ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}`);
      await set(messagesRef, null);

      // Clear local messages state
      setMessages([]);

      // Close menu
      setMenuVisible(false);

      // Show success message
      Alert.alert('Success', 'Chat history has been cleared');
    } catch (error) {
      console.error('Error clearing chat history:', error);
      Alert.alert('Error', 'Failed to clear chat history. Please try again.');
    }
  }, [userId, accountData?.id]);

  const renderContent = () => {
    if (!userId || !accountData?.id) {
      return (
        <View style={tw`flex-1 items-center justify-center`}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={tw`text-gray-500 text-center mt-4`}>Loading chat...</Text>
        </View>
      );
    }

    return (
      <>
        {isLoadingHistory ? (
          <View style={tw`flex-1 items-center justify-center`}>
            <ActivityIndicator size="large" color="#0000ff" />
            <Text style={tw`text-gray-500 text-center mt-4`}>Loading messages...</Text>
          </View>
        ) : loadError ? (
          <View style={tw`flex-1 items-center justify-center p-4`}>
            <Text style={tw`text-red-500 text-center`}>{loadError}</Text>
            <TouchableOpacity
              style={tw`mt-4 bg-blue-500 px-4 py-2 rounded-full`}
              onPress={fetchMessages}
            >
              <Text style={tw`text-white`}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={[...messages].reverse()}
            renderItem={renderMessage}
            keyExtractor={item => String(item.id)} // Ensure key is always a string
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'flex-end',
              padding: 15
            }}
            inverted
            showsVerticalScrollIndicator={false}
            onRefresh={fetchMessages}
            refreshing={isLoadingHistory}
            removeClippedSubviews={false} // Add this to prevent rendering issues
          />
        )}
      </>
    );
  };

  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    }
  };

  const sendMessage = async () => {
    if (message.trim() === '') return;

    const messageId = generateUUID();
    const timestamp = Date.now();

    const newMsg: FirebaseMessage = {
      id: messageId,
      text: message.trim(),
      senderId: userId || '',
      receiverId: accountData?.id || '',
      timestamp: timestamp,
      status: 'sent',
      isRead: false,
      isLocationRequest: false,
    };

    try {
      // Optimistically add message to UI
      setMessages(prev => [...prev, {...newMsg, isUser: true}]);
      setMessage('');
      scrollToBottom();

      if (!accountData?.id || !userId) {
        throw new Error('Account data or user ID is missing');
      }

      // Create a chat room ID by sorting and joining the user IDs
      const chatRoomId = [userId, accountData.id].sort().join('_');

      // Save message to Firebase
      await set(ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
      await set(ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), newMsg);

      // Also send to the API for backward compatibility if needed
      try {
        await axios.post(`${API_BASE}/chat/${userId}/${accountData.id}`, {
          content: message.trim(),
          senderId: userId,
          receiverId: accountData.id
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
      } catch (apiError) {
        console.warn('API fallback failed, but Firebase message was sent:', apiError);
        // Continue since Firebase message was sent successfully
      }
    } catch (err) {
      console.error('Sending message failed:', err);
      // Remove the optimistically added message
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      setMessage(message);
      Alert.alert(
        'Error',
        'Failed to send message. Please try again.'
      );
    }
  };

  // Function to handle button presses on messages
  const handleButtonPress = async (button: ChatButton, messageId: string) => {
    if (!userId || !accountData?.id) {
      console.error('User ID or account data missing for button press');
      Alert.alert('Error', 'Cannot process button action at this time.');
      return;
    }

    // Check if this is the "Share Location" button
    if (button.value === 'share_location') {
      // Handle location sharing
      setCurrentLocationRequestMessageId(messageId); // Store the message ID
      setLocationRequestModalVisible(true); // Show the location request modal
      return; // Stop further processing for this button
    }

    const responseMessageId = generateUUID();
    const timestamp = Date.now();
    const responseText = button.text; // Use the button text as the message content

    const newMsg: FirebaseMessage = {
      id: responseMessageId,
      text: responseText,
      senderId: userId, // Sent by the user
      receiverId: accountData.id, // Sent to the official account
      timestamp: timestamp,
      status: 'sent',
      isRead: false,
      isLocationRequest: false,
    };

    try {
      // Optimistically add message to UI
      setMessages(prev => [...prev, {...newMsg, isUser: true}]);
      // Optionally, you could disable the buttons on the original message here
      // by updating its state or marking it as responded to.

      // Create a chat room ID by sorting and joining the user IDs
      const chatRoomId = [userId, accountData.id].sort().join('_');

      // Save message to Firebase
      await set(ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${responseMessageId}`), newMsg);
      await set(ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}/${responseMessageId}`), newMsg);

      scrollToBottom();

      // TODO: Implement sending to API if needed for the official account to receive the response
      // Similar to the sendMessage function, you might need to send this button response
      // to your backend API so the official account system can process it.

    } catch (err) {
      console.error('Sending button response message failed:', err);
      // Remove the optimistically added message if sending failed
      setMessages(prev => prev.filter(msg => msg.id !== responseMessageId));
      Alert.alert(
        'Error',
        'Failed to send button response. Please try again.'
      );
    }
  };

  // Function to handle sharing location (will be called from the modal)
  const handleShareLocation = async () => {
    setLocationRequestModalVisible(false); // Hide the modal

    if (!userId || !accountData?.id || !currentLocationRequestMessageId) {
      console.error('User ID, account data, or current location request message ID missing for sharing location');
      Alert.alert('Error', 'Cannot share location at this time.');
      return;
    }

    // TODO: Implement actual location retrieval and permission request here
    // This part requires using React Native's Geolocation API and handling permissions.
    // For demonstration, we'll just send a placeholder message.

    Alert.alert('Location Sharing', 'Simulating location sharing...');

    const locationMessageId = generateUUID();
    const timestamp = Date.now();
    const locationText = 'My location (simulated): [Latitude, Longitude]'; // Placeholder text

    const newMsg: FirebaseMessage = {
      id: locationMessageId,
      text: locationText,
      senderId: userId, // Sent by the user
      receiverId: accountData.id, // Sent to the official account
      timestamp: timestamp,
      status: 'sent',
      isRead: false,
      isLocationRequest: false, // This is the user's response, not a request
      // You might want to link this message back to the original location request message
      // e.g., respondsToMessageId: currentLocationRequestMessageId
    };

    try {
      // Optimistically add message to UI
      setMessages(prev => [...prev, {...newMsg, isUser: true}]);
      scrollToBottom();

      const chatRoomId = [userId, accountData.id].sort().join('_');

      // Save message to Firebase
      await set(ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${locationMessageId}`), newMsg);
      await set(ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}/${locationMessageId}`), newMsg);

      // Simulate receiving the "Thank you" message from the official account
      const thankYouMessageId = generateUUID();
      const thankYouTimestamp = Date.now() + 500; // A little after the user's message

      const thankYouMsg: ChatMessage = {
        id: thankYouMessageId,
        text: 'Thank you for sharing your location.',
        timestamp: thankYouTimestamp,
        senderId: accountData.id, // Sent by the official account
        receiverId: userId, // Sent to the user
        isUser: false,
        status: 'read',
        isRead: true,
        isAutomated: true, // Mark as automated for styling
        isLocationRequest: false, // Not a location request
      };

      // Add the simulated thank you message to the state
      setMessages(prev => [...prev, thankYouMsg]);
      scrollToBottom(); // Scroll again to show the new message

    } catch (err) {
      console.error('Sending location message failed:', err);
      // Remove the optimistically added message if sending failed
      setMessages(prev => prev.filter(msg => msg.id !== locationMessageId));
      Alert.alert(
        'Error',
        'Failed to send location. Please try again.'
      );
    }

    setCurrentLocationRequestMessageId(null); // Clear the stored message ID
  };

  // Function to upload image and send message
  const uploadImageAndSend = async (imageUri: string) => {
    console.log('uploadImageAndSend: Function started.'); // Log start of function

    if (!userId || !accountData?.id) {
      console.error('uploadImageAndSend: Prerequisites missing (userId or accountData).');
      Alert.alert('Error', 'Cannot send image at this time.');
      setIsLoading(false);
      return;
    }

    if (!imageUri) {
      console.log('uploadImageAndSend: Image URI is missing.');
      setIsLoading(false);
      return;
    }

    // Dismiss the message type selection modal
    setIsMessageTypePopupVisible(false);

    setIsLoading(true); // Show loading indicator while uploading
     console.log('uploadImageAndSend: Loading indicator set to true.');


    const apiUrl = 'https://api.php.nityasha.com/uploadfilesserver/index.php';
    const formData = new FormData();

    // Determine file name and type from URI
    const uriParts = imageUri.split('/');
    const fileName = uriParts[uriParts.length - 1];
    // Basic type guess, improve if necessary based on file extension or mime type library
    const fileType = fileName.split('.').pop() === 'jpg' ? 'image/jpeg' : 'image/png'; // Add more types if needed

    formData.append('image', {
      uri: imageUri,
      name: fileName,
      type: fileType,
    } as any); // Use 'as any' to bypass TypeScript type checking for FormData file append

    console.log('uploadImageAndSend: Attempting to fetch image blob from URI:', imageUri);
    let blob;
    try {
       const response = await fetch(imageUri);
       blob = await response.blob();
       console.log('uploadImageAndSend: Successfully fetched image blob.');
    } catch (blobError) {
       console.error('uploadImageAndSend: Error fetching image blob:', blobError);
       Alert.alert('Error', `Failed to read image: ${blobError instanceof Error ? blobError.message : String(blobError)}`);
       setIsLoading(false);
       return; // Stop if blob fetch fails
    }


    console.log('uploadImageAndSend: Attempting to upload image to:', apiUrl);
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
          // TODO: Add any required authentication headers here
        },
      });
      console.log('uploadImageAndSend: Fetch response received, status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('uploadImageAndSend: Image upload failed with status:', response.status, 'response:', errorText);
        throw new Error(`Image upload failed with status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('uploadImageAndSend: Image upload successful, response:', responseData);

      // *** ASSUMPTION: The API response contains the image URL in responseData.url ***
      // *** IMPORTANT: You might need to adjust 'responseData.url' based on your API's actual response format ***
      const uploadedImageUrl = responseData.url || responseData.imageUrl; // Try common keys

      if (uploadedImageUrl) {
        console.log('uploadImageAndSend: Uploaded image URL:', uploadedImageUrl);
        console.log('uploadImageAndSend: Calling sendImageMessage...');
        // Send the image URL as a message using the new function
        await sendImageMessage(uploadedImageUrl);
        console.log('uploadImageAndSend: sendImageMessage call finished.');
      } else {
        console.error('uploadImageAndSend: Image URL not found in API response:', responseData);
        Alert.alert('Error', 'Image upload successful, but could not get image URL from the server response.');
      }

    } catch (uploadError) {
       console.error('uploadImageAndSend: Error during image upload:', uploadError);
       // Ensure the error is displayed to the user
       Alert.alert('Error', `Failed to upload image: ${uploadError instanceof Error ? uploadError.message : String(uploadError)}`);
        // You might want to add a 'failed' status to the message in state if you were tracking upload progress
    } finally {
      setIsLoading(false); // Hide loading indicator regardless of success or failure
      console.log('uploadImageAndSend: Loading indicator set to false. Function finished.');
    }
  };

  // New function to send image message after upload
  const sendImageMessage = async (uploadedImageUrl: string) => {
    console.log('sendImageMessage: Function started with URL:', uploadedImageUrl); // Add log at start of sendImageMessage

     if (!userId || !accountData?.id || !uploadedImageUrl) {
       console.error('sendImageMessage: Prerequisites missing (userId, accountData, or uploadedImageUrl).');
       Alert.alert('Error', 'Cannot send image message.');
       console.log('sendImageMessage: Prerequisites check failed.'); // Add log for failed check
       return;
     }

    const messageId = generateUUID();
    const timestamp = Date.now();
    const chatRoomId = [userId, accountData.id].sort().join('_');

     const newMsg: ChatMessage = {
       id: messageId,
       text: '', // Image messages typically have no text
       senderId: userId,
       receiverId: accountData.id,
       timestamp: timestamp,
       status: 'sent',
       isRead: false,
       isLocationRequest: false,
       isAutomated: false,
       type: 'image',
       imageUrl: uploadedImageUrl, // Use the URL from the upload response
     };

     console.log('sendImageMessage: Attempting to optimistically update UI with message:', newMsg); // Log before optimistic update
     try {
       // Optimistically add message to UI
       setMessages(prev => [...prev, { ...newMsg, isUser: true }]);
       scrollToBottom();
       console.log('sendImageMessage: Optimistic UI update successful.');


       // Save message to Firebase Realtime Database
       console.log('sendImageMessage: Attempting to save message to Firebase official_chats:', chatRoomId, messageId); // Log before Firebase set 1
       const messageDbRef = ref(db, `${FIREBASE_OFFICIAL_CHAT_PREFIX}/${chatRoomId}/${messageId}`);
       await set(messageDbRef, newMsg);
       console.log('sendImageMessage: Message saved to official_chats in Firebase.');

       console.log('sendImageMessage: Attempting to save message to Firebase official_messages:', chatRoomId, messageId); // Log before Firebase set 2
       const messagesDbRef = ref(db, `${FIREBASE_OFFICIAL_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`);
       await set(messagesDbRef, newMsg); // Save to official_messages too
       console.log('sendImageMessage: Message saved to official_messages in Firebase.');

       console.log('sendImageMessage: Firebase saves complete.');


       // TODO: Optionally send to your backend API if needed for the official account
       // If your backend needs to be notified of the image message, send a request here.
       // await axios.post(`${API_BASE}/chat/${userId}/${accountData.id}`, {
       //   type: 'image',
       //   content: uploadedImageUrl, // Send the URL
       //   senderId: userId,
       //   receiverId: accountData.id
       // }, {
       //   headers: { 'Content-Type': 'application/json' }
       // });
       console.log('sendImageMessage: API notification section.'); // Log before optional API call


     } catch (err) {
       console.error('sendImageMessage: Error during sending message to Firebase or API:', err); // More specific error log
       // Remove the optimistically added message if sending failed
       setMessages(prev => prev.filter(msg => msg.id !== messageId));
       Alert.alert(
         'Error',
         'Failed to send image message. Please try again.'
       );
     }
     console.log('sendImageMessage: Function finished.'); // Log end of function
   };

  // Your provided pickImage function
  const pickImage = async () => {
    // Corrected prerequisite check
    if (!userId || !accountData?.id) {
        console.log('pickImage: Prerequisites missing (userId or accountData.id).');
        Alert.alert('Error', 'Cannot pick image. User or account data missing.');
        // Dismiss modal even if prerequisites fail
        setIsMessageTypePopupVisible(false);
        return;
    }

    // Dismiss the message type selection modal
    setIsMessageTypePopupVisible(false); // Dismiss modal before opening picker

    console.log('pickImage: Opening image library.');

    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert('Permission Required', 'Permission to access media library is required to send images.');
      return;
    }

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images, // Only allow images
      allowsEditing: false, // Set to true if you want editing capabilities
      aspect: [4, 3], // Specify desired aspect ratio if editing is allowed
      quality: 1, // Image quality (0 to 1)
    });

    console.log('pickImage: Image picker result:', result);

    if (!result.canceled && result.assets && result.assets.length > 0) {
      const selectedImageUri = result.assets[0].uri;
      console.log('pickImage: Image selected, URI:', selectedImageUri);
      console.log('pickImage: Calling uploadImageAndSend...');
      // Upload the image and then send the message
      await uploadImageAndSend(selectedImageUri);
      console.log('pickImage: uploadImageAndSend call finished.');
    } else {
      console.log('pickImage: Image picking cancelled or failed.');
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    // Add special styling for automated messages
    if (item.type === 'image' && item.imageUrl) {
      console.log('renderMessage: Rendering image message:', item); // Log image messages being rendered
      return (
        <TouchableOpacity
          onPress={() => navigation.navigate('FullScreenimage', { imageUrl: item.imageUrl })}
          style={[
            styles.messageContainer,
            item.isUser ? styles.userMessage : styles.otherMessage, // Images also have sender/receiver styling
            styles.imageMessage, // Apply specific image message styles
          ]}
        >
          <Image
            source={{ uri: item.imageUrl }}
            style={styles.chatImage} // Apply image styling
          />
         
        </TouchableOpacity>
      );
    }

    // Render text/button messages
    return (
      <View
        style={[
          styles.messageContainer,
          item.isUser ? styles.userMessage : styles.otherMessage,
          item.isAutomated && styles.automatedMessage,
          // Add padding at the bottom if there are buttons
          item.buttons && item.buttons.length > 0 && styles.messageContainerWithButtons,
        ]}
      >
        <Text style={[styles.messageText, item.isAutomated && styles.automatedMessageText]}>{item.text}</Text>
        {item.isAutomated && (
          <Text style={styles.automatedLabel}>Automated Response</Text>
        )}

        {/* Render buttons if they exist */}
        {item.buttons && item.buttons.length > 0 && (
          <View style={styles.buttonContainer}>
            {item.buttons.map((button, index) => (
              <TouchableOpacity
                key={index} // Using index as key for now, consider a more stable key if possible
                style={[styles.button, item.isAutomated && styles.automatedButton]}
                onPress={() => {
                  handleButtonPress(button, item.id); // Call the handler function
                }}
              >
                <Text style={[styles.buttonText, item.isAutomated && styles.automatedButtonText]}>{button.text}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      keyboardVerticalOffset={100}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />

        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={styles.logoContainer}>
              <Image source={{ uri: accountData?.logo || 'https://via.placeholder.com/150' }} style={tw`w-full h-full`} />
            </View>
            <View>
              <Text style={styles.headerTitle}>{accountData?.name || 'User'}</Text>
              <Text style={styles.headerSubtitle}>
                {accountData?.online ? 'Online' : 'Offline'}{accountData?.verified && ' ✓'}
              </Text>
            </View>
          </View>
          <TouchableOpacity onPress={() => setMenuVisible(true)}>
            <Feather name="more-horizontal" size={24} color="black" />
          </TouchableOpacity>

          {/* Menu Modal */}
          <Modal
            visible={menuVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setMenuVisible(false)}
          >
            <TouchableOpacity
              style={styles.modalOverlay}
              activeOpacity={1}
              onPress={() => setMenuVisible(false)}
            >
              <View style={styles.menuContainer}>
                {accountData?.online && (
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={clearChatHistory}
                  >
                    <Feather name="trash-2" size={20} color="#FF3B30" />
                    <Text style={styles.menuItemText}>Clear Chat History</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => setMenuVisible(false)}
                >
                  <Feather name="x" size={20} color="#007AFF" />
                  <Text style={styles.menuItemText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </Modal>

          {/* Location Request Modal */}
          <Modal
            visible={locationRequestModalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setLocationRequestModalVisible(false)}
          >
            <TouchableOpacity
              style={styles.modalOverlay}
              activeOpacity={1}
              onPress={() => setLocationRequestModalVisible(false)}
            >
              <View style={styles.locationModalContainer}>
                <Text style={styles.locationModalTitle}>Share Location</Text>
                <Text style={styles.locationModalText}>
                  The official account is requesting to view your current location.
                </Text>
                <View style={styles.locationModalButtonContainer}>
                  <TouchableOpacity
                    style={styles.locationModalButtonCancel}
                    onPress={() => setLocationRequestModalVisible(false)}
                  >
                    <Text style={styles.locationModalButtonTextCancel}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.locationModalButtonShare}
                    onPress={handleShareLocation}
                  >
                    <Text style={styles.locationModalButtonTextShare}>Share Location</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableOpacity>
          </Modal>

          {/* Message Type Selection Modal */}
          <Modal
            visible={isMessageTypePopupVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setIsMessageTypePopupVisible(false)}
          >
            <TouchableOpacity
              style={styles.modalOverlay}
              activeOpacity={1}
              onPress={() => setIsMessageTypePopupVisible(false)}
            >
              <View style={styles.messageTypeModalContainer}>
                <TouchableOpacity
                  style={styles.messageTypeMenuItem}
                  onPress={pickImage} // Call pickImage when 'Image' is pressed
                >
                  <Feather name="image" size={20} color="#007AFF" />
                  <Text style={styles.messageTypeMenuItemText}>Image</Text>
                </TouchableOpacity>
                 {/* Add other message types here (e.g., Location, Document) */}
                <TouchableOpacity
                  style={styles.messageTypeMenuItem}
                  onPress={() => setIsMessageTypePopupVisible(false)} // Add a Cancel option
                >
                  <Feather name="x" size={20} color="#FF3B30" />
                  <Text style={styles.messageTypeMenuItemText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </Modal>

        </View>

        {/* Chat Messages */}
        {renderContent()}

        {/* Typing Indicator */}
        {otherUserTyping && accountData?.online && (
          <View style={styles.typingIndicator}>
            <Text style={styles.typingText}>{accountData?.name || 'User'} is typing...</Text>
          </View>
        )}

        {/* Message Input */}
        <View
          style={[
            styles.inputContainer,
            { marginBottom: keyboardHeight }, // Adjust for keyboard height
          ]}
        >
          <View style={styles.inputWrapper}>
            {/* Connect plus button to show message type modal */}
            <TouchableOpacity style={styles.plusButton} onPress={() => setIsMessageTypePopupVisible(true)}>
              <Feather name="plus" size={24} color="#4CAF50" />
            </TouchableOpacity>
            <TextInput
              style={styles.input}
              placeholder="Type a message..."
              placeholderTextColor="#999"
              value={message}
              onChangeText={(text) => {
                setMessage(text);

                // Update typing status
                if (!isTyping && text.trim().length > 0) {
                  setIsTyping(true);
                  if (userStatusRef.current) {
                    update(userStatusRef.current, {
                      isTyping: true,
                      typingInChatWith: accountData?.id // Add the recipient ID
                    });
                  }
                } else if (isTyping && text.trim().length === 0) {
                  setIsTyping(false);
                  if (userStatusRef.current) {
                    update(userStatusRef.current, {
                      isTyping: false,
                      typingInChatWith: null
                    });
                  }
                }

                // Clear any existing timeout
                if (typingTimeoutRef.current) {
                  clearTimeout(typingTimeoutRef.current);
                }

                // Set a timeout to stop typing indicator after 3 seconds of inactivity
                typingTimeoutRef.current = setTimeout(() => {
                  if (isTyping) {
                    setIsTyping(false);
                    if (userStatusRef.current) {
                      update(userStatusRef.current, {
                        isTyping: false,
                        typingInChatWith: null
                      });
                    }
                  }
                }, 3000);
              }}
              onFocus={scrollToBottom}
            />
          </View>
          {/* Show loading indicator or send button */}
          {isLoading ? (
            <View style={styles.loadingIndicatorContainer}>
              <ActivityIndicator size="small" color="#4CAF50" />
            </View>
          ) : (
            <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
              <Feather name="send" size={24} color="white" />
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c5e8c8',
  },
  typingIndicator: {
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 10,
    marginHorizontal: 15,
    marginBottom: 5,
  },
  typingText: {
    fontSize: 12,
    color: '#2c5e2e',
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingRight: 10,
    paddingTop: 60,
  },
  menuContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 5,
    width: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
  },
  menuItemText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#333',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBlockColor: '#B0DEBA',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 45,
    height: 45,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    overflow: 'hidden',
  },
  headerTitle: {
    fontSize: 15,
    fontFamily: 'Helvetica_bold',
    color: '#2c5e2e',
  },
  headerSubtitle: {
    fontSize: 13,
    color: '#2c5e2e',
    fontFamily: 'Helvetica_bold',
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 20,
    marginVertical: 4,
  },
  userMessage: {
    backgroundColor: '#DCF8C6',
    alignSelf: 'flex-end',
    marginLeft: '20%',
  },
  otherMessage: {
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    marginRight: '20%',
  },
  messageText: {
    fontSize: 16,
  },
  automatedMessage: {
    backgroundColor: '#F0F8FF', // Light blue background for automated messages
    borderWidth: 1,
    borderColor: '#ADD8E6',
  },
  automatedMessageText: {
    color: '#4682B4', // Steel blue color for text
  },
  automatedLabel: {
    fontSize: 10,
    color: '#778899', // Slate gray for the label
    marginTop: 4,
    fontStyle: 'italic',
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 10,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    marginRight: 10,
  },
  plusButton: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    paddingVertical: 10,
    paddingRight: 15,
    color: '#333',
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageList: {
    flexGrow: 1,
    justifyContent: 'flex-end',
    padding: 15,
  },
  // Add styles for messages with buttons
  messageContainerWithButtons: {
    paddingBottom: 8, // Add some padding at the bottom
  },
  buttonContainer: {
    marginTop: 10,
    flexDirection: 'column', // Stack buttons vertically
  },
  button: {
    marginTop: 5,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 15,
    backgroundColor: '#007AFF', // Default button color (Apple blue)
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  automatedButton: {
    backgroundColor: '#ADD8E6', // Light blue button for automated messages
  },
  automatedButtonText: {
    color: '#4682B4', // Steel blue text for automated buttons
  },
  locationModalContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    alignItems: 'center',
  },
  locationModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  locationModalText: {
    fontSize: 16,
    color: '#333',
    marginBottom: 20,
  },
  locationModalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  locationModalButtonCancel: {
    backgroundColor: '#FF3B30',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginRight: 5,
  },
  locationModalButtonShare: {
    backgroundColor: '#4CAF50',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginLeft: 5,
  },
  locationModalButtonTextCancel: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  locationModalButtonTextShare: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imageMessage: {
    width: '100%',
    height: 200,
    borderRadius: 10,
    marginVertical: 4,
  },
  chatImage: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  messageTypeModalContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 10,
    width: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    // Position the modal near the plus button - you might need to adjust these values
    position: 'absolute',
    bottom: 80, // Above the input container
    left: 10, // To the left side
  },
  messageTypeMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
  },
  messageTypeMenuItemText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#333',
  },
  // Add style for loading indicator in input area
  loadingIndicatorContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E0E0E0', // Light gray background
  },
  senderIdText: {
    fontSize: 10,
    color: '#555', // A muted color for the ID
    marginTop: 4, // Space above the ID
  },
  userSenderId: {
    textAlign: 'right', // Align right for user messages
  },
  otherSenderId: {
    textAlign: 'left', // Align left for other messages
  },
});
