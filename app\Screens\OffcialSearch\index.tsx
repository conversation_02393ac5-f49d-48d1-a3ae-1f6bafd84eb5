import React, { useState, useEffect, useCallback, useRef } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, SafeAreaView, TextInput, Image, TouchableOpacity, ScrollView, ImageBackground, ActivityIndicator, Keyboard, BackHandler } from 'react-native';
import { Ionico<PERSON>, Feather } from '@expo/vector-icons';
import debounce from 'lodash/debounce';
import { CommonActions, useNavigation, useFocusEffect } from '@react-navigation/native';
import { Badge, Searchbar } from 'react-native-paper';
import tw from 'twrnc';
import { useOfficialSearchPreload } from '@/context/OfficialSearchPreloadContext';

interface OfficialAccount {
  id: number;
  name: string;
  logo: string;
  description: string;
  verified: number;
  online: number;
  unread: string;
}

export default function OffcialSearch({ navigation }) {
  const searchInputRef = useRef<any>(null);
  const [accounts, setAccounts] = useState<OfficialAccount[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<OfficialAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  // Get preloaded data from context
  const { isPreloaded, preloadedAccounts, preloadOfficialSearch } = useOfficialSearchPreload();

  useEffect(() => {
    // If we have preloaded data, use it immediately
    if (isPreloaded && preloadedAccounts.length > 0) {
      setAccounts(preloadedAccounts);
      setFilteredAccounts(preloadedAccounts);
      setLoading(false);
      console.log('Using preloaded official accounts data');
    } else {
      // Otherwise fetch data
      fetchAccounts();
    }

    // Always refresh data in the background
    preloadOfficialSearch();
  }, [isPreloaded, preloadedAccounts]);

  const fetchAccounts = async () => {
    if (!loading) setLoading(true);
    setError(null);
    try {
      const response = await fetch('https://api.search.nityasha.com/official_accounts');
      if (!response.ok) {
        throw new Error('Failed to fetch accounts');
      }
      const data = await response.json();
      setAccounts(data);
      setFilteredAccounts(data);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      setError('Failed to load accounts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchText: string) => {
      if (!searchText.trim()) {
        setFilteredAccounts(accounts);
        return;
      }

      const searchLower = searchText.toLowerCase();
      const filtered = accounts.filter(account =>
        account.name.toLowerCase().includes(searchLower) ||
        account.description.toLowerCase().includes(searchLower)
      );

      setFilteredAccounts(filtered);
    }, 300),
    [accounts]
  );

  // Handle search input change
  const handleSearch = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchQuery('');
    setFilteredAccounts(accounts);
  };

  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Handle back button when keyboard is open
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        if (isKeyboardVisible) {
          Keyboard.dismiss();
          return true; // Prevents default back action
        }
        return false; // Allows default back action
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        subscription.remove();
      };
    }, [isKeyboardVisible])
  );

  // Force keyboard to open when component mounts
  useEffect(() => {
    // Short timeout to ensure the component is fully mounted
    const timer = setTimeout(() => {
      if (searchInputRef.current) {
        // First focus to ensure the component is ready
        searchInputRef.current.focus();

        // Then dismiss and re-focus to ensure keyboard appears
        // This trick helps on some Android devices where keyboard might not show
        setTimeout(() => {
          Keyboard.dismiss();
          setTimeout(() => {
            if (searchInputRef.current) {
              searchInputRef.current.focus();
            }
          }, 50);
        }, 50);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={styles.container}>
      <StatusBar backgroundColor='#bbeac4' />

     <View style={tw`py-3 items-center justify-center`}>
        <Text style={styles.headerTitle}>Official Accounts</Text>
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search accounts..."
          onChangeText={handleSearch}
          value={searchQuery}
          iconColor="#999"
          clearIcon="close-circle"
          ref={searchInputRef}
          autoFocus={true}
          showSoftInputOnFocus={true}
          onPress={() => {
            // Preload official search data before navigating
            preloadOfficialSearch();
            // Navigate using CommonActions to avoid animation
            navigation.dispatch(
              CommonActions.navigate({
                name: 'Search',
                params: {},
              })
            );
          }}
          onClearIconPress={handleClearSearch}
          autoCorrect={false}
        />
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchAccounts}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView style={styles.accountList}>
          {filteredAccounts.length === 0 ? (
            <View style={styles.noResultsContainer}>
              <Text style={styles.noResultsText}>No accounts found</Text>
            </View>
          ) : (
            filteredAccounts.map((account) => (
              <TouchableOpacity
                key={account.id}
                style={styles.accountItem}
                onPress={() => navigation.navigate('offcailchat', {
                  accountData: {
                    id: account.id,
                    name: account.name,
                    logo: account.logo,
                  }
                })}
              >
                <View style={styles.accountIcon}>
                  <Image
                    source={{uri: account.logo}}
                    style={styles.iconImage}
                  />
                </View>
                <View style={styles.accountInfo}>
                  <View style={styles.nameContainer}>
                    <Text style={styles.accountName}>{account.name}</Text>
                    {account.verified === 1 && (
                      <Ionicons name="checkmark-circle" size={16} color="#1DA1F2" />
                    )}
                  </View>
                  <Text style={styles.accountDescription}>{account.description}</Text>
                </View>
                <View style={styles.badgeContainer}>
                  {account.online === 1 && (
                    <Text style={styles.onlineText}>Online</Text>
                  )}
                  {Number(account.unread) > 0 && (
                    <View style={[styles.notificationBadge, styles.smallBadge]}>
                      <Text style={styles.badgeText}>{account.unread}</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))
          )}
        </ScrollView>
      )}
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c8f0d0',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'Helvetica_bold',
    color: '#333',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e8f8e8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 20,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  accountList: {
    flex: 1,
    marginTop: 8,
  },
  accountItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f8e8',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 6,
    padding: 12,
  },
  accountIcon: {
    width: 50,
    height: 50,
    borderColor : '#000',
    borderWidth: 2.5,
    borderRadius: 12,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  iconImage: {
    width: 40,
    height: 40,
  },
  accountInfo: {
    flex: 1,
    marginLeft: 12,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginRight: 4,
  },
  accountDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  onlineText: {
    fontSize: 14,
    color: '#4CAF50',
    marginRight: 6,
  },
  notificationBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
    justifyContent: 'center',
  },
  smallBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  bottomNav: {
    flexDirection: 'row',
    height: 60,
    backgroundColor: '#f5f5f5',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  centerButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#000',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 10,
  },
  navText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 40,
  },
  noResultsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
