import React, { useState, useEffect, useRef } from 'react';
import tw from 'twrnc';
import { ChevronLeft, Eye, MessageCircle, Send, Trash2 } from 'lucide-react-native';
import {
  ImageBackground,
  ScrollView,
  View,
  Text,
  Image,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  RefreshControl
} from 'react-native';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Reply, PostScreenParams } from '../../types/replies';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

type RootStackParamList = {
  OpenFullPost: { post: PostScreenParams['post'] };
};

type OpenFullPostRouteProp = RouteProp<RootStackParamList, 'OpenFullPost'>;
type OpenFullPostNavigationProp = StackNavigationProp<RootStackParamList, 'OpenFullPost'>;

interface OpenFullPostProps {
  route: OpenFullPostRouteProp;
  navigation: OpenFullPostNavigationProp;
}

export default function OpenFullPost({ route, navigation }: OpenFullPostProps) {
  const { post } = route.params;
  const [replyText, setReplyText] = useState('');
  const [replies, setReplies] = useState<Reply[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchingReplies, setFetchingReplies] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userId, setUserId] = useState<string | number | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  console.log(post);
  // Fetch user ID from AsyncStorage
  useEffect(() => {
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          setUserId(parsedSession?.userId || userSession);
        }
      } catch (error) {
        console.error("Error fetching user ID:", error);
      }
    };
    getUserId();
  }, []);
  
  // Fetch replies for the post
  useEffect(() => {
    fetchReplies();
 
  }, [post.id]);
  
  // Track post visit
  useEffect(() => {
    const trackPostVisit = async () => {
      try {
        // Track the visit via the API
        await axios.get(`https://posts-api.nityasha.com/api/posts/visist/post/${post.id}`);
        console.log('Post visit tracked successfully');

        // === Add Push Notification Logic Here ===
        try {
          // Get the viewing user's name from session if available
          let viewingUserName = 'Someone'; // Default to 'Someone'
          const userSession = await AsyncStorage.getItem("userSession");
          if (userSession) {
            const parsedSession = JSON.parse(userSession);
            // Assuming user name is stored as 'userName' or similar in the session
            viewingUserName = parsedSession?.userName || parsedSession?.name || 'Someone';
          }


          // 1. Fetch post author's push token
          const userResponse = await axios.get(`https://api.nityasha.com/api/v1/users/${post.user_id}`);
          const authorPushToken = userResponse.data?.pushtoken;

          if (authorPushToken) {
            // 2. Send push notification
            const message = {
              to: authorPushToken,
              sound: 'default',
              title: `Someone liked your post 👀`,
body: `${viewingUserName} just viewed your post – who knows, it might be something special...`,           
              data: { postId: post.id }, // Optional: include data to open the post
            };

            await axios.post('https://exp.host/--/api/v2/push/send', message);
            console.log('Post view push notification sent successfully');
          } else {
            console.log('Post author does not have a push token, cannot send view notification.');
          }
        } catch (notificationError) {
          console.error('Error sending post view push notification:', notificationError);
          // Continue without stopping the visit tracking process
        }
        // === End Push Notification Logic ===

      } catch (error) {
        console.error('Error tracking post visit:', error);
        // Silent fail - don't show error to user as this is background tracking
      }
    };

    trackPostVisit();
  }, [post.id, post.user_id]); // Added post.user_id to dependency array

  // Function to handle post deletion
  const handleDeletePost = async () => {
    if (!userId || userId !== post.user_id) {
      console.log("User is not the post owner, cannot delete.");
      return;
    }

    setLoading(true); // Assuming you want to show a loading state while deleting
    try {
      // This is the call to your backend delete endpoint
      const response = await axios.delete(`https://posts-api.nityasha.com/api/posts/${post.id}`);

      if (response.data && response.data.success) {
        console.log('Post deleted successfully');
        // Navigate back after successful deletion
        navigation.goBack();
      } else {
        console.error("Failed to delete post:", response.data?.message);
        // Optionally show an error message to the user
      }
    } catch (error) {
      console.error("Error deleting post:", error);
      // Optionally show an error message to the user
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch replies
  const fetchReplies = async () => {
    setFetchingReplies(true);
    try {
      const response = await axios.get(`https://posts-api.nityasha.com/api/posts/${post.id}/replies`);
      if (response.data && response.data.success) {
        // Process replies to handle nested structure
        // The actual replies are in the data property of the response
        const processedReplies = processReplies(response.data.data || []);
        setReplies(processedReplies);

        // Update reply count in UI if needed
        if (response.data.count !== undefined) {
          // You could update a reply count state here if needed
          console.log(`Total replies: ${response.data.count}`);
        }
      } else {
        console.log("No replies available or invalid response format");
        setReplies([]);
      }
    } catch (error) {
      console.error("Error fetching replies:", error);
      setReplies([]);
    } finally {
      setFetchingReplies(false);
      setRefreshing(false); // Make sure to reset refreshing state
    }
  };

  // Handle pull-to-refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchReplies();
  };

  // Process replies to organize them into a nested structure and sort
  const processReplies = (repliesData: Reply[]): Reply[] => {
    const replyMap: Record<string | number, Reply> = {};
    const topLevelReplies: Reply[] = [];

    // First pass: create a map of all replies, ensuring replies array exists
    repliesData.forEach((reply: Reply) => {
      replyMap[reply.id] = { ...reply, replies: reply.replies || [] }; // Ensure replies array exists
    });

    // Second pass: organize into parent-child structure
    repliesData.forEach((reply: Reply) => {
      if (reply.parent_reply_id) {
        // This is a nested reply
        if (replyMap[reply.parent_reply_id]) {
           // Add the reply map object to the parent's replies array
           // Check if it's not already added (e.g., if the input repliesData had nested replies)
           const parentReplies = replyMap[reply.parent_reply_id].replies;
           if (!parentReplies.some(r => r.id === reply.id)) {
               parentReplies.push(replyMap[reply.id]);
           }
        } else {
            // If parent not found in map, treat this as a top-level reply (fallback)
            // This might happen if the repliesData is incomplete
            topLevelReplies.push(replyMap[reply.id]);
        }
      } else {
        // This is a top-level reply
        topLevelReplies.push(replyMap[reply.id]);
      }
    });

    // Remove duplicate top-level replies that might have been added by the fallback
     const uniqueTopLevelReplies = Array.from(new Map(topLevelReplies.map(item => [item.id, item])).values());


    // Sort top-level replies by created_at descending (latest first)
    uniqueTopLevelReplies.sort((a, b) => {
      const dateA = new Date(a.created_at || 0).getTime(); // Use 0 for invalid dates to put them earlier/at the start of sorting range
      const dateB = new Date(b.created_at || 0).getTime();
      return dateB - dateA; // Descending order (latest first)
    });

    // Recursively sort nested replies by created_at ascending (earliest first for conversation flow)
    const sortNestedReplies = (repliesArray: Reply[]) => {
        if (!repliesArray || repliesArray.length === 0) {
            return;
        }
        repliesArray.sort((a, b) => {
            const dateA = new Date(a.created_at || 0).getTime();
            const dateB = new Date(b.created_at || 0).getTime();
            return dateA - dateB; // Ascending order (earliest first)
        });
        repliesArray.forEach(reply => {
            if (reply.replies && reply.replies.length > 0) {
                sortNestedReplies(reply.replies);
            }
        });
    };

    // Apply sorting to nested replies of all top-level replies
    uniqueTopLevelReplies.forEach(reply => {
        if (reply.replies && reply.replies.length > 0) {
            sortNestedReplies(reply.replies);
        }
    });


    return uniqueTopLevelReplies;
  };

  // Function to handle reply submission
  const handleReplySubmit = async () => {
    if (!replyText.trim()) return;
    if (!userId) {
      console.log("User not logged in");
      // Optionally alert user they need to log in
      return;
    }

    const submittedReplyText = replyText.trim();
    setLoading(true); // Set loading for the initial reply submission
    let userReplyId = null; // Variable to store the ID of the successfully posted user reply

    try {
      // 1. Post the user's reply
      const postReplyResponse = await axios.post(`https://posts-api.nityasha.com/api/posts/${post.id}/replies`, {
        user_id: userId,
        content: submittedReplyText
      });

      if (postReplyResponse.data && postReplyResponse.data.success) {
        userReplyId = postReplyResponse.data.data.id; // Capture the ID of the new reply

        // Clear input immediately after successful post
        setReplyText('');

        // 2. Refresh the replies list to include the new user reply
        // Await fetchReplies to ensure the state is updated before potentially adding AI reply
        await fetchReplies();

        // Scroll to bottom after a short delay to allow new reply/replies to render
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 300);


        // === Add Push Notification Logic Here ===
        // This part can run in parallel or after fetching replies,
        // but let's keep it here as it was.
        try {
          // 1. Fetch post author's push token
          const userResponse = await axios.get(`https://api.nityasha.com/api/v1/users/${post.user_id}`);
          const authorPushToken = userResponse.data?.pushtoken;

          if (authorPushToken) {
            // 2. Send push notification
            const message = {
              to: authorPushToken,
              sound: 'default',
              title: `New reply on your post`,
              body: `Someone replied to your post!`,
              data: { postId: post.id }, // Optional: include data to open the post
            };

            await axios.post('https://exp.host/--/api/v2/push/send', message);
            console.log('Push notification sent successfully');
          } else {
            console.log('Post author does not have a push token.');
          }
        } catch (notificationError) {
          console.error('Error sending push notification:', notificationError);
          // Continue without stopping the reply submission process
        }
        // === End Push Notification Logic ===


        // === Add AI API Call and AI Reply Logic Here ===
        // Check if the user's reply contained "@nityasha" and the reply was successfully posted
        if (submittedReplyText.includes('@nityasha') && userReplyId) {
          console.log('Detected @nityasha mention. Calling AI API...');
          // Note: AI API call doesn't need to block the main loading state
          // but we will await it here for sequential processing as planned.

          try {
            // Call the AI API with the specified message including post content and user reply
            const aiResponse = await axios.post('https://nx.ai.api.nityasha.com/chat', {
              message: `this is the post: ${post.content}, and what the user said: ${submittedReplyText} ` // Using the payload including user's full message
            });

            // Check if the AI response data exists and contains the 'response' field (based on your console output)
            if (aiResponse.data && typeof aiResponse.data.response === 'string') { // Changed check to .response and added type check
              console.log('AI API call successful:', aiResponse.data);

              // Create a mock AI reply object using the AI's response and the specified user ID
              const aiReplyContent = aiResponse.data.response; // Get the content from the AI response's 'response' field
              const aiReplyObject: Reply = {
                id: `ai-${Date.now()}-${userReplyId}`, // Unique temporary client-side ID
                post_id: post.id,
                user_id: 1765, // Set the AI user ID to 1765 as requested
                content: aiReplyContent,
                created_at: new Date().toISOString(), // Timestamp for when AI replied
                parent_reply_id: userReplyId, // This AI reply is a response to the user's reply
                author_name: 'Nityasha AI', // Name for the AI user
                user_avatar: 'https://ui-avatars.com/api/?name=AI&background=blue&color=fff', // A generic AI avatar
                replies: [], // AI reply won't have nested replies in this scenario
                // Add any other required Reply properties with default/placeholder values
              };

              // Update the replies state to include the new AI reply
              // This happens AFTER fetchReplies has updated the state with the user's reply.
              setReplies(currentReplies => {
                // Deep clone the current replies state using the standard method
                const newReplies = JSON.parse(JSON.stringify(currentReplies)); // Corrected deep clone

                // Helper function to find the user's reply and add the AI reply to its children
                const findAndAddAIReply = (repliesArray: Reply[], targetId: string | number): boolean => {
                  for (let i = 0; i < repliesArray.length; i++) {
                    const reply = repliesArray[i];
                     // Compare IDs ensuring both are treated as strings for consistent comparison
                    if (String(reply.id) === String(targetId)) {
                      // Found the user's reply, add the AI reply to its nested replies
                       if (!reply.replies) {
                           reply.replies = [];
                       }
                      reply.replies.push(aiReplyObject);
                      // Sort nested replies again after adding the new one to maintain order
                       reply.replies.sort((a, b) => {
                           const dateA = new Date(a.created_at || 0).getTime();
                           const dateB = new Date(b.created_at || 0).getTime();
                           return dateA - dateB; // Ascending for nested
                       });
                       console.log('AI reply added to state.');
                      return true; // Found and updated
                    }
                    // Recursively search in nested replies if they exist
                    if (reply.replies && reply.replies.length > 0) {
                      if (findAndAddAIReply(reply.replies, targetId)) {
                        return true; // Found and updated in a nested level
                      }
                    }
                  }
                  return false; // Not found
                };

                // Start the search from the top level replies
                findAndAddAIReply(newReplies, userReplyId);

                // Return the updated state. Top-level sorting is already handled by processReplies
                // when fetchReplies runs, and nested sorting is done when adding the AI reply.
                return newReplies;
              });

               // Optionally, scroll to the new AI reply or the user's reply section
               // This might require finding the position of the user's reply or the AI reply in the rendered list.
               // Skipping for now for simplicity.

            } else {
               // Log the actual data received when the expected field is missing
               console.warn('AI API call successful but no expected response data received. Data:', aiResponse.data); // Added data to log
               // Optionally inform the user that the AI did not respond as expected
            }

          } catch (aiError) {
            console.error('Error calling AI API:', aiError);
            // Handle AI API error - maybe display a message to the user
            // indicating the AI feature failed for this reply.
          }
        }
        // === End AI API Call and AI Reply Logic ===

      } else {
        // Handle unsuccessful response for user's reply
        console.error("Failed to post reply:", postReplyResponse.data?.message);
        // Optionally show an error message to the user about their reply failing
      }
    } catch (error) {
      console.error("Error posting reply:", error);
      // Optionally show a generic error message to the user if the API call fails completely
    } finally {
      // Set main loading to false after the initial reply post and fetchReplies are done,
      // regardless of whether the AI call happened or succeeded.
      // The AI call is awaited within the try block, so this finally will run after it.
      setLoading(false);
    }
  };

  // Format date to relative time
  const formatRelativeTime = (dateString: string): string => {
    try {
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Recently';
      }

      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffInSeconds < 0) return 'Just now'; // Handle future dates
      if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
      if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
      if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
      return `${Math.floor(diffInSeconds / 86400)} days ago`;
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Recently';
    }
  };

  // Render a reply component (recursive for nested replies)
  const renderReply = (reply: Reply, level = 0) => {
    // Skip rendering if reply data is invalid
    if (!reply || !reply.id) {
      return null;
    }

    // Format date safely
    let formattedTime = 'Just now';
    try {
      if (reply.created_at) {
        formattedTime = formatRelativeTime(reply.created_at);
      }
    } catch (e) {
      console.error('Error formatting date:', e);
    }

    return (
      <View key={reply.id} style={tw`mb-3 ml-${level * 4}`}>
        <View style={tw`bg-white rounded-xl p-4`}>
          <View style={tw`flex-row items-center mb-2`}>
            <Image
              source={{ uri: reply.user_avatar || `https://ui-avatars.com/api/?name=${reply.user_name || 'User'}` }}
              style={tw`w-10 h-10 rounded-full mr-3`}
            />
            <View>
              <View style={tw`flex-row items-center`}>
                <Text style={tw`font-bold text-[#1F2937]`}>{reply.author_name || 'Anonymous'}</Text>
              </View>
              <Text style={tw`text-gray-500 text-xs`}>{formattedTime}</Text>
            </View>
          </View>
          <Text style={tw`text-[#1F2937] mb-2`}>{reply.content || 'No content'}</Text>
          <View style={tw`flex-row items-center`}>
          </View>
        </View>

        {/* Render nested replies */}
        {reply.replies && reply.replies.length > 0 && (
          <View style={tw`mt-1`}>
            {reply.replies.map((nestedReply: Reply) => renderReply(nestedReply, level + 1))}
          </View>
        )}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={tw`flex-1`}
    >
      <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`w-full h-full`}>
        {/* Header */}
        <View style={tw`w-full items-center justify-between flex-row px-5 py-3`}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <ChevronLeft size={30} color={'#000'} />
          </TouchableOpacity>
          <Text style={[tw`text-black text-2xl`, { fontFamily: 'Helvetica_bold' }]}>Detail</Text>
          <View style={tw`w-8`} />
        </View>

        <ScrollView
          style={tw`flex-1 px-4`}
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#0000ff']}
              tintColor="#0000ff"
            />
          }
        >
          {/* Main Post Card */}
          <View style={tw`bg-[#fff] rounded-xl mb-4 overflow-hidden`}>
            {/* Post Header */}
            <View style={tw`p-4 flex-row justify-between items-center`}>
              <View style={tw`flex-row items-center`}>
                <Image
                  source={{ uri: post.pfp || 'https://ui-avatars.com/api/?name=Default' }}
                  style={tw`w-10 h-10 rounded-full border-2 border-black`}
                />
                <View style={tw`ml-3`}>
                  <View style={tw`flex-row items-center`}>
                    <Text style={[tw`text-black font-bold`, { fontFamily: 'Helvetica_bold' }]}>
                      {post.author_name || 'Anonymous User'}
                    </Text>
                  </View>
                  {post.hasOwnProperty('created_at') && typeof (post as any).created_at === 'string' && (
                    <Text style={tw`text-gray-500 text-xs`}>
                      {formatRelativeTime((post as any).created_at)}
                    </Text>
                  )}
                </View>
              </View>
            </View>

            {/* Post Content */}
            <View style={tw`px-4 pb-2`}>
              <Text style={tw`text-black text-base mb-3`}>
                {post.content || "No content available"}
              </Text>

              {/* Post Stats */}
              <View style={tw`flex-row items-center border-t border-white/20 pt-2`}>
                <View style={tw`flex-row items-center mr-4`}>
                  <MessageCircle size={18} color="black" />
                  <Text style={tw`text-black ml-1`}>{replies.length || 0} {replies.length === 1 ? 'Reply' : 'Replies'}</Text>
                </View>
                <View style={tw`flex-row items-center`}>
                  <Eye size={18} color="black" />
                  <Text style={tw`text-black ml-1`}>{post.view_count || 0} {post.view_count === 1 ? 'View' : 'Views'}</Text>
                </View>
                 {/* Delete Icon - Conditionally Rendered */}
                {userId && userId === post.user_id && (
                  <TouchableOpacity onPress={handleDeletePost} style={tw`ml-auto`}>
                    <Trash2 size={18} color="red" />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>

          {/* Reply Input */}
          <View style={tw`bg-[#F0F2F5] rounded-xl mb-4 p-3 flex-row items-center`}>
            <Image
              source={{ uri: 'https://ui-avatars.com/api/?name=Me' }}
              style={tw`w-8 h-8 rounded-full mr-3`}
            />
            <TextInput
              style={tw`flex-1 text-[#1F2937]`}
              placeholder={`Reply to ${post.author_name || 'this'} post...`}
              placeholderTextColor="#65676B"
              value={replyText}
              onChangeText={setReplyText}
              multiline
            />
            {loading ? (
              <ActivityIndicator size="small" color="#0000ff" style={tw`ml-2`} />
            ) : (
              <TouchableOpacity
                onPress={handleReplySubmit}
                disabled={!replyText.trim()}
                style={tw`ml-2 ${!replyText.trim() ? 'opacity-50' : ''}`}
              >
                <Send size={20} color="#0000ff" />
              </TouchableOpacity>
            )}
          </View>

          {/* Comments/Replies */}
          <View style={tw`mb-4`}>
            {fetchingReplies ? (
              <View style={tw`py-4 items-center`}>
                <ActivityIndicator size="large" color="#0000ff" />
                <Text style={tw`mt-2 text-gray-500`}>Loading replies...</Text>
              </View>
            ) : replies.length > 0 ? (
              replies.map(reply => renderReply(reply))
            ) : (
              <View style={tw`py-8 items-center`}>
                <Text style={tw`text-gray-500 text-lg`}>No replies yet</Text>
                <Text style={tw`text-gray-400 mt-1`}>Be the first to reply!</Text>
              </View>
            )}
          </View>
        </ScrollView>
      </ImageBackground>
    </KeyboardAvoidingView>
  );
}
