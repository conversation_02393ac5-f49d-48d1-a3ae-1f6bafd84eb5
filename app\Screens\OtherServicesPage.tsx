import React from 'react';
import { View, Text } from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/navigation/types';

type OtherServicesPageRouteProp = RouteProp<RootStackParamList, 'OtherServicesPage'>;

interface OtherServicesPageProps {
  route: OtherServicesPageRouteProp;
  navigation: NativeStackNavigationProp<RootStackParamList>;
}

const OtherServicesPage = ({ route = { params: { consultantId: '' } } }: OtherServicesPageProps) => {
  const { consultantId } = route.params;

  return (
    <View>
      <Text>Other Services Page for Consultant ID: {consultantId}</Text>
      {/* Add your other services content here */}
    </View>
  );
};

export default OtherServicesPage; 