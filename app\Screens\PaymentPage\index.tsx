import React, { useEffect, useState } from "react";
import { View, Text, ActivityIndicator } from "react-native";
import { useRoute } from "@react-navigation/native";
import { WebView } from "react-native-webview";

const PaymentPage = () => {
  const route = useRoute();
  const { redirectInfo } = route.params;
  const [loading, setLoading] = useState(true);

  // Handle WebView load state
  const handleWebViewLoad = () => {
    setLoading(false);
  };

  return (
    <View style={{ flex: 1 }}>
      {loading && (
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text>Loading Payment Page...</Text>
        </View>
      )}
      {redirectInfo && (
        <WebView
          source={{ uri: redirectInfo.url }} // Assuming the URL is in redirectInfo.url
          onLoad={handleWebViewLoad}
          style={{ flex: 1 }}
        />
      )}
    </View>
  );
};

export default PaymentPage;
