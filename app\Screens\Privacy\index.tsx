import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import tw from 'twrnc';

function Page() {
    const [sections, setSections] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchPrivacy = async () => {
            try {
                const response = await fetch('https://j3zs8egtxg23.nityasha.com/storage/privacy.json');
                const data = await response.json();
                setSections(data);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchPrivacy();
    }, []);

    if (loading) {
        return (
            <View style={tw`flex-1 items-center justify-center`}>
                <Text>Loading privacy policy...</Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={tw`flex-1 items-center justify-center`}>
                <Text>Error loading privacy policy: {error}</Text>
            </View>
        );
    }

    return (
        <ScrollView>
            <View style={tw`flex-1 items-center justify-center p-6`}>
                <Text style={tw`font-bold text-2xl`}>Privacy Policy</Text>
                <View style={tw`w-full`}>
                    {sections.map((section, index) => (
                        <View key={index} style={tw`mt-8`}>
                            <Text style={tw`text-2xl font-semibold mb-2`}>{section.title}</Text>
                            <Text style={tw`text-lg`}>{section.content}</Text>
                        </View>
                    ))}
                </View>
                <Text style={tw`text-center mt-8 text-lg`}>
                    By using our services, you acknowledge that you have read and understood
                    this Privacy Policy and agree to its terms.
                </Text>
            </View>
        </ScrollView>
    );
}

export default Page;
