import {
  View,
  Image,
  Text,
  TouchableOpacity,
  Alert,
  Modal,
  ActivityIndicator,
  StatusBar,
  ImageBackground,
} from "react-native";
import React, { useState, useEffect, useCallback } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import tw from "twrnc";
import {
  ImagePlus,
  UserRoundCog,
  LogOut,
  Newspaper,
  Book,
} from "lucide-react-native";
import { useFonts } from "expo-font";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { launchImageLibrary } from "react-native-image-picker";
import {
  Button,
  Dialog,
  Portal,
  PaperProvider,
} from "react-native-paper";

export default function Profile({ navigation }) {
  const [modalVisible, setModalVisible] = useState(false);
  const [userData, setUserData] = useState(null);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = React.useState(false);

  const showDialog = () => setVisible(true);

  const hideDialog = () => setVisible(false);
  const openLogoutModal = () => {
    setModalVisible(true);
  };

  const closeLogoutModal = () => {
    setModalVisible(false);
  };

  const [loaded] = useFonts({
    Helvetica_bold: require("@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf"),
  });

  const checkLoginStatus = useCallback(async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        setUserId(parsedSession.userId);
      } else {
        navigation.reset({
          index: 0,
          routes: [{ name: "Welcome" }],
        });
      }
    } catch (error) {
      console.error("Error checking login status:", error);
    }
  }, [navigation]);

  useEffect(() => {
    checkLoginStatus();
  }, [checkLoginStatus]);

  useEffect(() => {
    const fetchUserData = async () => {
      if (userId) {
        const fetchUrl = `https://nityasha.vercel.app/api/v1/users/${userId}`;
        try {
          const response = await fetch(fetchUrl);
          const data = await response.json();
          setUserData(data);  
        } catch (error) {
          console.error("Error fetching user data:", error);
        }
      }
    };
    fetchUserData();
  }, [userId]);

  const updateProfilePicture = async (imageUri) => {
    setLoading(true);
    const formData = new FormData();
    formData.append("profile_picture", {
      uri: imageUri,
      type: "image/jpeg", // Change this based on the image type
      name: "profile.jpg", // Name of the image file
    });
    try {
      const response = await fetch(
        `https://nityasha.vercel.app/api/v1/users/${userId}/pfp`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "multipart/form-data",
          },
          body: formData,
        }
      );
      if (!response.ok) {
        throw new Error(
          `Failed to update profile picture: ${response.statusText}`
        );
      }
      const updatedData = await response.json();
      setUserData(updatedData);
    } catch (error) {
      console.error("Error updating profile picture:", error);
      Alert.alert(
        "Error",
        "Failed to update profile picture. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const selectImage = () => {
    launchImageLibrary(
      {
        mediaType: "photo",
        quality: 1,
      },
      (response) => {
        if (response.didCancel) {
          // User canceled image picker
        } else if (response.error) {
          console.error("ImagePicker Error: ", response.error);
        } else {
          const uri = response.assets[0].uri; // Get the selected image URI
          updateProfilePicture(uri); // Call the function to upload the image
        }
      }
    );
  };

  if (!userData) {
    return (
      <View style={tw`flex-1 justify-center`}>
        <ActivityIndicator size="small" color="#000" />
      </View>
    );
  }

  const handleLogout = async () => {
    try {
      await AsyncStorage.removeItem("userSession"); // Clear user session
      navigation.reset({
        index: 0,
        routes: [{ name: "Welcome" }],
      });
    } catch (error) {
      console.error("Error during logout:", error);
      Alert.alert("Error", "Failed to log out. Please try again.");
    }
  };

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1`}>
      <SafeAreaView style={tw` h-full items-center px-5`}>
        {/* Loading Indicator */}
        {loading && (
          <ActivityIndicator
            size="large"
            color="#000"
            style={tw`absolute top-0 left-0 right-0 bottom-0`}
          />
        )}

        {/* Profile Picture */}
        <View
          style={tw`flex items-center justify-center w-[100px] h-[100px] flex-row relative`}
        >
          <TouchableOpacity onPress={selectImage}>
            <Image
              source={{ uri: userData.pfp }}
              style={tw`w-[100px] h-[100px] rounded-full`}
            />
          </TouchableOpacity>
          <View
            style={tw`absolute bottom-0 bg-[#fff] border-[#D7D9DC] border-2 w-[32px] items-center justify-center flex h-[32px] rounded-[8px] right-0`}
          >
            <ImagePlus size={16} color={"#39434F"} />
          </View>
        </View>

        {/* Username */}
        <Text style={tw`mt-3 text-[16px] font-semibold`}>
          {userData.username || "Guest"}
        </Text>

        {/* Account Settings */}
        <View
          style={[
            tw`flex items-start justify-center w-full mt-5`,
            { fontFamily: "Helvetica_bold" },
          ]}
        >
          <TouchableOpacity
            onPress={() => navigation.navigate("Account_Information")}
            style={tw`flex-row justify-between py-3 items-center rounded-[8px]  w-full`}
          >
            <Text style={{ fontFamily: "Helvetica_bold" }}>
              Account Information
            </Text>
            <UserRoundCog size={20} color={"#39434F"} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate("Terms")}
            style={tw`flex-row justify-between py-3 items-center rounded-[8px]  w-full`}
          >
            <Text style={{ fontFamily: "Helvetica_bold" }}>
              Terms & Conditions
            </Text>
            <Book size={20} color={"#39434F"} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate("Privacy")}
            style={tw`flex-row justify-between py-3 items-center rounded-[8px]  w-full`}
          >
            <Text style={{ fontFamily: "Helvetica_bold" }}>Privacy Policy</Text>
            <Book size={20} color={"#39434F"} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={showDialog}
            style={tw`flex-row justify-between py-3 items-center rounded-[8px]  w-full`}
          >
            <Text style={{ fontFamily: "Helvetica_bold" }}>Logout</Text>
            <LogOut size={20} color={"#39434F"} />
          </TouchableOpacity>
        </View>
        <View>
          <Portal>
            <Dialog visible={visible} onDismiss={hideDialog}>
              <Dialog.Title>Are you sure you want to log out?</Dialog.Title>
              <Dialog.Content>
                <Text style={tw`text-white`}>Clicking "Log Out" will end your current session and you will be
                logged out of the application. If you have any unsaved changes,
                they will be lost.g</Text>
              </Dialog.Content>
              <Dialog.Actions>
                <Button onPress={hideDialog} mode="contained" style={tw`px-3`}>
                Cancel
                </Button>
                <Button  onPress={handleLogout} mode="contained" style={tw`px-3`}>
                Log Out
                </Button>
              </Dialog.Actions>
            </Dialog>
          </Portal>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
}
