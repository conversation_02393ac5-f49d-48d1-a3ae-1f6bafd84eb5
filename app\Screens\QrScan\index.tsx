import { Camera<PERSON>iew, Camera } from "expo-camera";
import {
  View,
  SafeAreaView,
  StatusBar,
  Platform,
  AppState,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import { useEffect, useRef, useState } from "react";
import tw from "twrnc";
import axios from "axios";
import {
  Provider as PaperProvider,
  Portal,
  Dialog,
  Paragraph,
  Button,
} from "react-native-paper";
import { CheckCircle2, XCircle, AlertTriangle } from "lucide-react-native";
import { useNavigation } from "@react-navigation/native"; // ✅ Import navigation
import useAuth from "@/hooks/auth"

const { width, height } = Dimensions.get("window");
const scanBoxSize = 250;

export default function QRScanner() {
  const navigation = useNavigation(); // ✅ Initialize navigation
  const qrLock = useRef(false);
  const appState = useRef(AppState.currentState);
  const [hasPermission, setHasPermission] = useState(null);
    const userId = useAuth()
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogMessage, setDialogMessage] = useState("");

  const showDialog = (title, message) => {
    setDialogTitle(title);
    setDialogMessage(message);
    setDialogVisible(true);
  };

  const hideDialog = () => setDialogVisible(false);

  useEffect(() => {
    const getPermission = async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");
    };
    getPermission();
  }, []);

  useEffect(() => {
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        qrLock.current = false;
      }
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []);

  const confirmLogin = async (sessionId, userId) => {
    try {
      const response = await axios.post(
        "http://api.chats.nityasha.com/api/auth/confirm-login",
        {
          sessionId,
          userId,
        }
      );

      if (response.status === 200) {
        showDialog("✅ Login Confirmed", "You have been logged in.");
        setTimeout(() => {
          hideDialog();
          navigation.goBack(); // ✅ Auto go back after login
        }, 1500);
      } else {
        showDialog("❌ Failed", "Login confirmation failed.");
      }
    } catch (error) {
      showDialog("🚫 Error", error.message);
    }
  };

  const handleBarcodeScanned = ({ data }) => {
    if (!data || qrLock.current) return;
    qrLock.current = true;

    console.log("📷 Scanned QR Data:", data);

    setTimeout(async () => {
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(data);

      try {
        if (isUUID) {
          await confirmLogin(data, userId);
        } else {
          showDialog("Not Recognized", "QR code is not a valid login code.");
        }
      } catch (err) {
        showDialog("Error", err.message);
      } finally {
        qrLock.current = false;
      }
    }, 500);
  };

  if (hasPermission === null) {
    return (
      <PaperProvider>
        <View style={tw`flex-1 items-center justify-center bg-black`}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      </PaperProvider>
    );
  }

  if (!hasPermission) {
    return (
      <PaperProvider>
        <View style={tw`flex-1 items-center justify-center bg-black`}>
          <Paragraph style={tw`text-white text-lg`}>
            Camera permission is required to scan QR codes.
          </Paragraph>
        </View>
      </PaperProvider>
    );
  }

  return (
    <PaperProvider>
      <SafeAreaView style={tw`flex-1 bg-black`}>
        {Platform.OS === "android" && <StatusBar hidden />}

        <CameraView
          style={tw`absolute inset-0`}
          facing="back"
          onBarcodeScanned={handleBarcodeScanned}
        />

        {/* Overlays */}
        <View style={[tw`absolute left-0 right-0 bg-black/80`, { top: 0, height: (height - scanBoxSize) / 2 }]} />
        <View style={[tw`absolute top-0 bottom-0 bg-black/80`, { left: 0, width: (width - scanBoxSize) / 2 }]} />
        <View style={[tw`absolute top-0 bottom-0 bg-black/80`, { right: 0, width: (width - scanBoxSize) / 2 }]} />
        <View style={[tw`absolute left-0 right-0 bg-black/80`, { bottom: 0, height: (height - scanBoxSize) / 2 }]} />

        {/* Center Box */}
        <View
          style={[
            tw`absolute border-4 border-white rounded-xl`,
            {
              width: scanBoxSize,
              height: scanBoxSize,
              top: height / 2 - scanBoxSize / 2,
              left: width / 2 - scanBoxSize / 2,
            },
          ]}
        />

        {/* Paper Dialog with Lucide Icons */}
        <Portal>
          <Dialog visible={dialogVisible} onDismiss={hideDialog}>
            <Dialog.Title>{dialogTitle}</Dialog.Title>
            <Dialog.Content style={tw`flex-row items-center gap-3`}>
              {dialogTitle.includes("Login") && <CheckCircle2 color="green" size={30} />}
              {dialogTitle.includes("Error") && <XCircle color="red" size={30} />}
              {dialogTitle.includes("Not Recognized") && <AlertTriangle color="orange" size={30} />}
              <Paragraph style={tw`flex-1`}>{dialogMessage}</Paragraph>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hideDialog}>OK</Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
      </SafeAreaView>
    </PaperProvider>
  );
}
