import { View, Text, Alert, Linking } from 'react-native';
import React from 'react';
import { SafeAreaView } from 'react-native';
import tw from 'twrnc';
import Button from '@/components/ui/Button';
import base64 from 'react-native-base64';

export default function Index({ route, navigation }) {
    const { amount } = route.params;
    const merchantId = "PGTESTPAYUAT86";
    const gst = 0.18 * amount;
    const payableAmount = amount + gst;

    const initiatePhonePePayment = async () => {
        try {
            const merchantTransactionId = `MT${Date.now()}`;
            const payload = {
                merchantId: merchantId,
                merchantTransactionId: merchantTransactionId,
                merchantUserId: `MUID${Date.now()}`,
                amount: Math.round(payableAmount * 100),
                redirectUrl: "yourapp://payment-result",
                redirectMode: "REDIRECT",
                callbackUrl: "https://yourapp.com/callback",
                mobileNumber: "9999999999",
                paymentInstrument: {
                    type: "UPI_INTENT",
                    targetApp: "com.phonepe.app",
                    targetFlowType: "INTENT"
                }
            };

            // Convert the payload to base64
            const base64Payload = base64.encode(JSON.stringify(payload));

            // In a production app, you would make an API call to your backend here
            // Your backend would handle the X-VERIFY header generation and actual PhonePe API call
            // For testing, we'll use the PhonePe test environment directly
            const response = await fetch('https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/pay', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-VERIFY': 'test_checksum' // In production, this should come from your backend
                },
                body: JSON.stringify({
                    request: base64Payload
                })
            });

            const data = await response.json();

            if (data.success) {
                // For UPI_INTENT, PhonePe will return a URL to redirect to
                const redirectUrl = data.data.instrumentResponse.redirectInfo.url;
                await Linking.openURL(redirectUrl);

                // You might want to set up a deep link handler to capture the payment result
                // when the user returns to your app
            } else {
                Alert.alert('Payment Failed', data.message || 'Failed to initiate payment');
            }
        } catch (error) {
            console.error('Payment Error:', error);
            Alert.alert('Error', 'Something went wrong while processing payment');
        }
    };

    return (
        <SafeAreaView style={tw`flex-1 bg-white px-2`}>
            <View style={tw`flex flex-row items-center justify-between w-full`}>
                <Text style={tw`text-black text-sm mt-2 font-bold`}>Recharge Amount</Text>
                <Text style={tw`text-black text-sm mt-2 font-bold`}>₹{amount}</Text>
            </View>
            <View style={tw`flex flex-row items-center justify-between w-full`}>
                <Text style={tw`text-black text-sm mt-2`}>GST (18%)</Text>
                <Text style={tw`text-black text-sm mt-2 font-bold`}>₹{gst.toFixed(2)}</Text>
            </View>
            <View style={tw`flex flex-row items-center justify-between w-full`}>
                <Text style={tw`text-black text-sm mt-2`}>Payable Amount</Text>
                <Text style={tw`text-black text-sm mt-2 font-bold`}>₹{payableAmount.toFixed(2)}</Text>
            </View>
            <View style={tw`mt-4`}>
                <Button onPress={initiatePhonePePayment}>Pay with PhonePe</Button>
            </View>
        </SafeAreaView>
    );
}
