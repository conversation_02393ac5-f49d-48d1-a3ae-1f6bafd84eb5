import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  View,
  Text,
  Image,
  TextInput,
  Alert,
  ScrollView,
  StatusBar,
  BackHandler,
  Keyboard,
  ImageBackground,
} from "react-native";
import axios from "axios";
import tw from "twrnc";
import { Search as SearchIcon } from "lucide-react-native";

import { useFonts } from "expo-font";
import ConsultantS from "@/components/ConsultantS";
import {
  CommonActions,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native"; // Import useFocusEffect
import Logo from "@/assets/images/image.png";
import Wallet from "@/components/Wallet";
import { Searchbar } from "react-native-paper";
import { IconButton } from "react-native-paper";
import { PaperProvider } from "react-native-paper";
import useTokenPush from "@/hooks/tokenpush";
import useOnlineStatus from '@/hooks/useOnlineisOnline';
import { Skeleton } from 'moti/skeleton'
interface User {
  id: string;
  name: string;
  category_name?: string;
  perMinuteRate: number;
}


interface SearchProps {
  route: any; // You might want to define a proper type for route
  navigation: any; // You might want to define a proper type for navigation
}

const Search = ({ route }: SearchProps) => {
  const navigation = useNavigation();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<TextInput>(null);
  const [balance, setBalance] = useState(100);
  const [chatDuration, setChatDuration] = useState(5);
  const [modalVisible, setModalVisible] = useState(false);
  const [rechargeAmount, setRechargeAmount] = useState("");
  const [visible, setVisible] = React.useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  useOnlineStatus();

  useTokenPush();

  const openMenu = () => setVisible(true);

  const closeMenu = () => setVisible(false);

  const deductBalance = async (userId: string, totalDeduction: number) => {
    const newBalance = Math.max(0, balance - totalDeduction);
    try {
      const updateURL = `https://nityasha.vercel.app/api/v1/users/${userId}`;
      const response = await fetch(updateURL, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ balance: newBalance }),
      });

      if (response.ok) {
        Alert.alert(`Balance updated. Your new balance is ${newBalance}.`);
        setBalance(newBalance);
        navigation.navigate("Chat", {
          balance: newBalance,
          consultantId: userId,
        });
      } else {
        Alert.alert("Error updating balance.");
      }
    } catch (error) {
      console.error("Error updating balance:", error);
      Alert.alert("Error updating balance.");
    }
  };

  const handleRecharge = () => {
    const rechargeValue = parseInt(rechargeAmount, 10);
    if (!isNaN(rechargeValue) && rechargeValue > 0) {
      const newBalance = balance + rechargeValue;
      setBalance(newBalance);
      setModalVisible(false);
      Alert.alert(`Recharge successful! Your new balance is ${newBalance}.`);
      setRechargeAmount("");

      // After recharge, check if balance is now sufficient for minimum chat
      const minChatDuration = 3;
      const requiredBalance =
        minChatDuration * (route.params?.perMinuteRate || 20);
      if (newBalance >= requiredBalance) {
        // If balance is now sufficient, proceed with chat
        deductBalance(route.params?.userId, requiredBalance);
      }
    } else {
      Alert.alert("Please enter a valid amount to recharge.");
    }
  };

  const handleChatNow = (
    userId: string,
    name: string,
    perMinuteRate: number
  ) => {
    // Minimum chat duration is 3 minutes
    const minChatDuration = 3;
    const requiredBalance = minChatDuration * perMinuteRate;

    if (balance < requiredBalance) {
      // Calculate minimum recharge amount for 3 minutes
      const minRecharge = requiredBalance - balance;
      setRechargeAmount(minRecharge.toString());
      setModalVisible(true);
    } else {
      // If balance is sufficient, proceed with chat
      deductBalance(userId, requiredBalance);
    }
  };

  const [loaded] = useFonts({
    Helvetica_bold: require("@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf"),
    Satoshi: require("@/assets/fonts/Satoshi-Medium.otf"),
  });

  // We no longer automatically focus the input when the screen is focused
  // This prevents the keyboard from automatically opening

  useEffect(() => {
    const fetchData = async () => {
      try {
        const usersResponse = await axios.get(
          "https://nityasha.vercel.app/api/v1/consultants"
        );
        setUsers(usersResponse.data);
        setFilteredUsers(usersResponse.data); // Set filtered users to all users initially
      } catch (err) {
        console.error("Failed to load data", err);
        setError("Failed to load data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);



  const handleSearch = useCallback(
    async (text) => {
      setSearch(text);

      // If text is empty, show all consultants
      if (!text) {
        setFilteredUsers(users);
        return;
      }

      // Check if input starts with @
      if (text.startsWith('@')) {
        const username = text.slice(1); // Remove @ symbol
        try {
          const response = await axios.get(
            `https://api.search.nityasha.com/users/search?name=${username}`
          );
          setFilteredUsers(response.data);
        } catch (error) {
          console.error("Error searching users:", error);
        }
      } else {
        const filtered = users.filter(
          (user) =>
            (user.category_name &&
              user.category_name.toLowerCase().includes(text.toLowerCase())) ||
            (user.name && user.name.toLowerCase().includes(text.toLowerCase())) ||
            (user.number && user.number.includes(text))
        );
        setFilteredUsers(filtered);


      }
    },
    [users]
  );



  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        if (isKeyboardVisible) {
          Keyboard.dismiss();
          return true; // Prevents default back action
        }
        return false; // Allows default back action
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        subscription.remove();
      };
    }, [isKeyboardVisible])
  );

  if (!loaded || loading) {
    return (
      <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 py-3`}>
             <StatusBar translucent backgroundColor="#BCEAC5" />

        <View
          style={tw`flex-row items-center border border-gray-300 rounded-full px-3 mx-2 mb-2`}
        >
          <SearchIcon size={20} color="gray" style={tw`mr-2`} />
          <TextInput
            style={tw`flex-1 h-10`}
            placeholder="Search Consultant"
            value={search}
            onChangeText={handleSearch}
          />
        </View>
        <View style={tw`gap-3 mx-3`}>
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
          <Skeleton width={'100%'} height={90} radius={16} colorMode="light" transition={{ type: "timing", duration: 1000, loop: true }} colors={["#E1E9E1", "#F5FCF5"]} />
        </View>

      </ImageBackground>
    );
  }

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 `}>
      <View style={tw`flex-1`}>
        <StatusBar translucent backgroundColor="#BCEAC5" />
        <View style={tw`h-16 flex-row items-center px-4`}>
          {/* Left Section (Back Button) */}
          <View style={tw`flex-1`}>
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={() => {
                navigation.dispatch(
                  CommonActions.reset({
                    index: 0,
                    routes: [{ name: "BottomTabs" }],
                  })
                );
              }}
              style={{
                backgroundColor: "#F5FCF5",
                borderRadius: '100%',
                width: 50,
                height: 50,
                justifyContent: "center",
                alignItems: "center",
                elevation: 5,
              }}
            />
          </View>

          <View style={tw`flex-1 items-center`}>
            <Image
              source={Logo}
              style={[
                tw`w-12 h-12 rounded-full bg-black`,
                { tintColor: "#fff" },
              ]}
            />
          </View>

          <View style={tw`flex-1 items-end`}>
            <Wallet />
          </View>
        </View>

        <View style={tw`flex-1 rounded-t-[30px] pt-4 px-3`}>
          <Searchbar
            placeholder="Find Something New"
            style={{
              borderRadius: 300,
              elevation: 3, // Shadow effect
              backgroundColor: "#F5FCF5",
            }}
            placeholderTextColor="#000"
            ref={inputRef}
            value={search}
            onChangeText={handleSearch}
            autoFocus={false}
            onPressIn={() => {
              if (inputRef.current) {
                inputRef.current.focus();
              }
            }}
            onKeyPress={({ nativeEvent }) => {
              if (nativeEvent.key === 'Backspace' && search.length === 0) {
                setFilteredUsers(users);
                if (inputRef.current) {
                  inputRef.current.blur();
                }
                Keyboard.dismiss();
              }
            }}
          />

          <ConsultantS
            filteredUsers={filteredUsers} />
        </View>
      </View>
    </ImageBackground>
  );
};

export default Search;
