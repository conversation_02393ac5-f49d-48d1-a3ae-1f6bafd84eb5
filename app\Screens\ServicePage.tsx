import React, { useState } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity, TextInput, StatusBar } from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/navigation/types';
import tw from 'twrnc';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ArrowLeft, FlagTriangleLeft, UserCircle, CalendarDays, Clock, MapPin } from 'lucide-react-native';

type ServicePageRouteProp = RouteProp<RootStackParamList, 'ServicePage'>;

interface ServicePageProps {
    route: ServicePageRouteProp;
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

const ServicePage = ({ route = { params: { consultantId: '' } } }: ServicePageProps) => {
    const { consultantId } = route.params;
    const navigation = useNavigation();
    const [name, setName] = useState('');

    const saveProfile = async () => {
        try {
            const profileData = {
                profiles: {
                    profile1: {
                        Name: name
                    }
                }
            };
            await AsyncStorage.setItem('userProfile', JSON.stringify(profileData));
            navigation.navigate('ServicePage1', { consultantId });
        } catch (error) {
            console.error('Error saving profile:', error);
        }
    };

    return (
        <SafeAreaView style={tw`h-full bg-white`}>
            <StatusBar backgroundColor={'white'} />
            <View style={tw`w-full h-28`}>
                <View style={tw`flex-1 p-4 flex-row gap-2`}>
                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <ArrowLeft size={24} color="black" />
                    </TouchableOpacity>
                    <Text style={tw`text-base ml-3 font-bold`}>Enter Your Details</Text>
                </View>
                <View style={tw`gap-5 flex-row items-start pl-4`}>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <UserCircle size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
                        <FlagTriangleLeft size={15} color="black" />
                    </View>
                    <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
                        <CalendarDays size={15} color="black" />
                    </View>
                    <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
                        <Clock size={15} color="black" />
                    </View>
                    <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
                        <MapPin size={15} color="black" />
                    </View>
                </View>
            </View>
            <View style={tw`flex-1 mt-10 px-5`}>
                <View style={tw`flex-1`}>
                    <Text style={[tw`text-2xl font-bold text-gray-500`, { fontFamily: 'Helvetica_bold' }]}>Hey there!</Text>
                    <Text style={[tw`text-2xl font-bold mt-2 text-gray-500 mb-5`, { fontFamily: 'Helvetica_bold' }]}>What is your name?</Text>
                    <TextInput
                        placeholder="Enter your name"
                        value={name}
                        onChangeText={setName}
                        style={tw`border-b border-gray-300 py-2 text-base`}
                    />
                </View>
                <TouchableOpacity
                    onPress={saveProfile}
                    style={tw`bg-emerald-500 p-4 rounded-lg mb-5`}
                >
                    <Text style={tw`text-white text-center font-bold`}>Next</Text>
                </TouchableOpacity>
            </View>
        </SafeAreaView>
    );
};

export default ServicePage;
