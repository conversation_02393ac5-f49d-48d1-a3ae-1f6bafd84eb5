import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity, StatusBar } from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/navigation/types';
import tw from 'twrnc';
import {
  ArrowLeft,
  FlagTriangleLeft,
  UserCircle,
  CalendarDays,
  Clock,
  MapPin,
} from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

type ServicePageRouteProp = RouteProp<RootStackParamList, 'ServicePage'>;

interface ServicePageProps {
  route: ServicePageRouteProp;
  navigation: NativeStackNavigationProp<RootStackParamList>;
}

const ServicePage1 = ({ route, navigation }: ServicePageProps) => {
  const { consultantId } = route.params;
  const [selectedGender, setSelectedGender] = useState<string | null>(null);
  const [name, setName] = useState<string | null>(null);

  // Retrieve gender (and optionally name) from AsyncStorage when the component mounts
  useEffect(() => {
    const getUserProfile = async () => {
      try {
        const userProfile = await AsyncStorage.getItem('userProfile');
        if (userProfile) {
          const parsedData = JSON.parse(userProfile);
          const gender = parsedData.profiles?.profile1?.gender;
          if (gender) {
            setSelectedGender(gender);
          }
          // If you plan to retrieve the name as well, you could do something like:
          // const storedName = parsedData.profiles?.profile1?.name;
          // if (storedName) setName(storedName);
        }
      } catch (error) {
        console.error('Error reading from AsyncStorage:', error);
      }
    };
    getUserProfile();
  }, []);

  // Save gender (and keep name if already set, or default to empty string) in AsyncStorage
  const handleGenderSelect = async (gender: string) => {
    setSelectedGender(gender);

    try {
      const userProfile = await AsyncStorage.getItem('userProfile');
      const parsedData = userProfile ? JSON.parse(userProfile) : {};

      // Retrieve any existing profile1 data, defaulting name to an empty string if not present
      const existingProfile1 = parsedData.profiles?.profile1 || { name: '' };

      // Ensure that profile1 always has the keys 'name' and 'gender'
      parsedData.profiles = {
        ...parsedData.profiles,
        profile1: {
          ...existingProfile1,
          gender: gender,
        },
      };

      await AsyncStorage.setItem('userProfile', JSON.stringify(parsedData));
      console.log('Updated userProfile:', parsedData);
    } catch (error) {
      console.error('Error saving to AsyncStorage:', error);
    }
  };

  const handleNext = () => {
    if (selectedGender) {
      navigation.navigate('ServicePage2', { consultantId, gender: selectedGender });
    }
  };

  return (
    <SafeAreaView style={tw`h-full bg-white`}>
      <StatusBar backgroundColor="white" />
      <View style={tw`w-full h-28`}>
        <View style={tw`flex-1 p-4 flex-row gap-2`}>

          <TouchableOpacity onPress={() => navigation.goBack()}>
            <ArrowLeft size={24} color="black" />
          </TouchableOpacity>
          <Text style={tw`text-base ml-3 font-bold`}>Enter Your Details</Text>
        </View>
        <View style={tw`gap-5 flex-row items-start pl-4`}>
          <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
            <UserCircle size={15} color="white" />
          </View>
          <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
            <FlagTriangleLeft size={15} color="white" />
          </View>
          <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
            <CalendarDays size={15} color="black" />
          </View>
          <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
            <Clock size={15} color="black" />
          </View>
          <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
            <MapPin size={15} color="black" />
          </View>
        </View>
      </View>
      <View style={tw`flex-1 mt-10 px-5`}>
        <View style={tw`flex-1`}>
          <Text style={tw`text-2xl font-bold text-gray-500`}>What is your gender?</Text>
          <View style={tw`flex-row gap-2 justify-between items-center my-5 `}>
           <View style={tw`items-center justify-center gap-3`}>
           <TouchableOpacity
              style={tw`items-center justify-center rounded-full w-40 h-40 ${
                selectedGender === 'Male' ? 'bg-emerald-500' : 'bg-gray-200'
              }`}
              onPress={() => handleGenderSelect('Male')}
            >
              <Text style={tw`text-[60px] ${selectedGender === 'Male' ? 'text-white' : 'text-black'}`}>
                ♂
              </Text>
            </TouchableOpacity>
            <Text style={[tw`text-center text-2xl`,{fontFamily:'Helvetica_bold'}]}>Male</Text>
           </View>
          <View style={tw`items-center justify-center gap-3`}>
          <TouchableOpacity
              style={tw`items-center justify-center rounded-full w-40 h-40 ${
                selectedGender === 'Female' ? 'bg-emerald-500' : 'bg-gray-200'
              }`}
              onPress={() => handleGenderSelect('Female')}
            >
              <Text style={tw`text-[60px] ${selectedGender === 'Female' ? 'text-white' : 'text-black'}`}>
                ♀
              </Text>
            </TouchableOpacity>
            <Text style={[tw`text-center text-2xl`,{fontFamily:'Helvetica_bold'}]}>Female</Text>
          </View>
          </View>
          <TouchableOpacity

            style={tw`bg-emerald-500 p-3 rounded-md w-full mt-3 ${selectedGender ? '' : 'opacity-50'}`}
            onPress={handleNext}
            disabled={!selectedGender}
          >
            <Text style={tw`text-white text-center`}>Next</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ServicePage1;
