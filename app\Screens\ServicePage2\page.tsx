import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity, Platform } from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/navigation/types';
import tw from 'twrnc';
import { ArrowLeft, FlagTriangleLeft, UserCircle, CalendarDays, Clock, MapPin } from 'lucide-react-native';
import { StatusBar } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';

type ServicePageRouteProp = RouteProp<RootStackParamList, 'ServicePage'>;

interface ServicePageProps {
    route: ServicePageRouteProp;
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

const ServicePage2 = ({ route = { params: { consultantId: '' } } }: ServicePageProps) => {
    const { consultantId } = route.params;
    const navigation = useNavigation();
    const [date, setDate] = useState(new Date());
    const [showPicker, setShowPicker] = useState(false);

    useEffect(() => {
        const getuserProfile = async () => {
            try {
                const userProfile = await AsyncStorage.getItem('userProfile');
                if (userProfile) {
                    const parsedData = JSON.parse(userProfile);
                    const dob = parsedData.profiles?.profile1?.dob;
                    if (dob) {
                        setDate(new Date(dob));
                    }
                }
            } catch (error) {
                console.error('Error reading profile data:', error);
            }
        };
        getuserProfile();
    }, []);

    const onChange = (event: any, selectedDate?: Date) => {
        const currentDate = selectedDate || date;
        setShowPicker(Platform.OS === 'ios');
        setDate(currentDate);
    };

    const handleNext = async () => {
        try {
            const userProfile = await AsyncStorage.getItem('userProfile');
            const updatedData = userProfile ? JSON.parse(userProfile) : {};
            updatedData.profiles = {
                ...updatedData.profiles,
                profile1: {
                    ...updatedData.profiles?.profile1,
                    dob: date.toISOString(),
                },
            };
            await AsyncStorage.setItem('userProfile', JSON.stringify(updatedData));
            navigation.navigate('ServicePage3', { consultantId, dob: date.toISOString() });
            console.log(userProfile)
        } catch (error) {
            console.error('Error saving profile data:', error);
        }
    };

    return (
        <SafeAreaView style={tw`h-full bg-white`}>
            <StatusBar backgroundColor={'white'} />
            <View style={tw`w-full h-28`}>
                <View style={tw`flex-1 p-4 flex-row gap-2`}>

                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <ArrowLeft size={24} color="black" />
                    </TouchableOpacity>
                    <Text style={tw`text-base ml-3 font-bold`}>Enter Your Details</Text>
                </View>
                <View style={tw`gap-5 flex-row items-start pl-4`}>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <UserCircle size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <FlagTriangleLeft size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <CalendarDays size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
                        <Clock size={15} color="black" />
                    </View>
                    <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
                        <MapPin size={15} color="black" />
                    </View>
                </View>
            </View>
            <View style={tw`flex-1 mt-10 px-5`}>
                <View style={tw`flex-1`}>
                    <Text style={[tw`text-2xl font-bold text-gray-500`, { fontFamily: 'Helvetica_bold' }]}>What's your birth date?</Text>
                    
                    <TouchableOpacity 
                        style={tw`mt-4 p-3 border border-gray-300 rounded-md`}
                        onPress={() => setShowPicker(true)}
                    >
                        <Text style={tw`text-gray-700`}>
                            {date.toLocaleDateString()}
                        </Text>
                    </TouchableOpacity>

                    {showPicker && (
                        <DateTimePicker
                            testID="dateTimePicker"
                            value={date}
                            mode="date"
                            display="spinner"
                            onChange={onChange}
                            maximumDate={new Date()}
                            locale="en"
                            themeVariant="light"
                            style={tw`w-full`}
                        />
                    )}

                    <TouchableOpacity 
                        style={tw`bg-emerald-500 p-3 rounded-md w-full mt-3`} 
                        onPress={handleNext}
                    >
                        <Text style={[tw`text-white text-center`, { fontFamily: 'Helvetica_bold' }]}>Next</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
};

export default ServicePage2;
