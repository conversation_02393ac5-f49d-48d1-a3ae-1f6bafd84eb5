import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity, StatusBar } from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/navigation/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { ArrowLeft, FlagTriangleLeft, UserCircle, CalendarDays, Clock, MapPin } from 'lucide-react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';  // Import the modal picker

type ServicePageRouteProp = RouteProp<RootStackParamList, 'ServicePage'>;

interface ServicePageProps {
    route: ServicePageRouteProp;
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

const ServicePage3 = ({ route = { params: { consultantId: '' } } }: ServicePageProps) => {
    const { consultantId } = route.params;
    const navigation = useNavigation();
    const [showTimePicker, setShowTimePicker] = useState(false);
    const [selectedTime, setSelectedTime] = useState(() => {
        const now = new Date();
        now.setSeconds(0);
        now.setMilliseconds(0);
        return now;
    });

    // When the component mounts, load the saved userbirthtime (if any)
    useEffect(() => {
        const loaduserbirthtime = async () => {
            try {
                const userProfile = await AsyncStorage.getItem('userProfile');
                if (userProfile) {
                    const parsedData = JSON.parse(userProfile);
                    const userbirthtime = parsedData?.profiles?.profile1?.userbirthtime;
    
                    if (userbirthtime) {
                        const time = new Date(userbirthtime);
                        console.log('Saved Birth Time:', time);
    
                        if (!isNaN(time.getTime())) { // Check if it's a valid date
                            setSelectedTime(time);
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading userbirthtime:', error);
            }
        };
    
        loaduserbirthtime();
    }, []);
    

    // Handler for time picker changes
    const onTimeChange = (selectedDate: Date) => {
        setShowTimePicker(false);
        if (selectedDate) {
            setSelectedTime(selectedDate); // Directly set the selected date
        }
    };
    
    
    console.log('selectedDate', selectedTime)

    // Format the selected time for display
    const formatTime = (date: Date) => {
        return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });
    };
    

    // When the user clicks "Next", merge the selected userbirthtime into the existing profile.
    // This way any other details (like gender) already stored will be preserved.
    const handleNext = async () => {
        try {
            const storedProfile = await AsyncStorage.getItem('userProfile');
            let updatedProfile;

            if (storedProfile) {
                const parsedProfile = JSON.parse(storedProfile);
                // Ensure profile1 has required fields with defaults
                updatedProfile = {
                    ...parsedProfile,
                    profiles: {
                        ...parsedProfile.profiles,
                        profile1: {
                            ...parsedProfile.profiles?.profile1,
                            userbirthtime: selectedTime.toISOString(),
                        },

                    },
                };
            } else {
                // If no profile exists, create one with default values
                updatedProfile = {
                    profiles: {
                        profile1: {
                            userbirthtime: selectedTime.toISOString(),
                        },
                    },
                };
            }

            await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
            // Navigate to the next page with the required parameters
            navigation.navigate('ServicePage4', { 
                consultantId,
                userbirthtime: selectedTime.toISOString() 
            });
        } catch (error) {
            console.error('Error saving user profile:', error);
        }
    };

    return (
        <SafeAreaView style={tw`h-full bg-white`}>
            <StatusBar backgroundColor={'white'} />
            <View style={tw`w-full h-28`}>
                <View style={tw`flex-1 p-4 flex-row gap-2`}>
                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <ArrowLeft size={24} color="black" />
                    </TouchableOpacity>
                    <Text style={tw`text-base ml-3 font-bold`}>Enter Your Details</Text>
                </View>
                <View style={tw`gap-5 flex-row items-start pl-4`}>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <UserCircle size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <FlagTriangleLeft size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <CalendarDays size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <Clock size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-gray-200 w-7 h-7 items-center justify-center`}>
                        <MapPin size={15} color="black" />
                    </View>
                </View>
            </View>
            <View style={tw`flex-1 mt-10 px-5`}>
                <View style={tw`flex-1`}>
                    <Text style={[tw`text-2xl font-bold text-gray-500`, { fontFamily: 'Helvetica_bold' }]}>
                        Enter your birth time
                    </Text>
                    
                    <TouchableOpacity 
                        style={[tw`border border-gray-300 rounded-md p-3 w-full mt-2`, { fontFamily: 'Helvetica_bold' }]}
                        onPress={() => setShowTimePicker(true)}
                    >
                        <Text style={tw`text-gray-500`}>
                            {formatTime(selectedTime)}
                        </Text>
                    </TouchableOpacity>

                    <DateTimePickerModal
                        isVisible={showTimePicker}
                        mode="time"
                        date={selectedTime}
                        onConfirm={onTimeChange}
                        onCancel={() => setShowTimePicker(false)}
                        is24Hour={false}
                    />

                    <TouchableOpacity 
                        style={tw`bg-emerald-500 p-3 rounded-md w-full mt-3`} 
                        onPress={handleNext}
                    >
                        <Text style={[tw`text-white text-center`, { fontFamily: 'Helvetica_bold' }]}>
                            Next
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
};

export default ServicePage3;
