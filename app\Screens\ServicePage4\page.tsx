import React, { useEffect, useState } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity, TextInput, FlatList, StatusBar } from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/navigation/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { ArrowLeft, FlagTriangleLeft, UserCircle, CalendarDays, Clock, MapPin } from 'lucide-react-native';

type ServicePageRouteProp = RouteProp<RootStackParamList, 'ServicePage'>;

interface ServicePageProps {
    route: ServicePageRouteProp;
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

// Add this type for the search result items
type SearchResultItem = {
    display_name: string;
    place_id: string;
};

const ServicePage4 = ({ route = { params: { consultantId: '' }, key: 'ServicePage', name: 'ServicePage' } }: ServicePageProps) => {
    const { consultantId } = route.params;
    const navigation = useNavigation();
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [balance, setBalance] = useState(0);
    const [error, setError] = useState<string | null>(null);

    // Add this useEffect to check for existing birthplace
    useEffect(() => {
        const checkExistingBirthplace = async () => {
            try {
                const storeduserProfile = await AsyncStorage.getItem('userProfile');
                if (storeduserProfile) {
                    const userProfile = JSON.parse(storeduserProfile);
                    if (userProfile?.profiles?.profile1?.birthplace) {
                        setSearchQuery(userProfile.profiles.profile1.birthplace);
                    }
                }
            } catch (error) {
                console.error('Error fetching birthplace:', error);
            }
        };

        checkExistingBirthplace();
    }, []);

    const handleSearch = async (query: string) => {
        setSearchQuery(query);
        if (query.length > 2) {
            setIsSearching(true);
            try {
                const response = await fetch(`https://api.locationiq.com/v1/autocomplete?key=***********************************&q=${query}`);
                const data = await response.json();
                setSearchResults(data);
            } catch (error) {
                console.error('Search error:', error);
            } finally {
                setIsSearching(false);
            }
        } else {
            setSearchResults([]);
        }
    };

    const handleNext = async () => {
        if (!searchQuery) return;

        try {
            // Get existing profile data from AsyncStorage
            const storeduserProfile = await AsyncStorage.getItem('userProfile');
            let userProfile = storeduserProfile ? JSON.parse(storeduserProfile) : {};

            // Ensure the profile structure exists
            if (!userProfile.profiles) userProfile.profiles = {};
            if (!userProfile.profiles.profile1) userProfile.profiles.profile1 = {};

            // Save birthplace in userProfile
            userProfile.profiles.profile1.birthplace = searchQuery;
            await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
            console.log('storeduserProfile', userProfile);

            // Fetch consultant details
            const consultantResponse = await fetch(`https://nityasha.vercel.app/api/v1/consultants/${consultantId}`);
            if (!consultantResponse.ok) throw new Error('Failed to fetch consultant details');
            const consultantData = await consultantResponse.json();

            // Calculate chat duration and cost
            const perMinuteRate = consultantData.per_minute_rate; // Rate per minute
            const affordableDuration = Math.floor(balance / perMinuteRate); // Maximum minutes user afford kar sakta hai
            const chatDuration = Math.min(affordableDuration); // User balance aur consultant ke max limit ka comparison
            
            const totalCost = chatDuration * perMinuteRate;
            const newBalance = balance - totalCost;

            // Navigate to Chat screen with parameters
            navigation.navigate('Chat', { 
                consultantId,
                chatDuration,
                astrologer: true,
                balance: balance
            });
        } catch (error) {
            console.error('Error saving birthplace:', error);
        }
    };
    const fetchUserBalance = async (userId: string) => {
        try {
          const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
          if (!response.ok) throw new Error('Failed to fetch balance');
          const userData = await response.json();
          setBalance(parseFloat(userData.balance) || 0);
        } catch (err) {
          if (err instanceof Error) {
            setError(err.message);
          } else {
            setError('An unknown error occurred');
          }
        }
      };

    useEffect(() => {
        const intervalId = setInterval(() => {
            AsyncStorage.getItem('userSession').then((session) => {
                if (session) {
                    const userId = JSON.parse(session).userId;
                    fetchUserBalance(userId);
                }
            });
        }, 6000);

        return () => clearInterval(intervalId);
    }, []);

    // Add type to renderItem parameter
    const renderItem = ({ item }: { item: SearchResultItem }) => (
        <TouchableOpacity 
            style={tw`p-3 border-b border-gray-200`}
            onPress={() => {
                setSearchQuery(item.display_name);
                setSearchResults([]);
            }}
        >
            <Text style={tw`text-gray-700`}>{item.display_name}</Text>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView style={tw`h-full bg-white`}>
            <StatusBar backgroundColor={'white'} />
            <View style={tw`w-full h-28`}>
                <View style={tw`flex-1 p-4 flex-row gap-2`}>

                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <ArrowLeft size={24} color="black" />
                    </TouchableOpacity>
                    <Text style={tw`text-base ml-3 font-bold`}>Enter Your Details</Text>
                </View>
                <View style={tw`gap-5 flex-row items-start pl-4`}>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <UserCircle size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <FlagTriangleLeft size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <CalendarDays size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <Clock size={15} color="white" />
                    </View>
                    <View style={tw`rounded-full bg-emerald-500 w-7 h-7 items-center justify-center`}>
                        <MapPin size={15} color="white" />
                    </View>
                </View>
            </View>
            <View style={tw`flex-1 mt-10 px-5`}>
                <View style={tw`flex-1`}>
                    <Text style={[tw`text-2xl font-bold text-gray-500`, { fontFamily: 'Helvetica_bold' }]}>
                        Where were you born?
                    </Text>
                    <TextInput
                        placeholder="Enter your birth place"
                        value={searchQuery}
                        onChangeText={handleSearch}
                        style={[tw`border border-gray-300 rounded-md p-3 w-full mt-2`, { fontFamily: 'Helvetica_bold' }]}
                    />
                    {isSearching && <Text style={tw`text-gray-500 mt-2`}>Searching...</Text>}
                    {searchResults.length > 0 && (
                        <FlatList
                            data={searchResults}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => `${item.place_id}-${index}-${Date.now()}`}
                            style={tw`mt-2 max-h-40`}
                        />
                    )}
                    <TouchableOpacity 
                        style={tw`bg-emerald-500 p-3 rounded-md w-full mt-3`} 
                        onPress={handleNext}
                        disabled={!searchQuery}
                    >
                        <Text style={[tw`text-white text-center`, { fontFamily: 'Helvetica_bold' }]}>
                            Next
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
};

export default ServicePage4;
