import { View, Text, SafeAreaView, TouchableOpacity, TextInput, Alert, StatusBar, Image, ScrollView, StyleSheet, Modal, FlatList, ImageBackground } from 'react-native';
import Checkbox from 'expo-checkbox';
import React, { useState } from 'react';
import tw from 'twrnc';
import { ChevronLeft } from 'lucide-react-native';
import { useFonts } from 'expo-font';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage for session management

// Define styles
const styles = StyleSheet.create({
  countryCodeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 12,
    borderWidth: 2,
    borderRightWidth: 0,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    borderColor: '#D7D9DC',
    minWidth: 60,
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 2,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    borderColor: '#D7D9DC',
  },
});

// Define navigation prop type
type SignUpProps = {
  navigation: {
    navigate: (screen: string) => void;
    goBack: () => void;
  };
};

export default function SignUp({ navigation }: SignUpProps) {
  const [loaded] = useFonts({
    'Satoshi-Variable': require('@/assets/fonts/Satoshi-Medium.otf'),
  });

  // State for phone number, password, username, country code, and terms acceptance
  const [phoneNumber, setPhoneNumber] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [countryCode, setCountryCode] = useState('+91'); // Default country code for India
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [termsError, setTermsError] = useState(false); // Track if user attempted to sign up without accepting terms
  const [showTermsModal, setShowTermsModal] = useState(false); // State for terms and conditions modal

  // Common country codes
  const countryCodes = [
    { code: '+91', country: 'India' },
    { code: '+977', country: 'Nepal' }
  ];


  // Function to select a country code
  const selectCountryCode = (code: string) => {
    setCountryCode(code);
    setShowCountryModal(false);
  };

  // Function to handle Sign Up
  const handleSignUp = async () => {
    // Reset terms error state
    setTermsError(false);

    if (!phoneNumber || !password || !username) {
      Alert.alert('Error', 'Please enter username, phone number, and password.');
      return;
    }

    if (!termsAccepted) {
      setTermsError(true); // Set terms error to true to highlight the checkbox
      Alert.alert('Error', 'Please accept the terms and conditions to continue.');
      return;
    }

    // Validate phone number format (10 digits for India)
    if (!/^\d{10}$/.test(phoneNumber)) {
      Alert.alert('Error', 'Please enter a valid 10-digit phone number.');
      return;
    }

    // Combine country code and phone number
    const fullPhoneNumber = `${phoneNumber}`;

    try {
      const response = await fetch('https://nityasha.vercel.app/api/v1/users/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: fullPhoneNumber, password, username }), // Use phone number as email
      });

      const data = await response.json();

      if (response.ok) {
        // Check if user ID is returned after successful signup
        if (!data.userId) {
          Alert.alert('Error', 'User ID not received from the server.');
          return;
        }

        // Store the user ID in AsyncStorage to keep the user logged in
        await AsyncStorage.setItem('userSession', JSON.stringify({ userId: data.userId }));

        navigation.navigate('BottomTabs'); // Navigating to Home or Main app page
      } else {
        Alert.alert('Error', data.message);
      }
    } catch (error) {
      console.error('Error signing up:', error);
      Alert.alert('Error', 'Something went wrong. Please try again later.');
    }
  };

  if (!loaded) {
    return null;
  }

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 px-5 bg-white`}>
      <ScrollView>
        <StatusBar barStyle={'dark-content'} translucent />

        {/* Terms and Conditions Modal */}
        <Modal
          visible={showTermsModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowTermsModal(false)}
        >
          <View style={tw`flex-1 justify-center items-center bg-black bg-opacity-50`}>
            <View style={tw`bg-white w-90 rounded-lg p-4 max-h-[80%]`}>
              <View style={tw`flex-row justify-between items-center mb-4`}>
                <Text style={tw`text-lg font-bold`}>Terms and Conditions</Text>
                <TouchableOpacity onPress={() => setShowTermsModal(false)}>
                  <Text style={tw`text-lg`}>✕</Text>
                </TouchableOpacity>
              </View>
              <ScrollView style={tw`max-h-[500px]`}>
                <Text style={tw`mb-4`}>
                  By using the Nityasha application, you agree to the following terms and conditions:
                </Text>
                <Text style={tw`mb-2`}>1. User Information and Account</Text>
                <Text style={tw`mb-4 pl-4`}>
                  • You are responsible for maintaining the confidentiality of your account information.
                  {"\n"}
                  • You must provide accurate and complete information when creating an account.
                  {"\n"}
                  • You are responsible for all activities that occur under your account.
                </Text>
                <Text style={tw`mb-2`}>2. Privacy Policy</Text>
                <Text style={tw`mb-4 pl-4`}>
                  • We collect and process your personal data in accordance with our Privacy Policy.
                  {"\n"}
                  • By using the application, you consent to our data practices as described in the Privacy Policy.
                </Text>
                <Text style={tw`mb-2`}>3. User Conduct</Text>
                <Text style={tw`mb-4 pl-4`}>
                  • You agree not to use the application for any illegal or unauthorized purpose.
                  {"\n"}
                  • You agree not to interfere with the proper functioning of the application.
                </Text>
                <Text style={tw`mb-2`}>4. Limitation of Liability</Text>
                <Text style={tw`mb-4 pl-4`}>
                  • The application is provided "as is" without warranties of any kind.
                  {"\n"}
                  • We are not liable for any damages arising from your use of the application.
                </Text>
                <Text style={tw`mb-4`}>
                  By checking the box, you acknowledge that you have read, understood, and agree to these terms and conditions.
                </Text>
              </ScrollView>
              <TouchableOpacity
                onPress={() => {
                  setTermsAccepted(true);
                  setTermsError(false);
                  setShowTermsModal(false);
                }}
                style={tw`px-5 py-3 mt-4 flex items-center justify-center text-center rounded-[8px] bg-[#000] text-white w-full`}
              >
                <Text style={tw`text-white`}>Accept Terms</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>



        <TouchableOpacity onPress={() => navigation.goBack()} style={tw`flex items-start justify-start pt-10`}>
          <ChevronLeft size={30} color={"#000"} />
        </TouchableOpacity>
        <View style={tw`flex mt-10 gap-5`}>
          <View style={tw`bg-[#222222] rounded-full w-15 h-15 overflow-hidden`}>
            <Image source={require('@/assets/images/favicon.png')} style={tw`w-full h-full`} />
          </View>
          <Text style={[tw`w-full flex items-center justify-center text-2xl text-[#0E0E0E]`, { fontFamily: 'Geist-ExtraBold' }]}>Welcome To Nityasha</Text>
          <Text style={[tw`w-full flex items-center justify-center`, { fontFamily: 'Geist-Regular' }]}>
            Please enter your name, phone number, and password to register.
          </Text>
          <TextInput
            placeholder='Name'
            value={username}
            onChangeText={setUsername}
            style={[tw`w-full px-5 py-4 rounded-[20px] bg-white`, { fontFamily: 'Geist-Regular' }]}
          />

          <View style={tw`flex-row w-full`}>

            <TextInput
              placeholder='Phone Number'
              keyboardType='number-pad'
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              maxLength={10}
              style={[tw`w-full px-5 py-4 rounded-[20px] bg-white`, { fontFamily: 'Geist-Regular' }]}
            />
          </View>

          <TextInput
            placeholder='Password'
            secureTextEntry
            value={password}
            onChangeText={setPassword}
            style={[tw`w-full px-5 py-4 rounded-[20px] bg-white`, { fontFamily: 'Geist-Regular' }]}
          />

          <View style={tw`flex-row items-center mb-2`}>
            <Checkbox
              value={termsAccepted}
              onValueChange={(value) => {
                setTermsAccepted(value);
                if (value) setTermsError(false); // Clear error when user checks the box
              }}
              color={termsAccepted ? '#000' : undefined}
              style={[tw`mr-2 rounded-full`, termsError ? { borderColor: 'red', borderWidth: 2 } : null]}
            />
            <Text style={tw`text-sm text-gray-700 flex-1`}>
              I accept the {' '}
              <Text
                style={tw`text-black font-bold underline`}
                onPress={() => setShowTermsModal(true)}
              >
                Terms and Conditions
              </Text>
              {' '}and{' '}
              <Text
                style={tw`text-black font-bold underline`}
                onPress={() => setShowTermsModal(true)}
              >
                Privacy Policy
              </Text>
            </Text>
          </View>

          <TouchableOpacity onPress={handleSignUp} style={tw`w-full bg-black rounded-full flex items-center justify-center py-4`}>
            <Text style={[tw`text-[16px] text-[#fff] w-full text-center`, { fontFamily: 'Geist-SemiBold' }]}>Sign Up</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => navigation.navigate('Login')} style={tw`flex flex-row items-center justify-center gap-1`}>
            <Text style={[tw`text-black font-medium`, { fontFamily: 'Satoshi-Variable' }]}>
              Already have an account?
            </Text>
            <Text style={tw`text-[#000]`}>Login</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ImageBackground>
  );
}
