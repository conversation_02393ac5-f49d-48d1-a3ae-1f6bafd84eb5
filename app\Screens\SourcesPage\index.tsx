import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  Image,
  useColorScheme,
  ImageBackground
} from 'react-native';
import tw from 'twrnc';
import { useRoute } from '@react-navigation/native';
import { Svg, Path } from 'react-native-svg';

const SourcesPage = () => {
  const route = useRoute();
  const sources = route.params?.sources || [];
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const SourceItem = ({ item, index }) => (
    <TouchableOpacity style={tw`px-4 py-4 `}>
      <View style={tw`flex-row`}>
        <Text style={tw`${isDark ? 'text-white' : 'text-gray-600'} text-base mr-2 mt-1`}>
          {index + 1}.
        </Text>
        <View style={tw`flex-1`}>
          <Text
            style={tw`${isDark ? 'text-white' : 'text-gray-900'} text-base font-medium leading-6 mb-2`}
          >
            {item.title || 'Untitled Source'}
          </Text>

          {item.description ? (
            <Text style={tw`${isDark ? 'text-gray-300' : 'text-gray-600'} text-sm leading-5 mb-3`}>
              {item.description}
            </Text>
          ) : null}

          <View style={tw`flex-row items-center`}>
            <View style={tw`w-5 h-5 rounded-full items-center justify-center mr-2 overflow-hidden`}>
              <Image
                source={{
                  uri: item.favicon || `https://www.google.com/s2/favicons?domain=${new URL(item.link).hostname}&sz=32`
                }}
                style={tw`w-full h-full`}
                resizeMode="contain"
              />
            </View>
            <Text style={tw`${isDark ? 'text-gray-300' : 'text-gray-700'} text-sm`}>
              {item.website || new URL(item.link).hostname}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <ImageBackground  source={isDark ? require('@/assets/screens/AIBackgroundScreenDark.png') : require('@/assets/screens/AIBackgroundScreen.png')} style={tw`flex-1 ${isDark ? 'bg-black' : 'bg-white'}`}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={isDark ? '#000' : '#fff'}
      />
      <View style={tw`absolute items-center justify-center w-full h-full`}>
        <Svg width="440" height="947" viewBox="0 0 440 947" fill="none">
          <Path
            d="M80.463 8.00733V122.627M80.463 750.284H3.05556H140.556V773.308M80.463 750.284V693.725M80.463 750.284V887.928M140.556 773.308V796.332V957M140.556 773.308L114.074 797.834L220.509 693.725M220.509 693.725V281.794M220.509 693.725L80.9722 557.582M220.509 693.725H80.463M220.509 693.725L359.537 557.582M220.509 693.725L326.944 798.334M220.509 693.725L359.359 692.223M220.509 693.725C220.509 772.222 220.509 824.389 220.509 890.43M220.509 1V69.0712M80.463 693.725H21.3889V887.928H80.463M80.463 693.725V670.701M80.463 693.725C80.463 769.637 80.463 914.756 80.463 887.928M80.463 887.928L1.52778 809.846M80.463 670.701L1.01852 593.119M80.463 670.701V534.058M1.01852 692.223L141.065 555.219M439.491 693.725L220 477.999L141.065 555.219M80.463 474.996L1.01852 397.415M80.463 474.996V477.999M80.463 474.996V417.936M80.463 477.999H21.8981V281.794H218.981M80.463 477.999V534.058M218.981 281.794H220.509M218.981 281.794L80.463 145.651M359.537 477.999H419.12L418.611 281.794H220.509M359.537 477.999V534.058M359.537 477.999V475.496M220.509 281.794L359.537 417.936V475.496M220.509 281.794L80.463 417.936M220.509 281.794L331.019 173.578M220.509 281.794V69.0712M80.463 417.936V259.27M1.01852 339.854H141.065V534.058M141.065 555.219V534.058M141.065 534.058H80.463M220.509 957C220.509 932.682 220.509 910.89 220.509 890.43M359.537 557.582V534.058M359.537 557.582L359.386 671.702M359.537 534.058H298.935V339.854H359.537M440 339.854H359.537M359.537 339.854V259.27M359.537 1V65.5675M359.537 65.5675L418.102 4.50366V65.5675H359.537ZM359.537 65.5675V122.627M359.537 122.627H299.444V1M359.537 122.627V145.651M359.537 145.651L331.019 173.578M359.537 145.651V259.27M331.019 173.578L220.509 69.0712M331.019 173.578L438.472 280.793M220.509 69.0712L0 281.794M359.537 259.27L438.472 182.69M80.463 145.651V259.27M80.463 145.651V122.627M80.463 259.27L1.01852 181.689M299.444 798.334V750.284H359.282M439.491 750.284H419.12M419.12 750.284L420.648 827.865L437.963 810.347M419.12 750.284H359.282M419.12 750.284V693.725L359.359 692.223M359.282 750.284L359.359 692.223M359.359 692.223L359.386 671.702M359.386 671.702L439.491 592.619M359.537 475.496L439.491 397.415M80.463 122.627H141.065L142.083 1M220.509 890.43L193.009 915.457M220.509 890.43L244.954 915.457M21.8981 8.00733V65.5675H80.463L21.8981 8.00733Z"
            stroke={isDark ? '#BBEAC3' : '#9CC2A4'}
            stroke-opacity="0.23"
            style={tw`opacity-25`}
            stroke-width="3"
          />
        </Svg>
      </View>

      {/* Header */}
      <View style={tw`flex-row items-center px-4 py-4`}>
        <Text style={[tw`${isDark ? 'text-white' : 'text-gray-900'} text-2xl font-medium`, { fontFamily: 'Helvetica_bold' }]}>
          Sources
        </Text>
      </View>

      {/* Sources List */}
      <ScrollView style={tw`flex-1`}>
        {sources.map((source, index) => (
          <SourceItem key={source.id || index} item={source} index={index} />
        ))}
      </ScrollView>
    </ImageBackground>
  );
};

export default SourcesPage;
