import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Animated } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator,
  StatusBar,
  SafeAreaView,
  Alert,
  ToastAndroid,
  Modal,
  FlatList,
  StyleSheet,
  Linking,
  ViewToken
} from 'react-native';
import { Video, ResizeMode } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import tw from 'twrnc';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { RouteProp } from '@react-navigation/native';
import { Plus, Share2, Volume2 } from 'lucide-react-native';
import { Share } from 'react-native';
import * as NavigationBar from "expo-navigation-bar";

interface Status {
  id: number;
  user_id: string;
  media_url: string;
  created_at: string;
  body?: string;
  username?: string;
  pfp?: string | null;
  followers?: number; // Add followers count for algorithm ranking
}

interface RecentStatusUser {
  user_id: number;
  username: string;
  pfp: string | null;
  followers: number;
  statuses: RecentStatus[];
}

interface RecentStatus {
  user_id: number;
  status_id: number;
  status_user_id: string;
  status_media_url: string;
  status_created_at: string;
}

interface UserInfo {
  id: number | string;
  username?: string;
  pfp?: string;
}

// Define a type for interaction items that can be either a string or a user object
type InteractionItem = string | UserInfo;

type StatusViewRouteParams = {
  statuses: Status[];
  userName: string;
  onStatusUpdate?: () => void;
  statusId?: string; // Add statusId parameter
  showRecent?: boolean; // Flag to show recent statuses
  userData?: {
    id: number;
    name: string;
    phoneNumber?: string;
    contactName?: string;
    originalEmail?: string;
  } | null; // User data passed from Status component
};

type StatusViewRouteProp = RouteProp<{ params: StatusViewRouteParams }, 'params'>;

const { width, height } = Dimensions.get('window');
const PROGRESS_BAR_WIDTH = width - 40;

export default function StatusView({  }) {
  // Define all state hooks at the top level
  const [loadedStatus, setLoadedStatus] = useState<Status | null>(null);
  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'flex-end',
      backgroundColor: 'rgba(0,0,0,0.5)'
    },
    modalContent: {
      backgroundColor: 'white',
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 20,
      height: '70%'
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 15
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#333'
    },
    tabContainer: {
      flexDirection: 'row',
      marginBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#eee'
    },
    tab: {
      paddingVertical: 10,
      paddingHorizontal: 15,
      marginRight: 10
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: '#075E54'
    },
    tabText: {
      color: '#333',
      fontWeight: '500'
    },
    userItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 10,
      borderBottomWidth: 1,
      borderBottomColor: '#f0f0f0'
    },
    userAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: 10
    },
    userName: {
      fontSize: 16,
      color: '#333'
    },
    emptyText: {
      textAlign: 'center',
      marginTop: 20,
      color: '#999'
    }
  });
  const navigation = useNavigation();
  const route = useRoute<StatusViewRouteProp>();
  const { statuses, userName, statusId, showRecent = true, userData } = route.params;

  // State for recent statuses
  const [recentStatuses, setRecentStatuses] = useState<Status[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMorePages, setHasMorePages] = useState(true);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const progressAnim = useRef(new Animated.Value(0)).current;
  const [loading, setLoading] = useState(true);
  // buffering state is used in handlePlaybackStatusUpdate
  const [buffering, setBuffering] = useState(false);
  const [paused, setPaused] = useState(false); // Keeping state but removing functionality
  const [isCurrentUserStatus, setIsCurrentUserStatus] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [viewersList, setViewersList] = useState<InteractionItem[]>([]);
  const [likersList, setLikersList] = useState<InteractionItem[]>([]);
  const [showInteractions, setShowInteractions] = useState(false);
  const [interactionCount, setInteractionCount] = useState({ views: 0, likes: 0 });
  const [videoQuality, setVideoQuality] = useState<'low' | 'high'>('low'); // Start with low quality
  const [isMuted, setIsMuted] = useState(false);
  // Video progress tracking states - keeping setters for compatibility
  const [videoResizeMode, setVideoResizeMode] = useState(ResizeMode.CONTAIN);
 useEffect(() => {
    NavigationBar.setVisibilityAsync("hidden");
  }, []);
  // For vertical scrolling in recent view
  const [videoRefs, setVideoRefs] = useState<{[key: number]: any}>({});
  const [pausedVideos, setPausedVideos] = useState<{[key: number]: boolean}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [lastTap, setLastTap] = useState<number | null>(null);
  const [doubleTapActive, setDoubleTapActive] = useState(false);
  const heartAnimation = useRef(new Animated.Value(0)).current;

  const videoRef = useRef<Video>(null);
  const flatListRef = useRef<FlatList>(null);
  // Only combine statuses if we're in the recent view mode and no specific statuses were passed
  const combinedStatuses = (showRecent && statuses.length === 0) ? [...recentStatuses] : statuses;

  // Make sure we have a valid status at the current index
  const currentStatus = loadedStatus ||
    (combinedStatuses.length > 0 && currentIndex < combinedStatuses.length ?
      combinedStatuses[currentIndex] : null);
  const isVideo = currentStatus?.media_url?.endsWith('.mp4');

  // Function to fetch status by ID
  const fetchStatusById = async (id: string) => {
    try {
      setLoading(true);
      // Add timeout to prevent indefinite loading
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`https://status.api.nityasha.com/status/${id}`, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Fetched status by ID in StatusView:', data);
      setLoadedStatus(data);
    } catch (error) {
      console.error('Error fetching status by ID:', error);
      // Show error toast
      ToastAndroid.show('Failed to load status. Please try again.', ToastAndroid.SHORT);
      // Navigate back on error to prevent being stuck
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // Fetch status by ID if statusId is provided
  useEffect(() => {
    if (statusId) {
      fetchStatusById(statusId);
    } else {
      // Reset loadedStatus when no statusId is provided
      setLoadedStatus(null);
    }
  }, [statusId]);

  // Fetch recent statuses if showRecent is true and no specific statuses were provided
  useEffect(() => {
    if (showRecent && statuses.length === 0) {
      setIsLoading(true);
      fetchRecentStatuses(1, true);
    }
  }, [showRecent, statuses.length]);

  // Function to fetch recent statuses with TikTok-like algorithm
  const fetchRecentStatuses = async (page: number, reset: boolean = false) => {
    if (isLoadingMore) return;

    try {
      setIsLoadingMore(true);
      console.log(`Fetching recent statuses page ${page}`);

      const response = await fetch(`https://status.api.nityasha.com/statuses/recent?page=${page}`);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Recent statuses data:', data);

      // Process the data to match our Status interface
      const newStatuses: Status[] = [];

      if (data.users && Array.isArray(data.users)) {
        data.users.forEach((user: RecentStatusUser) => {
          if (user.statuses && Array.isArray(user.statuses)) {
            user.statuses.forEach((status: RecentStatus) => {
              newStatuses.push({
                id: status.status_id,
                user_id: status.status_user_id,
                media_url: status.status_media_url.startsWith('http')
                  ? status.status_media_url
                  : `https://status.api.nityasha.com/${status.status_media_url}`,
                created_at: status.status_created_at,
                // Store user information for display
                username: user.username,
                pfp: user.pfp,
                // Add follower count for algorithm ranking
                followers: user.followers || 0
              });
            });
          }
        });
      }

      // Apply TikTok-like algorithm for sorting
      // This prioritizes:
      // 1. Content from users with more followers
      // 2. Recent content
      // 3. Video content over images (TikTok style)
      newStatuses.sort((a, b) => {
        // Calculate a score for each status
        const scoreA = calculateStatusScore(a);
        const scoreB = calculateStatusScore(b);

        // Higher score first
        return scoreB - scoreA;
      });

      // Update state based on whether we're resetting or appending
      if (reset) {
        setRecentStatuses(newStatuses);
      } else {
        setRecentStatuses(prev => [...prev, ...newStatuses]);
      }

      // Check if we have more pages using the hasMore property from the API
      setHasMorePages(data.hasMore || false);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching recent statuses:', error);
    } finally {
      setIsLoadingMore(false);
      setIsLoading(false);
    }
  };

  // Calculate a score for the TikTok-like algorithm
  const calculateStatusScore = (status: Status): number => {
    // Base score starts at 0
    let score = 0;

    // Factor 1: Follower count (users with more followers get priority)
    // Scale follower count to have reasonable impact (log scale to prevent popular accounts from dominating)
    const followerScore = status.followers ? Math.log10(status.followers + 1) * 10 : 0;
    score += followerScore;

    // Factor 2: Recency (newer content gets priority)
    const ageInHours = (Date.now() - new Date(status.created_at).getTime()) / (1000 * 60 * 60);
    // Recency score decreases as content gets older (max 50 points for brand new content)
    const recencyScore = Math.max(0, 50 - ageInHours);
    score += recencyScore;

    // Factor 3: Content type (videos get priority in TikTok-style feeds)
    const isVideo = status.media_url.endsWith('.mp4');
    score += isVideo ? 20 : 0; // Bonus points for videos

    return score;
  };

  // Function to load more statuses when user reaches end of current list
  const loadMoreStatuses = () => {
    // Only load more statuses if we're in recent view mode and no specific statuses were provided
    if (!isLoadingMore && hasMorePages && showRecent && statuses.length === 0) {
      console.log(`Loading more statuses, page ${currentPage + 1}`);
      fetchRecentStatuses(currentPage + 1);
    }
  };

  // Handle deep links
  useEffect(() => {
    // Handle initial deep link
    const handleInitialDeepLink = async () => {
      try {
        const url = await Linking.getInitialURL();
        if (url) {
          console.log('Initial deep link URL:', url);
          // Match both nityasha://status/123 and https://status.api.nityasha.com/status/123
          const statusIdMatch = url.match(/\/status\/([\d]+)/) || url.match(/nityasha:\/\/status\/([\d]+)/);
          if (statusIdMatch && statusIdMatch[1]) {
            const deepLinkStatusId = statusIdMatch[1];
            console.log('Deep link status ID:', deepLinkStatusId);
            fetchStatusById(deepLinkStatusId);
          }
        }
      } catch (error) {
        console.error('Error handling initial deep link:', error);
      }
    };

    handleInitialDeepLink();

    // Listen for deep links while the component is mounted
    const subscription = Linking.addEventListener('url', ({ url }) => {
      console.log('Deep link received while app is running:', url);
      // Match both nityasha://status/123 and https://status.api.nityasha.com/status/123
      const statusIdMatch = url.match(/\/status\/([\d]+)/) || url.match(/nityasha:\/\/status\/([\d]+)/);
      if (statusIdMatch && statusIdMatch[1]) {
        const deepLinkStatusId = statusIdMatch[1];
        console.log('Deep link status ID:', deepLinkStatusId);
        fetchStatusById(deepLinkStatusId);
      }
    });

    return () => subscription.remove();
  }, []);

  // Use different durations for videos vs images
  const imageDuration = 5000; // 5 seconds for images
  const progressInterval = 100; // Update progress every 100ms

  // Check if the status belongs to the current user and check like/follow status
  useEffect(() => {
    // Reset states when currentStatus changes to prevent stale data
    setLoading(true);
    setIsCurrentUserStatus(false);
    setIsLiked(false);
    setIsFollowing(false);
    setProgress(0);
    setPaused(false);

    // Add a safety timeout to ensure loading state is reset even if something fails
    const safetyTimeoutId = setTimeout(() => {
      setLoading(false);
    }, 10000); // 10 second safety timeout

    const checkCurrentUser = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          const userId = parsedSession.userId?.toString();
          setCurrentUserId(userId);

          // Check if the current status belongs to the logged-in user
          if (currentStatus) {
            console.log('Checking status ownership:', {
              statusUserId: currentStatus.user_id,
              currentUserId: userId,
              userName: userName
            });

            // Helper function to normalize user IDs for comparison
            const normalizeId = (id: string | number | null | undefined): string => {
              if (id === null || id === undefined) return '';
              return id.toString().trim();
            };

            // Check if the status user_id matches the current user ID using normalized IDs
            if (normalizeId(currentStatus.user_id) === normalizeId(userId)) {
              setIsCurrentUserStatus(true);
              console.log('This status belongs to the current user - ID match');
            } else {
              // Check if the username is 'My Status' (set by the Status component)
              if (userName === 'My Status') {
                setIsCurrentUserStatus(true);
                console.log('This status is marked as My Status');
              } else {
                setIsCurrentUserStatus(false);
                console.log('This status does NOT belong to the current user');

                // Record a view if this is not the user's own status
                recordStatusView(currentStatus.id, userId);

                // Check if the user is following the status owner
                checkFollowStatus(currentStatus.user_id, userId);
              }
            }

            // Check if the user has liked this status
            checkLikeStatus(currentStatus.id, userId);
          } else {
            setIsCurrentUserStatus(false);
          }
        }
      } catch (error) {
        console.error('Error checking current user:', error);
        // Show error toast
        ToastAndroid.show('Error loading status details', ToastAndroid.SHORT);
      } finally {
        // Ensure loading is set to false even if there's an error
        if (!isVideo) {
          setLoading(false);
        }
        // Clear the safety timeout since we're done
        clearTimeout(safetyTimeoutId);
      }
    };

    checkCurrentUser();

    // Clean up the safety timeout if the component unmounts
    return () => clearTimeout(safetyTimeoutId);
  }, [currentStatus, userName]);

  // Record a view when a user views someone else's status
  // Use a debounce mechanism to prevent multiple rapid calls
  const viewRecordTimeouts = useRef<{[key: string]: NodeJS.Timeout}>({});

  // Track failed view recordings to avoid repeated failures
  const failedViewRecords = useRef<Set<string>>(new Set());

  // Cleanup function for view recording timeouts
  useEffect(() => {
    // Clear failed records immediately instead of using an interval
    if (failedViewRecords.current.size > 0) {
      console.log(`Clearing ${failedViewRecords.current.size} failed view records to allow retrying`);
      failedViewRecords.current.clear();
    }

    return () => {
      // Clear all pending view record timeouts when component unmounts
      Object.values(viewRecordTimeouts.current).forEach(timeout => {
        clearTimeout(timeout);
      });
      viewRecordTimeouts.current = {};
    };
  }, []);

  // Queue for storing view records that failed to send
  const pendingViewRecords = useRef<Array<{statusId: number, userId: string, timestamp: number}>>([]);

  // Load pending view records from AsyncStorage on component mount
  useEffect(() => {
    const loadPendingViewRecords = async () => {
      try {
        const storedRecords = await AsyncStorage.getItem('pendingViewRecords');
        if (storedRecords) {
          const parsedRecords = JSON.parse(storedRecords);
          pendingViewRecords.current = parsedRecords;
          console.log(`Loaded ${parsedRecords.length} pending view records from storage`);
        }
      } catch (error) {
        console.error('Error loading pending view records:', error);
      }
    };

    loadPendingViewRecords();
  }, []);

  // Function to store a view record locally when the API is unreachable
  const storeViewRecordLocally = async (statusId: number, userId: string) => {
    // Add to in-memory queue
    pendingViewRecords.current.push({
      statusId,
      userId,
      timestamp: Date.now()
    });
    console.log(`Stored view record locally for status ${statusId}, pending count: ${pendingViewRecords.current.length}`);

    // Limit the queue size to prevent memory issues
    if (pendingViewRecords.current.length > 50) {
      pendingViewRecords.current.shift(); // Remove oldest record
    }

    // Persist to AsyncStorage
    try {
      await AsyncStorage.setItem('pendingViewRecords', JSON.stringify(pendingViewRecords.current));
    } catch (error) {
      console.error('Error saving pending view records:', error);
    }
  };

  // Process pending view records once on component mount instead of using an interval
  useEffect(() => {
    const processPendingRecords = async () => {
      if (pendingViewRecords.current.length > 0) {
        console.log(`Processing ${pendingViewRecords.current.length} pending view records`);

        // Process records that are at least 1 minute old to avoid duplicates with recent attempts
        const now = Date.now();
        const recordsToProcess = pendingViewRecords.current.filter(record => now - record.timestamp > 60000);

        if (recordsToProcess.length > 0) {
          // Create a copy of the records to process
          const recordsToSend = [...recordsToProcess];

          // Remove these records from the pending queue
          pendingViewRecords.current = pendingViewRecords.current.filter(record =>
            !recordsToProcess.some(r => r.statusId === record.statusId && r.userId === record.userId)
          );

          // Update AsyncStorage with the modified pending records
          try {
            await AsyncStorage.setItem('pendingViewRecords', JSON.stringify(pendingViewRecords.current));
          } catch (error) {
            console.error('Error updating pending view records in storage:', error);
          }

          // Process each record
          recordsToSend.forEach(record => {
            console.log(`Processing pending view record for status ${record.statusId}`);
            sendViewRecordRequest(record.statusId, record.userId, 0, true);
          });
        }
      }
    };

    // Process records once on mount
    processPendingRecords();
  }, []);

  // Function to actually send the view recording request with retry logic - optimized for performance
  const sendViewRecordRequest = async (
    statusId: number,
    userId: string,
    retryCount = 0,
    isFromPendingQueue = false
  ): Promise<boolean> => {
    // Maximum number of retries
    const MAX_RETRIES = 1; // Reduced from 2 to 1
    // Exponential backoff delay: 1s, 2s
    const RETRY_DELAY = 1000 * Math.pow(2, retryCount); // Reduced from 2000 to 1000
    // Timeout reduced: 5s, 7s
    const TIMEOUT = 5000 + (retryCount * 2000); // Reduced from 10000 to 5000

    try {
      // Skip if we've already failed multiple times for this status (unless from pending queue)
      const recordKey = `${statusId}-${userId}`;
      if (!isFromPendingQueue && failedViewRecords.current.has(recordKey)) {
        console.log(`Skipping previously failed view record for status ${statusId}`);
        return false;
      }

      // Add timeout to prevent indefinite loading
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log(`View recording timeout for status ${statusId} (attempt ${retryCount + 1})`);
        controller.abort();
      }, TIMEOUT);

      console.log(`Recording view for status ${statusId} by user ${userId} (attempt ${retryCount + 1})`);

      // Use a more reliable approach with explicit timeout handling
      const fetchPromise = fetch(`https://status.api.nityasha.com/status/${statusId}/interact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId,
          type: 'view'
        }),
        signal: controller.signal
      });

      const response = await fetchPromise;
      clearTimeout(timeoutId);

      // The server now responds immediately for views, so we consider any response as success
      if (response.ok) {
        console.log(`Successfully recorded view for status ${statusId}`);
        return true;
      } else {
        console.error(`Failed to record view: HTTP ${response.status}`);

        // Only retry for server errors (5xx)
        if (response.status >= 500 && retryCount < MAX_RETRIES) {
          console.log(`Scheduling retry ${retryCount + 1} for status ${statusId} in ${RETRY_DELAY}ms`);
          return new Promise(resolve => {
            setTimeout(async () => {
              const success = await sendViewRecordRequest(statusId, userId, retryCount + 1, isFromPendingQueue);
              resolve(success);
            }, RETRY_DELAY);
          });
        }

        // For persistent server errors, store the record locally for later retry
        if (response.status >= 500 && retryCount >= MAX_RETRIES && !isFromPendingQueue) {
          storeViewRecordLocally(statusId, userId);
        }

        return false;
      }
    } catch (error: any) {
      // Provide more detailed error logging
      if (error?.name === 'AbortError') {
        console.error(`View recording aborted for status ${statusId}: Request timed out (attempt ${retryCount + 1})`);

        // Retry on timeout if we haven't exceeded max retries
        if (retryCount < MAX_RETRIES) {
          console.log(`Scheduling retry ${retryCount + 1} for status ${statusId} in ${RETRY_DELAY}ms`);
          return new Promise(resolve => {
            setTimeout(async () => {
              const success = await sendViewRecordRequest(statusId, userId, retryCount + 1, isFromPendingQueue);
              resolve(success);
            }, RETRY_DELAY);
          });
        } else if (!isFromPendingQueue) {
          // Store the record locally for later retry if we've exhausted immediate retries
          storeViewRecordLocally(statusId, userId);
          // Return true anyway since the server processes views in the background
          return true;
        }
      } else {
        console.error(`Error recording view for status ${statusId}:`, error);

        // For network errors, store the record locally
        if (!isFromPendingQueue && (error?.message?.includes('Network') || error?.message?.includes('network'))) {
          storeViewRecordLocally(statusId, userId);
        }
      }

      // If we've reached max retries or had a non-timeout error, mark this status as failed
      if (retryCount >= MAX_RETRIES) {
        const recordKey = `${statusId}-${userId}`;
        failedViewRecords.current.add(recordKey);
        console.log(`Added status ${statusId} to failed records after ${retryCount + 1} attempts`);
      }

      // Return true for view requests even if they fail, since they're non-critical
      // This prevents the UI from getting stuck waiting for view recordings
      return !isFromPendingQueue;
    }
  };

  const recordStatusView = async (statusId: number, userId: string | null) => {
    if (!userId) return;

    // Create a unique key for this status-user combination
    const recordKey = `${statusId}-${userId}`;

    // Skip if we've already failed multiple times for this status
    if (failedViewRecords.current.has(recordKey)) {
      return;
    }

    // Clear any existing timeout for this combination to implement debouncing
    if (viewRecordTimeouts.current[recordKey]) {
      clearTimeout(viewRecordTimeouts.current[recordKey]);
    }

    // Set a new timeout to actually record the view after a short delay
    viewRecordTimeouts.current[recordKey] = setTimeout(async () => {
      try {
        await sendViewRecordRequest(statusId, userId);
      } finally {
        // Clean up the timeout reference
        delete viewRecordTimeouts.current[recordKey];
      }
    }, 300); // Debounce for 300ms
  };

  // Format date to show how long ago the status was posted
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);

    if (diffMins < 60) {
      return `${diffMins} min ago`;
    } else if (diffMins < 1440) {
      return `${Math.floor(diffMins / 60)} hours ago`;
    } else {
      return `${Math.floor(diffMins / 1440)} days ago`;
    }
  };

  // Handle video playback status
  const handlePlaybackStatusUpdate = (status: any) => {
    // Always set these values regardless of status to avoid conditional hook calls
    const isLoaded = status?.isLoaded || false;
    const isBuffering = status?.isBuffering || false;
    const isPlaying = status?.isPlaying || false;
    const naturalSize = status?.naturalSize || { width: 0, height: 0 };
    const positionMillis = status?.positionMillis || 0;
    const durationMillis = status?.durationMillis || 0;
    const didJustFinish = status?.didJustFinish || false;
    const hasError = !!status?.error;

    // Update loading state only if the status is loaded or has an error
    if (isLoaded || hasError) {
      setLoading(false);
    }

    // Track buffering state - but don't pause video during buffering
    // This prevents unnecessary pauses during playback
    const wasBuffering = buffering;
    setBuffering(isBuffering);

    // Log buffering state changes for debugging
    if (wasBuffering !== isBuffering) {
      console.log(`Video buffering state changed: ${isBuffering ? 'buffering' : 'playing'}, isPlaying=${isPlaying}`);

      // If we're buffering but should be playing (not manually paused), ensure we try to continue playing
      if (isBuffering && !paused && !isPlaying) {
        console.log('Video is buffering but should be playing - ensuring playback continues');
        // Check if videoRef.current exists before attempting to play
        if (videoRef.current) {
          // First try to stop any other audio that might be playing
          // This helps prevent audio overlap during buffering recovery
          if (!isMuted) {
            console.log('Temporarily muting video during buffering recovery to prevent audio overlap');
            try {
              if (videoRef.current && typeof videoRef.current.setIsMutedAsync === 'function') {
                videoRef.current.setIsMutedAsync(true).catch(() => {});
              }
            } catch (error) {
              console.error('Error muting video during buffering recovery:', error);
            }
          }

          // Use a short timeout to allow buffering to progress before trying to play
          setTimeout(() => {
            // Check again if videoRef.current still exists and we're still not paused
            if (!paused && videoRef.current) {
              try {
                // Restore original mute state before playing
                if (!isMuted && videoRef.current && typeof videoRef.current.setIsMutedAsync === 'function') {
                  videoRef.current.setIsMutedAsync(false).catch(() => {});
                }

                videoRef.current.playAsync().catch(err => {
                  console.error('Error resuming playback after buffering:', err);

                  // Handle audio focus error specifically
                  if (err.message && err.message.includes('AudioFocusNotAcquired')) {
                    console.log('Audio focus error detected, trying with muted audio');
                    if (videoRef.current) {
                      videoRef.current.setIsMutedAsync(true)
                        .then(() => {
                          if (videoRef.current) {
                            videoRef.current.playAsync()
                              .catch((playErr) => console.error('Failed to play after muting:', playErr));
                          }
                        })
                        .catch((mutedErr) => console.error('Failed to mute video:', mutedErr));
                    }
                  }
                });
              } catch (error) {
                console.error('Unexpected error when resuming playback after buffering:', error);
              }
            }
          }, 1000);
        } else {
          console.log('Cannot resume playback after buffering: Video component not fully loaded');
        }
      }
    }

    // Update video dimensions and resize mode
    // Note: setVideoNaturalSize is not defined, so we'll skip this step
    // and directly determine the resize mode
    if (naturalSize.width > 0 && naturalSize.height > 0) {
      const screenRatio = width / height;
      const videoRatio = naturalSize.width / naturalSize.height;

      // Allow some tolerance for aspect ratio comparison
      const aspectRatioTolerance = 0.1;
      if (Math.abs(screenRatio - videoRatio) < aspectRatioTolerance) {
        setVideoResizeMode(ResizeMode.COVER); // Full screen
      } else {
        setVideoResizeMode(ResizeMode.CONTAIN); // Centered
      }
    }

    // Update progress for videos with duration
    if (durationMillis > 0) {
      const newProgress = positionMillis / durationMillis;
      setProgress(newProgress);

      // Animate the progress bar smoothly
      Animated.timing(progressAnim, {
        toValue: newProgress,
        duration: 250, // Smooth animation over 250ms
        useNativeDriver: false, // Width animations can't use native driver
      }).start();

      // Store video position and duration for the progress bar
      // Note: These setter functions are not defined, so we'll skip this step

      // When video reaches 80% of current playback, switch to high quality if not already
      if (newProgress > 0.8 && videoQuality === 'low') {
        setVideoQuality('high');
      }

      // If video is not playing but should be (not manually paused), try to resume playback
      // This helps recover from situations where the video might have paused unexpectedly
      if (isLoaded && !isPlaying && !paused && !isBuffering) {
        console.log('Video is loaded but not playing - attempting to resume playback');
        if (videoRef.current) {
          try {
            videoRef.current.playAsync().catch(err => {
              console.error('Error resuming playback:', err);

              // Handle audio focus error specifically
              if (err.message && err.message.includes('AudioFocusNotAcquired')) {
                console.log('Audio focus error detected, trying with muted audio');
                if (videoRef.current) {
                  videoRef.current.setIsMutedAsync(true)
                    .then(() => videoRef.current?.playAsync())
                    .catch((mutedErr) => console.error('Failed to play even when muted:', mutedErr));
                }
              }
            });
          } catch (error) {
            console.error('Unexpected error when resuming playback:', error);
          }
        } else {
          console.log('Cannot resume playback: Video component not fully loaded');
        }
      }
    }

    // For horizontal view, we'll let isLooping handle the looping
    // but we'll still log it for debugging purposes
    if (didJustFinish) {
      console.log('Video finished playing - should auto-loop due to isLooping=true');
      // No need to manually restart since isLooping=true handles this
      // But we'll ensure it's playing just in case
      if (videoRef.current) {
        try {
          videoRef.current.playAsync().catch((err: any) => {
            console.error('Error ensuring video continues playing after finish:', err);

            // Handle audio focus error specifically
            if (err.message && err.message.includes('AudioFocusNotAcquired')) {
              console.log('Audio focus error detected, trying with muted audio');
              if (videoRef.current) {
                videoRef.current.setIsMutedAsync(true)
                  .then(() => {
                    // Handle in separate then to avoid type issues
                    if (videoRef.current) {
                      videoRef.current.playAsync()
                        .catch((playErr: any) => console.error('Failed to play after muting:', playErr));
                    }
                  })
                  .catch((mutedErr: any) => console.error('Failed to mute video:', mutedErr));
              }
            }
          });
        } catch (error) {
          console.error('Unexpected error when ensuring video continues after finish:', error);
        }
      } else {
        console.log('Cannot ensure video continues after finish: Video component not fully loaded');
      }
    }

    // Log errors
    if (hasError) {
      console.error('Video error:', status.error);
      // Make sure loading is set to false even if there's an error
      setLoading(false);
      // Show error toast
      ToastAndroid.show('Error playing video. Please try again.', ToastAndroid.SHORT);

      // Try to reload the video after an error
      if (videoRef.current && currentStatus) {
        setTimeout(() => {
          // Check if videoRef.current still exists after the timeout
          if (videoRef.current) {
            try {
              videoRef.current.loadAsync({ uri: currentStatus.media_url })
                .then(() => {
                  if (!paused && videoRef.current) {
                    videoRef.current.playAsync()
                      .catch(playErr => {
                        console.error('Error playing video after reload:', playErr);
                      });
                  }
                })
                .catch(err => {
                  console.error('Error reloading video after error:', err);
                });
            } catch (error) {
              console.error('Unexpected error when reloading video after error:', error);
            }
          }
        }, 2000);
      }
    }
  };

  // Add a safety timeout to ensure loading state is reset after a certain time
  useEffect(() => {
    if (loading) {
      const timeoutId = setTimeout(() => {
        setLoading(false);
        console.log('Safety timeout triggered to reset loading state');
      }, 15000); // 15 second safety timeout

      return () => clearTimeout(timeoutId);
    }
  }, [loading]);

  // Note: Video quality is set in the state but applied directly in the Video component

  // Handle progress updates for images
  useEffect(() => {
    let interval: NodeJS.Timeout;

    // Only use interval for images
    if (currentStatus && !isVideo) {
      setProgress(0);
      setLoading(true);

      interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + (progressInterval / imageDuration);

          // Move to next status when progress reaches 1
          if (newProgress >= 1) {
            clearInterval(interval);
            if (currentIndex < combinedStatuses.length - 1) {
              setCurrentIndex(currentIndex + 1);
            } else if (showRecent && hasMorePages) {
              // Try to load more statuses if we're in recent view
              loadMoreStatuses();
              return 0;
            } else {
              // All statuses viewed, go back
           //   navigation.goBack();
            }
            return 0;
          }
          return newProgress;
        });
      }, progressInterval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentIndex, paused, currentStatus, isVideo]);

  // Handle touch to navigate between statuses
  const handleScreenTouch = (direction: 'left' | 'right') => {
    // Completely stop and unload the current video to prevent audio overlap
    if (videoRef.current) {
      try {
        // First pause the video
        videoRef.current.pauseAsync()
          .then(() => {
            // Then unload it completely to stop any audio
            if (videoRef.current) {
              videoRef.current.unloadAsync().catch(err => {
                console.error('Error unloading video during navigation:', err);
              });
            }
          })
          .catch(err => {
            console.error('Error pausing video during navigation:', err);
            // Try to unload anyway
            if (videoRef.current) {
              videoRef.current.unloadAsync().catch(() => {});
            }
          });
      } catch (error) {
        console.error('Unexpected error during video cleanup:', error);
      }
    }

    // Reset states to prevent stale data
    setLoading(true);
    setProgress(0);
    setPaused(false);
    setIsMuted(false); // Reset mute state for new video

    if (direction === 'left' && currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    } else if (direction === 'right' && currentIndex < combinedStatuses.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else if (direction === 'right' && showRecent && hasMorePages) {
      // If we're at the end and there are more pages, load more
      loadMoreStatuses();
    }
  };

  // Video pause/resume functionality removed

  // Handle double tap to like
  const handleDoubleTap = () => {
    if (!currentUserId || isCurrentUserStatus || !currentStatus) return;

    // Show heart animation
    setDoubleTapActive(true);

    // Animate the heart
    heartAnimation.setValue(0);
    Animated.sequence([
      Animated.timing(heartAnimation, {
        toValue: 1.2,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.timing(heartAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true
      })
    ]).start();

    // Like the status
    if (!isLiked) {
      handleLikeStatus();
    }

    // Hide heart animation after a delay
    setTimeout(() => {
      setDoubleTapActive(false);
    }, 1000);
  };

  // Check for double tap
  const checkDoubleTap = () => {
    const now = Date.now();
    const DOUBLE_TAP_DELAY = 300; // 300ms between taps

    if (lastTap && (now - lastTap) < DOUBLE_TAP_DELAY) {
      // Double tap detected
      handleDoubleTap();
      setLastTap(null); // Reset last tap
    } else {
      setLastTap(now);
    }
  };

  // Fetch user details by ID
  const fetchUserDetails = async (userId: string): Promise<UserInfo> => {
    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch user details for ID: ${userId}`);
      }
      const userData = await response.json();
      return {
        id: userId,
        username: userData.username || 'User',
        pfp: userData.pfp || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.username || 'User')}`
      };
    } catch (error) {
      console.error(`Error fetching user details for ID: ${userId}`, error);
      return {
        id: userId,
        username: 'User',
        pfp: `https://ui-avatars.com/api/?name=User`
      };
    }
  };

  // Check if the user has liked this status and fetch interaction data
  const checkLikeStatus = async (statusId: number, userId: string | null) => {
    if (!userId) return;

    try {
      // Add timeout to prevent indefinite loading
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`https://status.api.nityasha.com/status/${statusId}/interactions`, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error('Failed to fetch like status');
      }

      const data = await response.json();
      console.log('Interaction data received:', data);

      // Make sure we have arrays for views and likes
      let views = Array.isArray(data.views) ? data.views : [];
      let likes = Array.isArray(data.likes) ? data.likes : [];

      // Process views and likes to fetch user details for string IDs
      const processedViews = await Promise.all(
        views.map(async (view: InteractionItem) => {
          try {
            if (typeof view === 'string') {
              return await fetchUserDetails(view);
            }
            return view;
          } catch (error) {
            console.error('Error processing view:', error);
            // Return a basic user object if fetching details fails
            return { id: typeof view === 'string' ? view : 'unknown', username: 'User', pfp: null };
          }
        })
      );

      const processedLikes = await Promise.all(
        likes.map(async (like: InteractionItem) => {
          try {
            if (typeof like === 'string') {
              return await fetchUserDetails(like);
            }
            return like;
          } catch (error) {
            console.error('Error processing like:', error);
            // Return a basic user object if fetching details fails
            return { id: typeof like === 'string' ? like : 'unknown', username: 'User', pfp: null };
          }
        })
      );

      // Check if the current user's ID is in the likes array
      const userHasLiked = processedLikes.some((like: UserInfo) => {
        return like.id.toString() === userId;
      });

      console.log(`User ${userId} has liked status ${statusId}: ${userHasLiked}`);
      setIsLiked(userHasLiked);

      // Store the viewers and likers lists
      setViewersList(processedViews);
      setLikersList(processedLikes);

      // Update interaction counts
      const newInteractionCount = {
        views: data.view_count || views.length || 0,
        likes: data.like_count || likes.length || 0
      };
      console.log(`Setting interaction count for status ${statusId}:`, newInteractionCount);
      setInteractionCount(newInteractionCount);

      console.log('Processed interaction data:', { processedViews, processedLikes });
    } catch (error) {
      console.error('Error checking like status:', error);
      // Don't reset interaction counts on error to prevent UI from flickering
      // Only set empty lists for viewers and likers
      setViewersList([]);
      setLikersList([]);
      // Don't reset interaction count to 0 on error
      // setInteractionCount({ views: 0, likes: 0 });
    }
  };

  // Check if the user is following the status owner
  const checkFollowStatus = async (targetUserId: string, userId: string | null) => {
    if (!userId || !targetUserId || isCurrentUserStatus) return;

    try {
      // Add timeout to prevent indefinite loading
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`https://status.api.nityasha.com/users/${userId}/isFollowing/${targetUserId}`, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error('Failed to check follow status');
      }

      const data = await response.json();
      console.log('Follow status:', data);
      setIsFollowing(data.isFollowing);
    } catch (error) {
      console.error('Error checking follow status:', error);
      // Default to not following if there's an error
      setIsFollowing(false);
    }
  };

  // Handle follow/unfollow action
  const handleFollowToggle = async () => {
    if (!currentUserId || isCurrentUserStatus || !currentStatus) return;

    try {
      const action = isFollowing ? 'unfollow' : 'follow';
      console.log(`Sending ${action} request for user:`, currentStatus.user_id, 'by user:', currentUserId);

      // Add timeout to prevent indefinite loading
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`https://status.api.nityasha.com/users/${currentUserId}/follow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId: currentStatus.user_id,
          action: action
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to ${action} user`);
      }

      // Toggle follow state
      setIsFollowing(!isFollowing);

      // Show toast message
      ToastAndroid.show(
        isFollowing ? 'Unfollowed user' : 'Following user',
        ToastAndroid.SHORT
      );
    } catch (error) {
      console.error('Error following/unfollowing user:', error);
      ToastAndroid.show('Failed to update follow status', ToastAndroid.SHORT);
    }
  };

  // Handle like/unlike status
  const handleLikeStatus = async () => {
    if (!currentUserId || isCurrentUserStatus || !currentStatus) return;

    try {
      // Store the current like state before making the request
      const wasLiked = isLiked;
      console.log('Sending like request for status:', currentStatus.id, 'by user:', currentUserId, 'current like state:', wasLiked);

      // Optimistically update the UI
      setIsLiked(!wasLiked);

      // Update the interaction count optimistically
      setInteractionCount(prev => ({
        ...prev,
        likes: wasLiked ? Math.max(0, prev.likes - 1) : prev.likes + 1
      }));

      // Add timeout to prevent indefinite loading
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`https://status.api.nityasha.com/status/${currentStatus.id}/interact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: currentUserId,
          type: 'like'
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error('Failed to like status');
      }

      // Refresh interaction data in the background
      // This will update the counts with the actual server values
      checkLikeStatus(currentStatus.id, currentUserId);

      // Show toast message
      ToastAndroid.show(
        wasLiked ? 'Removed like' : 'Liked status',
        ToastAndroid.SHORT
      );
    } catch (error) {
      console.error('Error liking status:', error);
      // Revert the optimistic update on error
      setIsLiked(isLiked);
      // Use a more descriptive error message to help with debugging
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ToastAndroid.show(`Failed to like status: ${errorMessage}`, ToastAndroid.SHORT);
    }
  };

  // Create a combined list of viewers and likers with a flag for those who liked
  const getCombinedInteractionsList = () => {
    // Create a map to track unique users
    const userMap = new Map();

    // Add all viewers first
    viewersList.forEach(viewer => {
      if (typeof viewer === 'object' && viewer !== null) {
        userMap.set(viewer.id.toString(), {
          ...viewer,
          hasLiked: false
        });
      }
    });

    // Then add or update likers (marking them as having liked)
    likersList.forEach(liker => {
      if (typeof liker === 'object' && liker !== null) {
        const userId = liker.id.toString();
        if (userMap.has(userId)) {
          // User already in the map (viewed and liked)
          const existingUser = userMap.get(userId);
          userMap.set(userId, {
            ...existingUser,
            hasLiked: true
          });
        } else {
          // New user (only liked, didn't view separately)
          userMap.set(userId, {
            ...liker,
            hasLiked: true
          });
        }
      }
    });

    // Convert map values to array
    return Array.from(userMap.values());
  };

  const handleDeleteStatus = async () => {
    if (!isCurrentUserStatus || !currentStatus) return;

    Alert.alert(
      "Delete Status",
      "Are you sure you want to delete this status?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              // Add timeout to prevent indefinite loading
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

              const response = await fetch(`https://status.api.nityasha.com/status/${currentStatus.id}`, {
                method: 'DELETE',
                signal: controller.signal
              });

              clearTimeout(timeoutId);

              if (!response.ok) {
                throw new Error('Failed to delete status');
              }

              Alert.alert('Success', 'Status deleted successfully');
              navigation.goBack(); // Enable navigation back after successful deletion
            } catch (error) {
              console.error('Error deleting status:', error);
              Alert.alert('Error', 'Failed to delete status');
            }
          }
        }
      ]
    );
  };

  const handleShareStatus = async () => {
    if (!currentStatus) return;

    try {
      // Create a direct link to this status
      const directLink = `https://status.api.nityasha.com/status-redirect/${currentStatus.id}`;

      // Include the direct link in the share message
      const shareMessage = currentStatus.body
        ? `${currentStatus.body}\n\nView this status: ${directLink}\n\n${currentStatus.media_url}`
        : `View this status: ${directLink}\n\n${currentStatus.media_url}`;

      // Show toast with direct link info
      ToastAndroid.show(
        'Sharing direct link to status',
        ToastAndroid.SHORT
      );

      const shareOptions = {
        message: shareMessage,
        title: 'Share Status',
        url: directLink, // For iOS URL sharing
      };

      // Set a timeout for the share operation
      const sharePromise = new Promise<any>(async (resolve, reject) => {
        try {
          const result = await Share.share(shareOptions, {
            // iOS specific options
            dialogTitle: 'Share Status',
            excludedActivityTypes: [
              'com.apple.UIKit.activity.Print',
              'com.apple.UIKit.activity.AssignToContact',
            ],
          });
          resolve(result);
        } catch (e) {
          reject(e);
        }
      });

      // Create a timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Share operation timed out')), 5000);
      });

      // Race the share operation against the timeout
      const result = await Promise.race([sharePromise, timeoutPromise]);

      if (result && result.action === Share.sharedAction) {
        // Optionally track successful shares
        console.log('Shared successfully');
      }
    } catch (error) {
      console.error('Error sharing status:', error);
      ToastAndroid.show(
        'Failed to share status',
        ToastAndroid.SHORT
      );
    }
  };

  // This early return was moved to the end of the component

  // Define the viewability change handler
  const handleViewableItemsChanged = useCallback((info: { viewableItems: ViewToken[]; changed: ViewToken[] }) => {
    const { viewableItems } = info;
    if (viewableItems.length > 0 && showRecent) {
      const visibleIndex = viewableItems[0].index;
      if (visibleIndex !== null) {
        console.log('Current visible index:', visibleIndex);
        setCurrentIndex(visibleIndex);

        // Check like and follow status for the newly visible item
        const currentItem = recentStatuses[visibleIndex];
        if (currentItem && currentUserId) {
          // Fetch interaction data for the current status
          checkLikeStatus(currentItem.id, currentUserId);
          if (currentItem.user_id !== currentUserId) {
            checkFollowStatus(currentItem.user_id, currentUserId);
          }
        }
      }

      // Strictly enforce that only one video plays at a time
      Object.keys(videoRefs).forEach((key) => {
        const index = parseInt(key);
        const isCurrentlyVisible = index === visibleIndex;

        // First completely unload ALL videos to prevent audio overlap
        if (!isCurrentlyVisible && videoRefs[index]) {
          try {
            // Check if video ref is valid before calling methods
            if (typeof videoRefs[index].pauseAsync === 'function') {
              // First pause the video
              videoRefs[index].pauseAsync()
                .then(() => {
                  // Then completely unload it to stop any audio
                  if (videoRefs[index] && typeof videoRefs[index].unloadAsync === 'function') {
                    videoRefs[index].unloadAsync().catch(() => {});
                  }
                })
                .catch(() => {
                  // Try to unload anyway if pause fails
                  if (videoRefs[index] && typeof videoRefs[index].unloadAsync === 'function') {
                    videoRefs[index].unloadAsync().catch(() => {});
                  }
                });
              setPausedVideos(prev => ({ ...prev, [index]: true }));
            }
          } catch (err) {
            // Ignore errors when stopping videos
            console.log(`Could not properly stop video ${index} - it may not be loaded yet`);
          }
        }

        // Only play the currently visible video
        if (isCurrentlyVisible) {
          if (videoRefs[index]) {
            console.log(`Auto-playing visible video at index ${index}`);

            // Then play the current video with retry mechanism
            const playVideoWithRetry = async (retryCount = 0) => {
              const MAX_RETRIES = 3;
              try {
                // First check if the video is already playing
                if (typeof videoRefs[index].getStatusAsync !== 'function') {
                  console.log(`Video reference at index ${index} is not ready yet`);
                  return;
                }

                const status = await videoRefs[index].getStatusAsync();

                // If video is already playing, don't do anything
                if (status.isPlaying) {
                  console.log(`Video ${index} is already playing`);
                  setPausedVideos(prev => ({ ...prev, [index]: false }));
                  return;
                }

                // If video is loaded but not playing, play it
                if (status.isLoaded) {
                  console.log(`Playing loaded video ${index}`);
                  await videoRefs[index].playAsync();
                  setPausedVideos(prev => ({ ...prev, [index]: false }));
                } else {
                  // If video is not loaded, reload it
                  console.log(`Reloading video ${index} before playing`);
                  const videoUrl = recentStatuses[index]?.media_url;
                  if (videoUrl && typeof videoRefs[index].loadAsync === 'function') {
                    await videoRefs[index].loadAsync({ uri: videoUrl });
                    if (typeof videoRefs[index].playAsync === 'function') {
                      await videoRefs[index].playAsync();
                      setPausedVideos(prev => ({ ...prev, [index]: false }));
                    }
                  }
                }
              } catch (err: any) {
                console.error(`Error playing video ${index} (attempt ${retryCount + 1}):`, err);

                // Handle audio focus error
                if (err.message && err.message.includes('AudioFocusNotAcquired')) {
                  try {
                    console.log(`Audio focus error for video ${index}, trying muted playback`);
                    if (videoRefs[index] && typeof videoRefs[index].setIsMutedAsync === 'function') {
                      await videoRefs[index].setIsMutedAsync(true);
                      if (typeof videoRefs[index].playAsync === 'function') {
                        await videoRefs[index].playAsync();
                        setPausedVideos(prev => ({ ...prev, [index]: false }));
                        return;
                      }
                    }
                  } catch (mutedErr: any) {
                    console.error(`Failed to play video ${index} even when muted:`, mutedErr);
                  }
                }

                // Retry logic with exponential backoff
                if (retryCount < MAX_RETRIES) {
                  const delay = 500 * Math.pow(2, retryCount); // 500ms, 1000ms, 2000ms
                  console.log(`Retrying playback for video ${index} in ${delay}ms...`);
                  setTimeout(() => {
                    if (videoRefs[index]) { // Check if ref still exists before retry
                      playVideoWithRetry(retryCount + 1);
                    }
                  }, delay);
                } else {
                  // Last resort: completely reload the video
                  try {
                    const videoUrl = recentStatuses[index]?.media_url;
                    if (videoUrl && videoRefs[index] && typeof videoRefs[index].unloadAsync === 'function') {
                      console.log(`Last resort: completely reloading video ${index}`);
                      await videoRefs[index].unloadAsync(); // Completely unload
                      await new Promise(resolve => setTimeout(resolve, 300)); // Short delay
                      if (videoRefs[index] && typeof videoRefs[index].loadAsync === 'function') {
                        await videoRefs[index].loadAsync({ uri: videoUrl });
                        if (typeof videoRefs[index].playAsync === 'function') {
                          await videoRefs[index].playAsync();
                          setPausedVideos(prev => ({ ...prev, [index]: false }));
                        }
                      }
                    }
                  } catch (finalErr: any) {
                    console.error(`Final attempt to play video ${index} failed:`, finalErr);
                  }
                }
              }
            };

            // Start the retry process
            playVideoWithRetry();
          }
        }
      });
    }
  }, [currentUserId, pausedVideos, recentStatuses, showRecent, videoRefs, checkLikeStatus, checkFollowStatus]);

  // Create a ref for the viewability change handler
  const onViewableItemsChanged = useRef(handleViewableItemsChanged);

  // Update the ref when the callback changes
  useEffect(() => {
    onViewableItemsChanged.current = handleViewableItemsChanged;
  }, [handleViewableItemsChanged]);

  // Video pause/resume functionality removed

  // Record view when current index changes - with debounce
  const lastRecordedViewRef = useRef<{statusId: number, timestamp: number} | null>(null);

  useEffect(() => {
    // Safety check to prevent errors with invalid indices
    if (!recentStatuses || recentStatuses.length === 0 || currentIndex < 0 || currentIndex >= recentStatuses.length) {
      return;
    }

    try {
      const currentItem = recentStatuses[currentIndex];
      if (!currentItem) return; // Skip if item doesn't exist

      const isItemCurrentUserStatus = normalizeId(currentItem.user_id) === normalizeId(currentUserId);
      const now = Date.now();

      // Always check like status and fetch interaction data when current index changes
      if (currentUserId) {
        checkLikeStatus(currentItem.id, currentUserId);
      }

      // Only record view if:
      // 1. It's not the user's own status
      // 2. We have a valid user ID
      // 3. We haven't recorded this status recently (within 30 seconds)
      if (!isItemCurrentUserStatus && currentUserId &&
          (!lastRecordedViewRef.current ||
           lastRecordedViewRef.current.statusId !== currentItem.id ||
           now - lastRecordedViewRef.current.timestamp > 30000)) {

        // Update the last recorded view reference
        lastRecordedViewRef.current = {
          statusId: currentItem.id,
          timestamp: now
        };

        // Record view for the currently visible item
        recordStatusView(currentItem.id, currentUserId);
      }

      // If this is not the user's own status, check follow status
      if (!isItemCurrentUserStatus && currentUserId) {
        checkFollowStatus(currentItem.user_id, currentUserId);
      }
    } catch (err) {
      console.error('Error in view recording effect:', err);
    }
  }, [currentIndex, recentStatuses, currentUserId]);

  // Helper function to normalize user IDs for comparison
  const normalizeId = (id: string | number | null | undefined): string => {
    if (id === null || id === undefined) return '';
    return id.toString().trim();
  };

  // Render each status item for vertical scrolling (TikTok-style)
  const renderVerticalItem = ({ item, index }: { item: Status; index: number }) => {
    const isItemVideo = item.media_url.endsWith('.mp4');
    const isItemCurrentUserStatus = normalizeId(item.user_id) === normalizeId(currentUserId);

    return (
      <View style={{ width, height }}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            // Set current index before checking for double tap
            if (currentIndex !== index) {
              setCurrentIndex(index);
            } else {
              checkDoubleTap();
            }
          }}
          style={tw`flex-1 justify-center items-center bg-black`}
        >
          {isItemVideo ? (
            <Video
              ref={(ref) => {
                if (ref) {
                  videoRefs[index] = ref;
                  setVideoRefs(videoRefs);
                }
              }}
              source={{ uri: item.media_url }}
              rate={1.0}
              volume={1.0}
              isMuted={false}
              // Using standard implementation for better compatibility
              resizeMode={ResizeMode.COVER}
              shouldPlay={currentIndex === index} // Only play the current video
              isLooping={true} // Set to true to ensure continuous playback
              style={tw`w-full h-full`}
              useNativeControls={false}
              progressUpdateIntervalMillis={500} // Less frequent updates to reduce overhead
              onPlaybackStatusUpdate={(status) => {
                // Always handle status updates for all videos to ensure they keep playing
                // Check if status is a success status (not an error)
                if ('isLoaded' in status && status.isLoaded) {
                  const isPlaying = status.isPlaying || false;
                  const isBuffering = status.isBuffering || false;
                  const didJustFinish = status.didJustFinish || false;

                  // Only handle playback for the currently visible video
                  // This prevents multiple videos from playing audio simultaneously
                  if (currentIndex === index) {
                    // Force play if video is not playing and should be visible
                    if (!isPlaying && !isBuffering) {
                      console.log(`Video ${index} is not playing but should be - forcing playback`);
                      if (videoRefs[index] && typeof videoRefs[index].playAsync === 'function') {
                        try {
                          videoRefs[index].playAsync().catch((err: any) => {
                            console.error(`Error forcing video ${index} to play:`, err);

                            // Handle audio focus error specifically
                            if (err.message && err.message.includes('AudioFocusNotAcquired')) {
                              console.log(`Audio focus error detected for video ${index}, trying with muted audio`);
                              if (videoRefs[index] && typeof videoRefs[index].setIsMutedAsync === 'function') {
                                videoRefs[index].setIsMutedAsync(true)
                                  .then(() => {
                                    if (videoRefs[index] && typeof videoRefs[index].playAsync === 'function') {
                                      videoRefs[index].playAsync()
                                        .catch((playErr: any) => {
                                          console.error(`Failed to play video ${index} after muting:`, playErr);
                                        });
                                    }
                                  })
                                  .catch((mutedErr: any) => {
                                    console.error(`Failed to mute video ${index}:`, mutedErr);
                                  });
                              }
                            }
                          });
                        } catch (error) {
                          console.error(`Unexpected error when forcing video ${index} playback:`, error);
                        }
                      }
                    }
                  } else {
                    // If this is not the current video but it's playing, stop it
                    if (isPlaying) {
                      console.log(`Stopping non-visible video ${index} that is playing`);
                      if (videoRefs[index] && typeof videoRefs[index].pauseAsync === 'function') {
                        videoRefs[index].pauseAsync().catch(() => {});
                      }
                    }
                  }

                  // If video is stuck buffering, try to recover quickly
                  if (isBuffering && !isPlaying && status.positionMillis > 0) {
                    // Check if videoRefs[index] exists and is fully initialized before attempting recovery
                    if (videoRefs[index] && typeof videoRefs[index].setPositionAsync === 'function') {
                      try {
                        console.log(`Attempting to recover video ${index} from buffering`);
                        // Try to seek slightly ahead to get past buffering point
                        videoRefs[index].setPositionAsync(status.positionMillis + 500)
                          .then(() => {
                            // Check again if the video ref is still valid before playing
                            if (videoRefs[index] && typeof videoRefs[index].playAsync === 'function') {
                              return videoRefs[index].playAsync();
                            }
                            return Promise.resolve();
                          })
                          .catch((err: any) => {
                            console.error(`Error recovering video ${index} from buffering:`, err);
                            // If seeking fails, try reloading the video, but check if ref is still valid
                            if (videoRefs[index] && typeof videoRefs[index].loadAsync === 'function') {
                              videoRefs[index].loadAsync({ uri: item.media_url })
                                .then(() => {
                                  if (videoRefs[index] && typeof videoRefs[index].playAsync === 'function') {
                                    return videoRefs[index].playAsync();
                                  }
                                  return Promise.resolve();
                                })
                                .catch((loadErr: any) => {
                                  console.error(`Error reloading video ${index} after buffering:`, loadErr);
                                });
                            }
                          });
                      } catch (error) {
                        console.error(`Unexpected error during video ${index} buffering recovery:`, error);
                      }
                    } else {
                      console.log(`Cannot recover video ${index} from buffering: Video component not fully loaded`);
                    }
                  }

                  // If video has finished playing, ensure it restarts
                  if (didJustFinish) {
                    console.log(`Video ${index} finished playing - ensuring it restarts`);
                    // Check if videoRefs[index] exists and is fully initialized before attempting restart
                    if (videoRefs[index] && typeof videoRefs[index].setPositionAsync === 'function') {
                      try {
                        // Even though isLooping is true, we'll manually restart as a backup
                        videoRefs[index].setPositionAsync(0)
                          .then(() => {
                            if (videoRefs[index] && typeof videoRefs[index].playAsync === 'function') {
                              videoRefs[index].playAsync()
                                .catch((playErr: any) => {
                                  console.error(`Error playing video ${index} after restart:`, playErr);
                                });
                            }
                          })
                          .catch((err: any) => {
                            console.error(`Error restarting video ${index}:`, err);
                          });
                      } catch (error) {
                        console.error(`Unexpected error during video ${index} restart:`, error);
                      }
                    } else {
                      console.log(`Cannot restart video ${index}: Video component not fully loaded`);
                    }
                  }
                } else if ('error' in status) {
                  // Handle error status with immediate recovery
                  console.error(`Video ${index} playback error:`, status.error);
                  // Check if videoRefs[index] exists and is fully initialized before attempting recovery
                  if (videoRefs[index] && typeof videoRefs[index].loadAsync === 'function') {
                    try {
                      // Try to reload the video immediately after an error
                      videoRefs[index].loadAsync({ uri: item.media_url })
                        .then(() => {
                          if (videoRefs[index] && typeof videoRefs[index].playAsync === 'function') {
                            videoRefs[index].playAsync()
                              .catch((playErr: any) => {
                                console.error(`Error playing video ${index} after error recovery:`, playErr);
                              });
                          }
                        })
                        .catch((err: any) => {
                          console.error(`Error reloading video ${index} after error:`, err);
                        });
                    } catch (error) {
                      console.error(`Unexpected error during video ${index} error recovery:`, error);
                    }
                  } else {
                    console.log(`Cannot recover video ${index} from error: Video component not fully loaded`);
                  }
                }
              }}
              onLoad={() => {
                console.log(`Video ${index} loaded`);
                // Only play the video if it's the current one being viewed
                if (currentIndex === index) {
                  console.log(`Auto-playing newly loaded video ${index} because it's visible`);
                  setPausedVideos(prev => ({ ...prev, [index]: false }));

                  // First, make sure all other videos are completely unloaded to prevent audio overlap
                  Object.keys(videoRefs).forEach((key) => {
                    const otherIndex = parseInt(key);
                    if (otherIndex !== index && videoRefs[otherIndex]) {
                      try {
                        // Check if video ref is valid before calling methods
                        if (typeof videoRefs[otherIndex].unloadAsync === 'function') {
                          console.log(`Unloading other video ${otherIndex} to prevent audio overlap`);
                          videoRefs[otherIndex].unloadAsync().catch(() => {});
                          setPausedVideos(prev => ({ ...prev, [otherIndex]: true }));
                        }
                      } catch (err) {
                        // Ignore errors when unloading videos
                        console.log(`Could not unload video ${otherIndex}`);
                      }
                    }
                  });

                  // Play strategy with audio focus handling
                  if (videoRefs[index]) {
                    // First attempt - immediate play with audio focus handling
                    const playWithAudioFocusHandling = async () => {
                      try {
                        // Try to play the video
                        await videoRefs[index].playAsync();
                        console.log(`Successfully started playback for video ${index}`);
                      } catch (err: any) {
                        console.error(`First attempt to play video ${index} failed:`, err);

                        // Check if it's an audio focus error
                        if (err.message && err.message.includes('AudioFocusNotAcquired')) {
                          console.log(`Audio focus error detected for video ${index}, trying with muted audio`);

                          try {
                            // Try to play muted if audio focus can't be acquired
                            await videoRefs[index].setIsMutedAsync(true);
                            await videoRefs[index].playAsync();
                            console.log(`Playing video ${index} muted due to audio focus issues`);
                          } catch (mutedErr: any) {
                            console.error(`Failed to play video ${index} even when muted:`, mutedErr);
                          }
                        }
                      }
                    };

                    // Start the play process
                    playWithAudioFocusHandling();
                  }
                } else {
                  // Ensure non-visible videos are completely unloaded, not just paused
                  console.log(`Unloading newly loaded video ${index} because it's not visible`);
                  setPausedVideos(prev => ({ ...prev, [index]: true }));
                  // Safely unload with error handling
                  try {
                    if (videoRefs[index] && typeof videoRefs[index].unloadAsync === 'function') {
                      videoRefs[index].unloadAsync().catch(() => {});
                    }
                  } catch (err) {
                    // Ignore errors when unloading videos
                    console.log(`Could not unload video ${index}`);
                  }
                }
              }}
              onError={(error) => {
                console.error(`Error loading video ${index}:`, error);
                // If there's an error, try to reload the video
                if (videoRefs[index]) {
                  setTimeout(() => {
                    // Check if videoRefs[index] still exists after the timeout
                    if (videoRefs[index] && typeof videoRefs[index].loadAsync === 'function') {
                      try {
                        videoRefs[index].loadAsync({ uri: item.media_url })
                          .catch((loadErr: any) => {
                            console.error(`Error reloading video ${index} after onError:`, loadErr);
                          });
                      } catch (unexpectedError) {
                        console.error(`Unexpected error reloading video ${index} after onError:`, unexpectedError);
                      }
                    }
                  }, 1000);
                }
              }}
            />
          ) : (
            <Image
              source={{ uri: item.media_url }}
              style={tw`w-full h-full`}
              resizeMode="cover"
            />
          )}

          {/* User info overlay */}
          <View style={tw`absolute top-0 left-0 right-0 p-4 flex-row items-center z-10`}>
            <TouchableOpacity
              style={tw`mr-4`}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <Text style={tw`text-white font-bold text-lg`}>Status</Text>
          </View>

          {/* Status info overlay */}
          <View style={tw`absolute bottom-0 left-0 right-0 p-4 z-10`}>
            <View style={tw`flex-row items-center mb-2`}>
              <Image
                source={{ uri: item.pfp || (userData && userData.phoneNumber === item.user_id) ? `https://ui-avatars.com/api/?name=${encodeURIComponent(userData?.name || 'User')}` : `https://api.dicebear.com/7.x/initials/png?seed=${item.user_id}` }}
                style={tw`w-10 h-10 rounded-full mr-3`}
              />
              <View style={tw`flex-1`}>
                <Text style={tw`text-white font-bold`}>{(userData && userData.phoneNumber === item.user_id) ? userData.name : (item.username || `User ${item.user_id}`)}</Text>
                <Text style={tw`text-white text-xs opacity-80`}>{formatDate(item.created_at)}</Text>
                {userData && userData.phoneNumber === item.user_id && userData.contactName && userData.contactName !== userData.name && (
                  <Text style={tw`text-white text-xs opacity-60`}>Saved as: {userData.contactName}</Text>
                )}
              </View>
            </View>

            {/* Display caption if available */}
            {item.body && (
              <View style={tw`bg-black bg-opacity-50 rounded-lg p-3 mb-2`}>
                <Text style={tw`text-white text-base`}>{item.body}</Text>
              </View>
            )}

            {/* Stop video indicator removed */}

            {/* Double tap heart animation for vertical view */}
            {doubleTapActive && currentIndex === index && (
              <View style={tw`absolute top-1/2 left-1/2 -mt-12 -ml-12 z-20`}>
                <Animated.View style={{
                  transform: [{ scale: heartAnimation }]
                }}>
                  <Ionicons name="heart" size={96} color="#FF4136" />
                </Animated.View>
              </View>
            )}
          </View>

          {/* Right side action buttons (TikTok-style) */}
          <View style={tw`absolute right-4 bottom-20 items-center z-10`}>
            {/* Interaction count button (only show for current user's status) */}
            {isItemCurrentUserStatus && (
              <TouchableOpacity
                style={tw`items-center mb-4`}
                onPress={() => {
                  // Set current status before showing interactions
                  setCurrentIndex(index);
                  setTimeout(() => setShowInteractions(true), 100);
                }}
              >
                <View style={tw`flex-col items-center`}>
                  <Ionicons name="eye-outline" size={32} color="white" />
                  <Text style={tw`text-white text-xs mt-1`}>{interactionCount.views}</Text>
                </View>
              </TouchableOpacity>
            )}

            {/* Delete button (only show for current user's status) */}
            {isItemCurrentUserStatus && (
              <TouchableOpacity
                style={tw`items-center mb-4`}
                onPress={() => {
                  // Set current status before deleting
                  setCurrentIndex(index);
                  setTimeout(() => handleDeleteStatus(), 100);
                }}
              >
                <Ionicons name="trash-outline" size={32} color="white" />
              </TouchableOpacity>
            )}

            {/* Like button (only show if not current user's status) */}
            {!isItemCurrentUserStatus && (
              <TouchableOpacity
                style={tw`items-center mb-4`}
                onPress={() => {
                  // Set current status before liking
                  setCurrentIndex(index);
                  setTimeout(() => handleLikeStatus(), 100);
                }}
              >
                <Ionicons
                  name={currentIndex === index && isLiked ? "heart" : "heart-outline"}
                  size={32}
                  color={currentIndex === index && isLiked ? "#FF4136" : "white"}
                />
              </TouchableOpacity>
            )}

            {/* Share button */}
            <TouchableOpacity
              style={tw`items-center mb-4`}
              onPress={() => {
                // Set current status before sharing
                setCurrentIndex(index);
                setTimeout(() => handleShareStatus(), 100);
              }}
            >
              <Ionicons name="share-social-outline" size={32} color="white" />
            </TouchableOpacity>

            {/* Follow button (only show if not current user's status) */}
            {!isItemCurrentUserStatus && (
              <TouchableOpacity
                style={tw`items-center`}
                onPress={() => {
                  // Set current status before following
                  setCurrentIndex(index);
                  setTimeout(() => handleFollowToggle(), 100);
                }}
              >
                <Ionicons
                  name={currentIndex === index && isFollowing ? "person-remove-outline" : "person-add-outline"}
                  size={32}
                  color="white"
                />
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // Render function for vertical scrolling UI (TikTok-style)
  const renderVerticalScrollingUI = () => {
    return (
      <SafeAreaView style={tw`flex-1 bg-black`}>
        <StatusBar barStyle="light-content" backgroundColor="#000" />

        {isLoading ? (
          <View style={tw`flex-1 justify-center items-center bg-black`}>
            <ActivityIndicator size="large" color="#fff" />
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={recentStatuses}
            renderItem={renderVerticalItem}
            keyExtractor={(item) => item.id.toString()}
            pagingEnabled
            showsVerticalScrollIndicator={false}
            onEndReached={loadMoreStatuses}
            onEndReachedThreshold={0.5}
            onViewableItemsChanged={({ viewableItems, changed }) => {
              if (onViewableItemsChanged.current) {
                onViewableItemsChanged.current({ viewableItems, changed });
              }
            }}
            viewabilityConfig={{
              itemVisiblePercentThreshold: 80,  // Increase threshold for better detection
              minimumViewTime: 300,            // Minimum time item needs to be visible
            }}
            initialNumToRender={2}              // Reduce initial render for faster first load
            maxToRenderPerBatch={2}            // Render fewer items per batch for smoother scrolling
            windowSize={3}                     // Keep fewer items in memory
            removeClippedSubviews={true}       // Remove items outside of viewport
            snapToInterval={height}            // Snap to full height
            snapToAlignment="start"            // Align snapping to the start
            decelerationRate="fast"            // Fast deceleration for snappy scrolling
            bounces={false}                    // Disable bouncing for smoother experience
            ListFooterComponent={
              isLoadingMore ? (
                <View style={tw`py-5 bg-black h-20 justify-center items-center`}>
                  <ActivityIndicator size="large" color="#fff" />
                </View>
              ) : null
            }
            ListEmptyComponent={
              !isLoading ? (
                <View style={tw`flex-1 justify-center items-center h-full py-20`}>
                  <Text style={tw`text-white text-lg`}>No statuses found</Text>
                </View>
              ) : null
            }
          />
        )}
      </SafeAreaView>
    );
  };

  // Render function for horizontal scrolling UI
  const renderHorizontalScrollingUI = () => {
    return (
    <SafeAreaView style={tw`flex-1 bg-black`}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      {/* Header */}
      <View style={tw`w-full p-4 flex-row justify-between items-center absolute top-0`}>
        {/* Back Button - absolutely positioned on the left */}
        <TouchableOpacity
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        {/* Centered Title */}
        <View style={tw`items-center`}>
          <Text style={tw`text-white font-bold text-lg`}>
            {isCurrentUserStatus ? 'My Status' : userName}
          </Text>
          {userData && userData.contactName && userData.contactName !== userData.name && !isCurrentUserStatus && (
            <Text style={tw`text-white text-xs opacity-60`}>Saved as: {userData.contactName}</Text>
          )}
        </View>
        <View style={tw`flex-row`}>
          {isCurrentUserStatus && (
            <>
              <TouchableOpacity
                onPress={() => setShowInteractions(true)}
                style={tw`p-2 mr-2 flex-row items-center`}
              >
                <Ionicons name="eye-outline" size={22} color="white" />
                <Text style={tw`text-white ml-1`}>{interactionCount.views}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleDeleteStatus}
                style={tw`p-2`}
              >
                <Ionicons name="trash-outline" size={24} color="white" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>


          

      {/* Loading indicator for more statuses */}
      {isLoadingMore && showRecent && (
        <View style={tw`absolute top-16 left-0 right-0 items-center z-20`}>
          <View style={tw`bg-black bg-opacity-70 px-4 py-2 rounded-full`}>
            <Text style={tw`text-white`}>Loading more statuses...</Text>
          </View>
        </View>
      )}

      {/* Content */}
      <View style={tw`flex-1 justify-center items-center`}>
        <TouchableOpacity
          activeOpacity={1}
          onPressOut={() => {}}
          onPress={checkDoubleTap}
          style={tw`flex-1 w-full justify-center items-center`}
        >
          <View style={tw`flex-row w-full h-full relative`}>
            {/* Content area - Full width */}
            <View style={tw`w-full h-full justify-center items-center`}>
              {loading && (
                <ActivityIndicator
                  size="large"
                  color="#fff"
                  style={tw`absolute z-10`}
                />
              )}

              <View style={tw`w-full h-full relative`}>
                {currentStatus && isVideo ? (
                  <View style={tw`flex-1 justify-center items-center w-full h-full`}>
                    <Video
                      ref={videoRef}
                      source={{ uri: currentStatus.media_url }}
                      rate={1.0}
                      volume={1.0}
                      isMuted={false}
                      resizeMode={videoResizeMode}
                      shouldPlay={true}
                      isLooping={true} // Set to true for horizontal view
                      progressUpdateIntervalMillis={500} // More frequent updates
                      style={[tw`w-full h-full`, videoResizeMode === ResizeMode.CONTAIN ? { alignSelf: 'center' } : null]}
                      onError={(error) => console.error('Video error:', error)}
                      useNativeControls={false}
                    />
                  </View>
                ) : currentStatus ? (
                  <Image
                    source={{ uri: currentStatus.media_url }}
                    style={tw`w-full h-full`}
                    resizeMode="contain"
                    onLoad={() => setLoading(false)}
                  />
                ) : null}

                {/* Caption overlay */}
                {currentStatus?.body && (
                  <View style={tw`absolute bottom-8 left-0 right-0 px-4 py-2`}>
                    <View style={tw`bg-black bg-opacity-50 rounded-lg p-3`}>
                      <Text style={tw`text-white text-base`}>{currentStatus.body}</Text>
                    </View>
                  </View>
                )}
              </View>
            </View>

            {/* Touch areas as overlays */}
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => handleScreenTouch('left')}
              style={tw`absolute left-0 top-0 w-1/3 h-full z-10`}
            />
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => handleScreenTouch('right')}
              style={tw`absolute right-0 top-0 w-1/3 h-full z-10`}
            />
          </View>
        </TouchableOpacity>
      </View>


      {/* Double tap heart animation */}
      {doubleTapActive && (
        <View style={tw`absolute top-1/2 left-1/2 -mt-12 -ml-12 z-20`}>
          <Animated.View style={{
            transform: [{ scale: heartAnimation }]
          }}>
            <Ionicons name="heart" size={96} color="#FF4136" />
          </Animated.View>
        </View>
      )}

      <Modal
        animationType="slide"
        transparent={true}
        visible={showInteractions}
        onRequestClose={() => setShowInteractions(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Status Interactions</Text>
              <TouchableOpacity onPress={() => setShowInteractions(false)}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Interaction counts */}
            <View style={tw`flex-row justify-between mb-4`}>
              <View style={tw`flex-row items-center`}>
                <Ionicons name="eye-outline" size={20} color="#333" />
                <Text style={tw`ml-1 text-gray-700`}>{interactionCount.views} Views</Text>
              </View>
              <View style={tw`flex-row items-center`}>
                <Ionicons name="heart-outline" size={20} color="#333" />
                <Text style={tw`ml-1 text-gray-700`}>{interactionCount.likes} Likes</Text>
              </View>
            </View>

            <FlatList
              data={getCombinedInteractionsList()}
              keyExtractor={(item) => {
                return item.id.toString() + (item.hasLiked ? '-liked' : '-viewed');
              }}
              renderItem={({ item }) => {
                return (
                  <View style={styles.userItem}>
                    <View style={tw`relative`}>
                      <Image
                        source={{ uri: item.pfp || `https://ui-avatars.com/api/?name=${encodeURIComponent(item.username || 'User')}` }}
                        style={styles.userAvatar}
                      />
                      {item.hasLiked && (
                        <View style={tw`absolute -right-1 -bottom-1 bg-white rounded-full p-0.5`}>
                          <Ionicons name="heart" size={14} color="#FF5F6D" />
                        </View>
                      )}
                    </View>
                    <Text style={styles.userName}>{item.username || 'User'}</Text>
                  </View>
                );
              }}
              ListEmptyComponent={
                <Text style={styles.emptyText}>
                  No interactions yet
                </Text>
              }
            />
          </View>
        </View>
      </Modal>
      <View style={[tw`absolute right-4 bottom-20 items-center gap-7`, { elevation: 1, zIndex: 999, backfaceVisibility: 'hidden' }]}>
        {!isCurrentUserStatus && (
          <>
            <TouchableOpacity onPress={handleFollowToggle} style={[tw`bg-white rounded-full relative h-12 w-12 justify-center items-center`, { backfaceVisibility: 'hidden' }]}>
              <Image style={tw`rounded-full w-full h-full `} source={{ uri: `https://api.dicebear.com/7.x/initials/png?seed=${userName}` }} resizeMode="cover" />
              {!isFollowing && (
                <View style={tw`absolute -bottom-2 self-center bg-white rounded-full p-1 border-2`}>
                  <Plus size={15} color={"#000"} />
                </View>
              )}
            </TouchableOpacity>
          </>
        )}
        {!isCurrentUserStatus && (
          <TouchableOpacity
            style={{ backfaceVisibility: 'hidden' }}
            onPress={handleLikeStatus}
          >
            <Ionicons
              name={isLiked ? "heart" : "heart-outline"}
              size={26}
              color={isLiked ? "#FF5F6D" : "white"}
            />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[tw``, { backfaceVisibility: 'hidden' }]}
          onPress={() => {
            if (isVideo && videoRef.current) {
              videoRef.current.setIsMutedAsync(!isMuted);
              setIsMuted(!isMuted);
            }
          }}
        >
          <Volume2
            size={26}
            color={"#fff"}
            style={isMuted ? { opacity: 0.5 } : {}}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={[tw`flex items-center justify-center`, { backfaceVisibility: 'hidden' }]}
          onPress={handleShareStatus}
        >
          <Share2 size={26} color={"#fff"} />
        </TouchableOpacity>

      </View>

      {/* Video Progress Bar - Only show for videos */}
      {isVideo && (
        <View style={tw`absolute bottom-4 left-0 right-0 px-4`}>
          <View style={tw`bg-black bg-opacity-50 rounded-lg p-2`}>
            <View style={tw`h-1 bg-gray-600 rounded-full w-full overflow-hidden`}>
              <Animated.View
                style={[tw`h-full bg-white rounded-full`, { width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%']
                }) }]}
              />
            </View>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
  };

  // Return the appropriate UI based on showRecent flag
  if (!currentStatus) {
    return (
      <View style={tw`flex-1 justify-center items-center bg-black`}>
        <ActivityIndicator size="large" color="#fff" />
        <TouchableOpacity
          style={tw`absolute top-4 left-4 p-2`}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={tw`text-white mt-4`}>Loading status...</Text>
      </View>
    );
  }

  return showRecent ? renderVerticalScrollingUI() : renderHorizontalScrollingUI();

  // Helper function to format time in MM:SS format - moved to top level to avoid unused function warning
  // This function is kept for future use but currently not being used
  // const formatTime = (milliseconds: number): string => {
  //   if (!milliseconds) return '00:00';
  //
  //   const totalSeconds = Math.floor(milliseconds / 1000);
  //   const minutes = Math.floor(totalSeconds / 60);
  //   const seconds = totalSeconds % 60;
  //
  //   return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  // };
}