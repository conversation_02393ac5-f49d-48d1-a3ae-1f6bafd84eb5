import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Image,
  ActivityIndicator,
  Alert,
  AppState,
  Modal,
  Linking,
  Share,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import tw from 'twrnc';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase2';
import { ref, onValue, set, off, update, onDisconnect, get, runTransaction } from 'firebase/database';
import SokitOnlineStatusIndicator from '@/components/SokitOnlineStatusIndicator';
import { Camera, File, Images, MessageCircleOff, X } from 'lucide-react-native';
import { MotiView } from 'moti';
import axios from 'axios';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import { supabase } from '@/utils/supabase';
import * as Contacts from 'expo-contacts';
import * as Notifications from 'expo-notifications';
import { LegendList } from '@legendapp/list'
import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';
import { useKeyboardAnimation } from 'react-native-keyboard-controller';
import { Animated } from 'react-native';

// Firebase chat collection prefixes
const FIREBASE_CHAT_PREFIX = 'user_chats';
const FIREBASE_MESSAGES_PREFIX = 'user_messages';

// Move generateUUID outside the component
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Define message type
interface ChatMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  isUser?: boolean; // For UI rendering
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'error'; // Added 'sending'
  isRead?: boolean;
  readAt?: number;
  type?: string; // Add type for image/text
  imageUrl?: string; // Add imageUrl for image messages
  mediaUrl?: string; // For audio
  contact?: {
    id: string | number;
    name: string;
    phoneNumber?: string;
  };
  replyTo?: {
    id: string;
    text: string;
    senderId: string;
  };
  encrypted?: boolean; // Add encrypted flag
}

interface FirebaseMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  status?: 'sent' | 'delivered' | 'read' | 'error'; // Firebase won't store 'sending', only final states
  isRead?: boolean;
  readAt?: number;
  type?: string; // Add type for image/text
  imageUrl?: string; // Add imageUrl for image messages
  mediaUrl?: string; // For audio
  replyTo?: {
    id: string;
    text: string;
    senderId: string;
  };
  encrypted?: boolean; // Add encrypted flag
}

interface UserStatus {
  state: 'online' | 'offline';
  lastSeen: number;
  isTyping?: boolean;
  typingTimestamp?: number;
  typingInChatWith?: string; // ID of the user they're typing to
}

// Define route params interface
interface RouteParams {
  accountData?: {
    id: string;
    name: string;
    logo: string;
    online?: boolean;
    verified?: boolean;
  };
  // Direct parameters from UserInbox
  otherUserId?: string;
  name?: string;
  online?: boolean;
}

interface NavigationProps {
  navigation: any;
  route: { params?: RouteParams };
}

// --- E2EE HELPERS ---
const deriveSharedSecret = async (userId1: string, userId2: string) => {
  const ids = [userId1, userId2].sort().join(':');
  return await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, ids);
};

// UTF-8 helpers for emoji-safe XOR
function toUtf8Bytes(str: string): Uint8Array {
  return new TextEncoder().encode(str);
}
function fromUtf8Bytes(bytes: Uint8Array): string {
  return new TextDecoder().decode(bytes);
}

function xorEncrypt(text: string, key: string): string {
  const textBytes = toUtf8Bytes(text);
  const keyBytes = toUtf8Bytes(key);
  const result = new Uint8Array(textBytes.length);
  for (let i = 0; i < textBytes.length; i++) {
    result[i] = textBytes[i] ^ keyBytes[i % keyBytes.length];
  }
  // Convert to base64 for safe storage
  return btoa(String.fromCharCode(...result));
}

function xorDecrypt(encoded: string, key: string): string {
  const encryptedBytes = Uint8Array.from(atob(encoded), c => c.charCodeAt(0));
  const keyBytes = toUtf8Bytes(key);
  const result = new Uint8Array(encryptedBytes.length);
  for (let i = 0; i < encryptedBytes.length; i++) {
    result[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
  }
  return fromUtf8Bytes(result);
}

// Add type for contact
interface ContactType { id: string | number; name: string; phoneNumber?: string; }

export default function App({ navigation, route }: NavigationProps) {
  const [userId, setUserId] = useState<string | null>(null);
  const { accountData, otherUserId, name, online } = route.params || {};
  console.log('UserChatInbox route params:', { accountData, otherUserId, name, online, fullRoute: route })
  // Create a compatible accountData object if direct parameters are provided
  const effectiveAccountData = accountData || (otherUserId ? {
    id: otherUserId,
    name: name || 'User',
    logo: `https://api.dicebear.com/7.x/initials/png?seed=${name || 'Unknown'}`,
    online: online
  } : undefined);

  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const flatListRef = useRef<any>(null);
  const viewableItemsRef = useRef<string[]>([]);
  const [isReceiverOnline, setIsReceiverOnline] = useState<boolean>(online === true);
  const [lastSeen, setLastSeen] = useState<number | null>(null);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [showMenu, setShowMenu] = useState<boolean>(false);
  const [isReceiverTyping, setIsReceiverTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // State to store the recipient's Expo push token
  const [recipientPushToken, setRecipientPushToken] = useState<string | null>(null);
  const [username, setUsername] = useState<string | null>(null);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingRef = useRef<Audio.Recording | null>(null);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [playingAudioId, setPlayingAudioId] = useState<string | null>(null);
  const [audioSound, setAudioSound] = useState<Audio.Sound | null>(null);
  // Add at the top of the component (inside App)
  const [contactSent, setContactSent] = useState(false);
  // At the top of the component (inside App)
  const [contactSentIds, setContactSentIds] = useState<Set<number | string>>(new Set());
  // Add at the top of the component (inside App)
  const [showContactPicker, setShowContactPicker] = useState(false);
  // Ensure contactSearch is always a string
  const [contactSearch, setContactSearch] = useState<string>('');
  const [contactsList, setContactsList] = useState<ContactType[]>([]);
  // Reply functionality state
  const [replyToMessage, setReplyToMessage] = useState<ChatMessage | null>(null);
  const [replyToText, setReplyToText] = useState<string>('');
  const [replyToSenderId, setReplyToSenderId] = useState<string>('');
  // BAD WORDS and filter logic (copy from UserInbox if not imported)
  const BAD_WORDS = [
    'kill', 'rape', 'murder', 'suicide', 'terrorist', 'bomb', 'drugs', 'cocaine', 'heroin',
    'porn', 'sex', 'nude', 'naked', 'pussy', 'dick', 'cock', 'fuck', 'shit', 'asshole',
    'bitch', 'whore', 'slut', 'bastard', 'damn', 'hell', 'crap', 'dumb', 'stupid', 'idiot',
    'retard', 'nazi', 'hitler', 'terror', 'weapon', 'gun', 'shoot', 'blood', 'gore',
    'abuse', 'harass', 'stalk', 'hack', 'virus', 'malware', 'scam', 'fraud',
    'hate', 'racist', 'sexist', 'homophobic', 'transphobic', 'bigot', 'discriminate'
  ];

  function makeFlexibleRegex(word: string) {
    // Allow any non-word character (space, dot, dash, etc.) between letters
    return word.split('').map(char => `[${char}]\\W*`).join('');
  }

  const containsBadWords = (text: string): boolean => {
    if (!text) return false;
    const lowerText = text.toLowerCase();

    for (const word of BAD_WORDS) {
      // 1. Direct match (with word boundaries)
      const direct = new RegExp(`\\b${word}\\b`, 'gi');
      if (direct.test(lowerText)) return true;

      // 2. Leet variations
      const variations = [
        word.replace(/a/g, '4'),
        word.replace(/a/g, '@'),
        word.replace(/i/g, '1'),
        word.replace(/e/g, '3'),
        word.replace(/o/g, '0'),
        word.replace(/s/g, '5'),
        word.replace(/s/g, '\\$'),
        word.replace(/t/g, '7'),
        word.replace(/l/g, '1'),
        word.replace(/g/g, '9'),
        word.replace(/b/g, '8')
      ];
      for (const variation of variations) {
        const leet = new RegExp(`\\b${variation}\\b`, 'gi');
        if (leet.test(lowerText)) return true;
      }

      // 3. Flexible (spaces/symbols between letters)
      const flexible = new RegExp(makeFlexibleRegex(word), 'gi');
      if (flexible.test(lowerText)) return true;

      // 4. Flexible leet
      for (const variation of variations) {
        const flexibleLeet = new RegExp(makeFlexibleRegex(variation), 'gi');
        if (flexibleLeet.test(lowerText)) return true;
      }
    }
    return false;
  };
  // ... existing code ...
  // Add at the top of the component (inside App)
  const [badWordWarningCount, setBadWordWarningCount] = useState(0);
  const [isInputDisabled, setIsInputDisabled] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  // ... existing code ...

  // Add at the top of the component (inside App)
  const [requireBothOnline, setRequireBothOnline] = useState<boolean>(true);

  // Helper to get per-chat key
  const getRequireBothOnlineKey = (userId: string, otherId: string) => `requireBothOnline_${[userId, otherId].sort().join('_')}`;

  // Load per-chat preference on mount or when chat changes
  useEffect(() => {
    if (!userId || !effectiveAccountData?.id) return;
    const key = getRequireBothOnlineKey(userId, effectiveAccountData.id);
    AsyncStorage.getItem(key).then(val => {
      if (val === null) setRequireBothOnline(true); // default ON
      else setRequireBothOnline(val === 'true');
    });
  }, [userId, effectiveAccountData?.id]);

  // Save preference when changed
  useEffect(() => {
    if (!userId || !effectiveAccountData?.id) return;
    const key = getRequireBothOnlineKey(userId, effectiveAccountData.id);
    AsyncStorage.setItem(key, requireBothOnline ? 'true' : 'false');
  }, [requireBothOnline, userId, effectiveAccountData?.id]);

 useEffect(() => {
  const getusername = async () => {
     try {
      const userSession = await AsyncStorage.getItem('userDetails');
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        const name = parsedSession?.username || userSession;
        setUsername(name);
         console.log('Fetched username from AsyncStorage:', name);
      }
     } catch (error) {
      console.error('Error getting username:', error);
     }
  }
  getusername();
 }, []);
  // Get userId from AsyncStorage
  useEffect(() => {
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          const id = parsedSession?.userId || userSession;
          setUserId(id);
           console.log('Fetched userId from AsyncStorage:', id);
        } else {
          console.error('No user session found');
          Alert.alert('Error', 'Please login again');
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error getting user session:', error);
        Alert.alert('Error', 'Failed to get user information');
        navigation.goBack();
      }
    };

    getUserId();

    // Set up app state listener for online/offline status
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Clean up when component unmounts
    return () => {
      subscription.remove();

      // Clear any pending app state timeout
      if (appStateTimeoutRef.current) {
        clearTimeout(appStateTimeoutRef.current);
      }

      // Clear any typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }

      // Manual status updates on graceful unmount are handled by the other useEffect now
      if (userId) { // Check userId before calling updateTypingStatus
         updateTypingStatus(false); // Only reset typing status here
      }
      console.log('UserChatInbox component unmounted.');
    };
  }, [navigation, userId]); // Added userId dependency to cleanup logic

  // Set up initial online status and onDisconnect handlers
  useEffect(() => {
    if (!userId || !effectiveAccountData?.id) {
       console.log('Status/onDisconnect setup skipped: userId or effectiveAccountData.id missing.');
       return;
    }

    console.log('Setting up user status and onDisconnect handlers.');
    const userStatusRef = ref(db, `users/${userId}/status`);
    const visibilityRef = ref(db, `user_visibility/${userId}/visible_to/${effectiveAccountData.id}`);

    // Set up onDisconnect for general status
    const statusOnDisconnect = onDisconnect(userStatusRef);
    statusOnDisconnect.update({
      state: 'offline',
      lastSeen: Date.now(),
      isTyping: false,
      typingInChatWith: null
    }).catch(error => console.error('Failed to set onDisconnect status:', error));

    // Set up onDisconnect for visibility
    const visibilityOnDisconnect = onDisconnect(visibilityRef);
    visibilityOnDisconnect.remove().catch(error => console.error('Failed to set onDisconnect visibility:', error));

    // Set initial online status immediately
    set(userStatusRef, {
      state: 'online',
      lastSeen: Date.now(),
      isTyping: false,
      typingInChatWith: null
    }).catch(error => console.error('Error setting initial online status:', error));

    // Set initial visibility status immediately
    set(visibilityRef, {
      visible: true,
      timestamp: Date.now()
    }).catch(error => console.error('Error setting initial visibility status:', error));

    // Cleanup: manually set offline status on graceful unmount
    return () => {
      console.log('Component unmounting, setting offline status manually');
      // Manually set offline status
      set(userStatusRef, {
        state: 'offline',
        lastSeen: Date.now(),
        isTyping: false,
        typingInChatWith: null
      }).catch(error => console.error('Error setting offline status on unmount:', error));

      // Manually remove visibility
      set(visibilityRef, null).catch(error => console.error('Error removing visibility on unmount:', error));

       // It's good practice to also cancel onDisconnect for graceful exits
       // This prevents the onDisconnect from firing if the app closes right after manual update
       statusOnDisconnect.cancel().catch(error => console.error('Failed to cancel onDisconnect status:', error));
       visibilityOnDisconnect.cancel().catch(error => console.error('Failed to cancel onDisconnect visibility:', error));
    };

  }, [userId, effectiveAccountData?.id]); // Dependencies: userId and the other user's ID

  // Fetch recipient's push token when the chat is opened or recipient changes.
  // This effect runs whenever userId or effectiveAccountData.id changes.
  // It ensures we have the token regardless of the recipient's initial online status.
  useEffect(() => {
    const fetchRecipientToken = async () => {
      if (!userId || !effectiveAccountData?.id) {
         console.log('Fetch recipient token skipped: userId or effectiveAccountData.id missing.');
         setRecipientPushToken(null); // Ensure token state is cleared if IDs are missing
         return;
      }

      console.log('Attempting to fetch recipient push token for user ID:', effectiveAccountData.id);

      try {
        const response = await axios.get(`https://api.nityasha.com/api/v1/users/${effectiveAccountData.id}`);
        const recipientData = response.data;

        if (recipientData?.pushtoken) {
          console.log('Recipient push token fetched successfully.');
          setRecipientPushToken(recipientData.pushtoken);
        } else {
          console.log('Recipient does not have a push token.');
          setRecipientPushToken(null);
        }
      } catch (error) {
        console.error('Error fetching recipient push token:', error);
        setRecipientPushToken(null);
      }
    };

    // Fetch the token immediately when the component mounts or user/recipient changes
    fetchRecipientToken();

    // No specific cleanup needed for this fetch operation

  }, [userId, effectiveAccountData?.id]); // Dependencies: userId and the other user's ID

  // Effect to send the "chat opened" notification conditionally
  // This runs when userId, effectiveAccountData.id, isReceiverOnline, recipientPushToken, or username changes.
  useEffect(() => {
    const sendChatOpenedNotifIfOffline = () => {
      if (!userId || !effectiveAccountData?.id || !username || !recipientPushToken) {
        console.log('Send chat opened notification skipped: Missing userId, recipientId, username, or push token.');
        return;
      }

      // Only send this notification if the receiver is currently detected as offline
      console.log(`Checking if receiver is offline to send "chat opened" notification. isReceiverOnline: ${isReceiverOnline}`);
      if (!isReceiverOnline) {
        console.log('Receiver is offline. Sending chat opened notification...');
        // Add a small delay before sending the notification
        const notificationTimeout = setTimeout(() => {
           console.log('Sending chat opened notification after timeout.');
           sendChatOpenedNotification(recipientPushToken, username).catch(error => {
             console.error('Error sending chat opened notification:', error);
           });
        }, 1000); // 1 second delay

        // Return a cleanup function to clear the timeout
        return () => {
           console.log('Clearing chat opened notification timeout on cleanup.');
           clearTimeout(notificationTimeout);
        };
      } else {
         console.log('Receiver is online, not sending "chat opened" notification.');
      }

      // Return an empty cleanup function if no timeout was set
      return () => { /* no cleanup */ };
    };

    // Call the function and store the cleanup handler
    const cleanupHandler = sendChatOpenedNotifIfOffline();

    // Return the cleanup handler for the effect
    return cleanupHandler;

  }, [userId, effectiveAccountData?.id, isReceiverOnline, recipientPushToken, username]); // Dependencies for conditional sending

  // Handle app state changes for online status with debounce
  const appStateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentAppStateRef = useRef<string>(AppState.currentState);

  const handleAppStateChange = (nextAppState: string) => {
    console.log('App state changed to:', nextAppState);
    if (!userId || !effectiveAccountData?.id) {
       console.log('handleAppStateChange skipped: userId or effectiveAccountData.id missing.');
       return;
    }

    // Clear any pending timeout
    if (appStateTimeoutRef.current) {
      clearTimeout(appStateTimeoutRef.current);
       console.log('Cleared existing app state timeout.');
    }

    const userStatusRef = ref(db, `users/${userId}/status`);
    const visibilityRef = ref(db, `user_visibility/${userId}/visible_to/${effectiveAccountData.id}`);

    // Set a timeout to update status after a short delay
    appStateTimeoutRef.current = setTimeout(() => {
      if (nextAppState === 'active') {
        console.log('App is active after debounce, setting online status');
        // Set online status
        set(userStatusRef, {
          state: 'online',
          lastSeen: Date.now(),
          isTyping: false, // Reset typing on going online
          typingInChatWith: null // Reset typing chat on going online
        }).catch(error => console.error('Error setting online status on active:', error));

        // Set visibility
        set(visibilityRef, {
          visible: true,
          timestamp: Date.now()
        }).catch(error => console.error('Error setting visibility on active:', error));

      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        console.log('App is in background/inactive after debounce, setting offline status');
        // Add a delay before marking offline to prevent flicker
        setTimeout(() => {
          // Double-check the app is still in background
          if (AppState.currentState !== 'active') {
             console.log('App is still in background, setting offline status and removing visibility.');
            // Manually set offline status
            set(userStatusRef, {
              state: 'offline',
              lastSeen: Date.now(),
              isTyping: false,
              typingInChatWith: null
            }).catch(error => console.error('Error setting offline status on background:', error));

            // Manually remove visibility
            set(visibilityRef, null).catch(error => console.error('Error removing visibility on background:', error));
          } else {
             console.log('App state changed to active during offline delay, not setting offline status.');
          }
        }, 5000); // 5 second delay before marking offline
      }

      currentAppStateRef.current = nextAppState;
    }, 1000); // 1 second debounce
  };

  // Format last seen time
  const formatLastSeen = (timestamp: number): string => {
    const now = new Date();
    const lastSeenDate = new Date(timestamp);
    const diffMs = now.getTime() - lastSeenDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hr ago`;
    if (diffDays === 1) return 'yesterday';
    return `${diffDays} days ago`;
  };

  // Update user typing status in Firebase
  const updateTypingStatus = (isTyping: boolean) => {
    if (!userId || !effectiveAccountData?.id) {
       console.log('updateTypingStatus skipped: userId or effectiveAccountData.id missing.');
       return;
    }

    console.log('Updating typing status to:', isTyping);
    const userStatusRef = ref(db, `users/${userId}/status`);

    // Only update the typing-related fields, don't touch other status fields
    const updates = {
      isTyping,
      typingTimestamp: Date.now(),
      typingInChatWith: isTyping ? effectiveAccountData.id : null // Set the recipient ID when typing, null when not typing
    };

    // Update the typing status in Firebase
    update(userStatusRef, updates).catch(error => {
      console.error('Error updating typing status:', error);
    });
  };

  // Listen for messages from Firebase
  useEffect(() => {
    if (!userId || !effectiveAccountData?.id) {
       console.log('Firebase message listener setup skipped: userId or effectiveAccountData.id missing.');
       return;
    }

    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    const chatRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);

    setIsLoadingHistory(true);
    console.log('Setting up Firebase message listener for chat room:', chatRoomId);

    // Listen for changes to the chat messages
    const messagesListener = onValue(chatRef, async (snapshot) => {
      console.log('Firebase onValue listener triggered for chat messages.');
      try {
        const data = snapshot.val();
        let messageArray: ChatMessage[] = [];
        if (data) {
          // Convert object to array and sort by timestamp
           messageArray = await Promise.all(Object.values(data)
            .filter((msg: any) => !!msg && msg.encrypted === true)
            .map(async (msg: any) => {
              // Decrypt if encrypted and type is text (not media/contact)
              if (msg.encrypted && (!msg.type || msg.type === 'text')) {
                try {
                  const sharedSecret = await deriveSharedSecret(userId, effectiveAccountData.id);
                  const decryptedText = xorDecrypt(msg.text, sharedSecret);
                  return { ...msg, text: decryptedText, isUser: msg.senderId === userId };
                } catch (e) {
                  return { ...msg, text: '[Unable to decrypt]', isUser: msg.senderId === userId };
                }
              } else if (msg.encrypted && msg.type === 'image' && msg.imageUrl) {
                try {
                  const sharedSecret = await deriveSharedSecret(userId, effectiveAccountData.id);
                  const decryptedImageUrl = xorDecrypt(msg.imageUrl, sharedSecret);
                  return { ...msg, imageUrl: decryptedImageUrl, isUser: msg.senderId === userId };
                } catch (e) {
                  return { ...msg, imageUrl: '', text: '[Unable to decrypt image]', isUser: msg.senderId === userId };
                }
              } else {
                return { ...msg, isUser: msg.senderId === userId };
              }
            })
          );
          messageArray = messageArray.sort((a: ChatMessage, b: ChatMessage) => b.timestamp - a.timestamp);
          console.log(`Fetched ${messageArray.length} messages.`);

          // Count unread messages sent by the other user
          const unreadMessages = messageArray.filter(
            (msg: ChatMessage) => !msg.isUser && !msg.isRead
          );
          setUnreadCount(unreadMessages.length);
          console.log(`Unread messages from other user: ${unreadMessages.length}`);

          setMessages(messageArray); // Update the state with the new message list
        } else {
          setMessages([]);
          console.log('No chat data found.');
        }
        setLoadError(null);
      } catch (error) {
        console.error('Error processing chat data:', error);
        setLoadError('Failed to load chat history');
      } finally {
        setIsLoadingHistory(false);
         console.log('Finished processing chat data.');
      }
    }, (error) => {
      console.error('Firebase listener error:', error);
      setLoadError('Failed to connect to chat service');
      setIsLoadingHistory(false);
    });

    // Clean up listener on unmount
    return () => {
      console.log('Cleaning up Firebase message listener.');
      off(chatRef, 'value', messagesListener); // Specify event type and callback for clarity
    };
  }, [userId, effectiveAccountData?.id]); // Dependencies: userId and the other user's ID

    // New useEffect to handle marking newly incoming messages as read
  useEffect(() => {
      if (!userId || !effectiveAccountData?.id) {
         console.log('Messages state change effect skipped: userId or effectiveAccountData.id missing.');
         return;
      }

      const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');

      console.log('Messages state updated. Checking for new unread messages to mark.');

      // Find unread messages from the other user that haven't been marked as read yet
      const unreadOtherUserMessages = messages.filter(
          msg => !msg.isUser && !msg.isRead
      );

      console.log(`Found ${unreadOtherUserMessages.length} unread messages from other user in current state.`);

      // Iterate through these potential candidates
      unreadOtherUserMessages.forEach(message => {
           console.log(`Checking message ${message.id}: isUser=${message.isUser}, isRead=${message.isRead}`);

          // Add a check: only mark if it's within the last, say, 10 seconds of arrival
          // This prevents marking old unread messages every time the state updates for any reason.
          // This also helps focus on messages that arrived *while the chat was open*.
          // A larger threshold might be needed depending on network conditions.
          const messageAge = Date.now() - message.timestamp;
          const RECENT_MESSAGE_THRESHOLD = 10000; // 10 seconds

          if (messageAge <= RECENT_MESSAGE_THRESHOLD) {
               console.log(`Message ${message.id} is recent (${messageAge}ms) and unread. Attempting to mark as read.`);
               markMessageAsRead(chatRoomId, message.id);
          } else {
              console.log(`Message ${message.id} is unread but older than threshold (${messageAge}ms). Skipping real-time mark.`);
              // Messages older than the threshold will be marked on chat open/reopen by the initial logic
          }
      });

  }, [messages, userId, effectiveAccountData?.id]); // Dependency on messages state


  // *** New useEffect to scroll to bottom on initial load ***
  useEffect(() => {
    // Scroll to the bottom after messages are loaded and the list is ready
    if (!isLoadingHistory && messages.length > 0 && flatListRef.current) {
      console.log('Initial load complete and messages available, scrolling to bottom');
      // Use a small timeout to ensure the list has rendered
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false }); // Use animated: false for instant jump on load
      }, 0);
    }
  }, [isLoadingHistory, messages.length, flatListRef.current]); // Dependencies: messages count, loading state, and list ref

  // Listen for receiver's online status
  useEffect(() => {
    if (!effectiveAccountData?.id || !userId) {
       console.log('Receiver online status listener setup skipped: effectiveAccountData.id or userId missing.');
       return;
    }

    // Initialize with the value from navigation params if available
    if (online !== undefined) {
      console.log('Setting initial receiver online status from params:', online);
      setIsReceiverOnline(online);
    }

    // 1. Check if the user is visible to us specifically
    const visibilityRef = ref(db, `user_visibility/${effectiveAccountData.id}/visible_to/${userId}`);

    // 2. Also check the general status
    const userStatusRef = ref(db, `users/${effectiveAccountData.id}/status`);

    console.log('Setting up receiver online status listeners.');

    // Listen for visibility changes
    const visibilityListener = onValue(visibilityRef, (visibilitySnapshot) => {
      console.log('Receiver visibility listener triggered.');
      const visibilityData = visibilitySnapshot.val();
      const isVisibleToUs = visibilityData && visibilityData.visible === true;
      console.log('Is receiver visible to us?', isVisibleToUs);

      // If user is visible to us, check their actual status
      if (isVisibleToUs) {
        // Get the current status
        onValue(userStatusRef, (statusSnapshot) => {
          console.log('Receiver general status listener triggered.');
          const status = statusSnapshot.val() as UserStatus | null;
          if (status) {
            console.log('Received Firebase status:', status.state, 'last seen:', status.lastSeen, 'typing:', status.isTyping);
            const wasOnline = isReceiverOnline; // Capture previous online state
            const isNowOnline = status.state === 'online';

            setIsReceiverOnline(isNowOnline);
            setLastSeen(status.lastSeen);

            // Check if the user is typing and if they're typing to the current user
            if (status.isTyping === true && status.typingInChatWith === userId) {
              // Check if typing timestamp is recent (within last 10 seconds)
              const isRecentTyping = status.typingTimestamp &&
                (Date.now() - status.typingTimestamp < 10000); // 10 seconds threshold for typing

              setIsReceiverTyping(isRecentTyping || false);
               console.log('Receiver is typing to current user?', isRecentTyping);
            } else {
              setIsReceiverTyping(false);
               console.log('Receiver is not typing to current user.');
            }
          } else if (online === undefined) {
            // Only set to false if we didn't get an explicit online status from params
            console.log('No Firebase status data found, setting receiver status to default (offline).');
            setIsReceiverOnline(false);
            setIsReceiverTyping(false);
          } else {
             console.log('No Firebase status data found, but initial online status was provided. Keeping initial.');
          }
        }, { onlyOnce: true }); // Only check once when visibility changes
      } else {
        // If not visible to us, show as offline
        console.log('Receiver is not visible to us, setting receiver status to offline.');
        setIsReceiverOnline(false);
        setIsReceiverTyping(false);
      }
    }, (error) => {
        console.error('Firebase visibility listener error:', error);
    });

    // Listen for general status changes as well, in case visibility doesn't change but status does
    // This might be redundant with the visibility listener, but adds robustness
    const statusListener = onValue(userStatusRef, (statusSnapshot) => {
       console.log('Receiver general status listener triggered (secondary).');
       const status = statusSnapshot.val() as UserStatus | null;
       if (status) {
           console.log('Received Firebase status (secondary):', status.state, 'last seen:', status.lastSeen, 'typing:', status.isTyping);
            const isNowOnline = status.state === 'online';
            setIsReceiverOnline(isNowOnline);
            setLastSeen(status.lastSeen);

             if (status.isTyping === true && status.typingInChatWith === userId) {
              const isRecentTyping = status.typingTimestamp &&
                (Date.now() - status.typingTimestamp < 10000);

              setIsReceiverTyping(isRecentTyping || false);
               console.log('Receiver is typing to current user? (secondary)', isRecentTyping);
            } else {
              setIsReceiverTyping(false);
              console.log('Receiver is not typing to current user (secondary).');
            }
       } else if (online === undefined) {
           console.log('No Firebase status data found (secondary), setting receiver status to default (offline).');
           setIsReceiverOnline(false);
           setIsReceiverTyping(false);
       } else {
            console.log('No Firebase status data found (secondary), but initial online status was provided. Keeping initial.');
       }
    }, (error) => {
         console.error('Firebase general status listener error:', error);
    });


    return () => {
      console.log('Cleaning up receiver online status listeners.');
      // Clean up listeners
      off(visibilityRef, 'value', visibilityListener);
       off(userStatusRef, 'value', statusListener);
    };
  }, [effectiveAccountData?.id, userId, online]); // Dependencies: effectiveAccountData.id, userId, and initial 'online' prop

  const fetchMessages = useCallback(() => {
    console.log('fetchMessages called. Firebase handles real-time updates, just showing loading.');
    // No need to manually fetch with Firebase, it's real-time
    // Just reset the loading state to show the refresh indicator
    setIsLoadingHistory(true);
    setTimeout(() => setIsLoadingHistory(false), 500);
  }, []);

  // Function to clear chat history
  const clearChatHistory = useCallback(() => {
    if (!userId || !effectiveAccountData?.id) {
      Alert.alert('Error', 'Cannot clear chat history at this time');
      console.log('clearChatHistory skipped: userId or effectiveAccountData.id missing.');
      return;
    }

    console.log('Clearing chat history.');

    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');

    // Set loading state
    setIsLoadingHistory(true);

    try {
      // Clear messages in user_chats collection
      const chatRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);
      set(chatRef, null).then(() => {
         console.log(`Cleared ${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);
      }).catch(error => console.error(`Error clearing ${FIREBASE_CHAT_PREFIX}/${chatRoomId}:`, error));

      // Clear messages in user_messages collection
      const messagesRef = ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}`);
      set(messagesRef, null).then(() => {
         console.log(`Cleared ${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}`);
      }).catch(error => console.error(`Error clearing ${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}:`, error));

      // Clear local messages state
      setMessages([]);
      console.log('Cleared local messages state.');

      // Reset unread count
      setUnreadCount(0);
      console.log('Reset unread count.');

      // Update unread count in user_chats for both users
      const userChatRef = ref(db, `user_chats/${userId}/${effectiveAccountData.id}`);
      set(userChatRef, { unread: 0 }).then(() => {
         console.log(`Reset unread count in user_chats for user ${userId}`);
      }).catch(error => console.error(`Error resetting unread count for user ${userId}:`, error));

      const receiverChatRef = ref(db, `user_chats/${effectiveAccountData.id}/${userId}`);
      set(receiverChatRef, { unread: 0 }).then(() => {
         console.log(`Reset unread count in user_chats for receiver ${effectiveAccountData.id}`);
      }).catch(error => console.error(`Error resetting unread count for receiver ${effectiveAccountData.id}:`, error));


      Alert.alert('Success', 'Chat history has been cleared');
    } catch (error) {
      console.error('Error clearing chat history:', error);
      Alert.alert('Error', 'Failed to clear chat history. Please try again.');
    } finally {
      setIsLoadingHistory(false);
       console.log('Finished clearChatHistory.');
    }
  }, [userId, effectiveAccountData?.id]);

  const renderContent = () => {
    if (!requireBothOnline && !userId) {
      // Wait for userId to load
      return (
        <View style={tw`flex-1 items-center justify-center`}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={tw`text-gray-500 text-center mt-4`}>Loading chat...</Text>
        </View>
      );
    }
    if (requireBothOnline && !isReceiverOnline) {
      // Show offline screen if restriction is ON
      return (
        <View style={tw`flex-1 items-center justify-center p-6`}>
          <MessageCircleOff size={48} color="#9E9E9E" style={tw`mb-4`} />
          <Text style={tw`text-gray-600 text-center text-lg font-semibold mb-2`}>
            {effectiveAccountData?.name || 'User'} is offline
          </Text>
          <Text style={tw`text-gray-500 text-center mb-4`}>
            {lastSeen ? `Last seen ${formatLastSeen(lastSeen)}` : 'Chat history is not available when user is offline'}
          </Text>
          <Text style={tw`text-gray-500 text-center`}>
            You can still send messages. They will be delivered when {effectiveAccountData?.name || 'User'} comes online.
          </Text>
        </View>
      );
    }
    if (!userId || !effectiveAccountData?.id) {
      console.log('renderContent: userId or effectiveAccountData.id missing, showing loading.');
      return (
        <View style={tw`flex-1 items-center justify-center`}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={tw`text-gray-500 text-center mt-4`}>Loading chat...</Text>
        </View>
      );
    }

    return (
      <>
        {isLoadingHistory ? (
          <View style={tw`flex-1 items-center justify-center`}>
            <ActivityIndicator size="large" color="#0000ff" />
            <Text style={tw`text-gray-500 text-center mt-4`}>Loading messages...</Text>
          </View>
        ) : loadError ? (
          <View style={tw`flex-1 items-center justify-center p-4`}>
            <Text style={tw`text-red-500 text-center`}>{loadError}</Text>
            <TouchableOpacity
              style={tw`mt-4 bg-blue-500 px-4 py-2 rounded-full`}
              onPress={fetchMessages}
            >
              <Text style={tw`text-white`}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <LegendList
              ref={flatListRef}
              data={[...messages].reverse()}
              renderItem={renderMessage}
              keyExtractor={(item: any) => String(item.id)}
              contentContainerStyle={{
                padding: 15,
               
              }}
              showsVerticalScrollIndicator={false}
              bounces={true}
              onContentSizeChange={() => {
                if (flatListRef.current && messages.length > 0) {
                  setTimeout(() => {
                    flatListRef.current?.scrollToEnd({ animated: true });
                  }, 50);
                }
              }}
              onLayout={() => {
                if (flatListRef.current && messages.length > 0) {
                  setTimeout(() => {
                    flatListRef.current?.scrollToEnd({ animated: false });
                  }, 50);
                }
              }}
            />
            <TypingIndicator />
          </>
        )}
      </>
    );
  };

  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      console.log('scrollToBottom called.');
      // Use multiple attempts to ensure scrolling works
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 50);
      
      // Backup scroll attempt
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 150);
    } else {
       console.log('scrollToBottom called but list ref or messages are not ready.');
    }
  };

  // Handle viewable items changed
  const onViewableItemsChanged = useCallback(({ viewableItems }: { viewableItems: any[] }) => {
    console.log('onViewableItemsChanged triggered. Number of viewable items:', viewableItems.length);
    if (!userId || !effectiveAccountData?.id) {
        console.log('onViewableItemsChanged: userId or effectiveAccountData.id is missing.');
        return;
    }

    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    // The viewableIds ref is not strictly needed for the new logic, but keeping it.
    // viewableItemsRef.current = viewableIds;

    // Iterate through viewable items and mark unread messages from the other user as read
    viewableItems.forEach(viewableItem => {
      const message = viewableItem.item;
      // Only mark as read if it's from the other user and not already read
      // Check if message object and its properties exist before accessing
      if (message && !message.isUser && !message.isRead) {
        console.log(`Message ${message.id} became viewable and is unread. Attempting to mark as read from onViewableItemsChanged.`);
        markMessageAsRead(chatRoomId, message.id); // Call the modified markMessageAsRead
      } else if (message) {
          console.log(`Message ${message.id} in viewable items is not from other user or is already read. Skipping.`);
      } else {
          console.log('Undefined message item in viewableItems.');
      }
    });

    // No need to check online status here, markMessageAsRead handles the update directly
  }, [userId, effectiveAccountData?.id]); // Dependencies: userId and the other user's ID

  // Public method to mark messages as read by user ID
  // This is called from UserInbox component
  const markMessagesAsReadByUserId = (otherUserId: string) => {
    if (!userId) {
        console.log('markMessagesAsReadByUserId: userId is missing.');
        return;
    }

    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, otherUserId].sort().sort().join('_'); // Fixed sort() applied twice
    console.log('markMessagesAsReadByUserId called for other user:', otherUserId, 'chat room:', chatRoomId);
    markMessagesAsRead(chatRoomId);
  };

  // Make the function available via a ref that can be accessed by navigation params
  useEffect(() => {
    // Expose the markMessagesAsReadByUserId function to the navigation params
    console.log('Exposing markMessagesAsReadByUserId via navigation params.');
    navigation.setParams({
      markMessagesAsReadByUserId
    });
  }, [navigation, userId]);


  // Function to update message read status in Firebase
  const updateMessageReadStatus = (chatRoomId: string, messageId: string) => {
    console.log('updateMessageReadStatus called for message:', messageId, 'in chat room:', chatRoomId);
    // Update in both collections to ensure consistency
    const chatMessageRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`);
    const userMessageRef = ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`);

    // Use get to fetch current message data once from chat collection
    get(chatMessageRef).then((snapshot) => {
      const messageData = snapshot.val();
      if (messageData && !messageData.isRead) {
        console.log(`Message ${messageId} in chat collection is not read. Updating status.`);
        // Update with read status
        set(chatMessageRef, {
          ...messageData,
          isRead: true,
          readAt: Date.now(),
          status: 'read' // Ensure status is also marked as 'read'
        }).then(() => {
            console.log(`Successfully marked message ${messageId} as read in chat collection.`);
        }).catch(error => {
            console.error(`Error marking message ${messageId} as read in chat collection:`, error);
        });
      } else {
         console.log(`Message ${messageId} already read or not found in chat collection. No update needed in chat collection.`);
      }
    }).catch(error => {
        console.error(`Error fetching message ${messageId} from chat collection:`, error);
    });

    // Use get to fetch current message data once from user_messages collection
    get(userMessageRef).then((snapshot) => {
      const messageData = snapshot.val();
      // Check if status is not 'read' or isRead is false for user_messages
      if (messageData && (messageData.status !== 'read' || !messageData.isRead)) {
         console.log(`Message ${messageId} in user_messages collection needs status update. Updating status.`);
        // Update with read status
        set(userMessageRef, {
          ...messageData,
          isRead: true,
          readAt: Date.now(),
          status: 'read' // Ensure status is also marked as 'read'
        }).then(() => {
            console.log(`Successfully marked message ${messageId} as read in user_messages collection.`);
        }).catch(error => {
            console.error(`Error marking message ${messageId} as read in user_messages collection:`, error);
        });
      } else {
         console.log(`Message ${messageId} in user_messages collection is already read or not found. No update needed in user_messages collection.`);
      }
    }).catch(error => {
        console.error(`Error fetching message ${messageId} from user_messages collection:`, error);
    });
  };

  // Mark a single message as read
  const markMessageAsRead = (chatRoomId: string, messageId: string) => {
    console.log('markMessageAsRead called for message:', messageId, 'in chat room:', chatRoomId);
    // Always attempt to update the message status regardless of receiver's online status
    console.log('Attempting to mark message as read immediately:', messageId);
    updateMessageReadStatus(chatRoomId, messageId);
    // No need to queue if we mark immediately
  };

  // Mark all messages as read by chat room ID
  const markMessagesAsRead = async (chatRoomId: string) => {
    console.log('markMessagesAsRead (on chat open) called for chat room:', chatRoomId);
    if (!userId || !effectiveAccountData?.id) {
        console.log('Cannot mark messages as read: userId or effectiveAccountData.id is missing.');
        return;
    }

    try {
      // Always update unread count in user_chats for the current user
      // This is just UI state and doesn't affect the actual read status
      if (userId) {
        const userChatRef = ref(db, `user_chats/${userId}/${effectiveAccountData.id}`);
        set(userChatRef, { unread: 0 }).then(() => {
            console.log(`Successfully reset unread count for user ${userId} in chat with ${effectiveAccountData.id}.`);
        }).catch(error => {
            console.error('Error resetting unread count for current user:', error);
        });
      }

      // Get all messages in the chat room from user_chats
      const chatRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);
      get(chatRef).then((snapshot) => { // Use get here as well for initial read
        const data = snapshot.val();
        if (!data) {
            console.log('No chat data found to mark as read on chat open.');
            return;
        }

        console.log('Checking messages to mark as read on chat open.');
        // Find unread messages sent by the other user and mark them as read
        Object.entries(data).forEach(([key, value]: [string, any]) => {
          // Mark as read if it's from the other user AND not already read
          if (value.senderId === effectiveAccountData?.id && (!value.isRead || value.status !== 'read')) {
            console.log(`Message ${key} from other user detected as unread on chat open. Marking as read.`);
            // Call the update function directly
            updateMessageReadStatus(chatRoomId, key);
          } else {
             console.log(`Message ${key} is not from other user or is already read. Skipping on chat open.`);
          }
        });
      }).catch(error => {
        console.error('Error fetching chat data to mark as read on chat open:', error);
      });

    } catch (error) {
      console.error('Error in markMessagesAsRead (on chat open):', error);
    }
  };

  // Function to send a push notification when the chat is opened
  const sendChatOpenedNotification = async (pushToken: string, senderName: string) => {
    if (!pushToken) {
        console.log('sendChatOpenedNotification skipped: pushToken is missing.');
        return;
    }

    console.log(`Attempting to send chat opened notification to push token: ${pushToken}`);

    try {
      const notificationMessage = {
        to: pushToken,
        sound: 'default',
        title: `Arre suno, ${username} abhi online hai, chat karna hai kya? 👀`,
        body: 'Tap to start chatting or reply directly from notification',
        data: { 
          screen: 'UserChatInbox', 
          otherUserId: effectiveAccountData?.id || '', // Use receiver's ID, not sender's ID
          notificationType: 'chat_opened',
          senderName: username
        },
        // For interactive notifications with Expo
        _displayInForeground: true,
        // Android specific settings
        android: {
          channelId: 'chat-messages',
          priority: 'high',
          sound: 'default',
          // Enable interactive actions for Android
          categoryId: 'chat_message'
        },
        // iOS specific settings
        ios: {
          categoryId: 'chat_message'
        }
      };

      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationMessage),
      });

      const responseData = await response.json();
      console.log('Chat opened push notification sent response:', responseData);

      if (response.ok && responseData?.data?.status === 'ok') {
         console.log('Chat opened push notification sent successfully');
      } else {
         console.error('Error sending chat opened push notification. Response:', responseData);
      }

    } catch (error) {
      console.error('Error sending chat opened push notification:', error);
    }
  };

  // Function to send a push notification when a message is sent
  const sendNewMessageNotification = async (pushToken: string, senderName: string, messageText: string) => {
    if (!pushToken) {
       console.log('sendNewMessageNotification skipped: pushToken is missing.');
       return;
    }

     console.log(`Attempting to send new message notification to push token: ${pushToken}`);

    try {
      const notificationMessage = {
        to: pushToken,
        sound: 'default',
        title: `New message from ${username}`,
        body: messageText.length > 50 ? `${messageText.substring(0, 50)}...` : messageText, // Truncate long messages
        data: { 
          screen: 'UserChatInbox', 
          otherUserId: effectiveAccountData?.id || '', // Use receiver's ID with null check
          notificationType: 'new_message',
          senderName: username,
          messageText: messageText
        },
        // For interactive notifications with Expo
        _displayInForeground: true,
        // Android specific settings
        android: {
          channelId: 'chat-messages',
          priority: 'high',
          sound: 'default',
          // Enable interactive actions for Android
          categoryId: 'chat_message'
        },
        // iOS specific settings
        ios: {
          categoryId: 'chat_message'
        }
      };

      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationMessage),
      });

      const responseData = await response.json();
      console.log('New message push notification sent response:', responseData);

       if (response.ok && responseData?.data?.status === 'ok') {
         console.log('New message push notification sent successfully');
      } else {
         console.error('Error sending new message push notification. Response:', responseData);
      }

    } catch (error) {
      console.error('Error sending new message push notification:', error);
    }
  };

  const sendMessage = async () => {
    if (message.trim() === '') {
       console.log('sendMessage skipped: message is empty.');
       return;
    }
    if (!userId || !effectiveAccountData?.id) {
      console.log('sendMessage: userId or effectiveAccountData.id missing. Showing error alert.');
      Alert.alert('Error', 'Cannot send message at this time');
      return;
    }

    console.log('Attempting to send message:', message.trim());

    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    const messageId = generateUUID();

    // Encrypt message text
    const sharedSecret = await deriveSharedSecret(userId, effectiveAccountData.id);
    const encryptedText = xorEncrypt(message.trim(), sharedSecret);

    const newMsg: ChatMessage = {
      id: messageId,
      text: encryptedText, // Store encrypted text
      senderId: userId,
      receiverId: effectiveAccountData.id,
      timestamp: Date.now(),
      status: 'sending',
      isRead: false,
      encrypted: true, // Mark as encrypted
      ...(replyToMessage && {
        replyTo: {
          id: replyToMessage.id,
          text: replyToMessage.text,
          senderId: replyToMessage.senderId
        }
      })
    };

    setMessages(prev => [...prev, {...newMsg, isUser: true}]);
    setMessage('');
    setReplyToMessage(null);
    setReplyToText('');
    setReplyToSenderId('');
    flatListRef.current?.scrollToEnd({ animated: true });
    console.log('Added message to local state and scrolled to end.');

    try {
      updateTypingStatus(false);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
        console.log('Cleared typing timeout on message send.');
      }

      const firebaseMsg: FirebaseMessage = {
         id: newMsg.id,
         text: newMsg.text, // Encrypted text
         senderId: newMsg.senderId,
         receiverId: newMsg.receiverId,
         timestamp: newMsg.timestamp,
         status: 'sent',
         isRead: newMsg.isRead,
         encrypted: true, // Mark as encrypted
         ...(newMsg.replyTo && {
           replyTo: {
             id: newMsg.replyTo.id,
             text: newMsg.replyTo.text,
             senderId: newMsg.replyTo.senderId
           }
         })
      };
      console.log('Attempting primary Firebase writes for message:', messageId);
      await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), firebaseMsg);
      await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), firebaseMsg);
      console.log('Primary Firebase writes successful for message:', messageId);

      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId ? { ...msg, status: 'sent' } : msg
        )
      );
      console.log('Updated local message status to "sent".');

      try {
        const rtdbReceiverUnreadRef = ref(db, `user_chats/${effectiveAccountData.id}/${userId}`);
        console.log('Attempting to update receiver unread count via transaction.');
        runTransaction(rtdbReceiverUnreadRef, (currentChatData) => {
          if (currentChatData) {
            const currentUnread = currentChatData.unread || 0;
            currentChatData.unread = currentUnread + 1;
          } else {
            currentChatData = { unread: 1 };
          }
          console.log('Transaction: New unread count will be', currentChatData.unread);
          return currentChatData;
        }).then((result) => {
          if (result.committed) {
            console.log('Unread count incremented successfully via transaction.');
          } else {
            console.log('Transaction aborted (e.g., due to concurrent modification).');
          }
        }).catch((error) => {
          console.error('Firebase transaction failed:', error);
        });

        console.log('Finished unread count transaction. Proceeding to check notification conditions.');

        if (!isReceiverOnline && recipientPushToken && userId) {
          console.log('Receiver is offline, recipientPushToken exists. Attempting to send new message notification.');
          const senderName = username || 'You';
          sendNewMessageNotification(recipientPushToken, senderName, message.trim()).catch(error => {
             console.error('Error sending push notification:', error);
          });
        } else {
          console.log('New message notification skipped: Conditions not met.');
        }

      } catch (subsequentError) {
        console.error('An error occurred in subsequent send operations:', subsequentError);
      }

    } catch (primaryWriteError) {
      console.error('Primary message write failed:', primaryWriteError);
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId ? { ...msg, status: 'error' } : msg
        )
      );
      console.log('Showing error alert for failed message send.');
      Alert.alert(
        'Error',
        'Failed to send message. Please check your connection and try again.'
      );
      setMessage(message.trim());
    }
  };

  const renderMessage = ({ item }: { item: any }) => {
    if (item.type === 'image' && item.imageUrl) {
      return (
        <TouchableOpacity
          onPress={() => navigation.navigate('FullScreenimage', { imageUrl: item.imageUrl })}
          onLongPress={() => {
            Alert.alert(
              'Actions',
              '',
              [
                {
                  text: 'Share Image',
                  onPress: () => shareImage(item.imageUrl),
                },
                {
                  text: 'Reply',
                  onPress: () => handleReplyToMessage(item),
                },
                {
                  text: 'Cancel',
                  style: 'cancel',
                },
              ],
              { cancelable: true }
            );
          }}
          style={[
            styles.messageContainer,
            item.isUser ? styles.userMessage : styles.otherMessage,
            { padding: 0, backgroundColor: 'transparent' },
          ]}
        >
          <Image
            source={{ uri: item.imageUrl }}
            style={{ width: 180, height: 180, borderRadius: 10 }}
          />
        </TouchableOpacity>
      );
    }
    if (item.type === 'audio' && item.mediaUrl) {
      return (
        <TouchableOpacity
          style={[
            styles.messageContainer,
            item.isUser ? styles.userMessage : styles.otherMessage,
            { flexDirection: 'row', alignItems: 'center', padding: 10 },
          ]}
          onPress={() => item.mediaUrl ? playAudio(item.mediaUrl, item.id) : undefined}
          onLongPress={() => handleReplyToMessage(item)}
        >
          <Feather name={playingAudioId === item.id ? 'pause' : 'mic'} size={24} color="#4CAF50" />
          <Text style={{ marginLeft: 10 }}>{playingAudioId === item.id ? 'Playing...' : 'Play Audio'}</Text>
        </TouchableOpacity>
      );
    }
    if (item.type === 'file' && item.mediaUrl) {
      return (
        <TouchableOpacity
          style={[
            styles.messageContainer,
            item.isUser ? styles.userMessage : styles.otherMessage,
            { flexDirection: 'row', alignItems: 'center', padding: 10 },
          ]}
          onPress={() => item.mediaUrl && Linking.openURL(item.mediaUrl)}
          onLongPress={() => handleReplyToMessage(item)}
        >
          <Feather name="file" size={24} color="#4CAF50" />
          <Text style={{ marginLeft: 10, color: '#007AFF', textDecorationLine: 'underline' }} numberOfLines={1}>
            {item.text || 'View File'}
          </Text>
        </TouchableOpacity>
      );
    }
    if (item.contact) {
      return (
        <TouchableOpacity
          onPress={() => handleReplyToMessage(item)}
          style={[
            styles.messageContainer,
            item.isUser ? styles.userMessage : styles.otherMessage,
            { backgroundColor: '#E7F3E2', borderColor: '#075E54', borderWidth: 1, borderRadius: 16, padding: 14, marginVertical: 6 },
          ]}
        >
          <Text style={{ fontSize: 15, marginTop: 4 }}>{item.contact.name}</Text>
          <Text style={{ fontSize: 14, color: '#666' }}>{item.contact.phoneNumber}</Text>
          <Text style={{ fontSize: 13, color: '#888' }}>User ID: {item.contact.id}</Text>
          <View style={{ flexDirection: 'row', marginTop: 10 }}>
            <TouchableOpacity
              style={{
                backgroundColor: '#075E54',
                borderRadius: 8,
                paddingVertical: 6,
                paddingHorizontal: 16,
                marginRight: 10,
              }}
              onPress={async () => {
                const { status } = await Contacts.requestPermissionsAsync();
                if (status === 'granted') {
                  const contact = {
                    [Contacts.Fields.FirstName]: item.contact.name,
                    [Contacts.Fields.PhoneNumbers]: [
                      {
                        label: 'mobile',
                        number: item.contact.phoneNumber,
                      },
                    ],
                    note: `Nityasha User ID: ${item.contact.id}`,
                    contactType: Contacts.ContactTypes.Person,
                    name: item.contact.name,
                  };
                  try {
                    await Contacts.addContactAsync(contact);
                    Alert.alert('Contact Saved', 'This contact has been saved to your device!');
                  } catch (e) {
                    Alert.alert('Error', 'Could not save contact.');
                  }
                } else {
                  Alert.alert('Permission Denied', 'Cannot save contact without permission.');
                }
              }}
            >
              <Text style={{ color: '#fff', fontWeight: 'bold' }}>Save Contact</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                backgroundColor: '#4CAF50',
                borderRadius: 8,
                paddingVertical: 6,
                paddingHorizontal: 16,
              }}
              onPress={() => {
                navigation.navigate('UserChatInbox', {
                  otherUserId: item.contact.id,
                  name: item.contact.name,
                });
              }}
            >
              <Text style={{ color: '#fff', fontWeight: 'bold' }}>Message</Text>
            </TouchableOpacity>
            
          </View>
        </TouchableOpacity>
      );
    }
    // Default: text message bubble
    return (
     <TouchableOpacity
  onPress={() => handleReplyToMessage(item)}
  activeOpacity={0.8}
>
  <View
    style={[
      tw`max-w-[80%] p-3 rounded-2xl my-1`,
      item.isUser ? tw`bg-[#DCF8C6] self-end ml-[20%]` : tw`bg-white self-start mr-[20%]`,
      item.status === 'error' ? tw`bg-red-50 border border-red-500` : null,
      item.status === 'sending' ? tw`bg-gray-200` : null,
    ]}
  >
    {/* Reply Preview */}
    {item.replyTo && (
      <View style={tw`mb-2 px-3 py-2 bg-gray-100 rounded-xl border border-gray-300`}>
        <Text style={tw`text-xs font-semibold text-green-600`}>
          {item.replyTo.senderId === userId ? 'You' : effectiveAccountData?.name || 'User'}
        </Text>
        <Text
          style={tw`text-xs text-gray-700 italic mt-0.5`}
          numberOfLines={2}
        >
          {item.replyTo.text}
        </Text>
      </View>
    )}

    {/* Main Message Content */}
    <View style={tw`flex-row items-end justify-between`}>
      <Text style={[tw`text-base text-gray-800 flex-shrink`, item.status === 'error' ? tw`text-red-500` : null]}>
        {item.text}
      </Text>

      {item.isUser && (
        <View style={tw`flex-row items-center ml-2`}>
          {item.status === 'error' ? (
            <Feather name="alert-circle" size={14} color="#FF5252" style={tw`ml-1`} />
          ) : item.status === 'sending' ? (
            <ActivityIndicator size={14} color="#9E9E9E" style={tw`ml-1`} />
          ) : item.isRead ? (
            <Feather name="check-circle" size={14} color="#4CAF50" style={tw`ml-1`} />
          ) : item.status === 'sent' || item.status === 'delivered' ? (
            <Feather name="check" size={14} color="#9E9E9E" style={tw`ml-1`} />
          ) : null}
        </View>
      )}
    </View>
  </View>
</TouchableOpacity>
    );
  };

  // No WebSocket ping/pong needed with HTTP-only approach

  // Close menu when clicking outside
  const handleOutsidePress = () => {
    if (showMenu) {
      console.log('handleOutsidePress: Closing menu.');
      setShowMenu(false);
    }
  };

  // Typing indicator component with animation
  const TypingIndicator = () => {
    if (!isReceiverTyping || !isReceiverOnline) return null;

    return (
      <View style={tw`w-12 flex-row items-center justify-center ml-5`}>
        <View style={tw`flex-row bg-white rounded-2xl px-3 py-2 h-10 items-center justify-center`}>
          <MotiView
            style={tw`w-2 h-2 rounded-full bg-green-500 mx-0.5`}
            from={{ opacity: 0.4, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'timing', duration: 600, loop: true }}
          />
          <MotiView
            style={tw`w-2 h-2 rounded-full bg-green-500 mx-0.5`}
            from={{ opacity: 0.4, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'timing', duration: 600, loop: true, delay: 200 }}
          />
          <MotiView
            style={tw`w-2 h-2 rounded-full bg-green-500 mx-0.5`}
            from={{ opacity: 0.4, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'timing', duration: 600, loop: true, delay: 400 }}
          />
        </View>
      </View>
    );
  };

  const pickImage = async () => {
    setIsImageModalVisible(false);
    if (!userId || !effectiveAccountData?.id) {
      Alert.alert('Error', 'Cannot pick image. User or account data missing.');
      return;
    }
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (permissionResult.granted === false) {
      Alert.alert('Permission Required', 'Permission to access media library is required to send images.');
      return;
    }
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: false,
      quality: 1,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      const selectedImageUri = result.assets[0].uri;
      await uploadImageAndSend(selectedImageUri);
    }
  };

  const uploadImageAndSend = async (imageUri: string) => {
    if (!userId || !effectiveAccountData?.id) {
      Alert.alert('Error', 'Cannot send image at this time.');
      return;
    }
    if (!imageUri) return;
    const apiUrl = 'https://api.php.nityasha.com/uploadfilesserver/index.php';
    const formData = new FormData();
    const uriParts = imageUri.split('/');
    const fileName = uriParts[uriParts.length - 1];
    const fileType = fileName.split('.').pop() === 'jpg' ? 'image/jpeg' : 'image/png';
    formData.append('image', {
      uri: imageUri,
      name: fileName,
      type: fileType,
    } as any);
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Image upload failed: ${errorText}`);
      }
      const responseData = await response.json();
      const uploadedImageUrl = responseData.url || responseData.imageUrl;
      if (uploadedImageUrl) {
        await sendImageMessage(uploadedImageUrl);
      } else {
        Alert.alert('Error', 'Image upload successful, but could not get image URL from the server response.');
      }
    } catch (err) {
      Alert.alert('Error', `Failed to upload image: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  const sendImageMessage = async (uploadedImageUrl: string) => {
    if (!userId || !effectiveAccountData?.id || !uploadedImageUrl) {
      Alert.alert('Error', 'Cannot send image message.');
      return;
    }
    const messageId = generateUUID();
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    // Encrypt the imageUrl
    const sharedSecret = await deriveSharedSecret(userId, effectiveAccountData.id);
    const encryptedImageUrl = xorEncrypt(uploadedImageUrl, sharedSecret);
    const newMsg: ChatMessage = {
      id: messageId,
      text: '',
      senderId: userId,
      receiverId: effectiveAccountData.id,
      timestamp: Date.now(),
      status: 'sent',
      isRead: false,
      type: 'image',
      imageUrl: encryptedImageUrl, // Store encrypted imageUrl
      encrypted: true,
    };
    // Optimistically add message to UI
    setMessages(prev => [{ ...newMsg, isUser: true }, ...prev]);
    flatListRef.current?.scrollToEnd({ animated: true });
    // Save message to Firebase
    await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
    await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), newMsg);

    // Send push notification if user is offline
    if (!isReceiverOnline && recipientPushToken && userId) {
      console.log('Receiver is offline, sending image notification.');
      const senderName = username || 'You';
      sendNewMessageNotification(recipientPushToken, senderName, '📷 Sent an image').catch(error => {
          console.error('Error sending image push notification:', error);
      });
    }
  };

  const startRecording = async () => {
    try {
      if (recordingRef.current) {
        await recordingRef.current.stopAndUnloadAsync();
        recordingRef.current = null;
      }
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        playThroughEarpieceAndroid: false,
        // interruptionModeIOS and interruptionModeAndroid are not present in expo-av
      });
      const { recording } = await Audio.Recording.createAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
      recordingRef.current = recording;
      setIsRecording(true);
      setRecordingDuration(0);
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration((prev) => prev + 1);
      }, 1000);
    } catch (err) {
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    try {
      if (!recordingRef.current) return;
      setIsRecording(false);
      if (recordingTimerRef.current) clearInterval(recordingTimerRef.current);
      await recordingRef.current.stopAndUnloadAsync();
      const uri = recordingRef.current.getURI();
      if (uri) await uploadAndSendAudio(uri);
      recordingRef.current = null;
    } catch (err) {
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const uploadAndSendAudio = async (uri: string) => {
    try {
      const response = await fetch(uri);
      if (!response.ok) throw new Error('Failed to fetch audio file');
      const blob = await response.blob();
      const fileName = `audio_${Date.now()}.m4a`;
      const filePath = `chat-audio/${fileName}`;
      const { data, error } = await supabase.storage.from('chat-media').upload(filePath, blob, {
        contentType: 'audio/m4a',
        cacheControl: '3600',
      });
      if (error) throw error;
      const { data: publicUrlData } = supabase.storage.from('chat-media').getPublicUrl(filePath);
      if (!publicUrlData.publicUrl) throw new Error('Failed to get public URL');
      await sendAudioMessage(publicUrlData.publicUrl);
    } catch (error) {
      Alert.alert('Error', 'Failed to send audio message. Please try again.');
    }
  };

  const sendAudioMessage = async (audioUrl: string) => {
    if (!userId || !effectiveAccountData?.id || !audioUrl) {
      Alert.alert('Error', 'Cannot send audio message.');
      return;
    }
    const messageId = generateUUID();
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    const newMsg: ChatMessage = {
      id: messageId,
      text: '',
      senderId: userId,
      receiverId: effectiveAccountData.id,
      timestamp: Date.now(),
      status: 'sent',
      isRead: false,
      type: 'audio',
      mediaUrl: audioUrl,
    };
    setMessages(prev => [{ ...newMsg, isUser: true }, ...prev]);
    flatListRef.current?.scrollToEnd({ animated: true });
    await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
    await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), newMsg);

    // Send push notification if user is offline
    if (!isReceiverOnline && recipientPushToken && userId) {
      console.log('Receiver is offline, sending audio notification.');
      const senderName = username || 'You';
      sendNewMessageNotification(recipientPushToken, senderName, '🎵 Sent a voice message').catch(error => {
          console.error('Error sending audio push notification:', error);
      });
    }
  };

  const playAudio = async (audioUrl: string | undefined, messageId: string) => {
    if (!audioUrl) return;
    try {
      if (audioSound) {
        await audioSound.unloadAsync();
        setAudioSound(null);
        setPlayingAudioId(null);
      }
      const { sound } = await Audio.Sound.createAsync({ uri: audioUrl });
      setAudioSound(sound);
      setPlayingAudioId(messageId);
      await sound.playAsync();
      sound.setOnPlaybackStatusUpdate((status) => {
        if (!status.isLoaded || status.didJustFinish) {
          setPlayingAudioId(null);
          sound.unloadAsync();
        }
      });
    } catch (err) {
      Alert.alert('Error', 'Failed to play audio');
    }

    // Send push notification if user is offline
    if (!isReceiverOnline && recipientPushToken && userId) {
      console.log('Receiver is offline, sending file notification.');
      const senderName = username || 'You';
      sendNewMessageNotification(recipientPushToken, senderName, `📄 Sent a file`).catch(error => {
          console.error('Error sending file push notification:', error);
      });
    }
  };

  // Add file and camera handlers
  const pickFile = async () => {
    setIsImageModalVisible(false);
    if (!userId || !effectiveAccountData?.id) {
      Alert.alert('Error', 'Cannot pick file. User or account data missing.');
      return;
    }
    try {
      // Note: getDocumentAsync is not available in expo-image-picker
      // You can use launchImageLibraryAsync with document types instead
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: false,
        quality: 1,
      });
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedFile = result.assets[0];
        await uploadAndSendFile(selectedFile.uri, selectedFile.fileName || 'file');
      }
    } catch (err) {
      Alert.alert('Error', 'Failed to pick file.');
    }
  };

  const uploadAndSendFile = async (uri: string, fileName: string) => {
    try {
      const response = await fetch(uri);
      if (!response.ok) throw new Error('Failed to fetch file');
      const blob = await response.blob();
      const filePath = `chat-files/${Date.now()}_${fileName}`;
      const { data, error } = await supabase.storage.from('chat-media').upload(filePath, blob, {
        contentType: blob.type || 'application/octet-stream',
        cacheControl: '3600',
      });
      if (error) throw error;
      const { data: publicUrlData } = supabase.storage.from('chat-media').getPublicUrl(filePath);
      if (!publicUrlData.publicUrl) throw new Error('Failed to get public URL');
      await sendFileMessage(publicUrlData.publicUrl, fileName);
    } catch (error) {
      Alert.alert('Error', 'Failed to send file. Please try again.');
    }
  };

  const sendFileMessage = async (fileUrl: string, fileName: string) => {
    if (!userId || !effectiveAccountData?.id || !fileUrl) {
      Alert.alert('Error', 'Cannot send file message.');
      return;
    }
    const messageId = generateUUID();
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    const newMsg: ChatMessage = {
      id: messageId,
      text: fileName,
      senderId: userId,
      receiverId: effectiveAccountData.id,
      timestamp: Date.now(),
      status: 'sent',
      isRead: false,
      type: 'file',
      mediaUrl: fileUrl,
    };
    setMessages(prev => [{ ...newMsg, isUser: true }, ...prev]);
    flatListRef.current?.scrollToEnd({ animated: true });
    await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
    await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
  };

  const pickCamera = async () => {
    setIsImageModalVisible(false);
    if (!userId || !effectiveAccountData?.id) {
      Alert.alert('Error', 'Cannot access camera. User or account data missing.');
      return;
    }
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
    if (permissionResult.granted === false) {
      Alert.alert('Permission Required', 'Permission to access camera is required.');
      return;
    }
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: false,
      quality: 1,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      const capturedImageUri = result.assets[0].uri;
      await uploadImageAndSend(capturedImageUri);
    }
  };

  // Helper to send a contact card message
  const sendContactMessage = async (contact: { id: string|number, name: string, phoneNumber?: string }) => {
    const recipientId = effectiveAccountData?.id || otherUserId;
    console.log('sendContactMessage called', { userId, recipientId, contact });
    if (!userId || !recipientId || !contact) {
      console.log('sendContactMessage: missing userId, recipientId, or contact', { userId, recipientId, contact });
      Alert.alert('Error', 'Cannot send contact card. Missing user or chat information.');
      return;
    }
    const chatRoomId = [userId, recipientId].sort().join('_');
    const messageId = generateUUID();
    const newMsg: ChatMessage = {
      id: messageId,
      text: '',
      senderId: userId,
      receiverId: recipientId,
      timestamp: Date.now(),
      status: 'sent',
      isRead: false,
      type: 'contact',
      contact: {
        id: contact.id,
        name: contact.name,
        phoneNumber: contact.phoneNumber || '',
      },
    };
    console.log('sendContactMessage: sending contact card', newMsg);
    setMessages(prev => [{ ...newMsg, isUser: true }, ...prev]);
    await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
    await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), newMsg);

    // Send push notification if user is offline
    if (!isReceiverOnline && recipientPushToken && userId) {
      console.log('Receiver is offline, sending contact notification.');
      const senderName = username || 'You';
      sendNewMessageNotification(recipientPushToken, senderName, `👤 Sent contact: ${contact.name}`).catch(error => {
          console.error('Error sending contact push notification:', error);
      });
    }
  };

  // On mount, check for sendContactCard param and send contact card if present
  useEffect(() => {
    const params = route.params as any;
    if (
      params &&
      params.sendContactCard &&
      params.sendContactCard._sendId &&
      !contactSentIds.has(String(params.sendContactCard._sendId))
    ) {
      // Send the contact card in this DM
      sendContactMessage(params.sendContactCard.contact);
      setContactSentIds(prev => {
        const newSet = new Set(prev);
        newSet.add(String(params.sendContactCard._sendId));
        return newSet;
      });
      // Clear the param so it doesn't resend
      navigation.setParams({ sendContactCard: undefined });
      // DO NOT redirect to the contact's DM!
    }
  }, [(route.params as any)?.sendContactCard, userId, effectiveAccountData?.id]);

  // Load contacts from cache when contact picker is opened
  useEffect(() => {
    if (showContactPicker) {
      AsyncStorage.getItem('cached_userss').then(data => {
        if (data) {
          setContactsList(JSON.parse(data));
        } else {
          setContactsList([]);
        }
      });
    }
  }, [showContactPicker]);

  // Ensure contactsList is always an array
  const safeContactsList = Array.isArray(contactsList) ? contactsList : [];
  const filteredContactsArr = safeContactsList.filter(item => {
    if (!contactSearch.trim()) return true;
    return (
      (item.name && item.name.toLowerCase().includes(contactSearch.toLowerCase())) ||
      (item.phoneNumber && item.phoneNumber.includes(contactSearch))
    );
  });

  const handleSelectContact = (user: ContactType) => {
    console.log('handleSelectContact', user);
    sendContactMessage({
      id: user.id,
      name: user.name,
      phoneNumber: user.phoneNumber,
    });
    setShowContactPicker(false);
    setContactSearch('');
  };

  // Add this function inside the component
  const handleShareContact = async (contact: any) => {
    try {
      await Share.share({
        message: `Contact: ${contact.name}\nPhone: ${contact.phoneNumber}\nUser ID: ${contact.id}`,
        title: `Share ${contact.name}`,
      });
    } catch (error) {
      Alert.alert('Error', 'Could not share contact.');
    }
  };

  // This function handles sending a reply from a notification
  const handleNotificationReply = async (recipientId: string, messageText: string) => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) {
        console.error("handleNotificationReply: No user session found, can't send reply.");
        return;
      }
      const parsedSession = JSON.parse(userSession);
      const senderId = parsedSession?.userId || userSession;

      if (!senderId || !recipientId || !messageText.trim()) {
        console.error('handleNotificationReply: Missing senderId, recipientId, or message text.');
        return;
      }

      console.log(`Sending notification reply from ${senderId} to ${recipientId}`);

      const chatRoomId = [senderId, recipientId].sort().join('_');
      const messageId = generateUUID();

      // Use plain text (no encryption)
      const trimmedMessageText = messageText.trim();

      const firebaseMsg: FirebaseMessage = {
        id: messageId,
        text: trimmedMessageText,
        senderId: senderId,
        receiverId: recipientId,
        timestamp: Date.now(),
        status: 'sent',
        isRead: false,
        encrypted: true, // Mark as encrypted if you want to encrypt notification replies too
      };

      // Write to Firebase
      await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), firebaseMsg);
      await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), firebaseMsg);
      
      console.log('handleNotificationReply: Reply sent successfully to Firebase.');

      // Update unread count for the receiver
      const receiverUnreadRef = ref(db, `user_chats/${recipientId}/${senderId}`);
      await runTransaction(receiverUnreadRef, (currentData) => {
        if (currentData) {
          return { ...currentData, unread: (currentData.unread || 0) + 1 };
        }
        return { unread: 1 };
      });

      console.log('handleNotificationReply: Unread count updated for receiver.');

    } catch (error) {
      console.error('handleNotificationReply: Error sending reply from notification:', error);
    }
  };

  // Effect to handle notification interactions (e.g., reply)
  useEffect(() => {
    // 1. Setup Notification Channels and Categories
    const setupNotifications = async () => {
      // Permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      if (finalStatus !== 'granted') {
        Alert.alert('Permission Required', 'Push notifications are disabled. Please enable them in settings to receive messages.');
        return;
      }

      // Android Channel
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('chat-messages', {
          name: 'Chat Messages',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          // Enable actions for this channel
          enableVibrate: true,
          enableLights: true,
        });
      }

      // iOS Category for interactive notifications
      await Notifications.setNotificationCategoryAsync('chat_message', [
        {
          identifier: 'reply',
          buttonTitle: 'Reply',
          textInput: {
            submitButtonTitle: 'Send',
            placeholder: 'Type a reply...',
          },
          options: {
            // opensInForeground: false, // This is default for background actions
          },
        },
        {
          identifier: 'mark_read',
          buttonTitle: 'Mark as Read',
          options: {
            // opensInForeground: false, // Default
          },
        },
      ]);

      console.log('Notification setup completed successfully');
    };

    setupNotifications();

    // 2. Add listener for notification responses
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response received:', JSON.stringify(response, null, 2));
      const { actionIdentifier, userText } = response;
      const notificationData = response.notification.request.content.data;

      if (actionIdentifier === 'reply' && typeof userText === 'string' && notificationData?.otherUserId) {
        // User tapped the "Reply" action and entered text
        console.log(`User replied with: "${userText}" to user ${notificationData.otherUserId}`);
        handleNotificationReply(notificationData.otherUserId, userText);

      } else if (actionIdentifier === 'mark_read' && notificationData?.otherUserId) {
        // User tapped "Mark as Read"
        // This is more complex as it requires knowing the current user's ID to form the chatRoomId
        // And then marking multiple messages as read. This is a "nice to have" but harder to implement here.
        // For now, let's just log it.
        console.log(`"Mark as Read" tapped for chat with ${notificationData.otherUserId}. Implementation pending.`);

      } else if (actionIdentifier === Notifications.DEFAULT_ACTION_IDENTIFIER) {
        // User tapped on the notification body itself
        if (notificationData?.screen === 'UserChatInbox' && notificationData?.otherUserId && notificationData?.name) {
          console.log(`Navigating to chat with ${notificationData.name}`);
          navigation.navigate('UserChatInbox', {
            otherUserId: notificationData.otherUserId,
            name: notificationData.name,
          });
        }
      }
    });

    // Cleanup
    return () => {
      responseSubscription.remove();
    };
  }, [navigation]); // Dependency on navigation to avoid stale closure

  // Handle reply to message
  const handleReplyToMessage = (message: ChatMessage) => {
    setReplyToMessage(message);
    setReplyToText(getReplyPreviewText(message));
    setReplyToSenderId(message.senderId);
    // Focus on input after a short delay
    setTimeout(() => {
      // You can add input focus logic here if needed
    }, 100);
  };

  // Cancel reply
  const cancelReply = () => {
    setReplyToMessage(null);
    setReplyToText('');
    setReplyToSenderId('');
  };

  const shareImage = async (imageUrl: string) => {
    try {
      // Download the image to a temporary cache directory
      const fileUri = FileSystem.cacheDirectory + `${generateUUID()}.jpg`;
      await FileSystem.downloadAsync(imageUrl, fileUri);

      // Use the built-in Share API
      if (Platform.OS === 'android') {
        // For Android, we need to read the file as a base64 string to share it
        const fileString = await FileSystem.readAsStringAsync(fileUri, { encoding: FileSystem.EncodingType.Base64 });
        const dataUrl = `data:image/jpeg;base64,${fileString}`;
        await Share.share({
          title: 'Share Image',
          url: dataUrl,
        });
      } else {
        // iOS can share the local file URI directly
        await Share.share({ url: fileUri });
      }
    } catch (error) {
      console.error('Error sharing image: ', error);
      Alert.alert('Error', 'Failed to share image.');
    }
  };

  // Helper to generate text for reply previews
  const getReplyPreviewText = (message: ChatMessage): string => {
    if (message.type === 'image') {
        return '📷 Image';
    }
    if (message.type === 'contact' && message.contact) {
        return `👤 ${message.contact.name}`;
    }
    if (message.type === 'audio') {
        return '🎵 Audio';
    }
    if (message.type === 'file' && message.text) {
        return `📄 ${message.text}`;
    }
    return message.text;
  }

  return (
    <SafeAreaView style={tw`flex-1 bg-[#c5e8c8]`}> 
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={80}
      >
      <StatusBar barStyle="dark-content" />
      {/* Invisible overlay to handle outside press */}
      {showMenu && (
        <TouchableOpacity
          style={tw`absolute inset-0 z-50`}
          activeOpacity={0}
          onPress={handleOutsidePress}
        />
      )}
      {/* Header */}
      <View style={tw`flex-row items-center border-b border-[#B0DEBA] justify-between px-4 py-2.5`}>
        <View style={tw`flex-row items-center`}>
          <View style={tw`w-11 h-11 rounded-2xl border-2 border-black justify-center items-center mr-2.5 overflow-hidden`}>
            <Image source={{ uri: effectiveAccountData?.logo || 'https://via.placeholder.com/150' }} style={tw`w-full h-full`} />
          </View>
          <View>
            <Text style={tw`text-sm font-bold text-[#2c5e2e]`}>{effectiveAccountData?.name || 'User'}</Text>
            <View style={tw`flex-row items-center gap-1`}>
              <SokitOnlineStatusIndicator
                userId={effectiveAccountData?.id || ''}
                size={8}
                showText={false}
              />
              <Text style={tw`text-xs text-[#2c5e2e] font-bold`}>
                {isReceiverTyping && isReceiverOnline ? 'typing...' :
                 isReceiverOnline ? 'Online' :
                 lastSeen ? `Last seen ${formatLastSeen(lastSeen)}` : 'Offline'}
                {effectiveAccountData?.verified && ' ✓'}
              </Text>
            </View>
          </View>
        </View>
        <View style={tw`flex-row items-center relative`}>
          {isReceiverOnline && (
            <TouchableOpacity onPress={() => {
               console.log('More options pressed. Toggling menu.');
               setShowMenu(!showMenu);
               }}>
              <Feather name="more-horizontal" size={24} color="black" />
            </TouchableOpacity>
          )}
          {showMenu && isReceiverOnline && (
            <View style={tw`absolute top-8 right-0 bg-white rounded-lg p-1 shadow-lg z-50 min-w-[180px]`}>
              <TouchableOpacity
                style={tw`flex-row items-center py-2.5 px-4`}
                onPress={() => {
                  setShowMenu(false);
                  Alert.alert(
                    requireBothOnline ? 'Disable Online Restriction' : 'Enable Online Restriction',
                    requireBothOnline
                      ? 'Turn OFF "Both must be online to view chat"? You will be able to view chat even if the other user is offline.'
                      : 'Turn ON "Both must be online to view chat"? You will only see chat if both are online.',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      { text: requireBothOnline ? 'Disable' : 'Enable', onPress: () => setRequireBothOnline(v => !v) },
                    ]
                  );
                }}
              >
                <Feather name={requireBothOnline ? 'toggle-right' : 'toggle-left'} size={18} color={requireBothOnline ? '#4CAF50' : '#9E9E9E'} style={tw`mr-2.5`} />
                <Text style={tw`text-sm text-gray-800`}>
                  {requireBothOnline ? 'Require both online to view chat (ON)' : 'Require both online to view chat (OFF)'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={tw`flex-row items-center py-2.5 px-4`}
                onPress={() => {
                  setShowMenu(false);
                  Alert.alert(
                    'Clear Chat History',
                    'Are you sure you want to clear all chat history? This action cannot be undone.',
                    [
                      { text: 'Cancel', style: 'cancel', onPress: () => {} },
                      { text: 'Clear', style: 'destructive', onPress: clearChatHistory }
                    ]
                  );
                }}
              >
                <Feather name="trash-2" size={18} color="#FF5252" style={tw`mr-2.5`} />
                <Text style={tw`text-sm text-gray-800`}>Clear Chat History</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
      {/* Chat Messages */}
      <View style={{ flex: 1 }}>
        {renderContent()}
      </View>
      {/* Message Input at the bottom */}
      <View style={tw`p-2.5 bg-white`}> 
        <View style={tw`flex-row items-end`}>
          <View style={tw`flex-1 bg-white rounded-2xl mr-2.5 overflow-hidden`}>
            {/* Reply Preview */}
            {replyToMessage && (
              <View style={tw`bg-gray-100 pt-2`}>
                <View style={tw`flex-row items-center px-2.5 pb-2`}>
                  <View style={tw`w-0.5 h-8 bg-green-500 rounded-sm mr-2`} />
                  <View style={tw`flex-1`}>
                    <Text style={tw`text-sm font-bold`}>
                      Replying to {replyToSenderId === userId ? 'yourself' : effectiveAccountData?.name || 'User'}
                    </Text>
                    <Text style={tw`text-sm text-gray-800 mt-0.5`} numberOfLines={1}>
                      {replyToText}
                    </Text>
                  </View>
                </View>
                <TouchableOpacity onPress={cancelReply} style={tw`absolute top-1 right-1 p-1`}>
                  <Feather name="x" size={20} color="#666" />
                </TouchableOpacity>
              </View>
            )}
            <View style={tw`flex-row items-center`}>
              <TouchableOpacity style={tw`p-2.5 justify-center items-center`} onPress={() => setIsImageModalVisible(true)}>
                <Feather name="plus" size={24} color="#4CAF50" />
              </TouchableOpacity>
              <TextInput
                style={tw`flex-1 py-2.5 text-gray-800`}
                placeholder={replyToMessage ? "Type your reply..." : "Type a message..."}
                placeholderTextColor="#999"
                value={message}
                onChangeText={(text: string) => {
                  if (isInputDisabled) return;
                  if (containsBadWords(text)) {
                    setMessage('');
                    setBadWordWarningCount(prev => {
                      const newCount = prev + 1;
                      if (newCount >= 3) {
                        setIsInputDisabled(true);
                        setWarningMessage('You have used inappropriate language too many times. Messaging is now disabled.');
                        Alert.alert('Messaging Disabled', 'You have used inappropriate language too many times. Messaging is now disabled.');
                      } else {
                        setWarningMessage('Inappropriate language is not allowed.');
                        Alert.alert('Warning', 'Inappropriate language is not allowed.');
                      }
                      return newCount;
                    });
                  } else {
                    setWarningMessage('');
                    setMessage(text);
                  }
                  // ...existing typing indicator logic...
                  if (text.length > 0) {
                    updateTypingStatus(true);
                    if (typingTimeoutRef.current) {
                      clearTimeout(typingTimeoutRef.current);
                    }
                    typingTimeoutRef.current = setTimeout(() => {
                      updateTypingStatus(false);
                    }, 2000);
                  } else {
                    updateTypingStatus(false);
                    if (typingTimeoutRef.current) {
                      clearTimeout(typingTimeoutRef.current);
                      typingTimeoutRef.current = null;
                    }
                  }
                }}
                onFocus={scrollToBottom}
                editable={!isInputDisabled}
              />
              <TouchableOpacity style={tw`p-2.5 justify-center items-center`} onPress={pickCamera}>
                <Feather name="camera" size={24} color="#4CAF50" />
              </TouchableOpacity>
            </View>
          </View>
          <TouchableOpacity style={tw`w-12 h-12 rounded-full bg-green-500 justify-center items-center`} onPress={sendMessage} disabled={isInputDisabled || message.trim() === ''}>
            <Feather name="send" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>
      {/* ...modals and overlays that should be above keyboard */}
      </KeyboardAvoidingView>
      {isRecording && (
        <View style={{
          position: 'absolute',
          bottom: 80,
          left: 0,
          right: 0,
          alignItems: 'center',
          zIndex: 10,
        }}>
          <View style={{
            backgroundColor: 'red',
            borderRadius: 20,
            paddingHorizontal: 24,
            paddingVertical: 10,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
            <Feather name="mic" size={20} color="#fff" />
            <Text style={{ color: 'white', marginLeft: 10, fontWeight: 'bold', fontSize: 16 }}>
              Recording... {recordingDuration}s
            </Text>
          </View>
        </View>
      )}
      {warningMessage ? (
        <Text style={{ color: '#FF5252', textAlign: 'center', marginTop: 4 }}>{warningMessage}</Text>
      ) : null}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c5e8c8',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBlockColor: '#B0DEBA',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 45,
    height: 45,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    overflow: 'hidden',
  },
  headerTitle: {
    fontSize: 15,
    fontFamily: 'Helvetica_bold',
    color: '#2c5e2e',
  },
  headerSubtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  headerSubtitle: {
    fontSize: 13,
    color: '#2c5e2e',
    fontFamily: 'Helvetica_bold',
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 20,
    marginVertical: 4,
  },
  userMessage: {
    backgroundColor: '#DCF8C6',
    alignSelf: 'flex-end',
    marginLeft: '20%',
  },
  otherMessage: {
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    marginRight: '20%',
  },
  messageText: {
    fontSize: 16,
  },
  messageContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  messageStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginLeft: 8,
  },
  readIcon: {
    marginLeft: 4,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  badgeContainer: {
    backgroundColor: '#FF5252',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    paddingHorizontal: 6,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  menuContainer: {
    position: 'absolute',
    top: 30,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
    minWidth: 180,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  menuIcon: {
    marginRight: 10,
  },
  menuItemText: {
    fontSize: 14,
    color: '#333',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  inputContainer: {
    padding: 10,
  },
  inputWrapper: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 20,
    marginRight: 10,
    overflow: 'hidden',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  plusButton: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    paddingVertical: 10,
    paddingRight: 15,
    color: '#333',
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageList: {
    flexGrow: 1,
    justifyContent: 'flex-end',
    padding: 15,
  },
  typingIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
    marginBottom: 10,
  },
  typingBubble: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginHorizontal: 3,
  },
  typingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  // New styles for error messages
  errorMessage: {
    backgroundColor: '#FFEBEE', // Light red background
    borderColor: '#FF5252', // Red border
    borderWidth: 1,
  },
  errorMessageText: {
    color: '#FF5252', // Red text color
  },
  // New style for sending messages
   sendingMessage: {
     backgroundColor: '#E0E0E0', // Light grey background for sending messages
   },
  // Reply functionality styles
  replyPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
  },
  replyLine: {
    width: 3,
    height: 30,
    backgroundColor: '#4CAF50',
    borderRadius: 2,
    marginRight: 8,
  },
  replyContent: {
    flex: 1,
  },
  replySender: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 2,
  },
  replyText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  replyPreviewContainer: {
    backgroundColor: '#f0f0f0',
    paddingTop: 8,
  },
  replyPreviewContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingBottom: 8,
  },
  replyPreviewText: {
    flex: 1,
  },
  replyPreviewLabel: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  replyPreviewMessage: {
    fontSize: 14,
    color: '#333',
    marginTop: 2,
  },
  cancelReplyButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    padding: 5,
  },
});
