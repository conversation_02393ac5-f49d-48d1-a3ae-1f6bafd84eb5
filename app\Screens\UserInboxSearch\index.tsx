import React, { useState, useEffect, useRef, useCallback } from 'react';
// Constants for AsyncStorage keys
const PERMISSION_GRANTED_KEY = 'contacts_permission_granted';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  StyleSheet,
  Alert,
  ActivityIndicator,
  StatusBar,
  Modal,
  Linking,
  ImageBackground,
  RefreshControl,
  Keyboard
} from 'react-native';
// NetInfo import removed
import * as Contacts from 'expo-contacts';
import { Ionicons, AntDesign } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { Searchbar } from 'react-native-paper';

import { Image, View as MotiView } from 'moti';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Status from '@/components/Status';
import { db } from '@/lib/Firebase';
import { ref, onValue, update, get, set, off, onDisconnect, serverTimestamp } from 'firebase/database';
import { AppState } from 'react-native';
import UserInboxSkeleton from '../../components/UserInboxSkeleton';

type NavigationProp = NativeStackNavigationProp<any>;

interface Status {
  id: number;
  user_id: string;
  media_url: string;
  created_at: string;
}

interface MappedUser {
  id: number;
  name: string;
  initial: string;
  contactName?: string;
  phoneNumber?: string;
  originalEmail?: string;
  lastMessage: string;
  timestamp: string;
  is_online: boolean;
  statuses?: Status[];
  hasUnseenStory: boolean;
  hasStatus?: boolean;
  messageStatus?: 'pending' | 'sent' | 'read';
}

const ChatListScreen = () => {
  const navigation = useNavigation<NavigationProp>();
  const [users, setUsers] = useState<MappedUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddContact, setShowAddContact] = useState(false);
  const [newContact, setNewContact] = useState({
    name: '',
    phoneNumber: ''
  });
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  // Add proper type for unread counts
  interface UnreadCounts {
    [key: string]: number;
  }

  // Initialize state with empty object
  const [unreadCounts, setUnreadCounts] = useState<UnreadCounts>({});

  // Firebase state
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [firebaseUnreadCounts, setFirebaseUnreadCounts] = useState<{[key: string]: number}>({});


  // Handle scroll events for the FlatList
  const onScroll = () => {
    // Scroll handling logic removed
  };

  const openSettings = () => {
    Linking.openSettings();
  };

  const requestContactPermissions = async () => {
    try {
      // First check if we have stored permission in AsyncStorage
      const storedPermission = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);

      if (storedPermission === 'true') {
        // If we have stored permission, use it without prompting
        setPermissionsGranted(true);
        return true;
      }

      // If no stored permission, check if we already have system permissions
      const { status: existingStatus } = await Contacts.getPermissionsAsync();

      if (existingStatus === 'granted') {
        // Save permission to AsyncStorage for future use
        await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'true');
        setPermissionsGranted(true);
        return true;
      }

      // If not, request permissions
      const { status } = await Contacts.requestPermissionsAsync();

      if (status === 'granted') {
        // Save permission to AsyncStorage for future use
        await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'true');
        setPermissionsGranted(true);
        return true;
      }

      // Permission was denied
      setPermissionsGranted(false);
      await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'false');

      // Show alert with option to open settings
      Alert.alert(
        'Permission Required',
        'This app needs access to contacts to show you which of your contacts are using this app.',
        [
          {
            text: 'Not Now',
            style: 'cancel'
          },
          {
            text: 'Open Settings',
            onPress: openSettings
          }
        ]
      );
      return false;
    } catch (error) {
      setPermissionsGranted(false);
      return false;
    }
  };

  const checkAndRequestPermissions = async () => {
    // First check AsyncStorage for permission status
    const storedPermission = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);

    if (storedPermission === 'true') {
      // If permission was already granted, skip the request and load contacts
      setPermissionsGranted(true);
      getContactsAndUsers();

      // Verify permission in the background without UI disruption
      const { status } = await Contacts.getPermissionsAsync();
      if (status !== 'granted') {
        // If permission was revoked in settings, update AsyncStorage silently
        await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'false');
        // Don't update UI here to avoid disruption - will be handled on next app open
      }
    } else {
      // If no stored permission or it was denied, request it
      const hasPermission = await requestContactPermissions();
      if (hasPermission) {
        getContactsAndUsers();
      } else {
        setPermissionsGranted(false);
        setLoading(false);
      }
    }
  };

  const getContactsAndUsers = async (showLoadingState = true) => {
    if (showLoadingState) {
      setLoading(true);

      // Add a timeout to prevent infinite loading
      const loadingTimeout = setTimeout(() => {

        setLoading(false);
      }, 15000); // 15 second timeout

      // Store the timeout ID to clear it later
      contactsLoadingTimeoutRef.current = loadingTimeout;
    }
    try {
      // First check AsyncStorage for permission status
      const storedPermission = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);

      if (storedPermission !== 'true') {
        // If no stored permission, check with the system
        const { status } = await Contacts.getPermissionsAsync();
        if (status !== 'granted') {
          setPermissionsGranted(false);
          setLoading(false);
          return;
        }
      }



      const { data: deviceContacts } = await Contacts.getContactsAsync({
        fields: [Contacts.Fields.PhoneNumbers, Contacts.Fields.Name],
      });


      // Improved phone number cleaning function
      const cleanPhoneNumber = (number: string | undefined): string => {
        if (!number) return '';
        // First remove all non-digit characters
        let cleaned = number.replace(/\D/g, '');
        // Remove country code if present (e.g., 91 for India)
        if (cleaned.startsWith('91') && cleaned.length > 10) {
          cleaned = cleaned.substring(2);
        }
        // Remove leading zeros
        cleaned = cleaned.replace(/^0+/, '');
        return cleaned;
      };

      // Process contact phone numbers more efficiently
      const contactPhoneNumbers = deviceContacts
        .filter(contact => contact.phoneNumbers && contact.phoneNumbers.length > 0)
        .flatMap(contact => {
          // Get all phone numbers for each contact
          return contact.phoneNumbers?.map(phoneNumber => ({
            name: contact.name,
            phoneNumber: cleanPhoneNumber(phoneNumber.number),
            originalNumber: phoneNumber.number // Keep original for debugging
          })) || [];
        });




      const response = await fetch('https://nityasha.vercel.app/api/v1/users/get');
      if (!response.ok) {

        throw new Error('Network response was not ok');
      }
      const apiUsers = await response.json();


      // Check if any users have statuses
      const usersWithStatuses = apiUsers.filter((user: any) => user.statuses && Array.isArray(user.statuses) && user.statuses.length > 0);
      if (usersWithStatuses.length > 0) {


      }

      // Create a map for faster lookups with multiple formats
      const contactMap = new Map();
      contactPhoneNumbers.forEach((contact: any) => {
        if (contact.phoneNumber) {
          // Store the standard format
          contactMap.set(contact.phoneNumber, contact);

          // Also store last 10 digits for matching with different formats
          if (contact.phoneNumber.length >= 10) {
            const last10Digits = contact.phoneNumber.slice(-10);
            contactMap.set(last10Digits, contact);
          }
        }
      });

      // Process API users more efficiently
      const matchedUsers = apiUsers
        .filter((user: any) => {
          // Clean the email field using the same function
          const userNumber = cleanPhoneNumber(user.email);

          // Check if it's a valid phone number
          const isPhoneNumber = /^\d+$/.test(userNumber || '');
          if (!isPhoneNumber) {
            return false;
          }

          // Try multiple matching strategies
          // 1. Direct match
          if (contactMap.has(userNumber)) {
            return true;
          }

          // 2. Match last 10 digits
          if (userNumber.length >= 10) {
            const last10Digits = userNumber.slice(-10);
            return contactMap.has(last10Digits);
          }

          return false;
        })
        .map((user: any) => {
          // Clean the email field using the same function
          const userNumber = cleanPhoneNumber(user.email);

          // Try to find a matching contact using multiple strategies
          let matchingContact = contactMap.get(userNumber);

          // If no direct match, try with last 10 digits
          if (!matchingContact && userNumber.length >= 10) {
            const last10Digits = userNumber.slice(-10);
            matchingContact = contactMap.get(last10Digits);
          }

          // Check if user has statuses
          const hasStatuses = user.statuses && Array.isArray(user.statuses) && user.statuses.length > 0;

          const mappedUser = {
            id: user.id,
            name: matchingContact?.name || user.username || 'Anonymous',
            initial: (matchingContact?.name?.[0] || user.username?.[0] || 'A').toUpperCase(),
            contactName: matchingContact?.name,
            phoneNumber: userNumber,
            originalEmail: user.email, // Keep for debugging
            lastMessage: user.last_message || 'Hey there!', // Use last_message if available
            timestamp: user.last_message_time || '2:17:25 PM', // Use last_message_time if available
            is_online: false, // Default to offline, will be updated by Firebase listener
            // Add message status if available
            messageStatus: user.message_status || (unreadCounts[user.id] > 0 ? 'pending' : undefined),
            // Preserve statuses if they exist
            ...(hasStatuses && {
              statuses: user.statuses,
              hasUnseenStory: true,
              hasStatus: true
            })
          };


          return mappedUser;
        });



      setUsers(matchedUsers);
      // Also update filtered users when users are updated
      setFilteredUsers(matchedUsers);
      if (!showLoadingState) {
        Alert.alert('Success', 'Contacts refreshed successfully');
      }
    } catch (error) {
      console.error('Error in getContactsAndUsers:', error);
      Alert.alert('Error', 'Failed to fetch users and contacts');
    } finally {
      // Clear the loading timeout if it exists
      if (contactsLoadingTimeoutRef.current) {
        clearTimeout(contactsLoadingTimeoutRef.current);
        contactsLoadingTimeoutRef.current = null;
      }

      setLoading(false);
      setRefreshing(false);

    }
  };

  const fetchUnreadCounts = async () => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) {

        return;
      }

      const parsedSession = JSON.parse(userSession);
      if (!parsedSession?.userId) {

        return;
      }

      // Set up Firebase listener for unread counts
      const userChatsRef = ref(db, `user_chats/${parsedSession.userId}`);
      onValue(userChatsRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
          const unreadData: {[key: string]: number} = {};
          let total = 0;

          // Process each chat entry
          Object.entries(data).forEach(([otherUserId, chatData]: [string, any]) => {
            if (chatData.unread && chatData.unread > 0) {
              unreadData[otherUserId] = chatData.unread;
              total += chatData.unread;
            }
          });

          // Update Firebase unread counts without affecting other state
          setFirebaseUnreadCounts(prev => {
            // Create a new object that preserves existing counts and adds/updates new ones
            const updated = { ...prev };

            // Update counts for users in the new data
            Object.entries(unreadData).forEach(([userId, count]) => {
              updated[userId] = count;

            });

            return updated;
          });

          // Combine Firebase unread counts with API unread counts
          setUnreadCounts(prev => {
            const combinedUnreadCounts = { ...prev };

            // Update counts for users in the new data
            Object.entries(unreadData).forEach(([userId, count]) => {
              combinedUnreadCounts[userId] = count;
            });

            return combinedUnreadCounts;
          });

          // Total unread count calculation removed




        }
      });

      // Also check for unread messages in user_messages collection
      // This ensures we catch any messages that might not be reflected in user_chats
      const userId = parsedSession.userId.toString();
      const messagesRef = ref(db, 'user_messages');
      onValue(messagesRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
          const unreadData: {[key: string]: number} = {};
          let total = 0;

          // Process each chat room
          Object.entries(data).forEach(([chatId, messages]: [string, any]) => {
            // Extract user IDs from chat ID (format: userId1_userId2)
            const userIds = chatId.split('_');
            if (userIds.includes(userId)) {
              // Find the other user's ID
              const otherUserId = userIds[0] === userId ? userIds[1] : userIds[0];

              // Count unread messages from the other user
              let unreadCount = 0;
              Object.values(messages).forEach((msg: any) => {
                if (msg.senderId === otherUserId && msg.status !== 'read') {
                  unreadCount++;
                }
              });

              if (unreadCount > 0) {
                unreadData[otherUserId] = unreadCount;
                total += unreadCount;
              }
            }
          });

          // Update state with the new unread counts without affecting other users
          setFirebaseUnreadCounts(prev => {
            const updated = { ...prev };

            // Update counts for users in the new data
            Object.entries(unreadData).forEach(([userId, count]) => {
              updated[userId] = count;

            });

            return updated;
          });

          setUnreadCounts(prev => {
            const updated = { ...prev };

            // Update counts for users in the new data
            Object.entries(unreadData).forEach(([userId, count]) => {
              updated[userId] = count;
            });

            return updated;
          });

          // Total unread count calculation removed




        }
      });

      // Try the new endpoint first
      try {
        const response = await fetch(`https://status.api.nityasha.com/messages/status/${parsedSession.userId}`);
        if (response.ok) {
          const data = await response.json();
          if (data && typeof data === 'object') {
            // Get API unread counts
            const apiUnreadCounts = data.pendingMessages || {};

            // Update unread counts without affecting existing Firebase counts
            setUnreadCounts(prev => {
              const updated = { ...prev };

              // Update counts for users in the API data
              Object.entries(apiUnreadCounts).forEach(([userId, count]) => {
                // Only update if there's a count, don't remove existing counts for other users
                if (Number(count) > 0) {
                  updated[userId] = Number(count) || 0; // Ensure count is a number
                }
              });

              return updated;
            });

            // Total unread count calculation removed


            return;
          }
        }
      } catch (newApiError) {

      }

      // Fall back to the old endpoint if the new one fails
      const response = await fetch(`https://api.search.nityasha.com/chat/unread-counts/${parsedSession.userId}`);
      if (!response.ok) throw new Error('Failed to fetch unread counts');

      const data = await response.json();
      if (data && typeof data === 'object') {
        // Get API unread counts
        const apiUnreadCounts = data.unreadCounts || {};

        // Update unread counts without affecting existing Firebase counts
        setUnreadCounts(prev => {
          const updated = { ...prev };

          // Update counts for users in the API data
          Object.entries(apiUnreadCounts).forEach(([userId, count]) => {
            // Only update if there's a count, don't remove existing counts for other users
            if (Number(count) > 0) {
              updated[userId] = Number(count) || 0; // Ensure count is a number
            }
          });

          return updated;
        });

        // Total unread count calculation removed
      }
    } catch (error) {

      // Don't set error state here to avoid UI disruption
    }
  };

  // Helper function to check if both users are online
  const checkBothUsersOnline = async (userId: string | number, otherUserId: string | number): Promise<boolean> => {
    try {
      // Convert IDs to strings for consistent comparison
      const userIdStr = userId.toString();
      const otherUserIdStr = otherUserId.toString();



      // Check all possible paths for online status
      let isCurrentUserOnline = false;
      let isOtherUserOnline = false;

      // 1. Check users/[userId]/status path
      try {
        // Check current user
        const currentUserRef = ref(db, `users/${userIdStr}/status`);
        const currentUserSnapshot = await get(currentUserRef);
        const currentUserStatus = currentUserSnapshot.val();
        if (currentUserStatus && currentUserStatus.state === 'online') {
          isCurrentUserOnline = true;

        }

        // Check other user
        const otherUserRef = ref(db, `users/${otherUserIdStr}/status`);
        const otherUserSnapshot = await get(otherUserRef);
        const otherUserStatus = otherUserSnapshot.val();
        if (otherUserStatus && otherUserStatus.state === 'online') {
          isOtherUserOnline = true;

        }
      } catch (usersError) {

      }

      // 2. Check online/[userId] path if users are not online in users path
      if (!isCurrentUserOnline || !isOtherUserOnline) {
        try {
          // Check current user
          const currentUserRef = ref(db, `online/${userIdStr}`);
          const currentUserSnapshot = await get(currentUserRef);
          const currentUserStatus = currentUserSnapshot.val();
          if (currentUserStatus && currentUserStatus.online === true) {
            isCurrentUserOnline = true;

          }

          // Check other user
          const otherUserRef = ref(db, `online/${otherUserIdStr}`);
          const otherUserSnapshot = await get(otherUserRef);
          const otherUserStatus = otherUserSnapshot.val();
          if (otherUserStatus && otherUserStatus.online === true) {
            isOtherUserOnline = true;

          }
        } catch (onlineError) {

        }
      }

      // 3. Check user_status/[userId] path if users are still not online
      if (!isCurrentUserOnline || !isOtherUserOnline) {
        try {
          // Check current user
          const currentUserRef = ref(db, `user_status/${userIdStr}`);
          const currentUserSnapshot = await get(currentUserRef);
          const currentUserStatus = currentUserSnapshot.val();
          if (currentUserStatus && currentUserStatus.online === true) {
            isCurrentUserOnline = true;

          }

          // Check other user
          const otherUserRef = ref(db, `user_status/${otherUserIdStr}`);
          const otherUserSnapshot = await get(otherUserRef);
          const otherUserStatus = otherUserSnapshot.val();
          if (otherUserStatus && otherUserStatus.online === true) {
            isOtherUserOnline = true;

          }
        } catch (userStatusError) {

        }
      }

      const areBothUsersOnline = isCurrentUserOnline && isOtherUserOnline;


      return areBothUsersOnline;
    } catch (error) {

      return false;
    }
  };

  const markMessagesAsRead = async (chatPartnerId: number | string) => {
    if (!chatPartnerId) {

      return;
    }

    // Convert chatPartnerId to string to ensure consistent comparison
    const chatPartnerIdStr = chatPartnerId.toString();

    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const parsedSession = JSON.parse(userSession);
      if (!parsedSession?.userId) return;

      // First check if both users are online
      const bothOnline = await checkBothUsersOnline(parsedSession.userId, chatPartnerIdStr);
      if (!bothOnline) {

        return;
      }

      // Mark messages as read in Firebase
      try {
        // Generate chat room ID
        const chatId = [parsedSession.userId.toString(), chatPartnerIdStr].sort().join('_');

        // We already checked if both users are online with checkBothUsersOnline()
        // Now we can proceed with marking messages as read

        // Update unread count in user_chats ONLY for this specific chat partner
        const userChatRef = ref(db, `user_chats/${parsedSession.userId}/${chatPartnerIdStr}`);
        update(userChatRef, { unread: 0 });

        // Get all messages in this chat
        const messagesRef = ref(db, `user_messages/${chatId}`);
        onValue(messagesRef, (snapshot) => {
          const data = snapshot.val();
          if (data) {
            // Find messages from the other user that aren't marked as read
            Object.entries(data).forEach(([messageId, message]: [string, any]) => {
              // IMPORTANT: Only mark messages from this specific chat partner as read
              if (message.senderId === chatPartnerIdStr && message.status !== 'read') {
                // First mark message as read
                update(ref(db, `user_messages/${chatId}/${messageId}`), {
                  status: 'read',
                  isRead: true,
                  readAt: Date.now()
                });

                // Then remove the message after marking it as read
                setTimeout(() => {
                  // Remove from user_messages
                  set(ref(db, `user_messages/${chatId}/${messageId}`), null);
                  // Also remove from user_chats if it exists there
                  set(ref(db, `user_chats/${chatId}/${messageId}`), null);

                }, 500); // Small delay to ensure read status is updated first
              }
            });
          }
        }, { onlyOnce: true }); // Only run once



        // Update the UI immediately for Firebase messages - ONLY for this specific chat partner
        setFirebaseUnreadCounts(prev => {
          const updated = { ...prev };
          delete updated[chatPartnerIdStr];
          return updated;
        });

        // Also update the combined unread counts - ONLY for this specific chat partner
        setUnreadCounts(prev => {
          const updated = { ...prev };
          // Only remove the unread count for this specific chat partner
          delete updated[chatPartnerIdStr];
          return updated;
        });

        // Total unread count update removed
      } catch (firebaseError) {

      }

      // Now proceed with marking messages as read via API
      // Try the new endpoint first
      try {
        const response = await fetch('https://status.api.nityasha.com/messages/mark-read', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: parsedSession.userId,
            senderId: chatPartnerIdStr, // Only mark messages from this specific sender as read
            bothOnline: true, // Add flag to indicate both users are online
            clearMessages: true, // Add flag to clear messages after marking as read
          }),
        });

        if (response.ok) {

          // Update the UI immediately - ONLY for this specific chat partner
          setUnreadCounts(prev => {
            const updated = { ...prev };
            // Only remove the unread count for this specific chat partner
            delete updated[chatPartnerIdStr];
            return updated;
          });

          // Total unread count update removed
          return;
        }
      } catch (newApiError) {

      }

      // Fall back to the old endpoint if the new one fails
      try {
        await fetch('https://api.search.nityasha.com/chat/mark-read', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: parsedSession.userId,
            chatPartnerId: chatPartnerIdStr, // Only mark messages from this specific chat partner as read
            bothOnline: true, // Add flag to indicate both users are online
            clearMessages: true, // Add flag to clear messages after marking as read
          }),
        });


        setUnreadCounts(prev => {
          const updated = { ...prev };
          // Only remove the unread count for this specific chat partner
          delete updated[chatPartnerIdStr];
          return updated;
        });

        // Total unread count update removed
      } catch (oldApiError) {

      }

    } catch (error) {

    }
  };

  const fetchStatuses = async () => {
    // Skip if we've already processed statuses
    if (statusesProcessedRef.current && statuses.length > 0) {

      return;
    }

    try {
      // First check if any users already have statuses attached
      const usersWithStatuses = users.filter(user => user.statuses && user.statuses.length > 0);
      const existingStatuses: Status[] = [];

      // Collect all statuses from users
      usersWithStatuses.forEach(user => {
        if (user.statuses) {
          existingStatuses.push(...user.statuses);
        }
      });

      // If we have statuses from users, use those
      if (existingStatuses.length > 0) {

        setStatuses(existingStatuses);
        await updateUsersWithStatuses(existingStatuses);
        // Mark as processed
        statusesProcessedRef.current = true;
        return;
      }

      // Get current user ID from AsyncStorage
      const userSession = await AsyncStorage.getItem('userSession');
      let currentUserId = null;
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        currentUserId = parsedSession.userId;
      }

      // Fetch all statuses
      let allStatuses: Status[] = [];

      // Fetch statuses for the current user if available
      if (currentUserId) {
        try {
          // Use the exact endpoint for the user's own statuses
          const userStatusResponse = await fetch(`https://status.api.nityasha.com/statuses?userId=${currentUserId}`);
          if (userStatusResponse.ok) {
            const userData = await userStatusResponse.json();


            // Check if the response is an array (direct statuses) or has a statuses property
            const userStatuses = Array.isArray(userData) ? userData : (userData.statuses || []);

            if (userStatuses.length > 0) {

              allStatuses = [...allStatuses, ...userStatuses];
            }
          }
        } catch (userStatusError) {

        }
      }

      // Fetch all other statuses
      const response = await fetch('https://status.api.nityasha.com/statuses');
      if (response.ok) {
        const data = await response.json();


        if (data && data.statuses && data.statuses.length > 0) {
          // Filter out any duplicates that might already be in allStatuses
          const newStatuses = data.statuses.filter((status: Status) =>
            !allStatuses.some(existingStatus => existingStatus.id === status.id)
          );
          allStatuses = [...allStatuses, ...newStatuses];
        }
      }

      // Set the combined statuses
      setStatuses(allStatuses);

      // After fetching statuses, update users with status information
      if (allStatuses.length > 0) {
        await updateUsersWithStatuses(allStatuses);
        // Mark as processed
        statusesProcessedRef.current = true;
      }
    } catch (error) {

    }
  };

  const updateUsersWithStatuses = async (statusesData: Status[]) => {
    // Skip if we've already processed statuses
    if (statusesProcessedRef.current && users.some(user => user.hasStatus)) {

      return;
    }

    // Get current user ID from AsyncStorage
    let currentUserId = null;
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        currentUserId = parsedSession.userId?.toString();

      }
    } catch (error) {

    }

    // First, create a map of phone numbers to statuses
    const phoneToStatusMap: { [key: string]: Status[] } = {};

    // Process all statuses and group them by phone number
    statusesData.forEach((status: Status) => {
      // Clean the user_id from status to match our phone number format
      const statusUserId = status.user_id
        ?.replace(/[\s-()]/g, '')
        ?.replace(/^\+?91/, '')
        ?.replace(/^0+/, '');

      if (statusUserId) {
        if (!phoneToStatusMap[statusUserId]) {
          phoneToStatusMap[statusUserId] = [];
        }
        phoneToStatusMap[statusUserId].push(status);
      }
    });



    // Only update users if we have status data
    if (Object.keys(phoneToStatusMap).length > 0) {
      setUsers(prevUsers => {
        return prevUsers.map(user => {
          // Skip if user already has status information
          if (user.hasStatus && user.statuses && user.statuses.length > 0) {
            return user;
          }

          // Check if this user's phone number has any statuses
          const userStatuses = user.phoneNumber ? phoneToStatusMap[user.phoneNumber] : undefined;

          // Check if this is the current user's ID - using exact match (=)
          const isCurrentUser = currentUserId && (user.id.toString() === currentUserId || user.id === parseInt(currentUserId));

          // If user has statuses or is the current user with matching statuses, add them to the user object
          if (userStatuses && userStatuses.length > 0) {
            return {
              ...user,
              statuses: userStatuses,
              hasUnseenStory: true, // Mark as having unseen story by default
              hasStatus: true
            };
          }

          // If this is the current user, check if there are any statuses with matching user_id
          if (isCurrentUser && currentUserId) {
            // Find statuses that belong to the current user - using exact match (=)
            const currentUserStatuses = statusesData.filter(status =>
              status.user_id === currentUserId.toString() || status.user_id === currentUserId
            );



            if (currentUserStatuses.length > 0) {
              return {
                ...user,
                statuses: currentUserStatuses,
                hasUnseenStory: true,
                hasStatus: true
              };
            }
          }

          return user;
        });
      });
    }
  };

  // Function to mark a status as seen was removed as it's not being used
  // If needed in the future, implement a function to update the hasUnseenStory flag

  // Use a ref to track if we've already processed statuses
  const statusesProcessedRef = useRef(false);
  const contactsLoadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Process statuses from users after they are loaded (only once)
  useEffect(() => {
    if (users.length > 0 && !statusesProcessedRef.current) {

      statusesProcessedRef.current = true;
      fetchStatuses();
    }
  }, [users]);

  // Function to set the current user's online status in Firebase
  const setUserOnlineStatus = async () => {
    try {
      // Get user session
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const parsedSession = JSON.parse(userSession);
      if (!parsedSession?.userId) return;

      const userId = parsedSession.userId.toString();


      // Try setting online status in multiple paths

      // 1. Try users/[userId]/status path
      try {

        const usersStatusRef = ref(db, `users/${userId}/status`);
        await set(usersStatusRef, {
          state: 'online',
          lastSeen: serverTimestamp()
        });

        // Set up onDisconnect to update status when connection is lost
        onDisconnect(usersStatusRef).update({
          state: 'offline',
          lastSeen: serverTimestamp()
        });


      } catch (usersError) {

      }

      // 2. Try online/[userId] path
      try {

        const onlineRef = ref(db, `online/${userId}`);
        await set(onlineRef, {
          online: true,
          lastSeen: serverTimestamp()
        });

        // Set up onDisconnect to update status when connection is lost
        onDisconnect(onlineRef).update({
          online: false,
          lastSeen: serverTimestamp()
        });


      } catch (onlineError) {

      }

      // 3. Try user_status/[userId] path
      try {

        const userStatusRef = ref(db, `user_status/${userId}`);
        await set(userStatusRef, {
          online: true,
          lastSeen: serverTimestamp()
        });

        // Set up onDisconnect to update status when connection is lost
        onDisconnect(userStatusRef).update({
          online: false,
          lastSeen: serverTimestamp()
        });


      } catch (userStatusError) {

      }


    } catch (error) {

    }
  };

  // Function to update user's online status based on app state
  const updateOnlineStatusBasedOnAppState = async (nextAppState: string) => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const parsedSession = JSON.parse(userSession);
      if (!parsedSession?.userId) return;

      const userId = parsedSession.userId.toString();

      if (nextAppState === 'active') {
        // User is active, set to online in all paths


        // 1. Try users/[userId]/status path
        try {
          const usersStatusRef = ref(db, `users/${userId}/status`);
          await set(usersStatusRef, {
            state: 'online',
            lastSeen: serverTimestamp()
          });
        } catch (usersError) {

        }

        // 2. Try online/[userId] path
        try {
          const onlineRef = ref(db, `online/${userId}`);
          await set(onlineRef, {
            online: true,
            lastSeen: serverTimestamp()
          });
        } catch (onlineError) {

        }

        // 3. Try user_status/[userId] path
        try {
          const userStatusRef = ref(db, `user_status/${userId}`);
          await set(userStatusRef, {
            online: true,
            lastSeen: serverTimestamp()
          });
        } catch (userStatusError) {

        }


      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // User is in background, set to offline after a delay
        setTimeout(() => {
          // Double-check the app is still in background
          if (AppState.currentState !== 'active') {


            // 1. Try users/[userId]/status path
            try {
              const usersStatusRef = ref(db, `users/${userId}/status`);
              set(usersStatusRef, {
                state: 'offline',
                lastSeen: serverTimestamp()
              });
            } catch (usersError) {

            }

            // 2. Try online/[userId] path
            try {
              const onlineRef = ref(db, `online/${userId}`);
              set(onlineRef, {
                online: false,
                lastSeen: serverTimestamp()
              });
            } catch (onlineError) {

            }

            // 3. Try user_status/[userId] path
            try {
              const userStatusRef = ref(db, `user_status/${userId}`);
              set(userStatusRef, {
                online: false,
                lastSeen: serverTimestamp()
              });
            } catch (userStatusError) {

            }


          }
        }, 5000); // 5 second delay before marking offline
      }
    } catch (error) {

    }
  };

  // Function to check Firebase database structure
  const checkFirebaseStructure = async () => {
    try {


      // Get a reference to the root of the database
      const rootRef = ref(db);
      const snapshot = await get(rootRef);
      const data = snapshot.val();



      // Check all top-level nodes


      // Check if 'users' node exists
      if (data && data.users) {


        // Check the structure of the first user
        const firstUserId = Object.keys(data.users)[0];
        if (firstUserId) {


          // Check if the user has a status node
          if (data.users[firstUserId].status) {

          } else {

          }
        }
      } else {

      }

      // Check if 'online' node exists (alternative structure)
      if (data && data.online) {


      }

      // Check if 'user_status' node exists (another alternative)
      if (data && data.user_status) {


      }
    } catch (error) {

    }
  };

  useEffect(() => {
    // Set user as online
    setUserOnlineStatus();

    // Check Firebase structure
    checkFirebaseStructure();

    // Proceed with initializations (except permissions check, which is handled after skeleton)
    fetchUnreadCounts();

    // We don't need the interval anymore since Firebase provides real-time updates
    // But we'll keep a less frequent interval for the API as a fallback
    const unreadCountsInterval = setInterval(fetchUnreadCounts, 60000);

    // Set up app state change listener
    const subscription = AppState.addEventListener('change', updateOnlineStatusBasedOnAppState);

    return () => {
      // Clean up intervals and listeners
      clearInterval(unreadCountsInterval);
      // No need to unsubscribe from online status listener
      subscription.remove();

      // Set user to offline when component unmounts
      updateOnlineStatusBasedOnAppState('background');
    };
  }, []);

  // Store online status in a ref to prevent unnecessary re-renders
  const onlineStatusCache = useRef<{[key: string]: {isOnline: boolean, timestamp: number}}>({});
  // Debounce time in milliseconds - increase to 3 seconds for more stability
  const ONLINE_STATUS_DEBOUNCE = 3000;

  useEffect(() => {
    const fetchUserStatusesFromFirebase = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) {
          return;
        }

        const parsedSession = JSON.parse(userSession);
        if (!parsedSession?.userId) {
          return;
        }

        // Try multiple Firebase paths for online status
        const tryPaths = ['users', 'online', 'user_status'];

        for (const path of tryPaths) {
          // Set up Firebase listener for the current path
          const statusRef = ref(db, path);

          // Listen for changes in real-time
          onValue(statusRef, (snapshot) => {
            const data = snapshot.val() || {};
            const updatedStatuses: { [key: string]: boolean } = {};
            const currentTime = Date.now();

            if (path === 'users') {
              // Process users path
              Object.entries(data).forEach(([userId, userData]: [string, any]) => {
                // Check if user has status data
                if (userData && userData.status) {
                  // Consider a user online if their status state is 'online'
                  updatedStatuses[userId] = userData.status.state === 'online';
                }
              });
            } else if (path === 'online') {
              // Process online path
              Object.entries(data).forEach(([userId, statusData]: [string, any]) => {
                // Check if user has online data
                if (statusData) {
                  // Consider a user online if their online property is true
                  updatedStatuses[userId] = statusData.online === true;
                }
              });
            } else if (path === 'user_status') {
              // Process user_status path
              Object.entries(data).forEach(([userId, statusData]: [string, any]) => {
                // Check if user has status data
                if (statusData) {
                  // Consider a user online if their online property is true
                  updatedStatuses[userId] = statusData.online === true;
                }
              });
            }

            // Only update users if we found some online statuses
            if (Object.keys(updatedStatuses).length > 0) {
              // Apply debounce logic to prevent rapid toggling
              const debouncedStatuses: { [key: string]: boolean } = {};

              Object.entries(updatedStatuses).forEach(([userId, isOnline]) => {
                const cachedStatus = onlineStatusCache.current[userId];

                // If no cached status or status changed and debounce time passed
                if (!cachedStatus ||
                    (cachedStatus.isOnline !== isOnline &&
                     currentTime - cachedStatus.timestamp > ONLINE_STATUS_DEBOUNCE)) {
                  // Update cache
                  onlineStatusCache.current[userId] = {
                    isOnline,
                    timestamp: currentTime
                  };
                  // Include in the update
                  debouncedStatuses[userId] = isOnline;
                }
              });

              // Only update UI if we have changes after debouncing
              if (Object.keys(debouncedStatuses).length > 0) {
                // Update users with their online status
                setUsers((prevUsers) => {
                  const updatedUsers = prevUsers.map((user) => {
                    const userId = user.id.toString();
                    // Only update if this user's status changed after debouncing
                    if (debouncedStatuses.hasOwnProperty(userId)) {
                      return {
                        ...user,
                        is_online: debouncedStatuses[userId],
                      };
                    }
                    return user;
                  });
                  return updatedUsers;
                });
              }
            }
          });
        }
      } catch (error) {
        // Error handling
      }
    };

    // Initialize Firebase status tracking
    fetchUserStatusesFromFirebase();

    // No need for interval as Firebase provides real-time updates
    // Return cleanup function to detach listeners when component unmounts
    return () => {
      // Clean up Firebase listeners if needed
      const paths = ['users', 'online', 'user_status'];
      paths.forEach(path => {
        const statusRef = ref(db, path);
        off(statusRef);
      });
    };
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    getContactsAndUsers(false);
  };

  // Memoize filtered users to prevent unnecessary re-renders
  const [filteredUsers, setFilteredUsers] = useState<MappedUser[]>([]);

  // Auto-focus search input when screen is focused
  useFocusEffect(
    useCallback(() => {
      // Focus the search input with a slight delay to ensure the UI is ready
      const timer = setTimeout(() => {
        if (searchInputRef.current) {
          // First focus to ensure the component is ready
          searchInputRef.current.focus();

          // Then dismiss and re-focus to ensure keyboard appears
          // This trick helps on some Android devices where keyboard might not show
          setTimeout(() => {
            Keyboard.dismiss();
            setTimeout(() => {
              if (searchInputRef.current) {
                searchInputRef.current.focus();
              }
            }, 50);
          }, 50);
        }
      }, 100);

      return () => clearTimeout(timer);
    }, [])
  );

  // Update filtered users when search query or users change
  useEffect(() => {
    if (searchQuery.trim() === '') {
      // If search query is empty, show all users
      setFilteredUsers(users);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = users.filter(user => {
      // Check name (most common search)
      if (user.name.toLowerCase().includes(query)) {
        return true;
      }

      // Check contact name
      if (user.contactName && user.contactName.toLowerCase().includes(query)) {
        return true;
      }

      // Check phone number (only if query has numbers)
      if (/\d/.test(query) && user.phoneNumber && user.phoneNumber.includes(query)) {
        return true;
      }

      // Check original email as last resort
      if (user.originalEmail && user.originalEmail.toLowerCase().includes(query)) {
        return true;
      }

      return false;
    });

    setFilteredUsers(filtered);
  }, [users, searchQuery]);

  const UserItem = ({ item, navigation, markMessagesAsRead }: { item: MappedUser; navigation: any; markMessagesAsRead: (id: number | string) => void }) => {
    // Convert item.id to string for consistent comparison
    const itemIdStr = item.id.toString();

    // Use useRef to store previous unread state to prevent unnecessary re-renders
    const unreadCountRef = useRef<number>(unreadCounts[itemIdStr] || firebaseUnreadCounts[itemIdStr] || 0);
    const [hasUnread, setHasUnread] = useState(unreadCountRef.current > 0);
    const [unreadCount, setUnreadCount] = useState<number>(unreadCountRef.current);

    useEffect(() => {
      // Update unread count and status when unreadCounts or firebaseUnreadCounts change
      // Only get counts for this specific user ID
      const newUnreadCount = unreadCounts[itemIdStr] || firebaseUnreadCounts[itemIdStr] || 0;

      // Always update the unread count to ensure it stays in sync with the actual data
      // This ensures that when chatting with one user, other users' unread counts remain visible
      unreadCountRef.current = newUnreadCount;
      setUnreadCount(newUnreadCount);
      setHasUnread(newUnreadCount > 0);

      // Log for debugging
      if (newUnreadCount > 0) {

      }
    }, [unreadCounts, firebaseUnreadCounts, itemIdStr, item.name]);

    // Track online status changes with a ref to prevent flickering
    const onlineStatusRef = useRef(item.is_online);

    // Update ref when online status changes
    useEffect(() => {
      // Only update if the status has actually changed
      if (onlineStatusRef.current !== item.is_online) {
        onlineStatusRef.current = item.is_online;
      }
    }, [item.is_online, itemIdStr, item.name]);

    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => {
          if (item?.id) {

            // Only try to mark messages as read for THIS SPECIFIC chat partner
            // The actual marking will happen in the markMessagesAsRead function based on online status
            markMessagesAsRead(itemIdStr);
            navigation.navigate('UserChatInbox', {
              name: item.name || 'Unknown',
              online: false, // Default to offline since we're not checking
              otherUserId: item.id,
              currentUserOnline: true, // Default to online since we're not checking
            });
          }
        }}
      >
        <View style={styles.avatarContainer}>
          <TouchableOpacity
            onPress={() => {
              if (item.hasStatus && item.statuses && item.statuses.length > 0) {
                navigation.navigate('StatusView', {
                  statuses: item.statuses,
                  userName: item.name,
                  otherUserId: item.id,
                });
              }
            }}
            style={[
              tw`rounded-full overflow-hidden`,
              item.hasStatus && styles.statusRing,
            ]}
          >
            <Image
              source={{
                uri: `https://api.dicebear.com/7.x/initials/png?seed=${item?.name || 'Unknown'}`,
              }}
              style={tw`w-full h-full rounded-full`}
            />
          </TouchableOpacity>
          {/* Online status indicator - use ref value for stability */}
          {onlineStatusRef.current && (
            <MotiView
              style={styles.onlineIndicator}
              from={{ opacity: 0.7, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'timing', duration: 500 }}
            />
          )}

        </View>
        <View style={styles.chatInfo}>
          <View style={styles.chatNameRow}>
            <Text style={[tw`capitalize`, styles.chatName]}>
              {item?.name || 'Unknown'}
            </Text>
            <Text style={styles.timestamp}>{item?.timestamp || ''}</Text>
          </View>
          <View style={styles.messageRow}>
            <View style={styles.messageStatusContainer}>
              {item.messageStatus === 'pending' && (
                <Text style={styles.pendingIndicator}>• </Text>
              )}
              <Text
                style={[
                  styles.lastMessage,
                  item.messageStatus === 'pending' ? styles.pendingMessage : null,
                  hasUnread ? styles.boldMessage : null,
                ]}
                numberOfLines={1}
              >
                {hasUnread ? `${item?.lastMessage || 'New message'}` : item?.lastMessage || ''}
              </Text>
            </View>
            {hasUnread && (
              <MotiView
                style={styles.unreadBadge}
                from={{ scale: 0.9 }}
                animate={{ scale: [0.9, 1.1, 0.9] }}
                transition={{
                  type: 'timing',
                  duration: 2000,
                  loop: true,
                }}
              >
                <Text style={styles.unreadCount}>{unreadCount > 9 ? '9+' : unreadCount}</Text>
              </MotiView>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Update renderUserItem to use the new UserItem component
  const renderUserItem = ({ item }: { item: MappedUser }) => (
    <UserItem item={item} navigation={navigation} markMessagesAsRead={markMessagesAsRead} />
  );

  const handleAddContact = async () => {
    try {
      // Validate inputs
      if (!newContact.name.trim() || !newContact.phoneNumber.trim()) {
        Alert.alert('Error', 'Please fill in all fields');
        return;
      }

      // Clean phone number
      const cleanNumber = newContact.phoneNumber
        .replace(/[\s-()]/g, '')   // Remove spaces, dashes, parentheses
        .replace(/^0+/, '');       // Remove leading zeros only

      // Validate phone number format
      if (!/^\d{10}$/.test(cleanNumber)) {
        Alert.alert('Error', 'Please enter a valid 10-digit phone number');
        return;
      }

      // Save to server
      const response = await fetch('https://nityasha.vercel.app/api/v1/contacts/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contact_name: newContact.name,
          phone_number: cleanNumber,
          // Get user ID from session
          user_id: JSON.parse(await AsyncStorage.getItem('userSession') || '{}')?.userId || ''
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save contact to server');
      }

      // Clear form and close modal
      setNewContact({ name: '', phoneNumber: '' });
      setShowAddContact(false);

      // Refresh contacts list
      await getContactsAndUsers(false);

      Alert.alert('Success', 'Contact added successfully');
    } catch (error) {

      Alert.alert('Error', 'Failed to add contact. Please try again.');
    }
  };

  const AddContactModal = () => (
    <Modal
      visible={showAddContact}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowAddContact(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowAddContact(false)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={e => e.stopPropagation()}
          >
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Add New Contact</Text>
                <TouchableOpacity
                  onPress={() => setShowAddContact(false)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <AntDesign name="close" size={24} color="#075E54" />
                </TouchableOpacity>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Name</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Enter contact name"
                  placeholderTextColor="#999"
                  value={newContact.name}
                  onChangeText={(text) => setNewContact(prev => ({ ...prev, name: text }))}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Phone Number</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Enter 10-digit number"
                  placeholderTextColor="#999"
                  value={newContact.phoneNumber}
                  onChangeText={(text) => setNewContact(prev => ({ ...prev, phoneNumber: text }))}
                  keyboardType="phone-pad"
                  maxLength={10}
                />
              </View>

              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddContact}
                activeOpacity={0.8}
              >
                <Text style={styles.addButtonText}>Add Contact</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  // First show skeleton loading state
  const [showSkeleton, setShowSkeleton] = useState(true);

  // Effect to handle initial loading sequence
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Check AsyncStorage for permission status first
        const storedPermission = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);

        // Show skeleton for a minimum time (500ms) to avoid flickering
        const skeletonTimer = setTimeout(() => {
          setShowSkeleton(false);

          if (storedPermission === 'true') {
            // If permission was already granted, skip the request UI and load contacts
            setPermissionsGranted(true);
            getContactsAndUsers();

            // Verify permission in the background without UI disruption
            Contacts.getPermissionsAsync().then(({ status }) => {
              if (status !== 'granted') {
                // If permission was revoked in settings, update AsyncStorage silently
                AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'false');
                // Don't update UI here to avoid disruption - will be handled on next app open
              }
            }).catch(() => {
              // Handle error silently
            });
          } else {
            // If no stored permission or it was denied, check and request it
            checkAndRequestPermissions();
          }
        }, 500);

        return () => clearTimeout(skeletonTimer);
      } catch (error) {
        // If there's an error reading from AsyncStorage, fall back to normal permission flow
        setShowSkeleton(false);
        checkAndRequestPermissions();
      }
    };

    initializeApp();
  }, []);

  // Show skeleton loading state first
  if (showSkeleton) {
    return  <ImageBackground source={require('@/assets/screens/screen7th.png')} style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>

           <StatusBar backgroundColor="#C5E8C4" />
         
          </ImageBackground>;
  }

  // Then check permissions
  if (!permissionsGranted) {
    return <PermissionRequest />;
  }

  // Then show loading state if needed
  if (loading) {
    return (
      <ImageBackground source={require('@/assets/screens/screen7th.png')} style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>

      <StatusBar backgroundColor="#C5E8C4"  />
     
    </ImageBackground>
    );
  }

  // Finally render the main content
  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={styles.container}>
      <StatusBar backgroundColor="#C5E8C4" barStyle="dark-content" />
      <View style={styles.header}>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>
            Chats
          </Text>
          {/* Online status indicator removed */}
        </View>
        <View style={styles.headerIcons}>
          <TouchableOpacity
            style={styles.headerIcon}
            onPress={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? (
              <ActivityIndicator size="small" color="#075E54" />
            ) : (
              <AntDesign name="reload1" size={24} color="#075E54" />
            )}
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerIcon} onPress={() => navigation.navigate('Profile')}>
            <AntDesign name="user" size={24} color="#075E54" />
          </TouchableOpacity>
        </View>
      </View>
      <AddContactModal />

      <Searchbar
        placeholder="Search"
        onChangeText={setSearchQuery}
        value={searchQuery}
        ref={searchInputRef}
        autoFocus={true}
        style={[tw`mx-3 my-5 rounded-full`,{borderRadius: 300,
          elevation: 3, // Shadow effect
          backgroundColor: "#F5FCF5",}]}
        iconColor="#7f8c8d"
        placeholderTextColor="#999"
      />
      {filteredUsers.length === 0 ? (
        <View style={styles.noContacts}>
          <Text style={styles.noContactsText}>No contacts found</Text>
        </View>
      ) : (
        <View style={{ flex: 1 }}>
          {/* <Status
            userStatuses={statuses}
            users={users}
            onStatusUpdate={() => {
              // Refresh statuses
              fetchStatuses();
            }}
          /> */}
          <FlatList
            data={filteredUsers}
            renderItem={renderUserItem}
            keyExtractor={item => item.id.toString()}
            onScroll={onScroll}
            style={[tw`pb-20`, { flex: 1 }]}
            contentContainerStyle={{ paddingBottom: 20 }}
            showsVerticalScrollIndicator={true}
            scrollEnabled={true}
            initialNumToRender={10}
            maxToRenderPerBatch={20}
            windowSize={21}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#075E54']}
                tintColor="#075E54"
              />
            }
          />
        </View>
      )}
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  statusRing: {
    borderWidth: 2,
    borderColor: '#075E54',
    padding: 2,
  },
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'column',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#075E54', // WhatsApp green
  },
  onlineStatusIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  onlineStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  onlineStatusText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  headerIcons: {
    flexDirection: 'row',
  },
  headerIcon: {
    padding: 8,
    marginLeft: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    margin: 16,
    padding: 2,
    borderRadius: 110,
    alignSelf: 'center',
  },
  searchIcon: {
    marginHorizontal: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  chatItem: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    width: 50,
    height: 50,
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarImage: {
    width: 50,
    height: 50,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  chatInfo: {
    flex: 1,
  },
  chatNameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  noContacts: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noContactsText: {
    fontSize: 16,
    color: '#666',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#075E54',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  navItemCenter: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  centerButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  navText: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  activeNavText: {
    fontSize: 12,
    color: '#075E54',
    marginTop: 4,
    fontWeight: '500',
  },
  activeNavItem: {
    borderBottomWidth: 0,
  },
  chatBadge: {
    backgroundColor: '#E7F3E2',
    padding: 8,
    borderRadius: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
  },
  modalContainer: {
    margin: 20,
    backgroundColor: 'transparent',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#075E54',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#075E54',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 10,
    elevation: 2,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  permissionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#075E54',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: '#075E54',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },

  messageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  unreadBadge: {
    backgroundColor: '#075E54',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginLeft: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  unreadCount: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Last message style is already defined above
  messageStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  pendingIndicator: {
    color: '#007AFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 2,
  },
  pendingMessage: {
    color: '#075E54',
    fontWeight: '500',
  },
  boldMessage: {
    fontWeight: 'bold',
    color: '#000',
  },
  onlineStatus: {
    color: '#075E54',
    fontSize: 12,
    marginLeft: 8,
  },

});

export default ChatListScreen;
