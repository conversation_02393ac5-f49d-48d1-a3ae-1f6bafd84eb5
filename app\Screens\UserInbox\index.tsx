import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  Alert,
  Platform,
  ActivityIndicator,
  RefreshControl,
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
  Dimensions,
  BackHandler,
  AppState,
  AppStateStatus,
  StatusBar,
  SafeAreaView,
  Modal,
  Pressable,
  Linking,
  ImageBackground,
} from 'react-native';
import { useNavigation, useFocusEffect, CommonActions } from '@react-navigation/native';
import { Ionicons, MaterialIcons, FontAwesome5, AntDesign } from '@expo/vector-icons';
import { db } from '@/lib/Firebase';
import { ref, onValue, set, push, update, remove, query, orderByChild, get, serverTimestamp, onDisconnect, off } from 'firebase/database';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Contacts from 'expo-contacts';
import { <PERSON><PERSON>, <PERSON>vide<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ider, Searchbar } from 'react-native-paper';
import tw from 'twrnc';
import { KeyboardController } from 'react-native-keyboard-controller';
import UserInboxSkeleton from '../../components/UserInboxSkeleton';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Status from '@/components/Status';
import { Camera, ChevronLeft, EllipsisVertical, Search, Settings } from 'lucide-react-native';
import * as Notifications from 'expo-notifications';
import { TouchableRipple } from 'react-native-paper';
import { Skeleton } from 'moti/skeleton';
import { AnimatePresence, MotiText, MotiView } from 'moti';
import * as Crypto from 'expo-crypto';

// Constants for AsyncStorage keys
const PERMISSION_GRANTED_KEY = 'contacts_permission_granted';
const CACHED_USERS_KEY = 'cached_userss';
const CACHED_STATUSES_KEY = 'cached_statusess';
const CACHED_UNREAD_COUNTS_KEY = 'cached_unread_countss';
const CACHED_CONTACTS_KEY = 'cached_contactss';

type NavigationProp = NativeStackNavigationProp<any>;

interface Status {
  id: number;
  user_id: string;
  media_url: string;
  created_at: string;
}

interface Message {
  text: string;
  timestamp: number;
  senderId: string;
  status: 'pending' | 'sent' | 'read';
  originalMessage?: string;  // Original message if it was filtered
  wasFiltered?: boolean;     // Flag to indicate if message was filtered
  encrypted?: boolean;      // Flag to indicate if message is encrypted
}

interface MappedUser {
  id: number;
  name: string;
  initial: string;
  contactName?: string;
  phoneNumber?: string;
  originalEmail?: string;
  lastMessage: string;
  timestamp: number;
  messageStatus?: 'pending' | 'sent' | 'read';
  unreadCount?: number;
  hasStatus?: boolean;
  statuses?: Status[];
  hasUnseenStory: boolean;
  is_online?: boolean; // Add online status property
}

// Update Contact interface to match expo-contacts types
interface Contact {
  name: string | null;
  phoneNumbers?: {
    number: string | undefined;
    id: string;
    label: string;
  }[];
}

// Add cleanPhoneNumber function at the top level
const cleanPhoneNumber = (number: string | undefined): string => {
  if (!number) return '';
  let cleaned = number.replace(/\D/g, '');
  if (cleaned.startsWith('91') && cleaned.length > 10) {
    cleaned = cleaned.substring(2);
  }
  cleaned = cleaned.replace(/^0+/, '');
  return cleaned;
};

// Add bad words list
const BAD_WORDS = [
  'kill', 'rape', 'murder', 'suicide', 'terrorist', 'bomb', 'drugs', 'cocaine', 'heroin',
  'porn', 'sex', 'nude', 'naked', 'pussy', 'dick', 'cock', 'fuck', 'shit', 'asshole',
  'bitch', 'whore', 'slut', 'bastard', 'damn', 'hell', 'crap',
  'retard', 'terror', 'weapon', 'gun', 'shoot', 'blood', 'gore',
   'harass', 'stalk', 'hack', 'virus', 'malware', 'scam', 'fraud',
  'racist', 'sexist', 'homophobic', 'transphobic', 'bigot', 'discriminate'
];

// Add function to filter bad words
const filterBadWords = (text: string): string => {
  if (!text) return text;
  
  let filteredText = text.toLowerCase();
  
  // Replace bad words with asterisks
  BAD_WORDS.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    filteredText = filteredText.replace(regex, '*'.repeat(word.length));
  });
  
  // Also check for variations with numbers or special characters
  BAD_WORDS.forEach(word => {
    const variations = [
      word.replace(/a/g, '4'),
      word.replace(/a/g, '@'),
      word.replace(/i/g, '1'),
      word.replace(/e/g, '3'),
      word.replace(/o/g, '0'),
      word.replace(/s/g, '5'),
      word.replace(/s/g, '$'),
      word.replace(/t/g, '7'),
      word.replace(/l/g, '1'),
      word.replace(/g/g, '9'),
      word.replace(/b/g, '8')
    ];
    
    variations.forEach(variation => {
      const regex = new RegExp(`\\b${variation}\\b`, 'gi');
      filteredText = filteredText.replace(regex, '*'.repeat(word.length));
    });
  });
  
  return filteredText;
};

// Add function to check if message contains bad words
function makeFlexibleRegex(word: string) {
  // Allow any non-word character (space, dot, dash, etc.) between letters
  return word.split('').map(char => `[${char}]\\W*`).join('');
}

const containsBadWords = (text: string): boolean => {
  if (!text) return false;
  const lowerText = text.toLowerCase();

  for (const word of BAD_WORDS) {
    // 1. Direct match (with word boundaries)
    const direct = new RegExp(`\\b${word}\\b`, 'gi');
    if (direct.test(lowerText)) return true;

    // 2. Leet variations
    const variations = [
      word.replace(/a/g, '4'),
      word.replace(/a/g, '@'),
      word.replace(/i/g, '1'),
      word.replace(/e/g, '3'),
      word.replace(/o/g, '0'),
      word.replace(/s/g, '5'),
      word.replace(/s/g, '\\$'),
      word.replace(/t/g, '7'),
      word.replace(/l/g, '1'),
      word.replace(/g/g, '9'),
      word.replace(/b/g, '8')
    ];
    for (const variation of variations) {
      const leet = new RegExp(`\\b${variation}\\b`, 'gi');
      if (leet.test(lowerText)) return true;
    }

    // 3. Flexible (spaces/symbols between letters)
    const flexible = new RegExp(makeFlexibleRegex(word), 'gi');
    if (flexible.test(lowerText)) return true;

    // 4. Flexible leet
    for (const variation of variations) {
      const flexibleLeet = new RegExp(makeFlexibleRegex(variation), 'gi');
      if (flexibleLeet.test(lowerText)) return true;
    }
  }
  return false;
};

// Modify the sendMessage function
const sendMessage = async (chatId: string, message: string, senderId: string) => {
  try {
    // Check for bad words before sending
    if (containsBadWords(message)) {
      // Instead of blocking, replace with "Hi"
      const messageRef = ref(db, `user_messages/${chatId}`);
      const newMessageRef = push(messageRef);
      
      await set(newMessageRef, {
        text: "Hi",
        senderId: senderId,
        timestamp: Date.now(),
        status: 'sent',
        originalMessage: message, // Store original message for reference
        wasFiltered: true // Flag to indicate this was a filtered message
      });

      return true;
    }

    // If message is clean, proceed with sending
    const messageRef = ref(db, `user_messages/${chatId}`);
    const newMessageRef = push(messageRef);
    
    await set(newMessageRef, {
      text: message,
      senderId: senderId,
      timestamp: Date.now(),
      status: 'sent',
      wasFiltered: false
    });

    return true;
  } catch (error) {
    console.error('Error sending message:', error);
    return false;
  }
};

// Define styles at the top level
const styles = StyleSheet.create({
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#075E54',
    textAlign: 'center',
  },
  statusRing: {
    borderWidth: 2,
    borderColor: '#075E54',
    padding: 2,
  },
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'column',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#075E54', // WhatsApp green
  },
  onlineStatusIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  onlineStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  onlineStatusText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  headerIcons: {
    flexDirection: 'row',
  },
  headerIcon: {
    padding: 8,
    marginLeft: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    margin: 16,
    padding: 2,
    borderRadius: 110,
    alignSelf: 'center',
  },
  searchIcon: {
    marginHorizontal: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  chatItem: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    width: 50,
    height: 50,
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarImage: {
    width: 50,
    height: 50,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  chatInfo: {
    flex: 1,
  },
  chatNameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  noContacts: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noContactsText: {
    fontSize: 16,
    color: '#666',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  navItemCenter: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  centerButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  navText: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  activeNavText: {
    fontSize: 12,
    color: '#075E54',
    marginTop: 4,
    fontWeight: '500',
  },
  activeNavItem: {
    borderBottomWidth: 0,
  },
  chatBadge: {
    backgroundColor: '#E7F3E2',
    padding: 8,
    borderRadius: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
  },
  modalContainer: {
    margin: 20,
    backgroundColor: 'transparent',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#075E54',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#075E54',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 10,
    elevation: 2,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  permissionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#075E54',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: '#075E54',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },

  messageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  unreadBadge: {
    backgroundColor: '#075E54',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginLeft: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  unreadCount: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Last message style is already defined above
  messageStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  pendingIndicator: {
    color: '#007AFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 2,
  },
  pendingMessage: {
    color: '#075E54',
    fontWeight: '500',
  },
  boldMessage: {
    fontWeight: 'bold',
    color: '#000',
  },
  onlineStatus: {
    color: '#075E54',
    fontSize: 12,
    marginLeft: 8,
  },
});

const ChatListScreen = () => {
  const navigation = useNavigation<NavigationProp>();
  const [users, setUsers] = useState<MappedUser[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [showAddContact, setShowAddContact] = useState(false);
  const [newContact, setNewContact] = useState({
    name: '',
    phoneNumber: ''
  });
  const [permissionsGranted, setPermissionsGranted] = useState(false);
 const [visible, setVisible] = React.useState(false);
 const [modelSelectVisible, setModelSelectVisible] = useState(false)
 const [selectedModel, setSelectedModel] = useState('Neorox Flash') // Default
  const openMenu = () => setVisible(true);

  const closeMenu = () => setVisible(false);

  // Add proper type for unread counts
  interface UnreadCounts {
    [key: string]: number;
  }

  // Initialize state with empty object
  const [unreadCounts, setUnreadCounts] = useState<UnreadCounts>({});

  // Firebase state
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [firebaseUnreadCounts, setFirebaseUnreadCounts] = useState<{[key: string]: number}>({});

  // Add new state for tracking data loading
  const [isCachedDataLoaded, setIsCachedDataLoaded] = useState(false);
  const [isLoadingFreshData, setIsLoadingFreshData] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Add connection state tracking
  const [isConnected, setIsConnected] = useState(true);

  // Add preload ref to prevent multiple preloads
  const preloadRef = useRef(false);
  const initialLoadTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Memoize filtered users to prevent unnecessary re-renders
  const [filteredUsers, setFilteredUsers] = useState<MappedUser[]>([]);

  // Restore notification configuration
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });

  // Restore notification handling functions
  const sendPushNotification = async (pushToken: string, title: string, body: string) => {
    try {
      const message = {
        to: pushToken,
        sound: 'default',
        title: title,
        body: body,
        data: { someData: 'goes here' },
      };

      await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });
    } catch (error) {
      console.error('Error sending push notification:', error);
    }
  };

  const registerForPushNotifications = async () => {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      const token = (await Notifications.getExpoPushTokenAsync()).data;
      return token;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  };

  // Restore notification setup useEffect
  useEffect(() => {
    registerForPushNotifications();
  }, []);

  // Modify the initial loading effect
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load all cached data first
        const [cachedUsers, cachedUnreadCounts, cachedStatuses] = await Promise.all([
          AsyncStorage.getItem(CACHED_USERS_KEY),
          AsyncStorage.getItem(CACHED_UNREAD_COUNTS_KEY),
          AsyncStorage.getItem(CACHED_STATUSES_KEY)
        ]);

        if (cachedUsers) {
          const parsedUsers = JSON.parse(cachedUsers);
          // If firebaseLastMessages already loaded, merge, else set as is
          setUsers(prev => mergeFirebaseLastMessages(parsedUsers, firebaseLastMessages));
          setFilteredUsers(prev => mergeFirebaseLastMessages(parsedUsers, firebaseLastMessages));
          setIsCachedDataLoaded(true);
          console.log('Loaded cached users. Sample user lastMessage:', parsedUsers.length > 0 ? parsedUsers[0].lastMessage : 'N/A'); // Log cached last message
        } else {
           console.log('No cached users found.');
        }

        if (cachedUnreadCounts) {
          const parsedUnreadCounts = JSON.parse(cachedUnreadCounts);
          setUnreadCounts(parsedUnreadCounts); // Set unread counts from cache immediately
          console.log('Loaded cached unread counts:', parsedUnreadCounts);
        } else {
           console.log('No cached unread counts found.');
        }

        if (cachedStatuses) {
          setStatuses(JSON.parse(cachedStatuses));
          console.log('Loaded cached statuses.');
        } else {
           console.log('No cached statuses found.');
        }

        // Check for new contacts and fetch fresh data in background
        checkForNewContacts();



      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setIsInitialLoad(false);
        console.log('Initial data load attempt finished.');
      }
    };

    loadInitialData();
  }, []); // Empty dependency array to run once on mount

  // Add separate useEffect to fetch initial last messages when users are loaded
  useEffect(() => {
    const fetchInitialMessages = async () => {
      if (users.length > 0 && isCachedDataLoaded) {
        try {
          const userSession = await AsyncStorage.getItem('userSession');
          if (userSession) {
            const parsedSession = JSON.parse(userSession);
            if (parsedSession?.userId) {
              console.log('Fetching initial last messages for loaded users...');
              await fetchInitialLastMessages(parsedSession.userId.toString(), users);
            }
          }
        } catch (error) {
          console.error('Error fetching initial messages:', error);
        }
      }
    };

    fetchInitialMessages();
  }, [users.length, isCachedDataLoaded]); // Trigger when users are loaded

  // Add this state at the top, after other useStates
  const [firebaseLastMessages, setFirebaseLastMessages] = useState<{ [id: string]: { lastMessage: string, timestamp: number, messageStatus?: 'pending' | 'sent' | 'read' } }>({});

  // Helper function to merge firebaseLastMessages into users
  const mergeFirebaseLastMessages = (users: MappedUser[], firebaseLastMessages: { [id: string]: { lastMessage: string, timestamp: number, messageStatus?: 'pending' | 'sent' | 'read' } }) => {
    return users.map(user => {
      const fbMsg = firebaseLastMessages[user.id];
      return {
        ...user,
        lastMessage: fbMsg?.lastMessage ?? user.lastMessage ?? '',
        timestamp: fbMsg?.timestamp ?? user.timestamp ?? 0,
        messageStatus: fbMsg?.messageStatus ?? user.messageStatus,
      };
    });
  };

  // Modify the checkForNewContacts function to handle duplicates and preserve last message/timestamp
  const checkForNewContacts = async () => {
    // Add this line at the beginning to indicate loading
    if (!isCachedDataLoaded) setIsLoadingFreshData(true);
    console.log('Checking for new contacts and fresh data...');
    try {
      // Check permissions first
      const storedPermission = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);
      if (storedPermission !== 'true') {
        const { status } = await Contacts.getPermissionsAsync();
        if (status !== 'granted') {
          setPermissionsGranted(false);
          return;
        }
      }

      // Load fresh contacts and API data in parallel
      const [contactsResult, apiResponse] = await Promise.all([
        Contacts.getContactsAsync({
          fields: [Contacts.Fields.PhoneNumbers, Contacts.Fields.Name],
        }),
        fetch('https://nityasha.vercel.app/api/v1/users/get')
      ]);

      if (!apiResponse.ok) {
        throw new Error('Network response was not ok');
      }

      const deviceContacts = contactsResult.data;
      const apiUsers = await apiResponse.json();
      console.log('Fetched fresh contacts and API users.');

      // Process data
      const contactPhoneNumbers = processContacts(deviceContacts as Contact[]);
      const matchedUsers = processApiUsers(apiUsers, contactPhoneNumbers);

      // Create a Map of existing users for faster lookup
      const existingUsersMap = new Map(users.map(user => [user.id, user]));

      // Filter out duplicates and merge with existing users, prioritizing Firebase lastMessage/timestamp if available
      const updatedUsers = matchedUsers.map(newUser => {
        const existingUser = existingUsersMap.get(newUser.id);
        if (existingUser) {
          // Merge new data with existing user, prioritizing Firebase lastMessage/timestamp if available
          const mergedUser = {
            ...existingUser, // Start with existing user data (including lastMessage, timestamp from cache)
            ...newUser,      // Overlay new data from API
            // Priority: Firebase > existingUser > newUser > default
            lastMessage: firebaseLastMessages[newUser.id]?.lastMessage || existingUser.lastMessage || newUser.lastMessage || '',
            timestamp: firebaseLastMessages[newUser.id]?.timestamp || existingUser.timestamp || newUser.timestamp || 0,
            messageStatus: firebaseLastMessages[newUser.id]?.messageStatus || existingUser.messageStatus || newUser.messageStatus,
            unreadCount: unreadCounts[newUser.id.toString()] || existingUser.unreadCount || newUser.unreadCount
          };
          return mergedUser;
        }
        // For new users, use the data from newUser, defaulting lastMessage/timestamp if needed
        const newUserWithDefaults = {
           ...newUser,
           lastMessage: firebaseLastMessages[newUser.id]?.lastMessage || newUser.lastMessage || '',
           timestamp: firebaseLastMessages[newUser.id]?.timestamp || newUser.timestamp || 0,
           messageStatus: firebaseLastMessages[newUser.id]?.messageStatus || newUser.messageStatus,
           unreadCount: unreadCounts[newUser.id.toString()] || newUser.unreadCount // Use cached or new unread count
        };
        return newUserWithDefaults;
      });

      // Update state with merged users
      const mergedUsers = mergeFirebaseLastMessages(updatedUsers, firebaseLastMessages);
      setUsers(mergedUsers);
      setFilteredUsers(mergedUsers);
      console.log('Users state updated with fresh data. Sample updated user lastMessage:', mergedUsers.length > 0 ? mergedUsers[0].lastMessage : 'N/A'); // Log updated last message

      // Update cache in background
      Promise.all([
        saveContactsToCache(deviceContacts),
        // Pass the updated users and current unread counts and statuses to cache
        saveDataToCache(mergedUsers, statuses, unreadCounts),
        // We don't need to call fetchUnreadCounts or fetchStatuses here explicitly,
        // as the real-time listeners and initial load handle this.
      ]).catch(error => {
        console.error('Error updating cache:', error);
      });

    } catch (error) {
      console.error('Error checking for new contacts:', error);
    } finally {
      // Add this line in the finally block to indicate loading is done
      setIsLoadingFreshData(false);
      console.log('Finished checking for new contacts.');
    }
  };

  // Optimize loadFreshData to be faster - use similar merging logic
  const loadFreshData = async () => {
    // Add this line at the beginning to indicate loading
    if (!isCachedDataLoaded) setIsLoadingFreshData(true);
     console.log('Loading fresh data...');
    try {
      // Check permissions first
      const storedPermission = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);
      if (storedPermission !== 'true') {
        const { status } = await Contacts.getPermissionsAsync();
        if (status !== 'granted') {
          setPermissionsGranted(false);
          return;
        }
      }

      // Load fresh contacts and API data in parallel
      const [contactsResult, apiResponse] = await Promise.all([
        Contacts.getContactsAsync({
          fields: [Contacts.Fields.PhoneNumbers, Contacts.Fields.Name],
        }),
        fetch('https://nityasha.vercel.app/api/v1/users/get')
      ]);

      if (!apiResponse.ok) {
        throw new Error('Network response was not ok');
      }

      const deviceContacts = contactsResult.data;
      const apiUsers = await apiResponse.json();
       console.log('Fetched fresh contacts and API users for loadFreshData.');

      // Process data
      const contactPhoneNumbers = processContacts(deviceContacts as Contact[]);
      const matchedUsers = processApiUsers(apiUsers, contactPhoneNumbers);

      // Create a Map of existing users for faster lookup
      const existingUsersMap = new Map(users.map(user => [user.id, user]));

      // Update state immediately, merging with existing users and prioritizing Firebase lastMessage/timestamp
      const usersWithMergedData = matchedUsers.map(newUser => {
          const existingUser = existingUsersMap.get(newUser.id);
           if (existingUser) {
               const mergedUser = {
                  ...existingUser, // Start with existing user data (including lastMessage, timestamp from current state/cache)
                  ...newUser,      // Overlay new data from API
                  // Priority: Firebase > existingUser > newUser > default
                  lastMessage: firebaseLastMessages[newUser.id]?.lastMessage || existingUser.lastMessage || newUser.lastMessage || '',
                  timestamp: firebaseLastMessages[newUser.id]?.timestamp || existingUser.timestamp || newUser.timestamp || 0,
                  messageStatus: firebaseLastMessages[newUser.id]?.messageStatus || existingUser.messageStatus || newUser.messageStatus,
                  unreadCount: unreadCounts[newUser.id.toString()] || existingUser.unreadCount || newUser.unreadCount
               };
                console.log(`Merging user ${mergedUser.name} (ID: ${mergedUser.id}) in loadFreshData. Existing lastMessage: "${existingUser.lastMessage}", New lastMessage: "${newUser.lastMessage}", Merged lastMessage: "${mergedUser.lastMessage}"`); // Log merge
               return mergedUser;
           }
           // For new users from API, use the data with defaults
            const newUserWithDefaults = {
                ...newUser,
                lastMessage: firebaseLastMessages[newUser.id]?.lastMessage || newUser.lastMessage || '',
                timestamp: firebaseLastMessages[newUser.id]?.timestamp || newUser.timestamp || 0,
                messageStatus: firebaseLastMessages[newUser.id]?.messageStatus || newUser.messageStatus,
                unreadCount: unreadCounts[newUser.id.toString()] || newUser.unreadCount // Use cached or new unread count
             };
              console.log(`Adding new user ${newUserWithDefaults.name} (ID: ${newUserWithDefaults.id}) in loadFreshData. lastMessage: "${newUserWithDefaults.lastMessage}"`); // Log new user
             return newUserWithDefaults;
      });

      const mergedUsers = mergeFirebaseLastMessages(usersWithMergedData, firebaseLastMessages);
      setUsers(mergedUsers);
      setFilteredUsers(mergedUsers);
      console.log('Users state updated in loadFreshData. Sample updated user lastMessage:', mergedUsers.length > 0 ? mergedUsers[0].lastMessage : 'N/A'); // Log updated last message

      // Update cache in background
      Promise.all([
        saveContactsToCache(deviceContacts),
        // Pass the fresh users data but existing unread counts and statuses to cache
        saveDataToCache(mergedUsers, statuses, unreadCounts),
        // No need to fetch unread counts or statuses here, listeners handle it
      ]).catch(error => {
        console.error('Error updating cache:', error);
      });

    } catch (error) {
      console.error('Error loading fresh data:', error);
    } finally {
      // Add this line in the finally block to indicate loading is done
      setIsLoadingFreshData(false);
       console.log('Finished loading fresh data.');
    }
  };

  // Update filtered users when search query or users change
  useEffect(() => {
    // Only filter if cached data is loaded or it's not the initial load anymore
    if (!isCachedDataLoaded && isInitialLoad) return;

    console.log('Updating filtered users...');
    const sortedUsers = [...users]
      // No filter on lastMessage here; show all users
      .sort((a, b) => {
        const aUnread = unreadCounts[a.id.toString()] || 0;
        const bUnread = unreadCounts[b.id.toString()] || 0;

        // Prioritize users with unread messages
        if (aUnread > 0 && bUnread === 0) return -1;
        if (aUnread === 0 && bUnread > 0) return 1;

        // Otherwise, sort by timestamp (latest first)
        const timeA = typeof a.timestamp === 'number' ? a.timestamp : (a.timestamp ? new Date(a.timestamp).getTime() : 0);
        const timeB = typeof b.timestamp === 'number' ? b.timestamp : (b.timestamp ? new Date(b.timestamp).getTime() : 0);
        return timeB - timeA;
      });

    if (searchQuery.trim() === '') {
      // If search query is empty, show all users sorted
      setFilteredUsers(sortedUsers);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = sortedUsers.filter(user => {
      // Check name (most common search)
      if (user.name.toLowerCase().includes(query)) {
        return true;
      }

      // Check contact name
      if (user.contactName && user.contactName.toLowerCase().includes(query)) {
        return true;
      }

      // Check phone number (only if query has numbers)
      if (/\d/.test(query) && user.phoneNumber && user.phoneNumber.includes(query)) {
        return true;
      }

      // Check original email as last resort
      if (user.originalEmail && user.originalEmail.toLowerCase().includes(query)) {
        return true;
      }

      return false;
    });

    setFilteredUsers(filtered);
  }, [users, searchQuery, unreadCounts, isCachedDataLoaded, isInitialLoad]); // Added isInitialLoad dependency

  const UserItem = React.memo(({ item, navigation, markMessagesAsRead }: { item: MappedUser; navigation: any; markMessagesAsRead: (id: number | string) => void }) => {
    const itemIdStr = item.id.toString();
    // The unread count should primarily come from the unreadCounts state in the parent
    const unreadCount = unreadCounts[itemIdStr] || 0;
    const hasUnread = unreadCount > 0;

    // Add logs to see when UserItem is rendering and with what count
    console.log(`Rendering UserItem ${item.name} (${itemIdStr}): hasUnread=${hasUnread}, unreadCount=${unreadCount}, timestamp: ${item.timestamp}`);

    // Format the timestamp for display
    const formatTimestamp = (timestamp: string | number): string => {
        if (!timestamp) return '';

        // Ensure we are working with a number for Date constructor
        const numericTimestamp = typeof timestamp === 'number' ? timestamp : (new Date(timestamp).getTime() || 0);

        // Check if the numeric timestamp is valid before creating Date object
        if (numericTimestamp === 0 || isNaN(numericTimestamp)) {
            return ''; // Return empty string for invalid or missing timestamps
        }

        const messageDate = new Date(numericTimestamp);

        const now = new Date();
        const isToday = messageDate.toDateString() === now.toDateString();
        const isYesterday = new Date(now.setDate(now.getDate() - 1)).toDateString() === messageDate.toDateString();

        if (isToday) {
          return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (isYesterday) {
          return 'Yesterday';
        } else {
          return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    };


    return (
      <TouchableRipple
        onPress={() => {
  markMessagesAsRead(item.id);

  navigation.dispatch(
    CommonActions.navigate({
      name: 'UserChatInbox',
      params: {
        otherUserId: item.id,
        name: item.name,
      }
    })
  );
}}
        onLongPress={() => handleUserLongPress(item)}
        rippleColor="rgba(0, 0, 0, .22)"  
        style={tw`flex-row items-center justify-between py-3 px-4`}
      >
        <View style={tw`flex-row items-center flex-1`}>
          <View style={tw`relative`}>
            {/* User status ring conditional rendering */}
            {item.hasStatus && item.statuses && item.statuses.length > 0 ? (
              <TouchableOpacity
                onPress={() => {
                  // Navigate to Status screen
                  navigation.navigate('StatusView', { userId: item.id });
                }}
                style={tw`p-[2px] rounded-full ${item.hasUnseenStory ? 'border-2 border-green-500' : 'border border-gray-300'}`}
              >
                <View style={tw`w-12 h-12 rounded-full bg-gray-200 items-center justify-center overflow-hidden`}>
                  {/* You can add a placeholder image or icon here if no status media */}
                  {item.statuses[0]?.media_url ? (
                     <Image source={{ uri: item.statuses[0].media_url }} style={tw`w-full h-full`} />
                  ) : (
                     <Text style={tw`text-lg font-bold`}>{item.initial}</Text>
                  )}
                </View>
              </TouchableOpacity>
            ) : (
              // Default avatar if no status
              <View style={tw`w-12 h-12 rounded-full bg-gray-200 items-center justify-center`}>
                <Text style={tw`text-lg font-bold`}>{item.initial}</Text>
              </View>
            )}
          </View>
        

          <View style={tw`ml-3 flex-1`}>
            <View style={tw`flex-row items-center justify-between`}>
              <Text style={tw`text-base font-semibold`}>{item.name}</Text>
              <Text style={tw`text-xs text-gray-500`}>{formatTimestamp(item.timestamp)}</Text>
            </View>
            <View style={tw`flex-row items-center justify-between mt-1`}>
              <Text
                style={[
                  tw`text-sm flex-1 mr-2`,
                  hasUnread ? tw`text-black font-semibold` : tw`text-gray-500`
                ]}
                numberOfLines={1}
              >
                {(item.lastMessage && item.lastMessage.trim().toLowerCase() !== 'hey there!') 
                  ? item.lastMessage 
                  : 'No messages yet'}
              </Text>
              {/* Unread badge conditional rendering */}
              {typeof unreadCount === 'number' && unreadCount > 0 && (
                <View style={tw`bg-green-500 rounded-full min-w-[20px] h-5 items-center justify-center px-1`}>
                  <Text style={tw`text-white text-xs font-bold`}>{unreadCount || 0 }</Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </TouchableRipple>
    );
  });

  // Update renderUserItem to use the new UserItem component
  const renderUserItem = useCallback(({ item }: { item: MappedUser }) => (
    <UserItem item={item} navigation={navigation} markMessagesAsRead={markMessagesAsRead} />
  ), [navigation, unreadCounts]); // Added unreadCounts dependency here

  // Add handleSearch function
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    // Filtering logic is now handled by the filteredUsers useEffect
  }, []);

  const openSettings = () => {
    Linking.openSettings();
  };

  const requestContactPermissions = async () => {
    try {
      // First check if we have stored permission in AsyncStorage
      const storedPermission = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);

      if (storedPermission === 'true') {
        // If we have stored permission, use it without prompting
        setPermissionsGranted(true);
        return true;
      }

      // If no stored permission, check if we already have system permissions
      const { status: existingStatus } = await Contacts.getPermissionsAsync();

      if (existingStatus === 'granted') {
        // Save permission to AsyncStorage for future use
        await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'true');
        setPermissionsGranted(true);
        return true;
      }

      // If not, request permissions
      const { status } = await Contacts.requestPermissionsAsync();

      if (status === 'granted') {
        // Save permission to AsyncStorage for future use
        await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'true');
        setPermissionsGranted(true);
        return true;
      }

      // Permission was denied
      setPermissionsGranted(false);
      await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'false');

      // Show alert with option to open settings
      Alert.alert(
        'Permission Required',
        'This app needs access to contacts to show you which of your contacts are using this app.',
        [
          {
            text: 'Not Now',
            style: 'cancel'
          },
          {
            text: 'Open Settings',
            onPress: openSettings
          }
        ]
      );
      return false;
    } catch (error) {
      setPermissionsGranted(false);
      return false;
    }
  };

  const checkAndRequestPermissions = async () => {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status === 'granted') {
        setPermissionsGranted(true);
        await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'true');
        // Load cached data immediately
        const cachedUsers = await AsyncStorage.getItem(CACHED_USERS_KEY);
        if (cachedUsers) {
          const parsedUsers = JSON.parse(cachedUsers);
          setUsers(parsedUsers);
          setFilteredUsers(parsedUsers);
          setIsCachedDataLoaded(true);
        }
        // Load fresh data in background
        loadFreshData();
      } else {
        setPermissionsGranted(false);
        await AsyncStorage.setItem(PERMISSION_GRANTED_KEY, 'false');
        Alert.alert(
          'Permission Required',
          'This app needs access to contacts to show you which of your contacts are using this app.',
          [
            {
              text: 'Not Now',
              style: 'cancel'
            },
            {
              text: 'Open Settings',
              onPress: openSettings
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error in checkAndRequestPermissions:', error);
      setPermissionsGranted(false);
    }
  };

  // Add function to save data to cache
  const saveDataToCache = async (users: MappedUser[], statuses: Status[], unreadCounts: UnreadCounts) => {
    try {
      await AsyncStorage.setItem(CACHED_USERS_KEY, JSON.stringify(users));
      await AsyncStorage.setItem(CACHED_STATUSES_KEY, JSON.stringify(statuses));
      await AsyncStorage.setItem(CACHED_UNREAD_COUNTS_KEY, JSON.stringify(unreadCounts));
      console.log('Data saved to cache.');
      console.log('Cached users sample lastMessage:', users.length > 0 ? users[0].lastMessage : 'N/A'); // Log saved last message
      console.log('Cached unread counts:', unreadCounts); // Log saved unread counts
    } catch (error) {
      console.error('Error saving data to cache:', error);
    }
  };

  // Add new function to load cached contacts
  const loadCachedContacts = async () => {
    try {
      const cachedContacts = await AsyncStorage.getItem(CACHED_CONTACTS_KEY);
      if (cachedContacts) {
        const parsedContacts = JSON.parse(cachedContacts);
        return parsedContacts;
      }
      return null;
    } catch (error) {
      console.error('Error loading cached contacts:', error);
      return null;
    }
  };

  // Add new function to save contacts to cache
  const saveContactsToCache = async (contacts: any[]) => {
    try {
      await AsyncStorage.setItem(CACHED_CONTACTS_KEY, JSON.stringify(contacts));
      console.log('Contacts saved to cache.');
    } catch (error) {
      console.error('Error saving contacts to cache:', error);
    }
  };

  const fetchUnreadCounts = async () => {
     // This function is no longer the primary source of unread counts after initial load.
     // Real-time listeners and cache handle this. Keeping for now, but might be refactored later.
     console.log('fetchUnreadCounts called (listener is primary source)');
    try {
      // Load cached unread counts first
      const cachedUnreadCounts = await AsyncStorage.getItem(CACHED_UNREAD_COUNTS_KEY);
      if (cachedUnreadCounts) {
        console.log('Loading cached unread counts in fetchUnreadCounts:', cachedUnreadCounts);
        // Only update state if the current state is empty or less complete
        setUnreadCounts(prev => {
           const cached = JSON.parse(cachedUnreadCounts);
           if (Object.keys(prev).length < Object.keys(cached).length) {
              console.log('Updating unreadCounts state from cache in fetchUnreadCounts');
              return cached;
           }
           return prev;
        });
      }
      // Removed the onValue listener from here.
      // Real-time updates will be handled by the listener in setupRealTimeListeners.

      // Removed API calls for unread counts.
    } catch (error) {
      console.error('Error in fetchUnreadCounts:', error);
    }
  };


  const fetchStatuses = async () => {
    // Skip if we've already processed statuses
    if (statusesProcessedRef.current && statuses.length > 0) {
      return;
    }
     console.log('Fetching statuses...');

    try {
      // Load cached statuses first
      const cachedStatuses = await AsyncStorage.getItem(CACHED_STATUSES_KEY);
      if (cachedStatuses) {
        setStatuses(JSON.parse(cachedStatuses));
         console.log('Loaded cached statuses.');
      }

      // Then fetch fresh data
      // First check if any users already have statuses attached
      const usersWithStatuses = users.filter(user => user.statuses && user.statuses.length > 0);
      const existingStatuses: Status[] = [];

      // Collect all statuses from users
      usersWithStatuses.forEach(user => {
        if (user.statuses) {
          existingStatuses.push(...user.statuses);
        }
      });

      // If we have statuses from users, use those
      if (existingStatuses.length > 0) {
        setStatuses(existingStatuses);
         console.log('Using statuses already attached to users.');
        await updateUsersWithStatuses(existingStatuses);
        // Mark as processed
        statusesProcessedRef.current = true;
        return;
      }

      // Get current user ID from AsyncStorage
      const userSession = await AsyncStorage.getItem('userSession');
      let currentUserId = null;
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        currentUserId = parsedSession.userId;
      }

      // Fetch all statuses
      let allStatuses: Status[] = [];

      // Fetch statuses for the current user if available
      if (currentUserId) {
        try {
          // Use the exact endpoint for the user's own statuses
          const userStatusResponse = await fetch(`https://status.api.nityasha.com/statuses?userId=${currentUserId}`);
          if (userStatusResponse.ok) {
            const userData = await userStatusResponse.json();

            // Check if the response is an array (direct statuses) or has a statuses property
            const userStatuses = Array.isArray(userData) ? userData : (userData.statuses || []);

            if (userStatuses.length > 0) {
               console.log('Fetched current user statuses from API.');
              allStatuses = [...allStatuses, ...userStatuses];
            }
          }
        } catch (userStatusError) {
            console.error('Error fetching current user statuses:', userStatusError);
        }
      }

      // Fetch all other statuses
      const response = await fetch('https://status.api.nityasha.com/statuses');
      if (response.ok) {
        const data = await response.json();

        if (data && data.statuses && data.statuses.length > 0) {
           console.log('Fetched all statuses from API.');
          // Filter out any duplicates that might already be in allStatuses
          const newStatuses = data.statuses.filter((status: Status) =>
            !allStatuses.some(existingStatus => existingStatus.id === status.id)
          );
          allStatuses = [...allStatuses, ...newStatuses];
        }
      }

      // Set the combined statuses
      setStatuses(allStatuses);
      console.log(`Total statuses fetched: ${allStatuses.length}`);

      // After fetching statuses, update users with status information
      if (allStatuses.length > 0) {
        await updateUsersWithStatuses(allStatuses);
        // Mark as processed
        statusesProcessedRef.current = true;
      }

      // After getting fresh data, update cache
      await AsyncStorage.setItem(CACHED_STATUSES_KEY, JSON.stringify(statuses));
       console.log('Statuses saved to cache.');
    } catch (error) {
      console.error('Error in fetchStatuses:', error);
    }
  };

  const updateUsersWithStatuses = async (statusesData: Status[]) => {
    // Skip if we've already processed statuses
    if (statusesProcessedRef.current && users.some(user => user.hasStatus)) {
      return;
    }
     console.log('Updating users with statuses...');

    // Get current user ID from AsyncStorage
    let currentUserId = null;
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        currentUserId = parsedSession.userId?.toString();

      }
    } catch (error) {
        console.error('Error getting current user ID for status update:', error);
    }

    // First, create a map of phone numbers to statuses
    const phoneToStatusMap: { [key: string]: Status[] } = {};

    // Process all statuses and group them by phone number
    statusesData.forEach((status: Status) => {
      // Clean the user_id from status to match our phone number format
      const statusUserId = status.user_id
        ?.replace(/[\s-()]/g, '')
        ?.replace(/^\+?91/, '')
        ?.replace(/^0+/, '');

      if (statusUserId) {
        if (!phoneToStatusMap[statusUserId]) {
          phoneToStatusMap[statusUserId] = [];
        }
        phoneToStatusMap[statusUserId].push(status);
      }
    });


    // Only update users if we have status data or current user ID
    if (Object.keys(phoneToStatusMap).length > 0 || currentUserId) {
      setUsers(prevUsers => {
        return prevUsers.map(user => {
          // Skip if user already has status information
          if (user.hasStatus && user.statuses && user.statuses.length > 0) {
            return user;
          }

          // Check if this user's phone number has any statuses
          const userStatuses = user.phoneNumber ? phoneToStatusMap[user.phoneNumber] : undefined;

          // Check if this is the current user's ID - using exact match (=)
          const isCurrentUser = currentUserId && (user.id.toString() === currentUserId || user.id === parseInt(currentUserId));

          // If user has statuses or is the current user with matching statuses, add them to the user object
          if (userStatuses && userStatuses.length > 0) {
            return {
              ...user,
              statuses: userStatuses,
              hasUnseenStory: true, // Mark as having unseen story by default
              hasStatus: true
            };
          }

          // If this is the current user, check if there are any statuses with matching user_id
          if (isCurrentUser && currentUserId) {
            // Find statuses that belong to the current user - using exact match (=)
            const currentUserStatuses = statusesData.filter(status =>
              status.user_id === currentUserId.toString() || status.user_id === currentUserId
            );


              
            if (currentUserStatuses.length > 0) {
              return {
                ...user,
                statuses: currentUserStatuses,
                hasUnseenStory: true,
                hasStatus: true
              };
            }
          }

          return user;
        });
      });
    }
  };

  // Function to mark a status as seen was removed as it's not being used
  // If needed in the future, implement a function to update the hasUnseenStory flag

  // Use a ref to track if we've already processed statuses
  const statusesProcessedRef = useRef(false);
  const contactsLoadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Process statuses from users after they are loaded (only once)
  useEffect(() => {
    if (users.length > 0 && !statusesProcessedRef.current) {
      // statusesProcessedRef.current = true; // Mark as processed here to avoid multiple calls
      fetchStatuses();
    }
  }, [users]);


  // Store online status in a ref to prevent unnecessary re-renders
  const onlineStatusCache = useRef<{[key: string]: {isOnline: boolean, timestamp: number}}>({});
  // Debounce time in milliseconds - increase to 3 seconds for more stability
  const ONLINE_STATUS_DEBOUNCE = 3000;

  useEffect(() => {
    const fetchUserStatusesFromFirebase = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) return;

        const parsedSession = JSON.parse(userSession);
        if (!parsedSession?.userId) return;

        // Set up listeners for all paths
        const paths = ['users', 'online', 'user_status'];
        const listeners = paths.map(path => {
          const statusRef = ref(db, path);
          return onValue(statusRef, (snapshot) => {
            const data = snapshot.val() || {};
            const updatedStatuses: { [key: string]: boolean } = {};
            const currentTime = Date.now();

            Object.entries(data).forEach(([userId, userData]: [string, any]) => {
              let isOnline = false;

              if (path === 'users' && userData?.status) {
                isOnline = userData.status.state === 'online';
              } else if (path === 'online' || path === 'user_status') {
                isOnline = userData?.online === true;
              }

              // Only update if the status has changed
              if (onlineStatusCache.current[userId]?.isOnline !== isOnline) {
                updatedStatuses[userId] = isOnline;
                onlineStatusCache.current[userId] = {
                  isOnline,
                  timestamp: currentTime
                };
              }
            });

            // Update users state if we have changes
            if (Object.keys(updatedStatuses).length > 0) {
              setUsers(prevUsers =>
                prevUsers.map(user => {
                  const userId = user.id.toString();
                  return updatedStatuses.hasOwnProperty(userId)
                    ? { ...user, is_online: updatedStatuses[userId] }
                    : user;
                })
              );
            }
          });
        });

        // Clean up listeners on unmount
        return () => {
          console.log('Cleaning up real-time listeners...');
          listeners.forEach(listener => {
            const statusRef = ref(db, paths[listeners.indexOf(listener)]);
            off(statusRef);
          });
        };
      } catch (error) {
        console.error('Error setting up status listeners:', error);
      }
    };

    fetchUserStatusesFromFirebase();
  }, []);
  
  useEffect(() => {
    KeyboardController.setInputMode(2); // 2 corresponds to AdjustPan
    return () => {
      KeyboardController.setInputMode(0); // 0 corresponds to None
    };
  }, []);

  useEffect(() => {
    // Enable smooth keyboard handling
    KeyboardController.setInputMode(16); // Corresponds to adjustResize

    return () => {
      // Clean up if necessary
      KeyboardController.setInputMode(32); // Corresponds to adjustPan
    };
  }, []);

  // Helper function to process contacts
  const processContacts = (contacts: Contact[]) => {
    return contacts
      .filter((contact: Contact) => contact.phoneNumbers && contact.phoneNumbers.length > 0)
      .flatMap((contact: Contact) => {
        return contact.phoneNumbers?.map((phoneNumber) => ({
          name: contact.name,
          phoneNumber: cleanPhoneNumber(phoneNumber.number),
          originalNumber: phoneNumber.number || ''
        })) || [];
      });
  };

  // Helper function to process API users
  const processApiUsers = (apiUsers: any[], contactPhoneNumbers: any[]) => {
    const contactMap = new Map();
    contactPhoneNumbers.forEach((contact: any) => {
      if (contact.phoneNumber) {
        contactMap.set(contact.phoneNumber, contact);
        if (contact.phoneNumber.length >= 10) {
          const last10Digits = contact.phoneNumber.slice(-10);
          contactMap.set(last10Digits, contact);
        }
      }
    });

    return apiUsers
      .filter((user: any) => {
        const userNumber = cleanPhoneNumber(user.email);
        const isPhoneNumber = /^\d+$/.test(userNumber || '');
        if (!isPhoneNumber) return false;

        if (contactMap.has(userNumber)) return true;
        if (userNumber.length >= 10) {
          const last10Digits = userNumber.slice(-10);
          return contactMap.has(last10Digits);
        }
        return false;
      })
      .map((user: any) => {
        const userNumber = cleanPhoneNumber(user.email);
        let matchingContact = contactMap.get(userNumber);
        if (!matchingContact && userNumber.length >= 10) {
          const last10Digits = userNumber.slice(-10);
          matchingContact = contactMap.get(last10Digits);
        }

        const hasStatuses = user.statuses && Array.isArray(user.statuses) && user.statuses.length > 0;

        return {
          id: user.id,
          name: matchingContact?.name || user.username || 'Anonymous',
          initial: (matchingContact?.name?.[0] || user.username?.[0] || 'A').toUpperCase(),
          contactName: matchingContact?.name,
          phoneNumber: userNumber,
          originalEmail: user.email,
          lastMessage: '', // Always blank, will be filled by Firebase/decrypted value
          timestamp: user.last_message_time || 0,
          is_online: false,
          messageStatus: user.message_status || undefined,
          ...(hasStatuses && {
            statuses: user.statuses,
            hasUnseenStory: true,
            hasStatus: true
          }),
        };
      });
  };


  // Add function to fetch initial last messages from Firebase
  const deriveSharedSecret = async (userId1: string, userId2: string) => {
    const ids = [userId1, userId2].sort().join(':');
    return await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, ids);
  };

  function toUtf8Bytes(str: string): Uint8Array {
    return new TextEncoder().encode(str);
  }
  function fromUtf8Bytes(bytes: Uint8Array): string {
    return new TextDecoder().decode(bytes);
  }
  function xorDecrypt(encoded: string, key: string): string {
    const encryptedBytes = Uint8Array.from(atob(encoded), c => c.charCodeAt(0));
    const keyBytes = toUtf8Bytes(key);
    const result = new Uint8Array(encryptedBytes.length);
    for (let i = 0; i < encryptedBytes.length; i++) {
      result[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
    }
    return fromUtf8Bytes(result);
  }

  const fetchInitialLastMessages = async (currentUserId: string, currentUsers: MappedUser[]) => {
    try {
      console.log('Fetching initial last messages from Firebase...');
      const messagesRef = ref(db, 'user_messages');
      const snapshot = await get(messagesRef);
      const data = snapshot.val();

      if (data) {
        const updatedUsersMap = new Map(currentUsers.map(user => [user.id.toString(), user]));
        let hasUpdates = false;
        const newFirebaseLastMessages: { [id: string]: { lastMessage: string, timestamp: number, messageStatus?: 'pending' | 'sent' | 'read' } } = {};

        await Promise.all(Object.entries(data).map(async ([chatId, messages]: [string, any]) => {
          const userIds = chatId.split('_');
          if (userIds.includes(currentUserId)) {
            const messagesArray = Object.values(messages) as Message[];
            messagesArray.sort((a, b) => b.timestamp - a.timestamp);
            const lastMessage = messagesArray[0];

            if (lastMessage) {
              const otherUserId = userIds[0] === currentUserId ? userIds[1] : userIds[0];
              let decryptedText = lastMessage.text;
              if (lastMessage.encrypted) {
                try {
                  const sharedSecret = await deriveSharedSecret(currentUserId, otherUserId);
                  decryptedText = xorDecrypt(lastMessage.text, sharedSecret);
                } catch (e) {
                  decryptedText = '[Unable to decrypt]';
                }
              }
              newFirebaseLastMessages[otherUserId] = {
                lastMessage: lastMessage.wasFiltered ? 'Hi' : filterBadWords(decryptedText || 'New message'),
                timestamp: lastMessage.timestamp,
                messageStatus: lastMessage.status,
              };
              const currentUser = updatedUsersMap.get(otherUserId);
              if (currentUser) {
                updatedUsersMap.set(otherUserId, {
                  ...currentUser,
                  lastMessage: lastMessage.wasFiltered ? 'Hi' : filterBadWords(decryptedText || 'New message'),
                  timestamp: lastMessage.timestamp,
                  messageStatus: lastMessage.status,
                });
                hasUpdates = true;
              }
            }
            // If no lastMessage, do NOT update or blank out the user's lastMessage/timestamp
          }
        }));
        setFirebaseLastMessages(newFirebaseLastMessages);
        if (hasUpdates) {
          const finalUsers = Array.from(updatedUsersMap.values());
          finalUsers.sort((a, b) => {
            const aUnread = unreadCounts[a.id.toString()] || 0;
            const bUnread = unreadCounts[b.id.toString()] || 0;
            if (aUnread > 0 && bUnread === 0) return -1;
            if (aUnread === 0 && bUnread > 0) return 1;
            const timeA = typeof a.timestamp === 'number' ? a.timestamp : (a.timestamp ? new Date(a.timestamp).getTime() : 0);
            const timeB = typeof b.timestamp === 'number' ? b.timestamp : (b.timestamp ? new Date(b.timestamp).getTime() : 0);
            return timeB - timeA;
          });
          setUsers(finalUsers);
          console.log('Users state updated with initial last messages from Firebase.');
        }
      }
      // If data is null/empty, do NOT update or blank out any user's lastMessage/timestamp
    } catch (error) {
      console.error('Error fetching initial last messages:', error);
    }
  };

  // Add real-time listeners effect
  useEffect(() => {
    const setupRealTimeListeners = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) return;

        const parsedSession = JSON.parse(userSession);
        if (!parsedSession?.userId) return;

        console.log('Setting up real-time listeners...');

        // Listen for online status changes
        const onlineRef = ref(db, 'online');
        const onlineListener = onValue(onlineRef, (snapshot) => {
          const data = snapshot.val() || {};
          setUsers(prevUsers =>
            prevUsers.map(user => {
              const userId = user.id.toString();
              const userData = data[userId];
              return {
                ...user,
                is_online: userData?.online === true
              };
            })
          );
        });

        // Listen for unread message changes from user_chats (PRIMARY SOURCE)
        const userChatsRef = ref(db, `user_chats/${parsedSession.userId}`);
        const unreadListener = onValue(userChatsRef, (snapshot) => {
          const data = snapshot.val();
          const newState: {[key: string]: number} = {}; // Start with an empty state

          if (data) {
            // Iterate through the snapshot data
            Object.entries(data).forEach(([otherUserId, chatData]: [string, any]) => {
              if (chatData?.unread > 0) {
                // Only add users to the new state if their unread count is greater than 0
                newState[otherUserId] = chatData.unread;
              }
            });
          }

          // Set the state directly to the new object.
          // This automatically removes entries for users whose unread count is 0 or not present.
          setUnreadCounts(newState);

          console.log('Updated unread counts from Firebase listener (processed snapshot):', newState);

          // Save the updated counts to cache whenever the state changes
          AsyncStorage.setItem(CACHED_UNREAD_COUNTS_KEY, JSON.stringify(newState)).catch(error => {
              console.error('Error caching unread counts after update:', error);
          });
        });


        // Listen for new messages with enhanced real-time updates (for last message/timestamp)
        // Keep this listener as it updates lastMessage and timestamp,
        // but ensure it does NOT update unreadCount.
        const messagesRef = ref(db, 'user_messages');
        const messagesListener = onValue(messagesRef, async (snapshot) => {
          const data = snapshot.val();
          if (data) {
            const userId = parsedSession.userId.toString();
            let hasUpdates = false;
            const updatedUsersMap = new Map(users.map(user => [user.id.toString(), user]));
            const latestChatInfo: { [chatId: string]: { lastMessage: string, timestamp: number, messageStatus: 'pending' | 'sent' | 'read', senderId: string, wasFiltered: boolean, encrypted?: boolean } } = {};

            Object.entries(data).forEach(([chatId, messages]: [string, any]) => {
              const userIds = chatId.split('_');
              if (userIds.includes(userId)) {
                const messagesArray = Object.values(messages) as Message[];
                messagesArray.sort((a, b) => b.timestamp - a.timestamp);
                const lastMessage = messagesArray[0];

                if (lastMessage) {
                  latestChatInfo[chatId] = {
                    lastMessage: lastMessage.text || 'New message',
                    timestamp: lastMessage.timestamp,
                    messageStatus: lastMessage.status,
                    senderId: lastMessage.senderId,
                    wasFiltered: lastMessage.wasFiltered || false,
                    encrypted: lastMessage.encrypted,
                  };
                }
                // If no lastMessage, do NOT update or blank out the user's lastMessage/timestamp
              }
            });

            setFirebaseLastMessages(prev => {
              const updated = { ...prev };
              Promise.all(Object.entries(latestChatInfo).map(async ([chatId, chatData]) => {
                const userIds = chatId.split('_');
                const otherUserId = userIds[0] === userId ? userIds[1] : userIds[0];
                let decryptedText = chatData.lastMessage;
                if (chatData.encrypted) {
                  try {
                    const sharedSecret = await deriveSharedSecret(userId, otherUserId);
                    decryptedText = xorDecrypt(chatData.lastMessage, sharedSecret);
                  } catch (e) {
                    decryptedText = '[Unable to decrypt]';
                  }
                }
                updated[otherUserId] = {
                  lastMessage: chatData.wasFiltered ? 'Hi' : filterBadWords(decryptedText),
                  timestamp: chatData.timestamp,
                  messageStatus: chatData.messageStatus,
                };
              })).then(() => {
                setUsers(prevUsers => mergeFirebaseLastMessages(prevUsers, updated));
              });
              return updated;
            });

            await Promise.all(Object.entries(latestChatInfo).map(async ([chatId, chatData]) => {
                const userIds = chatId.split('_');
                const userId = parsedSession.userId.toString();
                const otherUserId = userIds[0] === userId ? userIds[1] : userIds[0];

                const currentUser = updatedUsersMap.get(otherUserId);

                if (currentUser) {
                     let displayMessage = chatData.lastMessage;
                     if (chatData.wasFiltered) {
                       displayMessage = "Hi";
                     } else {
                       displayMessage = filterBadWords(displayMessage);
                     }

                     // Note: Timestamp formatting is now done in UserItem for display
                     updatedUsersMap.set(otherUserId, {
                       ...currentUser,
                       lastMessage: displayMessage,
                       timestamp: chatData.timestamp,
                       messageStatus: chatData.messageStatus,
                       // DO NOT update unreadCount here - it's handled by user_chats listener
                     });
                     hasUpdates = true;

                     if (chatData.senderId === otherUserId) {
                       try {
                         const response = await fetch(`https://api.nityasha.com/api/v1/users/${otherUserId}`);
                         if (response.ok) {
                           const userData = await response.json();
                           if (userData.pushtoken) {
                             await sendPushNotification(
                               userData.pushtoken,
                               currentUser.name,
                               displayMessage
                             );
                           }
                         }
                       } catch (error) {
                         console.error('Error sending notification:', error);
                       }
                     }
                   }
            }));


            if (hasUpdates) {
               const finalUsers = Array.from(updatedUsersMap.values());
               // Re-sort users after message/timestamp updates
               finalUsers.sort((a, b) => {
                 // Prioritize users with unread messages
                 const aUnread = unreadCounts[a.id.toString()] || 0;
                 const bUnread = unreadCounts[b.id.toString()] || 0;
                 if (aUnread > 0 && bUnread === 0) return -1;
                 if (aUnread === 0 && bUnread > 0) return 1;

                 // Otherwise, sort by timestamp (latest first)
                 const timeA = typeof a.timestamp === 'number' ? a.timestamp : (a.timestamp ? new Date(a.timestamp).getTime() : 0);
                 const timeB = typeof b.timestamp === 'number' ? b.timestamp : (b.timestamp ? new Date(b.timestamp).getTime() : 0);
                 return timeB - timeA;
               });
               setUsers(finalUsers);
                console.log('Users state updated from messages listener.');
            }
          } else {
             // If messages data is null/empty, do NOT update or blank out any user's lastMessage/timestamp
              console.log('Messages data is null or empty. No lastMessage/timestamp will be changed.');
          }
        });

        // Listen for message status changes
        const messageStatusRef = ref(db, 'message_status');
        const statusListener = onValue(messageStatusRef, (snapshot) => {
          const data = snapshot.val();
          if (data) {
             console.log('Message status data received:', data);
            setUsers(prevUsers =>
              prevUsers.map(user => {
                const userId = user.id.toString();
                const statusData = data[userId];
                if (statusData) {
                   console.log(`Updating message status for user ${userId}: ${statusData.status}`);
                  return {
                    ...user,
                    messageStatus: statusData.status
                  };
                }
                return user;
              })
            );
          } else {
             console.log('Message status data is null or empty.');
          }
        });

        // Cleanup listeners
        return () => {
          console.log('Cleaning up real-time listeners...');
          off(onlineRef);
          off(userChatsRef);
          off(messagesRef);
          off(messageStatusRef);
        };
      } catch (error) {
        console.error('Error setting up real-time listeners:', error);
      }
    };

    setupRealTimeListeners();

     return () => {
        if (initialLoadTimeoutRef.current) {
            clearTimeout(initialLoadTimeoutRef.current);
        }
     };
  }, []); // Empty dependency array to run once on mount


  const markMessagesAsRead = async (chatPartnerId: number | string) => {
    if (!chatPartnerId) return;

    const chatPartnerIdStr = chatPartnerId.toString();

    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const parsedSession = JSON.parse(userSession);
      if (!parsedSession?.userId) return;

      console.log(`Attempting to mark chat with ${chatPartnerIdStr} as read.`);

      // Use ref and set directly on the 'unread' node to explicitly write the number 0
      const unreadRef = ref(db, `user_chats/${parsedSession.userId}/${chatPartnerIdStr}/unread`);
      await set(unreadRef, 0); // Explicitly set the unread count to the number 0

      console.log(`Marked chat with ${chatPartnerIdStr} as read in Firebase.`);

      // Update local state immediately
      setUnreadCounts(prev => {
        const updated = { ...prev };
        // Ensure the count is removed or set to 0 in local state
        if (updated[chatPartnerIdStr] !== undefined) {
           delete updated[chatPartnerIdStr]; // Remove the entry for 0 unread
           // Alternatively, you could set it to 0 if you prefer to keep the key:
           // updated[chatPartnerIdStr] = 0;
        }
        console.log(`Updated local unread count for ${chatPartnerIdStr} after marking as read:`, updated);
        return updated;
      });

      // Removed the onValue listener that marked individual messages as read in user_messages
      // This is better handled by the UserChatInbox component based on viewability.

    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  // Optimize handleRefresh to use checkForNewContacts
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await checkForNewContacts(); // Fetch fresh data and merge
    } catch (error) {
      console.error('Error refreshing contacts:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Add at the top-level of ChatListScreen
  const [selectedUser, setSelectedUser] = useState<MappedUser | null>(null);
  const [showUserOptions, setShowUserOptions] = useState(false);

  const handleUserLongPress = (user: MappedUser) => {
    setSelectedUser(user);
    setShowUserOptions(true);
  };

  // Add state for contact selection and editing
  const [showContactPicker, setShowContactPicker] = useState(false);
  const [contactSearch, setContactSearch] = useState('');
  const [contactToSend, setContactToSend] = useState<MappedUser | null>(null);
  const [editedContactName, setEditedContactName] = useState('');
  const [isEditingContact, setIsEditingContact] = useState(false);

  // Handler for Send Contact option
  const handleSendContact = () => {
    // Do NOT update selectedUser here. It should remain the long-pressed user.
    setShowUserOptions(false);
    setShowContactPicker(true);
    setContactSearch('');
    setContactToSend(null);
    setIsEditingContact(false);
  };

  // Filter users for contact picker
  const filteredContacts = users.filter(u => {
    if (!contactSearch.trim()) return true;
    return (
      u.name.toLowerCase().includes(contactSearch.toLowerCase()) ||
      (u.phoneNumber && u.phoneNumber.includes(contactSearch))
    );
  });

  // Handler for selecting a contact to send
  const handleSelectContact = (user: MappedUser) => {
    setContactToSend(user); // Only update contactToSend, not selectedUser
    setEditedContactName(user.name);
    setIsEditingContact(true);
  };

  // Handler for confirming contact send
  const handleConfirmSendContact = async () => {
    if (!contactToSend) return;
    // Update contact name in users and AsyncStorage
    const updatedUsers = users.map(u =>
      u.id === contactToSend.id ? { ...u, name: editedContactName } : u
    );
    setUsers(updatedUsers);
    setFilteredUsers(updatedUsers);
    await AsyncStorage.setItem(CACHED_USERS_KEY, JSON.stringify(updatedUsers));
    // Always open/send to the DM of selectedUser (the long-pressed user)
    navigation.navigate('UserChatInbox', {
      otherUserId: contactToSend.id, // Open the DM of the contact you picked
      name: editedContactName,
      sendContactCard: {
        contact: {
          id: contactToSend.id,
          name: editedContactName,
          phoneNumber: contactToSend.phoneNumber,
        },
        _sendId: Date.now() + Math.random() // Unique value to force effect
      },
    });
    setShowContactPicker(false);
    setIsEditingContact(false);
    setContactToSend(null);
  };

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`flex-1 pt-4`}>
    <PaperProvider>

    {modelSelectVisible && (
        <Pressable
          onPress={() => setModelSelectVisible(false)}
          style={tw`absolute top-0 left-0 right-0 bottom-0 z-40`}
        />
      )}

       <View style={tw`w-full items-center justify-between flex-row px-3 py-3`}>
       
        <Text style={[tw`text-black text-2xl`, { fontFamily: 'Helvetica_bold' }]}>Nityasha</Text>
        <View style={tw`flex-row items-center`}>
          
          <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
        }}>
        <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
        }}>
      
      <View style={tw`items-end w-50`}>
      <TouchableOpacity  onPress={() => setModelSelectVisible(!modelSelectVisible)}><EllipsisVertical  size={24} color="#000" /></TouchableOpacity>
      <AnimatePresence>
          {modelSelectVisible && (
            <MotiView
              from={{ opacity: 0, translateY: -10 }}
              animate={{ opacity: 1, translateY: 0 }}
              exit={{ opacity: 0, translateY: -10 }}
              transition={{ type: 'timing', duration: 200 }}
              style={tw`absolute top-10 left-4 bg-white text-black border-zinc-200 rounded-[14px] px-4 w-[180px] py-1 z-50 border `} 
            >
             <TouchableOpacity style={tw`py-3 flex-row items-center gap-2`} onPress={() => navigation.navigate('Account_Information')}>
             <Settings size={24} color="#000" /> <Text style={[tw`text-[16px] text-black`, { fontFamily: 'Helvetica_bold' }]}>Edit Account</Text>
             </TouchableOpacity>
            </MotiView>
          )}
        </AnimatePresence>
      </View>
      </View>
      </View>
        </View>
      </View> 
 
      

      {/* Conditional rendering for initial sync */}
      {!isCachedDataLoaded && isLoadingFreshData && isInitialLoad ? (
        <View style={tw`flex-1 items-center justify-center`}>
          <ActivityIndicator size="large" color="#075E54" />
          <Text style={tw`mt-4 text-gray-600 text-lg`}>Syncing users...</Text>
        </View>
      ) : (
        <FlatList
        data={filteredUsers}
        ListHeaderComponent={
          <View style={tw`px-3`}>
        <Searchbar
          placeholder="Search chats..."
          value={searchQuery}
          onChangeText={handleSearch}
          style={{
            borderRadius: 300,
            elevation: 3,
            backgroundColor: "#F5FCF5",
          }}
          placeholderTextColor="#999"
          />
      </View>
         
        }
        renderItem={renderUserItem}
        keyExtractor={(item) => `user-${item.id}-${item.phoneNumber || ''}`}
        contentContainerStyle={tw`pb-5`}
        showsVerticalScrollIndicator={false}
        
        ListEmptyComponent={
          <>
 <View style={tw`flex-row items-center gap-4 px-4 py-3`}>
      {/* Avatar Skeleton */}
      <View style={tw`w-12 h-12 rounded-full bg-gray-300`} />

      {/* Text Skeletons */}
      <View style={tw`flex-1 gap-2`}>
        <View style={tw`w-32 h-3 rounded-full bg-gray-300`} />
        <View style={tw`w-24 h-3 rounded-full bg-gray-300`} />
      </View>
    </View>
     <View style={tw`flex-row items-center gap-4 px-4 py-3`}>
      {/* Avatar Skeleton */}
      <View style={tw`w-12 h-12 rounded-full bg-gray-300`} />

      {/* Text Skeletons */}
      <View style={tw`flex-1 gap-2`}>
        <View style={tw`w-32 h-3 rounded-full bg-gray-300`} />
        <View style={tw`w-24 h-3 rounded-full bg-gray-300`} />
      </View>
    </View>
     <View style={tw`flex-row items-center gap-4 px-4 py-3`}>
      {/* Avatar Skeleton */}
      <View style={tw`w-12 h-12 rounded-full bg-gray-300`} />

      {/* Text Skeletons */}
      <View style={tw`flex-1 gap-2`}>
        <View style={tw`w-32 h-3 rounded-full bg-gray-300`} />
        <View style={tw`w-24 h-3 rounded-full bg-gray-300`} />
      </View>
    </View>
     <View style={tw`flex-row items-center gap-4 px-4 py-3`}>
      {/* Avatar Skeleton */}
      <View style={tw`w-12 h-12 rounded-full bg-gray-300`} />

      {/* Text Skeletons */}
      <View style={tw`flex-1 gap-2`}>
        <View style={tw`w-32 h-3 rounded-full bg-gray-300`} />
        <View style={tw`w-24 h-3 rounded-full bg-gray-300`} />
      </View>
    </View>
     <View style={tw`flex-row items-center gap-4 px-4 py-3`}>
      {/* Avatar Skeleton */}
      <View style={tw`w-12 h-12 rounded-full bg-gray-300`} />

      {/* Text Skeletons */}
      <View style={tw`flex-1 gap-2`}>
        <View style={tw`w-32 h-3 rounded-full bg-gray-300`} />
        <View style={tw`w-24 h-3 rounded-full bg-gray-300`} />
      </View>
    </View>
     <View style={tw`flex-row items-center gap-4 px-4 py-3`}>
      {/* Avatar Skeleton */}
      <View style={tw`w-12 h-12 rounded-full bg-gray-300`} />

      {/* Text Skeletons */}
      <View style={tw`flex-1 gap-2`}>
        <View style={tw`w-32 h-3 rounded-full bg-gray-300`} />
        <View style={tw`w-24 h-3 rounded-full bg-gray-300`} />
      </View>
    </View>
    
           </>
          }
          />
      )}
      {showUserOptions && selectedUser && (
        <Modal
        visible={showUserOptions}
          transparent
          animationType="fade"
          onRequestClose={() => setShowUserOptions(false)}
          >
          <Pressable style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.3)' }} onPress={() => setShowUserOptions(false)} />
          <View style={{ position: 'absolute', bottom: 0, left: 0, right: 0, backgroundColor: 'white', borderTopLeftRadius: 20, borderTopRightRadius: 20, padding: 24 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16 }}>Options for {selectedUser?.name}</Text>
            <TouchableOpacity style={{ paddingVertical: 12 }} onPress={() => { setShowUserOptions(false); /* TODO: Implement change username */ }}>
              <Text style={{ fontSize: 16 }}>Change Username</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ paddingVertical: 12 }} onPress={handleSendContact}>
              <Text style={{ fontSize: 16 }}>Send Contact</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ paddingVertical: 12 }} onPress={() => setShowUserOptions(false)}>
              <Text style={{ fontSize: 16, color: 'red' }}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </Modal>
      )}
      {showContactPicker && (
        <Modal
        visible={showContactPicker}
        transparent
        animationType="slide"
        onRequestClose={() => setShowContactPicker(false)}
        >
          <Pressable style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.3)' }} onPress={() => setShowContactPicker(false)} />
          <View style={{ position: 'absolute', bottom: 0, left: 0, right: 0, backgroundColor: 'white', borderTopLeftRadius: 20, borderTopRightRadius: 20, padding: 24, maxHeight: '80%' }}>
            {!isEditingContact ? (
              <>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16 }}>Select a contact to send</Text>
                <TextInput
                  placeholder="Search contacts..."
                  value={contactSearch}
                  onChangeText={setContactSearch}
                  style={{ borderWidth: 1, borderColor: '#ccc', borderRadius: 8, padding: 8, marginBottom: 12 }}
                  />
                <FlatList
                  data={filteredContacts}
                  keyExtractor={item => `contact-${item.id}`}
                  renderItem={({ item }) => (
                    <TouchableOpacity style={{ paddingVertical: 10 }} onPress={() => handleSelectContact(item)}>
                      <Text style={{ fontSize: 16 }}>{item.name} {item.phoneNumber ? `(${item.phoneNumber})` : ''}</Text>
                    </TouchableOpacity>
                  )}
                  style={{ maxHeight: 300 }}
                  />
              </>
            ) : (
              <>
                <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16 }}>Edit contact name</Text>
                <TextInput
                  value={editedContactName}
                  onChangeText={setEditedContactName}
                  style={{ borderWidth: 1, borderColor: '#ccc', borderRadius: 8, padding: 8, marginBottom: 12 }}
                  />
                <TouchableOpacity style={{ backgroundColor: '#075E54', borderRadius: 8, padding: 12, alignItems: 'center', marginBottom: 8 }} onPress={handleConfirmSendContact}>
                  <Text style={{ color: 'white', fontSize: 16 }}>Send Contact</Text>
                </TouchableOpacity>
                <TouchableOpacity style={{ padding: 12, alignItems: 'center' }} onPress={() => setIsEditingContact(false)}>
                  <Text style={{ color: 'red', fontSize: 16 }}>Cancel</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </Modal>
      )}
    </PaperProvider>
    </ImageBackground>
  );
};

export default ChatListScreen;