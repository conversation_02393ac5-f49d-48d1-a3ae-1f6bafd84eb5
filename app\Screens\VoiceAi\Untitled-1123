"use client"

import { useRef, useState, useEffect } from "react"
import { View, Text, TouchableOpacity, StatusBar, Alert, Animated, Easing , ImageBackground } from "react-native"
import { WebView } from "react-native-webview"
import * as Speech from "expo-speech"
import { Audio } from "expo-av"
import tw from "twrnc"
import { ScrollView } from "moti"
import { Mic, X } from "lucide-react-native"
import { useNavigation } from "@react-navigation/native"
import useAuth from "@/hooks/auth"
import LottieView  from "lottie-react-native"
import { useKeepAwake } from 'expo-keep-awake';
import * as MailComposer from 'expo-mail-composer';
import { Button } from "react-native-paper"
// 30px Height Animation for Expo Speech
function ExpoSpeechVoiceAnimation({ isActive = false, isSpeaking = false, isUserSpeaking = false }) {
  // Circle animation for user speaking
  const circleScale = useRef(new Animated.Value(1)).current
  const circleOpacity = useRef(new Animated.Value(0.7)).current

  // Thick rounded bars for AI speaking - 30px height
  const bar1Scale = useRef(new Animated.Value(50)).current
  const bar2Scale = useRef(new Animated.Value(50)).current
  const bar3Scale = useRef(new Animated.Value(50)).current
  const bar4Scale = useRef(new Animated.Value(50)).current

  // Container transforms for smooth switching
  const circleContainerScale = useRef(new Animated.Value(1)).current
  const barsContainerScale = useRef(new Animated.Value(0)).current

  const barAnimations = [bar1Scale, bar2Scale, bar3Scale, bar4Scale]

  // Check if expo-speech is actually speaking
  const [isActuallySpeaking, setIsActuallySpeaking] = useState(false)

  useEffect(() => {
    let interval
    if (isSpeaking) {
      // Check expo-speech status every 100ms
      interval = setInterval(async () => {
        const speaking = await Speech.isSpeakingAsync()
        setIsActuallySpeaking(speaking)
      }, 100)
    } else {
      setIsActuallySpeaking(false)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isSpeaking])

  // Smooth switching function
  const switchToMode = (mode, callback) => {
    if (mode === 'bars') {
      // Switch to bars mode
      Animated.parallel([
        Animated.timing(circleContainerScale, {
          toValue: 0,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(barsContainerScale, {
          toValue: 1,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        })
      ]).start(callback)
    } else if (mode === 'circle') {
      // Switch to circle mode
      Animated.parallel([
        Animated.timing(barsContainerScale, {
          toValue: 0,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(circleContainerScale, {
          toValue: 1,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        })
      ]).start(callback)
    }
  }

  useEffect(() => {
    if (isActive && isSpeaking && !isUserSpeaking && isActuallySpeaking) {
      // AI Actually Speaking via Expo Speech - Switch to thick bars
      switchToMode('bars', () => {
        const createThickBarsAnimation = () => {
          const animations = barAnimations.map((barAnim, index) => {
            return Animated.loop(
              Animated.sequence([
                Animated.delay(index * 150),
                Animated.timing(barAnim, {
                  toValue: 42 + Math.random() * 18, // Height between 42-60px
                  duration: 400 + Math.random() * 200,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: false, // Must be false for height animation
                }),
                Animated.timing(barAnim, {
                  toValue: 24 + Math.random() * 12, // Height between 24-36px
                  duration: 300 + Math.random() * 150,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: false,
                }),
                Animated.timing(barAnim, {
                  toValue: 48 + Math.random() * 12, // Height between 48-60px
                  duration: 350 + Math.random() * 100,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: false,
                }),
                Animated.timing(barAnim, {
                  toValue: 30,
                  duration: 250,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: false,
                }),
              ]),
            )
          })

          // Initialize bars to base height
          barAnimations.forEach(barAnim => {
            Animated.timing(barAnim, {
              toValue: 30,
              duration: 200,
              easing: Easing.out(Easing.ease),
              useNativeDriver: false,
            }).start()
          })

          animations.forEach(animation => animation.start())
          return animations
        }

        const animations = createThickBarsAnimation()

        return () => {
          animations.forEach(animation => animation.stop())
        }
      })

      return () => {
        barAnimations.forEach(barAnim => barAnim.stopAnimation())
      }
    } else if (isActive && isUserSpeaking) {
      // User Speaking - Switch to circle
      switchToMode('circle', () => {
        const createPulseAnimation = () => {
          return Animated.loop(
            Animated.sequence([
              Animated.parallel([
                Animated.timing(circleScale, {
                  toValue: 1.3,
                  duration: 800,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: true,
                }),
                Animated.timing(circleOpacity, {
                  toValue: 0.9,
                  duration: 800,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: true,
                }),
              ]),
              Animated.parallel([
                Animated.timing(circleScale, {
                  toValue: 1,
                  duration: 800,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: true,
                }),
                Animated.timing(circleOpacity, {
                  toValue: 0.6,
                  duration: 800,
                  easing: Easing.inOut(Easing.ease),
                  useNativeDriver: true,
                }),
              ]),
            ]),
          )
        }

        // Reset bars to minimum height
        barAnimations.forEach(barAnim => {
          Animated.timing(barAnim, {
            toValue: 10,
            duration: 200,
            easing: Easing.out(Easing.ease),
            useNativeDriver: false,
          }).start()
        })

        const pulseAnimation = createPulseAnimation()
        pulseAnimation.start()

        return () => pulseAnimation.stop()
      })

      return () => {
        circleScale.stopAnimation()
        circleOpacity.stopAnimation()
      }
    } else if (isActive) {
      // Idle state - show circle
      switchToMode('circle', () => {
        const idleAnimation = Animated.loop(
          Animated.sequence([
            Animated.parallel([
              Animated.timing(circleScale, {
                toValue: 1.1,
                duration: 2000,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(circleOpacity, {
                toValue: 0.8,
                duration: 2000,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
            Animated.parallel([
              Animated.timing(circleScale, {
                toValue: 1,
                duration: 2000,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(circleOpacity, {
                toValue: 0.6,
                duration: 2000,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
          ]),
        )

        // Reset bars to minimum height
        barAnimations.forEach(barAnim => {
          Animated.timing(barAnim, {
            toValue: 10,
            duration: 300,
            easing: Easing.out(Easing.ease),
            useNativeDriver: false,
          }).start()
        })

        idleAnimation.start()
        return () => idleAnimation.stop()
      })
    } else {
      // Reset everything with smooth transform
      Animated.parallel([
        Animated.timing(circleContainerScale, {
          toValue: 0,
          duration: 300,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(barsContainerScale, {
          toValue: 0,
          duration: 300,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        })
      ]).start()
    }
  }, [isActive, isSpeaking, isUserSpeaking, isActuallySpeaking])

  if (!isActive) return null

  return (
    <View style={tw`flex items-center justify-center h-40`}>
      {/* User Speaking - Circle Container */}
      <Animated.View
        style={[
          tw`absolute`,
          {
            transform: [{ scale: circleContainerScale }],
          }
        ]}
      >
        <Animated.View
          style={[
            tw`rounded-full bg-black`,
            {
              width: 190,
              height: 190,
              transform: [{ scale: circleScale }],
            }
          ]}
        >

      </Animated.View>
      </Animated.View>

      {/* AI Speaking - Bars Container with animated height */}
      <Animated.View
        style={[
          tw`absolute flex-row items-center justify-center h-40`,
          {
            transform: [{ scale: barsContainerScale }],
          }
        ]}
      >
        {barAnimations.map((barAnim, index) => (
          <Animated.View
            key={index}
            style={[
              tw`bg-black mx-1 rounded-full`,
              {
                width: 30,
                height: barAnim, // Use animated value directly for height
              }
            ]}
          />
        ))}
      </Animated.View>
    </View>
  )
}

export default function VoiceAi() {
  const webViewRef = useRef(null)
  const [recognizedText, setRecognizedText] = useState("")
  const [response, setResponse] = useState("")
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [isUserSpeaking, setIsUserSpeaking] = useState(false)
  const [status, setStatus] = useState("connecting")
  const [isMuted, setIsMuted] = useState(false)
  const navigation = useNavigation()
  const speakingLock = useRef(false)
  const userId = useAuth()
  useKeepAwake()


  useEffect(() => {
    Audio.setAudioModeAsync({
      allowsRecordingIOS: true,
      staysActiveInBackground: true,
      playsInSilentModeIOS: true,
      shouldDuckAndroid: false,
      interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
    })
  }, [])

  useEffect(() => {
    if (isSpeaking) {
      setStatus("thinking")
      setIsUserSpeaking(false)
    } else if (isUserSpeaking) {
      setStatus("listening")
    } else if (!isSpeaking && recognizedText === "") {
      setStatus("connecting")
      setIsUserSpeaking(false)
    } else if (!isSpeaking) {
      setStatus("")
      setIsUserSpeaking(false)
    }
  }, [isSpeaking, recognizedText, isUserSpeaking])

const handleMessage = async (event) => {
  const message = event.nativeEvent.data

  if (message === "RECOGNITION_STARTED") {
    setIsUserSpeaking(true)
    return
  }

  if (message === "RECOGNITION_STOPPED") {
    setIsUserSpeaking(false)
    return
  }

  const speakingNow = await Speech.isSpeakingAsync()
  if (speakingNow) {
    Speech.stop()
    setIsSpeaking(false)
    setIsUserSpeaking(false)
    setStatus("interrupted by user")
    speakingLock.current = false
    return
  }

  if (speakingLock.current || isSpeaking) return

  speakingLock.current = true
  setRecognizedText(message)
  setIsUserSpeaking(false)
  webViewRef.current?.injectJavaScript(`stopRecognition();`)
  Speech.stop()
  setIsSpeaking(true)
  setStatus("thinking")

  try {
    const res = await fetch("https://nx.ai.api.nityasha.com/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ message, user_id: userId }),
    })

    const data = await res.json()
    let reply = data?.response || "कोई उत्तर नहीं मिला।"

    // ✅ Try to parse JSON response
    try {
      const parsed = JSON.parse(reply)

      if (parsed.type === "email") {
        reply = `बिलकुल! ये रहा आपका मेल:\nTo: ${parsed.to || "<EMAIL>"}\nSubject: ${parsed.subject}\n\n${parsed.body}`

        // Optionally send via MailComposer
        await MailComposer.composeAsync({
          recipients: [parsed.to || "<EMAIL>"],
          subject: parsed.subject,
          body: parsed.body,
        })
      } else if (parsed.type === "todo") {
        reply = `ठीक है, "${parsed.task}" को ${parsed.datetime} पर याद रखने के लिए नोट कर लिया गया है।`
        // 📝 Optionally: POST to your backend todo endpoint
      }
    } catch (err) {
      // Not JSON, do nothing
    }

    // 🛑 Prevent speaking raw JSON
    if (reply.trim().startsWith("{")) {
      reply = "JSON को सीधे मत भेजो। इंसानी अंदाज़ में जवाब दो, जैसे बात कर रहे हो।"
    }

    setResponse(reply)

    const onDoneHandler = () => {
      setIsSpeaking(false)
      setIsUserSpeaking(false)
      setStatus("")
      speakingLock.current = false
      webViewRef.current?.injectJavaScript(`startRecognition();`)
    }

    if (!isMuted) {
      Speech.stop()
      Speech.speak(reply, {
        language: "hi-IN",
        rate: 1.0,
        pitch: 1,
        onDone: onDoneHandler,
        onStopped: onDoneHandler,
        onError: onDoneHandler,
      })
    } else {
      onDoneHandler()
    }
  } catch (error) {
    Alert.alert("AI Error", "Failed to fetch response")
    setIsSpeaking(false)
    setIsUserSpeaking(false)
    setStatus("")
    speakingLock.current = false
    webViewRef.current?.injectJavaScript(`startRecognition();`)
  }
}

  return (
    <ImageBackground source={require('@/assets/screens/AIBackgroundScreen.png')} style={tw`flex-1`}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" translucent />

      {/* Header */}
      <View style={tw`pt-6 pb-4 px-6 flex-row items-center justify-between`}>
        <Text style={tw`text-lg font-medium text-gray-800`}>Advanced</Text>
        <TouchableOpacity>
          <View style={tw`w-6 h-6 rounded-full border border-gray-400 items-center justify-center`}>
            <Text style={tw`text-gray-600 text-xs`}>i</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Main Animation Area - Animated Height Bars */}
      <View style={tw`flex-1 items-center justify-center`}>
        <ExpoSpeechVoiceAnimation 
          isActive={true} 
          isSpeaking={isSpeaking} 
          isUserSpeaking={isUserSpeaking}
        />
      </View>

      {/* Response Text Area */}
      <View style={tw`px-6 h-32 mb-4`}>
        <ScrollView style={tw`h-full`} contentContainerStyle={tw`pr-2`} showsVerticalScrollIndicator={false}>
          <Text style={tw`text-gray-800 text-base leading-6 text-center`}>{response || ""}</Text>
        </ScrollView>
      </View>

      {/* Bottom Controls */}
      <View style={tw`items-center pb-10`}>
        <WebView
          ref={webViewRef}
          source={{ html: updatedSpeechHTML }}
          onMessage={handleMessage}
          javaScriptEnabled
          originWhitelist={["*"]}
          style={{ height: 0, width: 0 }}
          onLoadEnd={() => {
            webViewRef.current?.injectJavaScript(`startRecognition();`)
          }}
        />

        <View style={tw`flex w-full px-8 justify-between items-center flex-row`}>
          <TouchableOpacity
            onPress={() => setIsMuted(!isMuted)}
            style={tw`h-16 w-16 rounded-full ${isMuted ? "bg-gray-300" : "bg-white"} border-black flex items-center justify-center border-2`}
          >
            <Mic size={24} color={isMuted ? "#9CA3AF" : "#374151"} />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={tw`h-16 w-16 rounded-full bg-red-500 flex items-center justify-center border-2 border-black`}
          >
            <X size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>
    </ImageBackground>
  )
}

const updatedSpeechHTML = `
<html>
  <body>
    <script>
      var recognition;
      window.shouldRestartRecognition = true;

      function startRecognition() {
        window.shouldRestartRecognition = true;
        if (recognition) recognition.abort();
        recognition = new webkitSpeechRecognition();
        recognition.lang = 'hi-IN';
        recognition.interimResults = false;
        recognition.maxAlternatives = 1;
        recognition.continuous = true;

        recognition.onstart = function() {
          window.ReactNativeWebView.postMessage("RECOGNITION_STARTED");
        };

        recognition.onend = function() {
          window.ReactNativeWebView.postMessage("RECOGNITION_STOPPED");
          setTimeout(() => {
            if (window.shouldRestartRecognition) {
              startRecognition();
            }
          }, 1000);
        };

        recognition.onresult = function(event) {
          var transcript = event.results[event.results.length - 1][0].transcript;
          window.ReactNativeWebView.postMessage(transcript);
        };

        recognition.onerror = function(event) {
          window.ReactNativeWebView.postMessage("Error: " + event.error);
        };

        recognition.start();
      }

      function stopRecognition() {
        window.shouldRestartRecognition = false;
        if (recognition) recognition.stop();
      }
    </script>
  </body>
</html>
`
