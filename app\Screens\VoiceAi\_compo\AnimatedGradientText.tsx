"use client"

import type React from "react"
import { useEffect, useRef } from "react"
import { Text, Animated } from "react-native"
import { LinearGradient } from "expo-linear-gradient"

interface AnimatedGradientTextProps {
  children: React.ReactNode
  colors?: string[]
  style?: any
  isAnimating?: boolean
}

export const AnimatedGradientText: React.FC<AnimatedGradientTextProps> = ({
  children,
  colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7"],
  style,
  isAnimating = false,
}) => {
  const pulseAnimation = useRef(new Animated.Value(1)).current
  const glowAnimation = useRef(new Animated.Value(0)).current

  useEffect(() => {
    if (isAnimating) {
      // Pulse effect
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      )

      // Glow effect
      const glow = Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnimation, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: false,
          }),
          Animated.timing(glowAnimation, {
            toValue: 0.2,
            duration: 1500,
            useNativeDriver: false,
          }),
        ]),
      )

      pulse.start()
      glow.start()

      return () => {
        pulse.stop()
        glow.stop()
      }
    }
  }, [isAnimating])

  const shadowOpacity = glowAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.8],
  })

  return (
    <Animated.View
      style={{
        transform: [{ scale: pulseAnimation }],
      }}
    >
      <Animated.View
        style={{
          shadowColor: "#4ECDC4",
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: shadowOpacity,
          shadowRadius: 15,
          elevation: 10,
        }}
      >
        <LinearGradient
          colors={colors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            borderRadius: 15,
            paddingHorizontal: 12,
            paddingVertical: 6,
          }}
        >
          <Text
            style={[
              style,
              {
                color: "white",
                fontWeight: "600",
                textShadowColor: "rgba(0,0,0,0.4)",
                textShadowOffset: { width: 1, height: 1 },
                textShadowRadius: 2,
              },
            ]}
          >
            {children}
          </Text>
        </LinearGradient>
      </Animated.View>
    </Animated.View>
  )
}
