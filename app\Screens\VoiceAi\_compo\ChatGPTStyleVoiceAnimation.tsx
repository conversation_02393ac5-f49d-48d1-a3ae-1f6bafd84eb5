import React, { useEffect, useRef } from 'react';
import { View, Animated, Text } from 'react-native';

interface ChatGPTStyleVoiceAnimationProps {
  state: 'idle' | 'listening' | 'thinking';
  label?: string;
}

const DOT_COUNT = 5;
const CLOUD_CIRCLES = [
  { size: 120, x: 0, y: 0 },
  { size: 60, x: 70, y: -30 },
  { size: 40, x: -60, y: 30 },
  { size: 30, x: 50, y: 60 },
  { size: 20, x: -40, y: -40 },
];

const ChatGPTStyleVoiceAnimation: React.FC<ChatGPTStyleVoiceAnimationProps> = ({
  state = 'idle',
  label = '',
}) => {
  // Main circle pulse
  const mainScale = useRef(new Animated.Value(1)).current;
  // Dots for listening wave
  const dotScales = useRef(Array.from({ length: DOT_COUNT }, () => new Animated.Value(1))).current;
  // Cloud for thinking
  const cloudScales = useRef(CLOUD_CIRCLES.map(() => new Animated.Value(0.8))).current;

  useEffect(() => {
    let mainPulse: Animated.CompositeAnimation | null = null;
    let dotAnims: Animated.CompositeAnimation[] = [];
    let cloudAnims: Animated.CompositeAnimation[] = [];

    if (state === 'listening') {
      mainPulse = Animated.loop(
        Animated.sequence([
          Animated.timing(mainScale, { toValue: 1.08, duration: 600, useNativeDriver: true }),
          Animated.timing(mainScale, { toValue: 1, duration: 600, useNativeDriver: true }),
        ])
      );
      mainPulse.start();
      // Animate dots in a wave
      dotAnims = dotScales.map((dot, i) =>
        Animated.loop(
          Animated.sequence([
            Animated.delay(i * 100),
            Animated.timing(dot, { toValue: 1.5, duration: 300, useNativeDriver: true }),
            Animated.timing(dot, { toValue: 1, duration: 300, useNativeDriver: true }),
            Animated.delay((DOT_COUNT - i) * 60),
          ])
        )
      );
      dotAnims.forEach(anim => anim.start());
    } else if (state === 'thinking') {
      // Animate cloud circles in/out
      cloudAnims = cloudScales.map((circle, i) =>
        Animated.loop(
          Animated.sequence([
            Animated.delay(i * 120),
            Animated.timing(circle, { toValue: 1.1, duration: 600, useNativeDriver: true }),
            Animated.timing(circle, { toValue: 0.8, duration: 600, useNativeDriver: true }),
            Animated.delay((CLOUD_CIRCLES.length - i) * 60),
          ])
        )
      );
      cloudAnims.forEach(anim => anim.start());
    } else {
      // Idle: gentle pulse
      mainPulse = Animated.loop(
        Animated.sequence([
          Animated.timing(mainScale, { toValue: 1.03, duration: 1200, useNativeDriver: true }),
          Animated.timing(mainScale, { toValue: 1, duration: 1200, useNativeDriver: true }),
        ])
      );
      mainPulse.start();
    }
    return () => {
      mainPulse && mainPulse.stop && mainPulse.stop();
      dotAnims.forEach(anim => anim.stop && anim.stop());
      cloudAnims.forEach(anim => anim.stop && anim.stop());
    };
  }, [state]);

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center', width: '100%' }}>
      {/* Main Circle or Cloud */}
      {state !== 'thinking' ? (
        <Animated.View
          style={{
            width: 220,
            height: 220,
            borderRadius: 110,
            backgroundColor: '#000',
            transform: [{ scale: mainScale }],
            marginBottom: 32,
          }}
        />
      ) : (
        <View style={{ width: 220, height: 220, marginBottom: 32 }}>
          {CLOUD_CIRCLES.map((c, i) => (
            <Animated.View
              key={i}
              style={{
                position: 'absolute',
                left: 110 + c.x - c.size / 2,
                top: 110 + c.y - c.size / 2,
                width: c.size,
                height: c.size,
                borderRadius: c.size / 2,
                backgroundColor: '#000',
                opacity: 0.95,
                transform: [{ scale: cloudScales[i] }],
              }}
            />
          ))}
        </View>
      )}
      {/* Dots for listening */}
      {state === 'listening' && (
        <View style={{ flexDirection: 'row', marginBottom: 16 }}>
          {dotScales.map((dot, i) => (
            <Animated.View
              key={i}
              style={{
                width: 14,
                height: 14,
                borderRadius: 7,
                backgroundColor: '#000',
                marginHorizontal: 6,
                transform: [{ scale: dot }],
                opacity: 0.9,
              }}
            />
          ))}
        </View>
      )}
      {/* Label */}
      <Text style={{ color: '#000', fontSize: 18, fontWeight: '600', marginTop: 8, textAlign: 'center', opacity: 0.9 }}>{label}</Text>
    </View>
  );
};

export default ChatGPTStyleVoiceAnimation; 