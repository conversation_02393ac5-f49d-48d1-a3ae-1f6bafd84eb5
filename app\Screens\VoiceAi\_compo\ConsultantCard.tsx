import React from 'react'
import { View, Text, Image, TouchableOpacity } from 'react-native'
import tw from 'twrnc'

export default function ConsultantCard({ consultant }) {
  return (
    <View style={tw`bg-white p-4 rounded-xl mb-4 shadow-md flex-row`}>
      <Image
        source={{ uri: consultant.pfp || 'https://via.placeholder.com/100' }}
        style={tw`w-16 h-16 rounded-full mr-4`}
      />
      <View style={tw`flex-1`}>
        <Text style={tw`text-lg font-semibold text-gray-800`}>{consultant.name || "No Name"}</Text>
        <Text style={tw`text-gray-600`}>{consultant.category || "Category not defined"}</Text>
        <Text style={tw`text-blue-500 font-bold mt-1`}>₹ {consultant.price || "N/A"}</Text>
      </View>
    </View>
  )
}
