import React, { useEffect, useRef } from 'react';
import { Animated, View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { Video } from 'expo-av';
import tw from 'twrnc';

const MicCircle = ({ level = 0, state = 'idle' }) => {
    const scale = useRef(new Animated.Value(0.8)).current;
    const videoRef = useRef(null);

    useEffect(() => {
        let targetScale = 3.0;

        if (state === 'connecting') {
            targetScale = 1.0;
        } else if (state === 'listening') {
            targetScale = 3.0;
        } else if (state === 'speaking') {
            const clampedLevel = Math.min(level, 1.0);
            targetScale = 3.0 + clampedLevel * 0.6;
        }

        Animated.timing(scale, {
            toValue: targetScale,
            duration: 100,
            useNativeDriver: true,
        }).start();
    }, [level, state]);

    let backgroundColor = '#d1d5db';
    if (state === 'listening') {
        backgroundColor = '#000';
    } else if (state === 'speaking') {
        backgroundColor = level > 0.05 ? '#000' : '#000';
    }

    return (
        <View style={{ alignItems: 'center' }}>
            <Animated.View
                style={[
                    styles.circle,
                    {
                        backgroundColor,
                        transform: [{ scale }],
                        shadowOpacity: state === 'speaking' && level > 0.05 ? 0.4 : 0,
                    },
                ]}
            >
                    <Video
                        ref={videoRef}
                        source={require('@/assets/screens/323-135992580.mp4')}
                        style={styles.video}
                        isMuted
                        isLooping
                        shouldPlay
                            resizeMode={"cover" as any}
                    />
            
            </Animated.View>
            
        </View>
    );
};

const styles = StyleSheet.create({
    circle: {
        width: 70,
        height: 70,
        borderRadius: 35,
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowRadius: 10,
    },
    video: {
        width: '100%',
        height: '100%',
        borderRadius: 35,
    },
});

export default MicCircle;
