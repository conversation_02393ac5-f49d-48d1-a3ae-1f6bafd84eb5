"use client"

import { useRef, useEffect, useState } from "react"
import { View, Animated, Easing } from "react-native"
import tw from "twrnc"
import * as Speech from "expo-speech"
import { useColorScheme } from "react-native"
import { Video } from "expo-av"
// Add prop type for userVoiceLevel
interface ExpoSpeechVoiceAnimationProps {
  isActive?: boolean;
  isSpeaking?: boolean;
  isUserSpeaking?: boolean;
  isThinking?: boolean;
  AivoiceLevel?: number;
  userVoiceLevel?: number;
}

function ExpoSpeechVoiceAnimation({
  isActive = false,
  isSpeaking = false,
  isUserSpeaking = false,
  isThinking = false,
  AivoiceLevel = 0,
  userVoiceLevel = 0,
}: ExpoSpeechVoiceAnimationProps) {
  const circleScale = useRef(new Animated.Value(1)).current
  const circleOpacity = useRef(new Animated.Value(0.7)).current

  const bar1Scale = useRef(new Animated.Value(30)).current
  const bar2Scale = useRef(new Animated.Value(30)).current
  const bar3Scale = useRef(new Animated.Value(30)).current
  const bar4Scale = useRef(new Animated.Value(30)).current

  const circleContainerScale = useRef(new Animated.Value(1)).current
  const barsContainerScale = useRef(new Animated.Value(0)).current

  const thinkingCircleScale = useRef(new Animated.Value(1)).current
  const thinkingCircleOpacity = useRef(new Animated.Value(0.4)).current

  const colorScheme = useColorScheme()
  const isDarkMode = colorScheme === "dark"
  const barAnimations = [bar1Scale, bar2Scale, bar3Scale, bar4Scale]
  const [isActuallySpeaking, setIsActuallySpeaking] = useState(false)
  const videoScale = useRef(new Animated.Value(0.8)).current
  const videoOpacity = useRef(new Animated.Value(0)).current
  // Add rotation animated value
  const videoRotation = useRef(new Animated.Value(0)).current

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined
    if (isSpeaking) {
      interval = setInterval(async () => {
        const speaking = await Speech.isSpeakingAsync()
        setIsActuallySpeaking(speaking)
      }, 100)
    } else {
      setIsActuallySpeaking(false)
    }
    return () => interval && clearInterval(interval)
  }, [isSpeaking])

  videoScale.setValue(0.8)
  videoOpacity.setValue(0)

  Animated.parallel([
    Animated.timing(videoScale, {
      toValue: 1,
      duration: 0.1,
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    }),
    Animated.timing(videoOpacity, {
      toValue: 1,
      duration: 0.1,
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    }),
  ]).start()

  // Start slow rotation animation
  const rotateAnim = Animated.loop(
    Animated.timing(videoRotation, {
      toValue: 1,
      duration: 86400000, // 24 hours for one full rotation (Earth speed)
      easing: Easing.linear,
      useNativeDriver: true,
    })
  )
  rotateAnim.start()

  // Interpolate rotation value to degrees
  const videoRotateInterpolate = videoRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  })

  // Enhanced random bars animation function with more randomness
  const animateSmoothRandomBars = () => {
    barAnimations.forEach((bar, index) => {
      // More random height range (20 to 120)
      const randomHeight = 20 + Math.random() * 100;

      // Random duration between 500ms and 1500ms
      const randomDuration = 500 + Math.random() * 1000;

      // Random easing selection
      const easingOptions = [
        Easing.bezier(0.25, 0.1, 0.25, 1),
        Easing.bezier(0.4, 0, 0.2, 1),
        Easing.bezier(0.68, -0.55, 0.265, 1.55),
        Easing.bezier(0.175, 0.885, 0.32, 1.275),
        Easing.inOut(Easing.ease),
        Easing.out(Easing.ease),
      ];
      const randomEasing = easingOptions[Math.floor(Math.random() * easingOptions.length)];

      Animated.timing(bar, {
        toValue: randomHeight,
        duration: randomDuration,
        easing: randomEasing,
        useNativeDriver: false,
      }).start();
    });
  };

  const switchToMode = (mode: string, callback?: () => void) => {
    if (mode === "bars") {
      Animated.parallel([
        Animated.timing(circleContainerScale, {
          toValue: 0,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(barsContainerScale, {
          toValue: 1,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
      ]).start(callback)
    } else {
      Animated.parallel([
        Animated.timing(barsContainerScale, {
          toValue: 0,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(circleContainerScale, {
          toValue: 1,
          duration: 400,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
      ]).start(callback)
    }
  }

  useEffect(() => {
    let animationLoop: Animated.CompositeAnimation | undefined
    let levelInterval: NodeJS.Timeout | undefined

    if (isActive && isSpeaking && !isUserSpeaking && isActuallySpeaking) {
      switchToMode("bars", () => {
        barAnimations.forEach((barAnim) => {
          Animated.timing(barAnim, {
            toValue: 30,
            duration: 200,
            easing: Easing.out(Easing.ease),
            useNativeDriver: false,
          }).start()
        })

        // Start enhanced random animation with variable intervals
        const startRandomAnimation = () => {
          animateSmoothRandomBars();
          // Random interval between 300ms and 900ms
          const randomInterval = 300 + Math.random() * 600;
          levelInterval = setTimeout(() => {
            startRandomAnimation();
          }, randomInterval);
        };

        startRandomAnimation();
      })
    } else if (isActive && isUserSpeaking) {
      switchToMode("circle", () => {
        const pulseAnimation = Animated.loop(
          Animated.sequence([
            Animated.parallel([
              Animated.timing(circleScale, {
                toValue: 1,
                duration: 800,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(circleOpacity, {
                toValue: 1,
                duration: 800,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
            Animated.parallel([
              Animated.timing(circleScale, {
                toValue: 1,
                duration: 800,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(circleOpacity, {
                toValue: 1,
                duration: 800,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
          ])
        )
        barAnimations.forEach((barAnim) => {
          Animated.timing(barAnim, {
            toValue: 10,
            duration: 200,
            easing: Easing.out(Easing.ease),
            useNativeDriver: false,
          }).start()
        })
        pulseAnimation.start()
        animationLoop = pulseAnimation
      })
    } else if (isActive && isThinking) {
      switchToMode("circle", () => {
        const thinkingAnim = Animated.loop(
          Animated.sequence([
            Animated.parallel([
              Animated.timing(thinkingCircleScale, {
                toValue: 1,
                duration: 1500,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(thinkingCircleOpacity, {
                toValue: 1,
                duration: 1500,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
            Animated.parallel([
              Animated.timing(thinkingCircleScale, {
                toValue: 1,
                duration: 1500,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(thinkingCircleOpacity, {
                toValue: 1,
                duration: 1500,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
          ])
        )
        thinkingAnim.start()
        animationLoop = thinkingAnim
      })
    } else if (isActive) {
      switchToMode("circle", () => {
        const idleAnimation = Animated.loop(
          Animated.sequence([
            Animated.parallel([
              Animated.timing(circleScale, {
                toValue: 1.05,
                duration: 2000,
                useNativeDriver: true,
              }),
              Animated.timing(circleOpacity, {
                toValue: 1.05,
                duration: 2000,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
            Animated.parallel([
              Animated.timing(circleScale, {
                toValue: 1,
                duration: 2000,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(circleOpacity, {
                toValue: 1,
                duration: 2000,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ]),
          ])
        )
        barAnimations.forEach((barAnim) => {
          Animated.timing(barAnim, {
            toValue: 1,
            duration: 300,
            easing: Easing.out(Easing.ease),
            useNativeDriver: false,
          }).start()
        })
        idleAnimation.start()
        animationLoop = idleAnimation
      })
    } else {
      Animated.parallel([
        Animated.timing(circleContainerScale, {
          toValue: 1.05,
          duration: 300,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(barsContainerScale, {
          toValue: 1,
          duration: 300,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
      ]).start()
    }

    return () => {
      if (animationLoop && animationLoop.stop) animationLoop.stop()
      if (levelInterval) clearTimeout(levelInterval)
    }
  }, [isActive, isSpeaking, isUserSpeaking, isActuallySpeaking, isThinking, AivoiceLevel])

  // Animate circle scale based on userVoiceLevel when user is speaking
  useEffect(() => {
    if (isUserSpeaking) {
      // Clamp scale between 1 and 1.4
      const scale = Math.min(1.4, 1 + userVoiceLevel * 0.4);
      circleScale.setValue(scale);
    } else {
      circleScale.setValue(1);
    }
  }, [userVoiceLevel, isUserSpeaking]);

  if (!isActive) return null

  return (
    <View style={tw`flex items-center justify-center h-40`}>
      {/* User Speaking - Circle */}
      <Animated.View
        style={[
          tw`absolute`,
          {
            transform: [{ scale: circleContainerScale }],
          },
        ]}
      >
        <Animated.View
          style={[
            tw`rounded-full overflow-hidden border-2 ${isDarkMode ? "border-zinc-950 bg-zinc-950 p-1" : "p-1 "}`,
            {
              backgroundColor: isDarkMode ? "#09090b" : "white",
              width: 270,
              height: 270,
              transform: [
                { rotate: videoRotateInterpolate },
                isUserSpeaking ? { scale: circleScale } : { scale: 1 },
              ],
            },
          ]}
        >
          <Video
            source={require('@/assets/screens/323-135992580.mp4')}
            style={tw`w-full h-full rounded-full`}
            shouldPlay
            isLooping
            isMuted
            resizeMode={"cover" as any}
          />
        </Animated.View>
      </Animated.View>

      {/* AI Speaking Bars */}
      <Animated.View
        style={[
          tw`absolute flex-row items-center justify-center h-40`,
          {
            transform: [{ scale: barsContainerScale }],
          },
        ]}
      >
        {barAnimations.map((barAnim, index) => (
          <Animated.View
            key={index}
            style={[
              tw`mx-1 rounded-full`,
              {
                backgroundColor: isDarkMode ? "#BBEAC3" : "black",
                width: 51,
                height: barAnim,
              },
            ]}
          />
        ))}
      </Animated.View>

    </View>
  )
}

export default ExpoSpeechVoiceAnimation