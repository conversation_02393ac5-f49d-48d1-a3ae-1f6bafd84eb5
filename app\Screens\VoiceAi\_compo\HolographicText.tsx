"use client"

import type React from "react"
import { useEffect, useRef } from "react"
import { Text, Animated } from "react-native"
import { LinearGradient } from "expo-linear-gradient"

interface HolographicTextProps {
  children: React.ReactNode
  style?: any
  isAnimating?: boolean
}

export const HolographicText: React.FC<HolographicTextProps> = ({ children, style, isAnimating = true }) => {
  const glowAnimation = useRef(new Animated.Value(0)).current
  const scaleAnimation = useRef(new Animated.Value(1)).current
  const opacityAnimation = useRef(new Animated.Value(0.8)).current

  useEffect(() => {
    if (isAnimating) {
      // Glow pulsing effect
      const glow = Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnimation, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: false,
          }),
          Animated.timing(glowAnimation, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: false,
          }),
        ]),
      )

      // Scale pulsing effect
      const scale = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnimation, {
            toValue: 1.05,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnimation, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
        ]),
      )

      // Opacity shimmer
      const opacity = Animated.loop(
        Animated.sequence([
          Animated.timing(opacityAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnimation, {
            toValue: 0.6,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      )

      glow.start()
      scale.start()
      opacity.start()

      return () => {
        glow.stop()
        scale.stop()
        opacity.stop()
      }
    }
  }, [isAnimating])

  const borderColor = glowAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ["rgba(0, 255, 255, 0.3)", "rgba(255, 0, 255, 0.8)"],
  })

  const shadowColor = glowAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ["rgba(0, 255, 255, 0.5)", "rgba(255, 0, 255, 0.9)"],
  })

  return (
    <Animated.View
      style={{
        transform: [{ scale: scaleAnimation }],
        opacity: opacityAnimation,
      }}
    >
      <Animated.View
        style={[
          {
            borderRadius: 20,
            borderWidth: 1,
            borderColor: borderColor,
            shadowColor: shadowColor,
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 1,
            shadowRadius: 10,
            elevation: 10,
          },
        ]}
      >
        <LinearGradient
          colors={[
            "#FF00FF", // Magenta
            "#00FFFF", // Cyan
            "#FFFF00", // Yellow
            "#FF00FF", // Magenta
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{
            borderRadius: 20,
            paddingHorizontal: 16,
            paddingVertical: 8,
          }}
        >
          <Text
            style={[
              style,
              {
                color: "white",
                fontWeight: "bold",
                textShadowColor: "rgba(0,0,0,0.5)",
                textShadowOffset: { width: 1, height: 1 },
                textShadowRadius: 3,
                textAlign: "center",
              },
            ]}
          >
            {children}
          </Text>
        </LinearGradient>
      </Animated.View>

      {/* Additional glow layers for holographic effect */}
      <Animated.View
        style={[
          {
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            borderRadius: 20,
            opacity: glowAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 0.3],
            }),
          },
        ]}
      >
        <LinearGradient
          colors={["transparent", "#FFFFFF", "transparent"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            flex: 1,
            borderRadius: 20,
          }}
        />
      </Animated.View>
    </Animated.View>
  )
}
