"use client"

import { useRef, useEffect } from "react"
import { View, Animated, Easing } from "react-native"
import { Image } from "moti"
import tw from "twrnc"

interface ThinkingBubbleAnimationProps {
  isVisible: boolean
}

export default function ThinkingBubbleAnimation({ isVisible }: ThinkingBubbleAnimationProps) {
  const scaleAnim = useRef(new Animated.Value(0)).current
  const opacityAnim = useRef(new Animated.Value(0)).current
  const floatAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    if (isVisible) {
      // Start the thinking bubble animation
      Animated.parallel([
        // Scale in animation
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500,
          easing: Easing.elastic(1.2),
          useNativeDriver: true,
        }),
        // Fade in animation
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
      ]).start()

      // Floating animation loop
      const floatingAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(floatAnim, {
            toValue: -10,
            duration: 2000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(floatAnim, {
            toValue: 10,
            duration: 2000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ]),
      )
      floatingAnimation.start()

      return () => {
        floatingAnimation.stop()
      }
    } else {
      // Hide the thinking bubble
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 300,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 200,
          easing: Easing.ease,
          useNativeDriver: true,
        }),
      ]).start()
    }
  }, [isVisible])

  if (!isVisible) return null

  return (
    <View style={tw`absolute items-center justify-center`}>
      <Animated.View
        style={[
          tw`items-center justify-center`,
          {
            transform: [{ scale: scaleAnim }, { translateY: floatAnim }],
            opacity: opacityAnim,
          },
        ]}
      >
       
      </Animated.View>
    </View>
  )
}
