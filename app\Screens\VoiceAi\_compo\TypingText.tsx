import React, { useEffect, useState, useRef } from "react";
import { Text } from "react-native";

interface TypingTextProps {
  text: string;
  isTyping: boolean;
  onTypingEnd?: () => void;
  style?: any;
  typingSpeed?: number; // ms per character
}

const TypingText: React.FC<TypingTextProps> = ({
  text,
  isTyping,
  onTypingEnd,
  style,
  typingSpeed = 1, // smoother, faster
}) => {
  const [displayed, setDisplayed] = useState("");
  const [showCursor, setShowCursor] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Blinking cursor effect
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor((prev) => !prev);
    }, 500);
    return () => clearInterval(cursorInterval);
  }, []);

  useEffect(() => {
    if (!isTyping) {
      setDisplayed(text);
      return;
    }
    setDisplayed("");
    let i = 0;
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      i++;
      setDisplayed(text.slice(0, i));
      if (i >= text.length) {
        if (intervalRef.current) clearInterval(intervalRef.current);
        onTypingEnd && onTypingEnd();
      }
    }, typingSpeed);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [text, isTyping, typingSpeed, onTypingEnd]);

  return (
    <Text style={style}>
      {displayed}
      {isTyping && showCursor ? "|" : " "}
    </Text>
  );
};

export default TypingText; 