// components/VoiceChatEndedSnackbar.tsx
import React, { useEffect } from "react";
import { View, Text, Animated, TouchableOpacity } from "react-native";
import { ThumbsUp, ThumbsDown, Mic } from "lucide-react-native";

interface VoiceChatEndedSnackbarProps {
  visible: boolean;
  onClose: () => void;
  onFeedback: (type: "up" | "down") => void;
  chatDuration?: number;
}

export default function VoiceChatEndedSnackbar({ 
  visible, 
  onClose, 
  onFeedback, 
  chatDuration = 0 
}: VoiceChatEndedSnackbarProps) {
  const opacity = React.useRef(new Animated.Value(0)).current;
  const translateY = React.useRef(new Animated.Value(-150)).current;

  useEffect(() => {
    if (visible) {
      // Reset position to start from above
      translateY.setValue(-150);
      opacity.setValue(0);
      
      // Slide down from above with fade
      Animated.parallel([
        Animated.timing(opacity, { 
          toValue: 1, 
          duration: 500, 
          useNativeDriver: true 
        }),
        Animated.timing(translateY, { 
          toValue: 0, 
          duration: 500, 
          useNativeDriver: true 
        })
      ]).start();

      const timeout = setTimeout(() => {
        // Slide back up to disappear
        Animated.parallel([
          Animated.timing(opacity, { 
            toValue: 0, 
            duration: 500, 
            useNativeDriver: true 
          }),
          Animated.timing(translateY, { 
            toValue: -150, 
            duration: 500, 
            useNativeDriver: true 
          })
        ]).start(() => onClose());
      }, 4000); // 4 seconds visible
      
      return () => clearTimeout(timeout);
    } else {
      // Hide immediately when not visible
      opacity.setValue(0);
      translateY.setValue(-150);
    }
  }, [visible]);

  // Format duration to show minutes and seconds
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  return (
    <Animated.View
      style={{
        position: "absolute",
        top: 60,
        alignSelf: "center",
        backgroundColor: "#fff",
        borderRadius: 18,
        flexDirection: "row",
        alignItems: "center",
        paddingHorizontal: 18,
        paddingVertical: 12,
        shadowColor: "#000",
        shadowOpacity: 0.15,
        shadowRadius: 10,
        elevation: 8,
        opacity,
        transform: [{ translateY }],
        zIndex: 1000,
        // Hide when not visible
        display: visible ? 'flex' : 'none',
      }}
    >
      <Mic size={24} color="#222" style={{ marginRight: 10 }} />
      <View style={{ flex: 1 }}>
        <Text style={{ fontWeight: "bold", fontSize: 16, color: "#222" }}>
          Voice chat ended
        </Text>
        <Text style={{ color: "#888", fontSize: 14 }}>
          {formatDuration(chatDuration)}
        </Text>
      </View>
      <TouchableOpacity 
        onPress={() => onFeedback("down")} 
        style={{ marginHorizontal: 6, padding: 4 }}
      >
        <ThumbsDown size={28} color="#444" />
      </TouchableOpacity>
      <TouchableOpacity 
        onPress={() => onFeedback("up")}
        style={{ padding: 4 }}
      >
        <ThumbsUp size={28} color="#444" />
      </TouchableOpacity>
    </Animated.View>
  );
}
