import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';
import tw from 'twrnc';

const VoiceVisualizer = ({ isSpeaking }) => {
  const barAnims = Array.from({ length: 7 }, () => useRef(new Animated.Value(1)).current);

  useEffect(() => {
    let loops = [];

    if (isSpeaking) {
      loops = barAnims.map(anim =>
        Animated.loop(
          Animated.sequence([
            Animated.timing(anim, {
              toValue: Math.random() * 2 + 0.5,
              duration: 200,
              useNativeDriver: true,
            }),
            Animated.timing(anim, {
              toValue: 1,
              duration: 200,
              useNativeDriver: true,
            }),
          ])
        )
      );
      loops.forEach(loop => loop.start());
    } else {
      barAnims.forEach(anim => anim.stopAnimation(() => anim.setValue(1)));
    }

    return () => {
      loops.forEach(loop => loop.stop());
      barAnims.forEach(anim => anim.setValue(1));
    };
  }, [isSpeaking]);

  return (
    <View style={tw`flex-row items-end justify-center h-40`}>
      {barAnims.map((anim, index) => (
        <Animated.View
          key={index}
          style={[
            tw`bg-purple-500 mx-1 rounded-full`,
            {
              width: 10,
              height: 60,
              transform: [{ scaleY: anim }],
            },
          ]}
        />
      ))}
    </View>
  );
};

export default VoiceVisualizer;
