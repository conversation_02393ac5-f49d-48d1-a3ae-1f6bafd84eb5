import React from 'react';
import { View, Text, TouchableOpacity, Image, Linking, useColorScheme } from 'react-native';
import tw from 'twrnc';
import { useNavigation } from "@react-navigation/native";
import { Svg, Path } from 'react-native-svg';
import { AnimatePresence } from 'moti';
import { MotiView } from 'moti';

const CompactSearchSources = ({ sources = [] }) => {
  const navigation = useNavigation();
  const colorScheme = useColorScheme();
  const isDarkMode = colorScheme === 'dark';

  if (sources.length === 0) return null;

  return (
    <TouchableOpacity
      style={[
        tw`flex-row items-center border rounded-full px-3 py-2 mx-2 my-1  ${isDarkMode ? "bg-[#9CC2A4]" : "bg-[#BBEAC3]"}`,
        { borderColor: isDarkMode ? '#BBEAC3' : 'white' }
      ]}
      onPress={() => {
        navigation.navigate('SourcesPage', { sources });
      }}
    >
      {/* Favicons */}
      <View style={tw`flex-row -space-x-1 mr-2`}>
        {sources.slice(0, 3).map((source, index, arr) => {
          const isLastTwo = index >= arr.length - 2;

          return (
            <View
              key={index}
              style={[
                tw`w-5 h-5 rounded-full overflow-hidden `,
                tw.style(
                  isDarkMode ? `bg-white border-black` : `bg-black border-white`,
                  isLastTwo && `-ml-3` // ✅ tailwind classes
                ),
                { borderWidth: 1 }
              ]}
            >
              <Image
                source={{
                  uri: source.favicon || `https://www.google.com/s2/favicons?domain=${new URL(source.link).hostname}&sz=32`
                }}
                style={tw`w-full h-full`}
                resizeMode="contain"
              />
            </View>
          );
        })}

      </View>

      {/* Text */}
      <Text style={[
        tw`text-sm font-medium`,
        { color: isDarkMode ? '#fff' : 'black' }
      ]}>
        {sources.length} source{sources.length !== 1 ? 's' : ''}
      </Text>
    </TouchableOpacity>
  );
};

export default CompactSearchSources;
