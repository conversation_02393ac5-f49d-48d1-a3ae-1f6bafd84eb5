import React, { useRef, useEffect } from "react"
import { Animated, Easing } from "react-native"
import Svg, { Path, Mask, Rect, Defs } from "react-native-svg"
import { useColorScheme } from "react-native"

const Thinking = (props) => {
  const colorScheme = useColorScheme()
  const isDark = colorScheme === "dark"

  const rotateAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 4000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start()
  }, [])

  const rotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  })

  return (
    <Animated.View style={{ transform: [{ rotate: rotation }] }}>
      <Svg
        width={337}
        height={352}
        viewBox="0 0 337 352"
        fill="none"
        {...props}
      >
        <Defs>
          <Mask id="mask0" fill={isDark ? "#fff" : "#000"}>
            <Path d="M121.331 27.9799C153.732 2.817 157.556 -0.900702 187.221 0.158159C191.79 0.481726 197.201 0.864947 203.944 1.12331C245.288 2.70735 271.17 55.1726 271.17 55.1726C368.516 100.393 340.738 183.118 297.051 217.321C285.848 295.879 215.308 342.753 142.097 282.785C108.092 298.496 11.7668 250.892 40.5865 159.914C7.33 75.8219 67.0244 28.2302 121.331 27.9799Z" />
          </Mask>
        </Defs>

        <Path
          d="M121.331 27.9799C153.732 2.817 157.556 -0.900702 187.221 0.158159C191.79 0.481726 197.201 0.864947 203.944 1.12331C245.288 2.70735 271.17 55.1726 271.17 55.1726C368.516 100.393 340.738 183.118 297.051 217.321C285.848 295.879 215.308 342.753 142.097 282.785C108.092 298.496 11.7668 250.892 40.5865 159.914C7.33 75.8219 67.0244 28.2302 121.331 27.9799Z"
          fill={isDark ? "#fff" : "#000"}
        />

        <Path
          d="M121.024 27.585C121.229 27.8483 121.433 28.1116 121.638 28.3748C121.46 28.0908 121.385 27.9385 121.411 27.9178C134.436 18.346 146.348 6.35635 162.158 1.7866C170.323 -0.344593 178.814 -0.0927468 187.221 0.158159C192.79 0.559373 198.364 0.913626 203.944 1.12331C235.024 2.94176 257.114 29.6014 271.17 55.1726C301.596 68.9928 331.138 93.5129 336.121 128.095C341.164 162.124 323.373 196.37 297.051 217.321C292.238 252.13 273.704 287.556 240.919 302.919C207.896 318.961 168.743 305.479 142.097 282.785C77.5477 297.691 14.8445 222.102 40.5865 159.914C25.381 124.611 27.2098 78.2196 57.9343 52.0037C62.9996 47.4378 68.6181 43.4936 74.5944 40.2178C88.8632 32.4069 105.149 28.3325 121.333 28.4799C121.332 28.1466 121.33 27.8133 121.329 27.4799C104.976 27.8076 88.7687 32.2056 74.5522 40.1416C68.5985 43.4714 62.9997 47.4379 57.9343 52.0037C27.2098 78.2196 25.381 124.611 40.5865 159.914C14.8445 222.102 77.5477 297.691 142.097 282.785C168.743 305.479 207.896 318.961 240.919 302.919C273.704 287.556 292.238 252.13 297.051 217.321C323.373 196.37 341.164 162.124 336.121 128.095C331.138 93.5129 301.596 68.9928 271.17 55.1726C257.114 29.6014 235.024 2.94176 203.944 1.12331C198.364 0.913626 192.79 0.559373 187.221 0.158159C178.814 -0.0927468 170.323 -0.344593 162.158 1.7866C146.348 6.35635 134.436 18.346 121.411 27.9178C121.385 27.9385 121.256 27.8276 121.024 27.585Z"
          fill={isDark ? "#fff" : "#000"}
          mask="url(#mask0)"
        />

      
      </Svg>
    </Animated.View>
  )
}

export default Thinking
