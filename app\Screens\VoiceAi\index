import React, { useRef, useState, useEffect, MutableRefObject } from "react"
import {
  View,
  TouchableOpacity,
  StatusBar,
  ImageBackground,
  Text,
  BackHandler,
  AppState,
} from "react-native"
import { WebView } from "react-native-webview"
import * as Speech from "expo-speech"
import tw from "twrnc"
import { Mic, X, Pause, Play, MoveLeft, EllipsisVertical } from "lucide-react-native"
import { useNavigation } from "@react-navigation/native"
import useAuth from "@/hooks/auth"
import { useKeepAwake } from "expo-keep-awake"
import * as MailComposer from "expo-mail-composer"
import * as Location from "expo-location"
import ExpoSpeechVoiceAnimation from "./_compo/ExpoSpeechVoiceAnimation"
import { useColorScheme } from "react-native"
import Source from "./_compo/source"
import { useFocusEffect } from "expo-router"
import TypingText from "./_compo/TypingText";
import { ScrollView } from "moti"
import { Audio } from "expo-av";
import * as Notifications from 'expo-notifications';

export default function VoiceAi() {
  const webViewRef = useRef<any>(null)
  const scrollRef = useRef<any>(null)

  const [recognizedText, setRecognizedText] = useState("")
  const [response, setResponse] = useState("")
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [isUserSpeaking, setIsUserSpeaking] = useState(false)
  const [status, setStatus] = useState("connecting")
  const [isMuted, setIsMuted] = useState(false)
  const [isVoiceLevels, setIsVoiceLevels] = useState(false)
  const [Sources, setSources] = useState([])
  const [voiceLevel, setVoiceLevel] = useState(0)
  const [isTyping, setIsTyping] = useState(false);
  const notificationIdRef = useRef<string | null>(null);
  const voiceInterval = useRef<NodeJS.Timeout | null>(null)
  const speakingLock = useRef<boolean>(false)
  const navigation = useNavigation()
  const userId = useAuth()
  const colorScheme = useColorScheme()
  const isDark = colorScheme === "dark"

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        navigation.reset({
          index: 0,
          routes: [{ name: 'BottomTabs' as never }],
        });
        return true; // Prevent default back behavior
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();

    }, [])
  );

  useKeepAwake()

  useEffect(() => {
    Notifications.requestPermissionsAsync();
  }, []);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', async (nextAppState) => {
      if (nextAppState === 'background' && isSpeaking) {
        // Show notification
        const id = await Notifications.scheduleNotificationAsync({
          content: {
            title: "Voice Agent Active",
            body: "AI se baat ho rahi hai...",
            sound: false,
            sticky: true, // Android only
          },
          trigger: null,
        });
        notificationIdRef.current = id;
      } else if (nextAppState === 'active') {
        // Remove notification
        if (notificationIdRef.current) {
          await Notifications.dismissNotificationAsync(notificationIdRef.current);
          notificationIdRef.current = null;
        }
      }
    });
    return () => subscription.remove();
  }, [isSpeaking]);

  useEffect(() => {
    if (isUserSpeaking) {
      setStatus("listening")
    } else if (!isSpeaking && recognizedText === "") {
      setStatus("connecting")
    }
  }, [isSpeaking, recognizedText, isUserSpeaking])

  useEffect(() => {
    setIsVoiceLevels(voiceLevel > 0)
  }, [voiceLevel])

  useEffect(() => {
    scrollRef.current?.scrollToEnd({ animated: true })
  }, [response])

  const speakWithVoiceLevel = (text: string, onDone: () => void) => {
    setIsSpeaking(true)
    setStatus("speaking")
    setIsTyping(true) // Start typing

    voiceInterval.current = setInterval(() => {
      setVoiceLevel(Math.random())
    }, 100)

    const cleanup = () => {
      if (voiceInterval.current) {
        clearInterval(voiceInterval.current)
        voiceInterval.current = null
      }
      setVoiceLevel(0)
      setIsTyping(false) // Stop typing
      onDone()
    }

    Speech.speak(text, {
      language: "hi-IN",
      rate: 1.0,
      pitch: 1.0,
      onDone: cleanup,
      onStopped: cleanup,
      onError: (e) => {
        console.warn("Speech error:", e)
        cleanup()
      },
    })
  }



  const handleMessage = async (event: { nativeEvent: { data: string } }) => {
    const message = event.nativeEvent.data

    if (message === "RECOGNITION_STARTED") {
      setIsUserSpeaking(true);
      return;
    }

    if (message === "RECOGNITION_STOPPED") {
      setIsUserSpeaking(false);
      return;
    }

    if (speakingLock.current || isSpeaking) return

    const normalizedMessage = message.toLowerCase().trim()
    setRecognizedText(normalizedMessage)

    const exitCommands = [
      "band kar do", "band karo", "stop", "exit", "close", "back",
      "बंद कर दो", "बंद करो", "वापस", "पीछे जाओ"
    ]

    if (exitCommands.some(cmd => normalizedMessage.includes(cmd))) {
      Speech.stop()
      setIsSpeaking(false)
      setIsUserSpeaking(false)
      speakingLock.current = false
      webViewRef.current?.injectJavaScript(`stopRecognition();`)
      Speech.speak("ठीक है, बंद कर रहा हूँ", {
        language: "hi-IN",
        onDone: () => navigation.goBack()
      })
      return
    }

    speakingLock.current = true
    setIsUserSpeaking(false)
    webViewRef.current?.injectJavaScript(`stopRecognition();`)
    setStatus("thinking")

    let fullAddress = null

    try {
      const { status } = await Location.requestForegroundPermissionsAsync()
      if (status === "granted") {
        const loc = await Location.getCurrentPositionAsync({})
        const geocode = await Location.reverseGeocodeAsync(loc.coords)
        if (geocode.length > 0) {
          const place = geocode[0]
          fullAddress = `${place.name || ""}, ${place.street || ""}, ${place.city || place.subregion || ""}, ${place.region || ""}, ${place.postalCode || ""}, ${place.country || ""}`
            .replace(/(, )+/g, ", ")
            .trim()
            .replace(/^,|,$/g, "")
        }
      }
    } catch (err) {
      console.warn("Location error:", err)
    }

    try {
      const res = await fetch("https://nx.ai.api.nityasha.com/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          message: normalizedMessage,
          user_id: userId,
          address: fullAddress || null,
        }),
      })

      const data = await res.json()
      let reply = data?.response || "कोई उत्तर नहीं मिला।"
      let source = data?.sources
      setSources(source)


      setResponse(reply)

      const onDoneHandler = () => {
        setIsSpeaking(false)
        setIsUserSpeaking(false)
        setStatus("")
        speakingLock.current = false
        webViewRef.current?.injectJavaScript(`startRecognition();`)
      }

      if (!isMuted) {
        speakWithVoiceLevel(reply, onDoneHandler)
      } else {
        onDoneHandler()
      }
    } catch (error) {
      console.error("AI Error", error)
      setResponse("माफ़ कीजिए, कुछ दिक्कत आ गई। कृपया फिर से कहिए।")
      setIsSpeaking(false)
      setIsUserSpeaking(false)
      setStatus("")
      speakingLock.current = false
      webViewRef.current?.injectJavaScript(`startRecognition();`)
    }
  }

  return (
    <ImageBackground
      source={isDark ? require('@/assets/screens/AIBackgroundScreenDark.png') : require('@/assets/screens/AIBackgroundScreen.png')}
      style={tw`flex-1`}
      resizeMode="cover"
    >
      <TouchableOpacity
        activeOpacity={1}
        style={[tw`absolute top-0 left-0 w-full h-full z-50`]} 
        onPress={() => {
          Speech.stop();
          setIsSpeaking(false);
          setIsUserSpeaking(false);
          speakingLock.current = false;
          webViewRef.current?.injectJavaScript(`stopRecognition();`);
        }}
      />
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor={isDark ? "#000" : "#fff"}
        translucent
      />

      <View style={tw`w-full absolute top-0 h-20 flex-row items-center justify-between pt-5`}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={tw`h-16 w-16 rounded-full flex items-center justify-center`}
          >
            <MoveLeft size={24} color={isDark ? "#fff" : "#000"} />
          </TouchableOpacity> 
          <Text style={[tw`text-lg font-medium ${isDark ? "text-white" : "text-black"}`, { fontFamily: 'GoogleSans-Bold' }]}>Nityasha</Text>

          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={tw`h-16 w-16 rounded-full flex items-center justify-center`}
          >
            <EllipsisVertical size={24} color={isDark ? "#fff" : "#000"} />
          </TouchableOpacity>
          
      </View>

      <View style={tw`flex-1 items-center justify-center`}>
        
      </View>

      <View style={tw`items-center pb-10`}>
        <WebView
          ref={webViewRef}
          source={{ html: updatedSpeechHTML }}
          onMessage={handleMessage}
          javaScriptEnabled
          originWhitelist={["*"]}
          style={{ height: 0, width: 0 }}
          onLoadEnd={() => {
            webViewRef.current?.injectJavaScript(`
              // Mute all audio/video elements
              Array.from(document.querySelectorAll('audio,video')).forEach(el => el.muted = true);
              // Disable speech synthesis if present
              if (window.speechSynthesis) window.speechSynthesis.cancel();
              startRecognition();
            `);
          }}
        />

        <ScrollView
          ref={scrollRef}
          style={tw`px-5 absolute bottom-50 z-40 h-15`}
          onContentSizeChange={() => scrollRef.current?.scrollToEnd({ animated: true })}
        >
          <TypingText
            text={response || ""}
            isTyping={isTyping}
            style={[tw`text-center`, { fontFamily: 'GoogleSans-Medium' }]}
          />

        </ScrollView>
        <View style={tw`flex w-full px-8 justify-between items-center flex-row`}>
          <TouchableOpacity
            onPress={() => setIsMuted(!isMuted)}
            style={tw`h-16 w-16 rounded-full flex items-center justify-center ${isDark ? "bg-white" : "bg-black"}`}
          >
            {isMuted ? (
              <Play size={24} color={isDark ? "#000" : "#fff"} />
            ) : (
              <Pause size={24} color={isDark ? "#000" : "#fff"} />
            )}
          </TouchableOpacity>

          <Source sources={Sources} />

          <TouchableOpacity
            onPress={() => navigation.reset({
              index: 0,
              routes: [{ name: 'BottomTabs' as never }],
            })}
            style={tw`h-16 w-16 rounded-full bg-red-600 flex items-center justify-center`}
          >
            <X size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
    </ImageBackground>
  )
}

const updatedSpeechHTML = `
<html>
  <head>
    <meta charset="UTF-8">
    <title>Speech Recognition</title>
  </head>
  <body style="background-color:black;">
    <script>
      var recognition;
      window.shouldRestartRecognition = true;

      function startRecognition() {
        window.shouldRestartRecognition = true;
        if (recognition) recognition.abort();

        recognition = new webkitSpeechRecognition();
        recognition.lang = 'hi-IN';
        recognition.interimResults = false;
        recognition.maxAlternatives = 1;
        recognition.continuous = true;

        recognition.onstart = function() {
          window.ReactNativeWebView.postMessage("RECOGNITION_STARTED");
        };

        recognition.onend = function() {
          window.ReactNativeWebView.postMessage("RECOGNITION_STOPPED");
          setTimeout(() => {
            if (window.shouldRestartRecognition) {
              startRecognition();
            }
          }, 1000);
        };

        recognition.onresult = function(event) {
          var transcript = event.results[event.results.length - 1][0].transcript;
          window.ReactNativeWebView.postMessage(transcript);
        };

        recognition.onerror = function(event) {}
        recognition.start();
      }

      function stopRecognition() {
        window.shouldRestartRecognition = false;
        if (recognition) recognition.stop();
      }
    </script>
  </body>
</html>
`
