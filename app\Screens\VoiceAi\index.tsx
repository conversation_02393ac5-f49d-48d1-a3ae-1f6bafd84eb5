// app/Screens/VoiceAi/index.tsx
import React, { useEffect, useState, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  useColorScheme,
  Pressable,
  ScrollView,
  ImageBackground,
} from 'react-native';
import {
  AudioSession,
  AndroidAudioTypePresets,
  BarVisualizer,
  LiveKitRoom,
  useIOSAudioManagement,
  useLocalParticipant,
  useParticipantTracks,
  useRoomContext,
  useTrackTranscription,
  useVoiceAssistant,
} from '@livekit/react-native';
import { Track } from 'livekit-client';
import { useConnectionDetails } from '@/hooks/useConnectionDetails';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { registerGlobals } from '@livekit/react-native';
import tw from 'twrnc'
import { Mic, MicOff, X } from 'lucide-react-native';
import CustomVisualizer from './_compo/CustomVisualizer';
registerGlobals();

export default function AssistantScreen() {
  useEffect(() => {
    (async () => {
      // Configure audio session for main loudspeaker output
      await AudioSession.configureAudio({
        android: {
          preferredOutputList: ['speaker'],
          audioTypeOptions: AndroidAudioTypePresets.media,
        },
        ios: {
          defaultOutput: 'speaker',
        },
      });
      await AudioSession.startAudioSession();
    })();
    return () => {
      AudioSession.stopAudioSession();
    };
  }, []);

  const connectionDetails = useConnectionDetails();
  
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <LiveKitRoom
        serverUrl={connectionDetails?.url}
        token={connectionDetails?.token}
        connect={true}
        audio={true}
        video={false}
      >
        <RoomView />
      </LiveKitRoom>
    </SafeAreaView>
  );
}

const RoomView = () => {
  const router = useRouter();
  const room = useRoomContext();
  useIOSAudioManagement(room, true);
  const { state } = useVoiceAssistant();

  const { isMicrophoneEnabled, localParticipant } = useLocalParticipant();

  const localTracks = useParticipantTracks(
    [Track.Source.Microphone],
    localParticipant.identity
  );
  const micTrack = localTracks[0];

  const {
    segments: userTranscriptions = [],
  } = micTrack ? useTrackTranscription(micTrack) : { segments: [] };

  const { segments: agentTranscriptions = [] } = useVoiceAssistant();

  // Also monitor agent transcriptions for speaking activity
  useEffect(() => {
    console.log('Agent transcriptions changed:', agentTranscriptions); // Debug log

    if (agentTranscriptions.length > 0) {
      const latestSegment = agentTranscriptions[agentTranscriptions.length - 1];
      console.log('Latest agent segment:', latestSegment); // Debug log

      // If there's a recent transcription, agent is likely speaking
      if (latestSegment && !latestSegment.final) {
        console.log('Agent is transcribing - starting audio animation'); // Debug log

        if (agentSpeakingInterval.current) {
          clearInterval(agentSpeakingInterval.current);
        }

        agentSpeakingInterval.current = setInterval(() => {
          const level = Math.random() * 0.6 + 0.3;
          setAgentAudioLevel(level);
        }, 100);
      } else if (latestSegment && latestSegment.final) {
        console.log('Agent finished speaking - stopping audio animation'); // Debug log

        // Agent finished speaking
        setTimeout(() => {
          if (agentSpeakingInterval.current) {
            clearInterval(agentSpeakingInterval.current);
            agentSpeakingInterval.current = null;
          }
          setAgentAudioLevel(0);
        }, 500); // Small delay to let animation finish
      }
    }
  }, [agentTranscriptions]);

  const [assistantReply, setAssistantReply] = useState<string>('');
  const [agentAudioLevel, setAgentAudioLevel] = useState<number>(0);
  const agentSpeakingInterval = useRef<NodeJS.Timeout | null>(null);

  // Handle agent speaking state based on useVoiceAssistant state
  useEffect(() => {
    console.log('Voice Assistant State:', state); // Debug log

    if (state === 'speaking') {
      console.log('Agent is speaking - starting audio animation'); // Debug log
      // Agent is speaking - start audio level animation
      if (agentSpeakingInterval.current) {
        clearInterval(agentSpeakingInterval.current);
      }

      agentSpeakingInterval.current = setInterval(() => {
        const level = Math.random() * 0.6 + 0.3; // Random value between 0.3 and 0.9
        setAgentAudioLevel(level);
        console.log('Setting agent audio level:', level); // Debug log
      }, 100);
    } else {
      console.log('Agent is not speaking - stopping audio animation'); // Debug log
      // Agent is not speaking - stop animation
      if (agentSpeakingInterval.current) {
        clearInterval(agentSpeakingInterval.current);
        agentSpeakingInterval.current = null;
      }
      setAgentAudioLevel(0);
    }

    return () => {
      if (agentSpeakingInterval.current) {
        clearInterval(agentSpeakingInterval.current);
      }
    };
  }, [state]);

  useEffect(() => {
    const handleData = (payload: Uint8Array) => {
      try {
        const decoded = new TextDecoder().decode(payload);
        const { reply } = JSON.parse(decoded);
        if (reply) setAssistantReply(reply);
      } catch (e) {
        console.warn('Failed parsing dataReceived', e);
      }
    };
    room.on('dataReceived', handleData);
    return () => {
      room.off('dataReceived', handleData);
    };
  }, [room]);

  return (
    <ImageBackground source={require('@/assets/screens/AIBackgroundScreen.png')} style={tw`w-full flex items-center justify-between h-full`}>
      <View style={tw`w-full`} />
      <View style={tw`h-auto my-6 items-center`}>
        <CustomVisualizer level={agentAudioLevel} state={state} />
      </View>

      <View style={tw`flex-row w-full justify-between px-4 pb-5`}>
        <Pressable
          style={tw`bg-gray-100 border-2 w-[70px] h-[70px] rounded-full flex items-center justify-center`}
          onPress={() => {
            router.back();
          }}
        >
          <X size={24} color="#000" />
        </Pressable>
        <Pressable
          style={tw`${isMicrophoneEnabled ? 'bg-gray-100 border-2' : 'bg-red-600'} w-[70px] h-[70px] rounded-full flex items-center justify-center`}
          onPress={() =>
            localParticipant.setMicrophoneEnabled(!isMicrophoneEnabled)
          }
        >
          {isMicrophoneEnabled ? (
            <Mic size={24} color="#000" />
          ) : (
            <MicOff size={24} color="#fff" />
          )}
        </Pressable>

      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, width: '100%', alignItems: 'center' },
  voiceAssistant: { width: '100%', height: 100 },
  logContainer: { flex: 1, width: '100%' },
  controlsContainer: { flexDirection: 'row', justifyContent: 'center' },
  button: {
    width: 70,
    height: 70,
    margin: 12,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userTranscription: {
    alignSelf: 'flex-end',
    margin: 8,
    padding: 8,
    borderRadius: 6,
    fontSize: 18,
  },
  userTranscriptionLight: { backgroundColor: '#B0B0B0' },
  userTranscriptionDark: { backgroundColor: '#404040' },
  agentTranscription: {
    alignSelf: 'flex-start',
    margin: 8,
    padding: 8,
    fontSize: 20,
  },
  assistantReplyBubble: {
    backgroundColor: '#DDEEFF',
    borderRadius: 6,
  },
  lightThemeText: { color: '#000' },
  darkThemeText: { color: '#fff' },
});
