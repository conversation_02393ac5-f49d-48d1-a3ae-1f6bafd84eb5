"use client"

import { useRef, useState, useEffect } from "react"
import { View, Text, TouchableOpacity, StatusBar, Alert, Animated, Easing, ImageBackground } from "react-native"
import { WebView } from "react-native-webview"
import * as Speech from "expo-speech"
import { Audio } from "expo-av"
import tw from "twrnc"
import { ScrollView } from "moti"
import { Mic, X } from "lucide-react-native"
import { useNavigation } from "@react-navigation/native"
import useAuth from "@/hooks/auth"
import { useKeepAwake } from 'expo-keep-awake'
import * as MailComposer from 'expo-mail-composer'

function ExpoSpeechVoiceAnimation({ isActive = false, isSpeaking = false, isUserSpeaking = false }) {
  const circleScale = useRef(new Animated.Value(1)).current
  const circleOpacity = useRef(new Animated.Value(0.7)).current
  const bar1Scale = useRef(new Animated.Value(50)).current
  const bar2Scale = useRef(new Animated.Value(50)).current
  const bar3Scale = useRef(new Animated.Value(50)).current
  const bar4Scale = useRef(new Animated.Value(50)).current
  const circleContainerScale = useRef(new Animated.Value(1)).current
  const barsContainerScale = useRef(new Animated.Value(0)).current
  const barAnimations = [bar1Scale, bar2Scale, bar3Scale, bar4Scale]
  const [isActuallySpeaking, setIsActuallySpeaking] = useState(false)

  useEffect(() => {
    let interval
    if (isSpeaking) {
      interval = setInterval(async () => {
        const speaking = await Speech.isSpeakingAsync()
        setIsActuallySpeaking(speaking)
      }, 100)
    } else {
      setIsActuallySpeaking(false)
    }
    return () => interval && clearInterval(interval)
  }, [isSpeaking])

  useEffect(() => {
    if (!isActive) return

    if (isSpeaking && !isUserSpeaking && isActuallySpeaking) {
      Animated.parallel([
        Animated.timing(circleContainerScale, { toValue: 0, duration: 400, easing: Easing.ease, useNativeDriver: true }),
        Animated.timing(barsContainerScale, { toValue: 1, duration: 400, easing: Easing.ease, useNativeDriver: true })
      ]).start()

      const animateBars = () => barAnimations.map((bar, i) =>
        Animated.loop(
          Animated.sequence([
            Animated.delay(i * 150),
            Animated.timing(bar, { toValue: 60, duration: 400, useNativeDriver: false }),
            Animated.timing(bar, { toValue: 24, duration: 300, useNativeDriver: false }),
            Animated.timing(bar, { toValue: 48, duration: 350, useNativeDriver: false }),
            Animated.timing(bar, { toValue: 30, duration: 250, useNativeDriver: false })
          ])
        ).start()
      )

      animateBars()

    } else {
      Animated.parallel([
        Animated.timing(barsContainerScale, { toValue: 0, duration: 400, easing: Easing.ease, useNativeDriver: true }),
        Animated.timing(circleContainerScale, { toValue: 1, duration: 400, easing: Easing.ease, useNativeDriver: true })
      ]).start()
    }
  }, [isActive, isSpeaking, isUserSpeaking, isActuallySpeaking])

  if (!isActive) return null

  return (
    <View style={tw`flex items-center justify-center h-40`}>
      <Animated.View style={[tw`absolute`, { transform: [{ scale: circleContainerScale }] }]}>
        <Animated.View style={[tw`rounded-full bg-black`, { width: 190, height: 190, transform: [{ scale: circleScale }] }]} />
      </Animated.View>
      <Animated.View style={[tw`absolute flex-row items-center justify-center h-40`, { transform: [{ scale: barsContainerScale }] }]}>
        {barAnimations.map((bar, i) => (
          <Animated.View key={i} style={[tw`bg-black mx-1 rounded-full`, { width: 30, height: bar }]} />
        ))}
      </Animated.View>
    </View>
  )
}

export default function VoiceAi() {
  const webViewRef = useRef(null)
  const [recognizedText, setRecognizedText] = useState("")
  const [response, setResponse] = useState("")
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [isUserSpeaking, setIsUserSpeaking] = useState(false)
  const [status, setStatus] = useState("connecting")
  const [isMuted, setIsMuted] = useState(false)
  const navigation = useNavigation()
  const speakingLock = useRef(false)
  const userId = useAuth()
  useKeepAwake()

  useEffect(() => {
    Audio.setAudioModeAsync({
      allowsRecordingIOS: true,
      staysActiveInBackground: true,
      playsInSilentModeIOS: true,
      shouldDuckAndroid: false,
      interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX
    })
  }, [])

  useEffect(() => {
    if (isSpeaking) setStatus("thinking")
    else if (isUserSpeaking) setStatus("listening")
    else if (!isSpeaking && recognizedText === "") setStatus("connecting")
    else if (!isSpeaking) setStatus("")
  }, [isSpeaking, recognizedText, isUserSpeaking])

  const handleMessage = async (event) => {
    const message = event.nativeEvent.data

    if (message === "RECOGNITION_STARTED") return setIsUserSpeaking(true)
    if (message === "RECOGNITION_STOPPED") return setIsUserSpeaking(false)
    if (message.startsWith("AUDIO_ERROR")) {
      console.warn("Audio error in WebView:", message)
      return
    }

    const speakingNow = await Speech.isSpeakingAsync()
    if (speakingNow) {
      Speech.stop()
      setIsSpeaking(false)
      setIsUserSpeaking(false)
      setStatus("interrupted by user")
      speakingLock.current = false
      return
    }

    if (speakingLock.current || isSpeaking) return

    speakingLock.current = true
    setRecognizedText(message)
    setIsUserSpeaking(false)
    webViewRef.current?.injectJavaScript(`stopRecognition();`)
    setIsSpeaking(true)
    setStatus("thinking")

    try {
      const res = await fetch("https://nx.ai.api.nityasha.com/speak", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message , user_id: userId })
      })

      const data = await res.json()
      let reply = data?.response || "कोई उत्तर नहीं मिला।"
      const audioBase64 = data?.audioBase64

      try {
        const parsed = JSON.parse(reply)
        if (parsed.type === "email") {
          reply = `बिलकुल! ये रहा आपका मेल:\nTo: ${parsed.to || "<EMAIL>"}\nSubject: ${parsed.subject}\n\n${parsed.body}`
          await MailComposer.composeAsync({
            recipients: [parsed.to || "<EMAIL>"],
            subject: parsed.subject,
            body: parsed.body
          })
        } else if (parsed.type === "todo") {
          reply = `ठीक है, \"${parsed.task}\" को ${parsed.datetime} पर याद रखने के लिए नोट कर लिया गया है।`
        }
      } catch {}

      if (reply.trim().startsWith("{")) {
        reply = "JSON को सीधे मत भेजो। इंसानी अंदाज़ में जवाब दो, जैसे बात कर रहे हो।"
      }

      setResponse(reply)

      const onDoneHandler = () => {
        setIsSpeaking(false)
        setIsUserSpeaking(false)
        setStatus("")
        speakingLock.current = false
        webViewRef.current?.injectJavaScript(`startRecognition();`)
      }

      if (audioBase64 && !isMuted) {
        webViewRef.current?.injectJavaScript(`playBase64PCM("${audioBase64}");`)
        setTimeout(onDoneHandler, 1500 + (audioBase64.length / 60)) // crude audio duration estimate
      } else {
        onDoneHandler()
      }

    } catch (err) {
      Alert.alert("AI Error", "Failed to fetch response")
      setIsSpeaking(false)
      setIsUserSpeaking(false)
      setStatus("")
      speakingLock.current = false
      webViewRef.current?.injectJavaScript(`startRecognition();`)
    }
  }

  return (
    <ImageBackground source={require('@/assets/screens/AIBackgroundScreen.png')} style={tw`flex-1`}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" translucent />

      <View style={tw`pt-6 pb-4 px-6 flex-row items-center justify-between`}>
        <Text style={tw`text-lg font-medium text-gray-800`}>Advanced</Text>
        <TouchableOpacity>
          <View style={tw`w-6 h-6 rounded-full border border-gray-400 items-center justify-center`}>
            <Text style={tw`text-gray-600 text-xs`}>i</Text>
          </View>
        </TouchableOpacity>
      </View>

      <View style={tw`flex-1 items-center justify-center`}>
        <ExpoSpeechVoiceAnimation isActive={true} isSpeaking={isSpeaking} isUserSpeaking={isUserSpeaking} />
      </View>

      <View style={tw`px-6 h-32 mb-4`}>
        <ScrollView style={tw`h-full`} contentContainerStyle={tw`pr-2`} showsVerticalScrollIndicator={false}>
          <Text style={tw`text-gray-800 text-base leading-6 text-center`}>{response}</Text>
        </ScrollView>
      </View>

      <View style={tw`items-center pb-10`}>
        <WebView
          ref={webViewRef}
          source={{ html: updatedSpeechHTML }}
          onMessage={handleMessage}
          javaScriptEnabled
          originWhitelist={["*"]}
          style={{ height: 0, width: 0 }}
          onLoadEnd={() => webViewRef.current?.injectJavaScript(`startRecognition();`)}
        />

        <View style={tw`flex w-full px-8 justify-between items-center flex-row`}>
          <TouchableOpacity
            onPress={() => setIsMuted(!isMuted)}
            style={tw`h-16 w-16 rounded-full ${isMuted ? "bg-gray-300" : "bg-white"} border-black flex items-center justify-center border-2`}
          >
            <Mic size={24} color={isMuted ? "#9CA3AF" : "#374151"} />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={tw`h-16 w-16 rounded-full bg-red-500 flex items-center justify-center border-2 border-black`}
          >
            <X size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>
    </ImageBackground>
  )
}

const updatedSpeechHTML = `
<html><body><script>
var recognition;
window.shouldRestartRecognition = true;

function startRecognition() {
  window.shouldRestartRecognition = true;
  if (recognition) recognition.abort();
  recognition = new webkitSpeechRecognition();
  recognition.lang = 'hi-IN';
  recognition.interimResults = false;
  recognition.maxAlternatives = 1;
  recognition.continuous = true;
  recognition.onstart = () => window.ReactNativeWebView.postMessage("RECOGNITION_STARTED");
  recognition.onend = () => {
    window.ReactNativeWebView.postMessage("RECOGNITION_STOPPED");
    setTimeout(() => window.shouldRestartRecognition && startRecognition(), 1000);
  };
  recognition.onresult = event => {
    const transcript = event.results[event.results.length - 1][0].transcript;
    window.ReactNativeWebView.postMessage(transcript);
  };
  recognition.onerror = e => window.ReactNativeWebView.postMessage("Error: " + e.error);
  recognition.start();
}

function stopRecognition() {
  window.shouldRestartRecognition = false;
  recognition && recognition.stop();
}

function playBase64PCM(base64, sampleRate = 24000) {
  try {
    const raw = atob(base64);
    const pcm = new Int16Array(raw.length / 2);
    for (let i = 0; i < pcm.length; i++) {
      pcm[i] = raw.charCodeAt(i * 2) | (raw.charCodeAt(i * 2 + 1) << 8);
    }

    const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    const buffer = audioCtx.createBuffer(1, pcm.length, sampleRate);
    const channelData = buffer.getChannelData(0);
    for (let i = 0; i < pcm.length; i++) {
      channelData[i] = pcm[i] / 32768;
    }

    const source = audioCtx.createBufferSource();
    source.buffer = buffer;
    source.connect(audioCtx.destination);
    source.start();
  } catch (err) {
    window.ReactNativeWebView.postMessage("AUDIO_ERROR: " + err.message);
  }
}
</script></body></html>`;
