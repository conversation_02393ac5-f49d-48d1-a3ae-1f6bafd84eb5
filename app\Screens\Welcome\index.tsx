import { View, Text, StatusBar, Platform, Touchable, TouchableOpacity, ImageBackground } from 'react-native';
import React, { useState, useEffect } from 'react';
import tw from 'twrnc';
import { Video } from 'expo-av'; // Import the Video component from expo-av
import Heading<PERSON>enter from '@/components/ui/HeadingCenter';
import Paragraph<PERSON>enter from '@/components/ui/ParagraphCenter';
import Button from '@/components/ui/Button';
import * as NavigationBar from 'expo-navigation-bar';
import { BlurView } from 'expo-blur'
import { Image } from 'moti';
import * as Contacts from 'expo-contacts'; // Import Contacts

NavigationBar.setBackgroundColorAsync('#fff'); // Set to black
export default function Welcome({ navigation }) {
    // Add state to track if we can proceed (permission granted)
    const [canProceed, setCanProceed] = useState(false);

    // Request contacts permission when the component mounts
    useEffect(() => {
        (async () => {
            const { status } = await Contacts.requestPermissionsAsync();
            if (status === 'granted') {
                console.log('Contacts permission granted');
                setCanProceed(true); // Set canProceed to true if permission is granted
            } else {
                console.log('Contacts permission denied');
                setCanProceed(false); // Keep canProceed false if permission is denied
            }
        })();
    }, []); // Empty dependency array ensures this runs only once on mount

    return (
        <ImageBackground  source={require('@/assets/screens/WelcomeBackground.png')} style={tw`flex-1`}>
            <StatusBar translucent />


            {/* <Video
                source={require('@/assets/7683259-hd_1080_1920_24fps.mp4')} // Replace with your video file
                style={tw`absolute top-0 left-0 w-full h-full`} // Full-screen styling
                resizeMode="cover" // Maintain aspect ratio
                shouldPlay // Automatically start playing
                isLooping // Loop the video
                isMuted // Mute the video
            /> */}

    <View style={tw`h-full absolute top-0 w-full flex items-center justify-center`}>
        <Image style={[tw`w-20 h-20`,{tintColor: '#fff'}]} source={require('@/assets/images/image.png')} />
                    <Text
                        style={[
                            tw`text-[19px] text-center font-medium text-[#fff] w-[150px] -mt-4`,
                            { fontFamily: 'GoogleSans-Bold' },
                        ]}
                    >
                        A place for your people
                    </Text>
    </View>

            <View style={tw`flex-1 justify-end h-full w-full`}>
                <View style={tw`w-full p-6  rounded-t-2xl`}>
          
                    <View
                        style={tw`w-full flex gap-2 pt-[2rem] mt-4 items-center justify-center`}
                    >
                        <TouchableOpacity
                            style={[tw`w-full bg-white rounded-full flex items-center justify-center py-4`]}
                            onPress={() => navigation.navigate('Login')}
                        >
                            <Text style={[tw`text-[16px] text-[#000] w-full text-center`,{fontFamily: 'Geist-SemiBold'}]}>Login</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={[tw`w-full bg-black rounded-full flex items-center justify-center py-4`]}
                            onPress={() => navigation.navigate('SignUp')}
                        >
                            <Text  style={[tw`text-[16px] text-[#fff] w-full text-center`,{fontFamily: 'Geist-SemiBold'}]}>Sign Up</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </ImageBackground>
    );
}
