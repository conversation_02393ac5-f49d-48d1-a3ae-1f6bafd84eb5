import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, ImageBackground, ActivityIndicator, RefreshControl , Image} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import debounce from 'lodash/debounce';
import { useNavigation, CommonActions } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Searchbar } from 'react-native-paper';
import tw from 'twrnc';
// import * as SplashScreen from 'expo-splash-screen';
import { useOfficialSearchPreload } from '@/context/OfficialSearchPreloadContext';
import theme from '@/app/theme';

// Keep the splash screen visible while we fetch resources
// We'll handle hiding it in the main app component instead
// SplashScreen.preventAutoHideAsync().catch(() => {
//   /* reloading the app might trigger some race conditions, ignore them */
// });

// Error boundary component for production
class ErrorBoundary extends React.Component<{children: React.ReactNode}, {hasError: boolean}> {
  constructor(props: {children: React.ReactNode}) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error) {
    // In production, you might want to log this to a service like Sentry
    console.error('Error in Discover screen:', error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20}}>
          <Text style={{fontSize: 18, marginBottom: 20, textAlign: 'center'}}>
            Something went wrong. Please try again later.
          </Text>
          <TouchableOpacity
            style={{backgroundColor: '#4CAF50', padding: 12, borderRadius: 8}}
            onPress={() => this.setState({ hasError: false })}
          >
            <Text style={{color: 'white', fontWeight: '600'}}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

// Define navigation types
type RootStackParamList = {
  offcailchat: {
    accountData: {
      id: number;
      name: string;
      logo: string;
    }
  };
  [key: string]: any; // For other routes
};
interface OfficialAccount {
  id: number;
  name: string;
  logo: string;
  description: string;
  verified: number;
  online: number;
  unread: string;
}

// Memoized account item component for better performance
const AccountItem = React.memo(({ account, onPress }: {
  account: OfficialAccount,
  onPress: () => void
}) => {
  return (
    <TouchableOpacity
      key={account.id}
      style={styles.accountItem}
      onPress={onPress}
    >
      <View style={[
        styles.accountIcon,
        account.online === 1 && styles.onlineAccountIcon
      ]}>
        <Image
          source={{uri: account.logo}}
          style={styles.iconImage}
          cachePolicy="memory-disk"
          contentFit="cover"
          transition={300}
          placeholder={{uri: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFdwI2QOQvhwAAAABJRU5ErkJggg=='}}
        />
      </View>
      <View style={styles.accountInfo}>
        <View style={styles.nameContainer}>
          <Text style={styles.accountName}>{account.name}</Text>
          {account.verified === 1 && (
            <Ionicons name="checkmark-circle" size={16} color="#1DA1F2" />
          )}
          {account.online === 1 && (
            <></>
          )}
        </View>
        <Text style={styles.accountDescription}>{account.description}</Text>
      </View>
      <View style={styles.badgeContainer}>
        {account.online === 1 && (
          <> </>
        )}
        {Number(account.unread) > 0 && (
          <View style={[styles.notificationBadge, styles.smallBadge]}>
            <Text style={styles.badgeText}>{account.unread}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
});

// Main app component
function DiscoverScreen() {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const [accounts, setAccounts] = useState<OfficialAccount[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<OfficialAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Get preloaded data from context
  const { preloadOfficialSearch, isPreloaded, preloadedAccounts } = useOfficialSearchPreload();

  // Use a constant for the API URL
  const API_URL = 'https://api.search.nityasha.com/official_accounts';

  // Define fetchAccounts outside of the effect to avoid dependency issues
  const fetchAccounts = useCallback(async () => {
    if (!refreshing) setLoading(true);
    setError(null);
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(API_URL, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch accounts: ${response.status}`);
      }

      const data = await response.json();
      setAccounts(data);
      setFilteredAccounts(data);
    } catch (error: any) {
      if (error.name === 'AbortError') {
        setError('Request timed out. Please check your connection and try again.');
      } else {
        setError('Failed to load accounts. Please try again.');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [refreshing]);

  // Load data when component mounts
  useEffect(() => {
    async function prepare() {
      try {
        // If we have preloaded data, use it immediately
        if (isPreloaded && preloadedAccounts.length > 0) {
          setAccounts(preloadedAccounts);
          setFilteredAccounts(preloadedAccounts);
          setLoading(false);
          console.log('Using preloaded official accounts data in discover screen');
        } else {
          // Otherwise fetch data
          await fetchAccounts();
        }
      } catch (e) {
        console.warn('Error loading initial data:', e);
      }
    }

    prepare();
  }, [fetchAccounts, isPreloaded, preloadedAccounts]);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAccounts();
  }, [fetchAccounts]);

  // Debounced search function with proper memoization
  const debouncedSearch = useMemo(() =>
    debounce((searchText: string, accountsData: OfficialAccount[]) => {
      if (!searchText.trim()) {
        setFilteredAccounts(accountsData);
        return;
      }

      const searchLower = searchText.toLowerCase();
      const filtered = accountsData.filter(account =>
        account.name.toLowerCase().includes(searchLower) ||
        account.description.toLowerCase().includes(searchLower)
      );

      setFilteredAccounts(filtered);
    }, 300),
    [] // Empty dependency array ensures this is only created once
  );

  // Handle search input change
  const handleSearch = useCallback((text: string) => {
    setSearchQuery(text);
    debouncedSearch(text, accounts);
  }, [accounts, debouncedSearch]);

  // Clear search
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    setFilteredAccounts(accounts);
  }, [accounts]);

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={styles.container}>
      <StatusBar hidden />

      <View style={tw`py-3 items-center justify-center pt-9`}>
        <Text style={styles.headerTitle}>Official Accounts</Text>
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search accounts..."
          onChangeText={handleSearch}
          value={searchQuery}
          iconColor="#999"
          theme={theme}
          clearIcon="close-circle"
          onPress={() => {
            // Preload official search data before navigating
            preloadOfficialSearch();
            // Navigate using CommonActions to avoid animation
            navigation.dispatch(
              CommonActions.navigate({
                name: 'OffcialSearch',
                params: {},
                screenOptions: {
                  animation: 'none'
                }
              })
            );
          }}
          onClearIconPress={handleClearSearch}
          autoCapitalize="none"
          autoCorrect={false}
          inputMode="none"
        />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchAccounts}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView
          style={styles.accountList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#4CAF50']}
              tintColor="#4CAF50"
            />
          }
        >
          {filteredAccounts.length === 0 ? (
            <View style={styles.noResultsContainer}>
              <Text style={styles.noResultsText}>No accounts found</Text>
            </View>
          ) : (
            filteredAccounts.map((account) => (
              <AccountItem
                key={account.id}
                account={account}
                onPress={() => navigation.navigate('offcailchat', {
                  accountData: {
                    id: account.id,
                    name: account.name,
                    logo: account.logo,
                  }
                })}
              />
            ))
          )}
        </ScrollView>
      )}
    </ImageBackground>
  );
}

// Export the component wrapped in the error boundary for production
export default function App() {
  return (
    <ErrorBoundary>
      <DiscoverScreen />
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c8f0d0',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'Helvetica_bold',
    color: '#333',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e8f8e8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  searchbar: {
    borderRadius: 20,
    elevation: 0,
    backgroundColor: 'white',
    height: 40,
  },
  searchInput: {
    fontSize: 16,
    color: '#333',
    height: 40,
    padding: 0,
    margin: 0,
  },
  accountList: {
    flex: 1,
    marginTop: 8,
  },
  accountItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f8e8',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 6,
    padding: 12,
  },
  accountIcon: {
    width: 50,
    height: 50,
    borderColor : '#000',
    borderWidth: 2.5,
    borderRadius: 12,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    position: 'relative',
  },
  onlineAccountIcon: {
    borderColor: '#4CAF50',
  },
  onlineDotIndicator: {
    position: 'absolute',
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4CAF50',
    borderWidth: 1,
    borderColor: '#fff',
    bottom: -2,
    right: -2,
  },
  iconImage: {
    width: 40,
    height: 40,
  },
  accountInfo: {
    flex: 1,
    marginLeft: 12,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nameOnlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginLeft: 4,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginRight: 4,
  },
  accountDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  onlineStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
  },
  onlineStatusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#4CAF50',
    marginRight: 4,
  },
  onlineText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '600',
  },
  notificationBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
    justifyContent: 'center',
  },
  smallBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  bottomNav: {
    flexDirection: 'row',
    height: 60,
    backgroundColor: '#f5f5f5',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  centerButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#000',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 10,
  },
  navText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 40,
  },
  noResultsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
