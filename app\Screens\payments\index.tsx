import React, { useState } from 'react';
import { StyleSheet, View, Button, Alert, Text } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import * as Crypto from 'expo-crypto';

const Payments = () => {
  const [isLoading, setIsLoading] = useState(false);

  const generateUniqueTransactionId = () => {
    return `${Date.now()}-${Math.floor(Math.random() * 10000)}`; // Generates a unique ID
  };

  const initiatePayment = async () => {
    const paymentData = {
      key: '8458003',
      txnid: generateUniqueTransactionId(), // Generate a unique transaction ID
      amount: 10000, // Amount in paise (e.g., 100.00 INR -> 10000 paise)
      productinfo: 'Product Info', // Ensure this is not empty
      firstname: 'First Name', // Ensure this is a valid first name
      email: '<EMAIL>', // Replace with a valid email
      phone: '9302035251', // Replace with a valid phone number
      surl: 'https://api.php.nityasha.com/success', // Replace with your success URL
      furl: 'https://api.php.nityasha.com/failure', // Replace with your failure URL
      salt: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDZxijyztDloK5a4oKCcbM/cYYoIdgJ72BuoMpmIhlhzPgxO97QqaTE92xnU6mii3XVh7ERwyueVSKdaT+85JWru0L7fXZJdZuo5xDyGN5oLjvE2WasQJXBBDGdKr/X6uK3DAj8q7cKCbBfSV96UnAEx7jjrN0R9zVV6rT3nqp6e3ICtS+YK+g96bV6e1iLsEZjyRNEXS3+E8gc2sQ1PwK9tjYpwIjni5XcnFlWXBIZlFon6ZJ5REMGRabIHGrBLnOJgWkWKmaVhJcfH/gJWpeMM6E1EK63hDGufzHs0R0d1vq64o3df1LSWOKSZy8RcUBAV3q6fo+SKWOCtR5WVSPTAgMBAAECggEATfqZ8Q/DV2Yo6JxRFTp3b9FLs5Ui0bPoeNSUhOvS4OVPPfntOBR2zpy2MW2QwYZkhDhZXD3hoYlkoq7sRswnF94vBvSO5ZD6UwmiVL447V8Ywc0C6c0akonY5ilHlp4h3l+t3CP0n+CGSihwytE4EU4e00+zvVoivAgWEtC6xK5EMNwozvGgh+5YJUio1LSmoxYWV7zVqU56pQtXHaZpEJ/cMEH2nzKBGKeTucjVs579GG/OaIQxjgEW40p4cmgsiC1wLxrFEa0rpefS97AdfYehhTCHAuZZomnTi4/+HcBz4mOEVeNvchEsZPDAyPwkTm1Z3SnaUVXN1P6ZmqMcqQKBgQDvnJtzG4vFsuzfODqU0gxkDYlKuOUOgS9HHyzSbxzgHLTBwtfsntlRvndCBiKWcmdBNq3tiK4Tb9BCA9P/WWNKdLWk5XsNS3g1nnE2GjweMY7lr7PwzOjskkjBiaNEr8a9r8jfx2JZo5WSRaEKJ8eF7ZauQtP3QgJtagpvb3sHVQKBgQDoqzGkMQWYARbSOf8+heedLVf5hyAjWRwYqb8MQs5K0/ZOzC9/gSNHiprnRwLjr8POtpk5DAFSX8lv7u7Ty10UPcf6Hjd1Kulhp5Feyb5+ymSvgx1Qq3CZasNLr338aQ+ZSoJz1AeDCI2X0VDjFIZmyY4fnuMZF03tHA4AppcuhwKBgHFmBIutdbDSxRe/XLohzotrfJZIet8qdF2Me4H75AaMj19D/zyU0PTG11n/3U6mRgevARFxpjC8sisL9MDcZQlPwsePc3UPSozVt26La/g+LAq35MkJsRKFCLnPkd1n8v7CuqZtjjYuHRNksJpFyT+w+abvKbHTI6L0lIu4jtkhAoGAWTLQjDbzAwxf2Ga9sRWa7wokMRiZvJN8DwR8ptGxzsQmKJ1lFEfI47OCoWn69Jk8wzID2ff9RbxNOKlsTCwp2gm5ce1Fq5iw8mxQcft8GZW0NglZIsUnHNnuV0THUw5gGY+UJZ3C53xO9EYd8YC76goZf4vlWogxqtZXk+5KBV8CgYEAtdzBN4TJVZ/Uwmg/hfDryJoqO/zXg+jgiUXGgBB/rCTTaNHTXednGMBfENq5fcV41YSnwuRfje7Hym9Ah4P7rwrvqK7dW8YtkOBg1C71tpql4BbnPf+WVASeWhbxVv6f9yOutLrwNZ02IKDn727DKDiSTNg92MNR8tm914NYD0w=', // Replace with your salt
    };

    try {
      // Generate hash for secure payment (important)
      const hashString = `${paymentData.key}|${paymentData.txnid}|${paymentData.amount}|${paymentData.productinfo}|${paymentData.firstname}|${paymentData.email}|||||||||||${paymentData.salt}`;
      const hash = await generateHash(hashString); // Use the generateHash function

      // Make sure you include all parameters in the URL
      const paymentUrl = `https://test.payu.in/_payment?key=${paymentData.key}&txnid=${paymentData.txnid}&amount=${paymentData.amount}&productinfo=${paymentData.productinfo}&firstname=${paymentData.firstname}&email=${paymentData.email}&phone=${paymentData.phone}&surl=${paymentData.surl}&furl=${paymentData.furl}&hash=${hash}`;
      
      setIsLoading(true);
      openPaymentUrl(paymentUrl);
    } catch (error) {
      Alert.alert('Error', 'There was an error initiating the payment. Please try again.');
      setIsLoading(false);
    }
  };

  const generateHash = async (hashString) => {
    try {
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA512,
        hashString,
        {
          encoding: Crypto.CryptoEncoding.HEX,
        }
      );
      return hash;
    } catch (error) {
      Alert.alert('Error', 'There was an error generating the payment hash.');
      throw error;
    }
  };

  const openPaymentUrl = async (url) => {
    try {
      const result = await WebBrowser.openBrowserAsync(url);
      setIsLoading(false);
      
      // Handle result based on the URL returned
      if (result.type === 'dismissed') {
        Alert.alert('Payment Process Dismissed', 'Please complete your payment.');
      } else {
        Alert.alert('Payment Status', 'Check your email for payment confirmation.');
      }
    } catch (error) {
      Alert.alert('Error', 'There was an error opening the payment page.');
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Button title="Pay Now" onPress={initiatePayment} disabled={isLoading} />
      {isLoading && <View style={styles.loadingContainer}><Text>Loading...</Text></View>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    marginTop: 20,
  },
});

export default Payments;
