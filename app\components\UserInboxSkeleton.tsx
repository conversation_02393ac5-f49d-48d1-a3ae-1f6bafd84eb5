import React from 'react';
import { View, StyleSheet, ImageBackground, Animated } from 'react-native';
import tw from 'twrnc';
import SkeletonLoader from '../../components/ui/SkeletonLoader';

const UserInboxSkeleton = () => {
  // Create an array of 8 items to render skeleton chat items
  const skeletonItems = Array(8).fill(null);

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTitleContainer}>
          <SkeletonLoader width={100} height={30} borderRadius={4} />
        </View>
        <View style={styles.headerIcons}>
          <SkeletonLoader width={40} height={40} borderRadius={20} />
        </View>
      </View>

      {/* Search Bar */}
      <View style={tw`mx-3 my-5`}>
        <SkeletonLoader width={'100%'} height={50} borderRadius={25} />
      </View>

      {/* Chat List */}
      <View style={tw`flex-1`}>
        {skeletonItems.map((_, index) => (
          <View key={index} style={styles.chatItem}>
            {/* Avatar */}
            <SkeletonLoader width={50} height={50} borderRadius={25} />

            {/* Chat Info */}
            <View style={styles.chatInfo}>
              <View style={styles.chatNameRow}>
                <SkeletonLoader width={120} height={20} borderRadius={4} />
                <SkeletonLoader width={50} height={15} borderRadius={4} />
              </View>
              <View style={styles.messageRow}>
                <SkeletonLoader width={'80%'} height={15} borderRadius={4} />
              </View>
            </View>
          </View>
        ))}
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#C5E8C4',
  },
  headerTitleContainer: {
    flexDirection: 'column',
  },
  headerIcons: {
    flexDirection: 'row',
  },
  chatItem: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  chatInfo: {
    flex: 1,
    marginLeft: 12,
  },
  chatNameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default UserInboxSkeleton;
