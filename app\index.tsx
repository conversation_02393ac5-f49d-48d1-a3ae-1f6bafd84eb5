import React, { useEffect, useRef, useState } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { PaperProvider, Provider, BottomNavigation } from "react-native-paper";
import { Pressable, StyleSheet, View, AppState, AppStateStatus, StatusBar, Platform, Animated, Text } from "react-native";
import { useFonts } from "expo-font";
import * as NavigationBar from "expo-navigation-bar";
import * as SplashScreen from "expo-splash-screen";
import { ToastProvider } from "../context/ToastContext";
import { MessageTabPreloadProvider, useMessageTabPreload } from "../context/MessageTabPreloadContext";
import { OfficialSearchPreloadProvider, useOfficialSearchPreload } from "../context/OfficialSearchPreloadContext";
import { KeyboardProvider, useKeyboard } from "../context/KeyboardContext";
import { VoiceChatEndedProvider, useVoiceChatEnded } from "../context/VoiceChatEndedContext";
import { VoiceModeProvider } from "../context/VoiceModeContext";
import { theme } from "./theme";
import { Home as HomeIcon, Search, Radio, User, MessageCircle, Users2, Users, Earth } from "lucide-react-native";
import Ai from "@/components/icons/Ai";
import { saveLastVisitedScreen, getLastVisitedScreen } from "@/utils/navigationState";
import { NavigationContainer, useNavigationContainerRef, useNavigationState, useIsFocused } from "@react-navigation/native";
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { useRouter, useSegments } from 'expo-router';
import VoiceChatEndedSnackbar from "@/app/Screens/VoiceAi/_compo/VoiceChatEndedSnackbar";
import { KeyboardController } from 'react-native-keyboard-controller';

// Global VoiceChatEndedSnackbar component
const GlobalVoiceChatEndedSnackbar = () => {
  const { showSnackbar, setShowSnackbar, handleSnackbarFeedback, chatDuration } = useVoiceChatEnded();
  
  return (
    <VoiceChatEndedSnackbar
      visible={showSnackbar}
      onClose={() => setShowSnackbar(false)}
      onFeedback={handleSnackbarFeedback}
      chatDuration={chatDuration}
    />
  );
};

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync().catch(() => {
  /* reloading the app might trigger some race conditions, ignore them */
});

import Welcome from "@/app/Screens/Welcome";
import Login from "@/app/Screens/Login";
import SignUp from "@/app/Screens/SignUp";
import Home from "./Screens/Home";
import Searchs from "./Screens/Search";
import ProfileView from "./Screens/ProfileView";
import Account_Information from "./Screens/Account_Information";
import Profile from "./Screens/Profile";
import Notification from "./Screens/Notification";
import Category from "./Screens/Category";
import Chat from "./Screens/Chat";
import Chatab from "./Screens/Chatab";
import Community from "./Screens/Community/index";
import Live from "./Screens/Live";
import InboxChat from "./Screens/ChatScreen";
import Balance from "./Screens/Balance";
import Recharge from "./Screens/Recharge";
import Terms from "./Screens/Terms";
import Privacy from "./Screens/Privacy";
import PaymentPage from "./Screens/PaymentPage";
import ServicePage from "@/app/Screens/ServicePage";
import ServicePage1 from "@/app/Screens/ServicePage1/page";
import ServicePage2 from "@/app/Screens/ServicePage2/page";
import ServicePage3 from "@/app/Screens/ServicePage3/page";
import ServicePage4 from "@/app/Screens/ServicePage4/page";
import CreateCommunity from "./Screens/Community/CreateCommunity";
import AiScreen from "./Screens/Ai";
import CommunityView from "./Screens/Community/CommunityView";
import ProfileCommunity from "./Screens/Community/ProfileCommunity";
import WebViewScreen from "@/components/WebViewScreen";
import FreeChat from '@/app/Screens/FreeChat'
import DmChat from '@/app/Screens/DmChat'
import UserInbox from '@/app/Screens/UserInbox'
import CommunityIcon from '@/components/icons/IconsHome'
import Discover from '@/app/Screens/discover'
import Discovericon from "@/components/icons/Discovericon";
import { Image } from "react-native";
import { useOnlineStatus } from "@/hooks/useOnlineStatus";
import Offcailchat from '@/app/Screens/OffcailChat'
import UserChatInbox from '@/app/Screens/UserChatInbox'
import CreatePost from "./Screens/CreatePost";
import SearchPage from "./Screens/SearchPage";
import OffcialSearch from "./Screens/OffcialSearch";
import UserInboxSearch from "./Screens/UserInboxSearch";
import StatusView from "./Screens/StatusView";
import CreateStatus from "./Screens/CreateStatus";
import CommunitySearch from "./Screens/CommunitySearch";
import FreeTestChat from "./Screens/FreeTestChat";
import ConsultantCommunity from '@/app/Screens/ConsultantCommunity'
import Assisstant from "@/components/Ai/assistant/index";
import OpenFullPost from './Screens/OpenFullPost'
import AddContact from './Screens/AddContact'
import FullScreenimage from './Screens/FullScreenimage'
import VoiceAi from './Screens/VoiceAi/index'
import RequestCommunity from "./Screens/Community/Requestcommunity";
import { MotiView } from "moti";
import Linkeddevics from './Screens/Linkeddevices'
import QRScanner from "./Screens/QrScan";
import Nearby from "./Screens/Nearby";
import SourcesPage from "./Screens/SourcesPage";

type RouteParams = {
  [key: string]: any;
};

// Define navigation types
export type RootStackParamList = {
  Welcome: undefined;
  Login: undefined;
  SignUp: undefined;
  BottomTabs: { initialTab?: string };
  AiScreen: undefined;
  InboxChat: undefined;
  Account_Information: undefined;
  ProfileView: undefined;
  Terms: undefined;
  Privacy: undefined;
  DmChat: undefined;
  Profile: undefined;
  ServicePage: undefined;
  ServicePage1: undefined;
  ServicePage2: undefined;
  ServicePage3: undefined;
  ServicePage4: undefined;
  OpenFullPost: undefined;
  CommunityView: undefined;
  ProfileCommunity: undefined;
  WebViewScreen: undefined;
  FreeChat: undefined;
  UserChatInbox: { otherUserId: string };
  CreatePost: { params: RouteParams };
  offcailchat: undefined;
  SearchPage: undefined;
  OffcialSearch: undefined;
  UserInboxSearch: undefined;
  CreateStatus: undefined;
  ConsultantCommunity: undefined;
  AddContact: undefined;
  FreeTestChat: undefined;
  CommunitySearch: undefined;
  RequestCommunity: undefined;
  Chat: undefined;
  VoiceAi: undefined;
  FullScreenimage: undefined;
  Nearby: undefined;
  QRScanner: undefined;
  Searchs: undefined;
  SourcesPage: undefined;
  Linkeddevics: undefined;
};

export type BottomTabParamList = {
  Community: undefined;
  Discover: undefined;
  Search: undefined;
  UserInbox: undefined;
  AiScreen: undefined;
};

// Create typed navigators
const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<BottomTabParamList>();

const getTabIcon = (routeName: string, color: string, size: number) => {
  const icons: Record<string, any> = {
    Home: HomeIcon,
    Search: Search,
    AiScreen: Ai,
    Discover: Discovericon,
    Community: Earth,
    UserInbox: MessageCircle,
  };
  const IconComponent = icons[routeName] || HomeIcon;
  return <IconComponent color={color} size={size} />;
};

// Define the props type for BottomTabs
type BottomTabsProps = {
  route?: {
    params?: {
      initialTab?: string;
    };
  };
};

const BottomTabs = React.memo((props: BottomTabsProps) => {
  const { preloadMessageTab } = useMessageTabPreload();
  const { preloadOfficialSearch } = useOfficialSearchPreload();
  const { isKeyboardVisible } = useKeyboard();
  const initialTab = props.route?.params?.initialTab;

  // Create a ref to hold animated values for each tab
  const animatedValuesRef = useRef<Record<string, Animated.Value>>(
    {
      Community: new Animated.Value(0),
      Discover: new Animated.Value(0),
      Search: new Animated.Value(0),
      UserInbox: new Animated.Value(0),
      AiScreen: new Animated.Value(0),
    }
  );

  // Get navigation state to track the focused tab
  const navigationState = useNavigationState(state => state);

  // Animate the dot when the focused tab changes
  useEffect(() => {
    if (!navigationState) return;
  
    const focusedRouteName = navigationState.routes[navigationState.index].name;
  
    Object.keys(animatedValuesRef.current).forEach((routeName) => {
      if (routeName === 'Search') return; // Skip Search tab animation
  
      const targetValue = routeName === focusedRouteName ? 1 : 0;
      Animated.timing(animatedValuesRef.current[routeName], {
        toValue: targetValue,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  
  }, [navigationState]);

  // Preload data only once when component mounts
  React.useEffect(() => {
    preloadMessageTab();
    preloadOfficialSearch();
  }, []);

  // Define routes array
  const [routes] = useState([
    { key: 'Community', title: 'Updates', focusedIcon: 'earth', unfocusedIcon: 'earth' },
    { key: 'AiScreen', title: 'Assistant', focusedIcon: 'robot', unfocusedIcon: 'robot' },
    { key: 'Search', title: '', focusedIcon: '', unfocusedIcon: '' },
    { key: 'UserInbox', title: 'Messages', focusedIcon: 'message-circle', unfocusedIcon: 'message-circle' },
    { key: 'Discover', title: 'Discover', focusedIcon: 'compass', unfocusedIcon: 'compass' },
  ]);

  // Calculate initial index based on initialTab parameter
  const getInitialIndex = () => {
    if (initialTab) {
      const tabIndex = routes.findIndex(route => route.key === initialTab);
      return tabIndex !== -1 ? tabIndex : 2; // Default to Search (index 2) if not found
    }
    return 2; // Default to Search tab (index 2)
  };

  // State for BottomNavigation
  const [index, setIndex] = useState(getInitialIndex());

  // Update index when initialTab changes
  useEffect(() => {
    const newIndex = getInitialIndex();
    setIndex(newIndex);
  }, [initialTab]);

  const isAiScreenActive = routes[index].key === 'AiScreen';
  const isFocused = useIsFocused();

  useEffect(() => {
    if (isFocused) {
      if (isAiScreenActive) {
        StatusBar.setBackgroundColor('#BBEAC4', true);
        StatusBar.setBarStyle('dark-content', true);
      } else {
        StatusBar.setBackgroundColor('#BBEAC4', true);
        StatusBar.setBarStyle('default', true);
      }
    }
  }, [isAiScreenActive, isFocused, index]);

  const renderScene = BottomNavigation.SceneMap({
    AiScreen: VoiceAi,
    Community: Community,
    Discover: Discover,
    Search: Searchs,
    UserInbox: UserInbox,
  });

  // Custom rendering for icons, particularly the Search button
  const renderIcon = ({ route, focused, color }: { route: any, focused: boolean, color: string }) => {
    if (route.key === "Search") {
      return (
        <View style={{
          backgroundColor: '#000',
          width: 50,
          height: 50,
          marginTop: 0,
          borderRadius: 18,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
           <Image
              source={require('@/assets/images/image.png')}
              style={{
                width: 50,
                height: 50,
                resizeMode: 'contain',
                tintColor: '#fff'
              }}
            />
        </View>
      );
    }
    return getTabIcon(route.key, color, 24);
  };

  return (
    <BottomNavigation
      theme={theme}
      navigationState={{ index, routes }}
      onIndexChange={setIndex}
      activeColor="#000"
      activeIndicatorStyle={
        routes[index].key === 'Search'
          ? { backgroundColor: 'transparent' }
          : {
              backgroundColor: '#E8F6E9',
              paddingHorizontal: 20,
              paddingVertical: 5,
              borderRadius: 100,
            }
      }
      shifting={false} 
      renderScene={renderScene}
      barStyle={{ 
        backgroundColor: '#fafafa', 
        height: 90, 
        justifyContent: 'center', 
        zIndex: 1000,
        display: isAiScreenActive ? 'none' : 'flex'
      }}
      labeled={true}
      renderIcon={renderIcon}
    />
  );
});

export default function App() {
  useOnlineStatus();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const initialTab = "Search";

  // Enable KeyboardController for Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      KeyboardController.setInputMode(16); // 16 = adjustResize
    }
  }, []);

  // Create a ref for the navigation container
  const navigationRef = useNavigationContainerRef<RootStackParamList>();

  // Check login status on app start and set up notifications
  useEffect(() => {
    const checkLoginStatusAndSetupNotifications = async () => {
      try {
        const userDetails = await AsyncStorage.getItem('userDetails');
        setIsLoggedIn(!!userDetails);

        // Request notification permissions and get token
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;
        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }
        if (finalStatus !== 'granted') {
          console.log('Failed to get push token for push notification!');
          return;
        }
        const token = (await Notifications.getExpoPushTokenAsync()).data;
        console.log('Expo Push Token:', token);

        if (Platform.OS === 'android') {
          Notifications.setNotificationChannelAsync('default', {
            name: 'default',
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF231F7C',
          });
        }

        // Add Notification Response Listener
        const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
          const notificationData = response.notification.request.content.data;
          console.log('Notification tapped with data:', notificationData);

          if (notificationData?.navigate) {
            const screen = notificationData.navigate as any;
            const params = (notificationData.params || {}) as any;

            console.log(`Navigating to ${screen} with params:`, params);

            setTimeout(() => {
              try {
                (navigationRef.current?.navigate as any)(screen, params);
              } catch (error) {
                console.error(`Error navigating from notification to ${screen}:`, error);
              }
            }, 100);
          }
        });

        const receivedListener = Notifications.addNotificationReceivedListener(notification => {
           console.log('Notification received in foreground:', notification);
        });

        return () => {
          Notifications.removeNotificationSubscription(responseListener);
          Notifications.removeNotificationSubscription(receivedListener);
        };

      } catch (error) {
        console.error('Error setting up app:', error);
        setIsLoggedIn(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkLoginStatusAndSetupNotifications();
  }, []);

  const [loaded] = useFonts({
    Helvetica_bold: require("@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf"),
    "Geist-Black": require("@/assets/Geist/Geist-Black.ttf"),
    "Geist-Bold": require("@/assets/Geist/Geist-Bold.ttf"),
    "Geist-ExtraLight": require("@/assets/Geist/Geist-ExtraLight.ttf"),
    "Geist-Regular": require("@/assets/Geist/Geist-Regular.ttf"),
    "Geist-SemiBold": require("@/assets/Geist/Geist-SemiBold.ttf"),
    "Geist-Thin": require("@/assets/Geist/Geist-Thin.ttf"),
    "Geist-ExtraBold": require("@/assets/Geist/Geist-ExtraBold.ttf"),
    "Geist-Light": require("@/assets/Geist/Geist-Light.ttf"),
    "Geist-Medium": require("@/assets/Geist/Geist-Medium.ttf"),
    "ProductSans-Black": require("@/assets/Geist/googlefont/ProductSans-Black.ttf"),
    "ProductSans-Medium": require("@/assets/Geist/googlefont/ProductSans-Medium.ttf"),
    "GoogleSans-Medium": require("@/assets/Google-Sans-Font/GoogleSans-Medium.ttf"),
    "GoogleSans-Bold": require("@/assets/Google-Sans-Font/GoogleSans-Bold.ttf"),
    "GoogleSans-Regular": require("@/assets/Google-Sans-Font/GoogleSans-Regular.ttf"),
    "Familiar_Pro": require("@/assets/familiar-pro.ttf")
  });

  // Hide the splash screen once fonts are loaded
  useEffect(() => {
    if (loaded) {
      const hideSplash = async () => {
        try {
          await SplashScreen.hideAsync();
        } catch (e) {
          console.warn('Error hiding splash screen:', e);
        }
      };

      hideSplash();
    }
  }, [loaded]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider>
        <VoiceChatEndedProvider>
          <VoiceModeProvider>
            <ToastProvider>
              <Stack.Navigator
                initialRouteName="BottomTabs"
                screenOptions={{
                  headerShown: false,
                  gestureEnabled: true,
                  animation: 'slide_from_right',
                }}
              >
                <Stack.Screen name="Welcome" component={Welcome} />
                <Stack.Screen name="Login" component={Login} />
                <Stack.Screen name="SignUp" component={SignUp} />
                <Stack.Screen name="AiScreen" component={AiScreen} />
                <Stack.Screen name="InboxChat" component={InboxChat} />
                <Stack.Screen name="Account_Information" component={Account_Information} />
                <Stack.Screen name="ProfileView" component={ProfileView} />
                <Stack.Screen name="QRScanner" component={QRScanner} />
                <Stack.Screen name="Terms" component={Terms} />
                <Stack.Screen name="Privacy" component={Privacy} />
                <Stack.Screen
                  name="BottomTabs"
                  component={BottomTabs}
                  initialParams={{ initialTab }}
                />
                <Stack.Screen name="DmChat" component={DmChat} />
                <Stack.Screen name="Chat" component={Chat} />
                <Stack.Screen name="Profile" component={Profile} />
                <Stack.Screen name="Searchs" component={Searchs as React.ComponentType<any>} />
                <Stack.Screen name="ServicePage" component={ServicePage as React.ComponentType<any>} />
                <Stack.Screen name="ServicePage1" component={ServicePage1 as React.ComponentType<any>} />
                <Stack.Screen name="ServicePage2" component={ServicePage2 as React.ComponentType<any>} />
                <Stack.Screen name="ServicePage3" component={ServicePage3 as React.ComponentType<any>} />
                <Stack.Screen name="ServicePage4" component={ServicePage4 as React.ComponentType<any>} />
                <Stack.Screen name="OpenFullPost" component={OpenFullPost as React.ComponentType<any>} />
                <Stack.Screen
                  name="CommunityView"
                  component={CommunityView as React.ComponentType<any>}
                  options={{
                    animation: 'none',
                  }}
                />
                <Stack.Screen name="ProfileCommunity" component={ProfileCommunity} />
                <Stack.Screen name="FreeChat" component={FreeChat} />
                <Stack.Screen name="UserChatInbox" component={UserChatInbox} options={{ animation: 'none' }} />
                <Stack.Screen 
                  name="CreatePost" 
                  component={CreatePost as React.ComponentType<any>} 
                />
                <Stack.Screen name="offcailchat" component={Offcailchat} />
                <Stack.Screen
                  name="SearchPage"
                  component={SearchPage}
                  options={{
                    animation: 'none',
                    presentation: 'transparentModal',
                  }}
                />
                <Stack.Screen
                  name="OffcialSearch"
                  component={OffcialSearch}
                  options={{
                    animation: 'none',
                    presentation: 'transparentModal',
                    animationDuration: 0
                  }}
                />
                <Stack.Screen
                  name="UserInboxSearch"
                  component={UserInboxSearch}
                  options={{
                    animation: 'none',
                    presentation: 'transparentModal',
                  }}
                />
                <Stack.Screen name="CreateStatus" component={CreateStatus} />
                <Stack.Screen name="SourcesPage" component={SourcesPage} />
                <Stack.Screen name="ConsultantCommunity" component={ConsultantCommunity} />
                <Stack.Screen name="VoiceAi" component={VoiceAi} />
                <Stack.Screen name="AddContact" component={AddContact} />
                <Stack.Screen name="FreeTestChat" component={FreeTestChat as React.ComponentType<any>} />
                <Stack.Screen name="RequestCommunity" component={RequestCommunity} />
                <Stack.Screen name="Linkeddevics" component={Linkeddevics} />
                <Stack.Screen name="Nearby" component={Nearby} />
                <Stack.Screen
                  name="CommunitySearch"
                  component={CommunitySearch}
                  options={{
                    animation: 'none',
                    presentation: 'transparentModal',
                  }}
                />
                <Stack.Screen name="FullScreenimage" component={FullScreenimage} />
              </Stack.Navigator>
              
              {/* Global VoiceChatEndedSnackbar */}
              <GlobalVoiceChatEndedSnackbar />
            </ToastProvider>
          </VoiceModeProvider>
        </VoiceChatEndedProvider>
      </Provider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  bottomBar: {
    backgroundColor: '#fafafa',
    height: 90,
    paddingTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    position: 'absolute', 
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  logo: {
    width: 100,
    height: 100,
    backgroundColor: theme.colors.background,
  }
});