import React, { useEffect, useState } from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import Welcome from "@/app/Screens/Welcome";
import Login from "@/app/Screens/Login";
import SignUp from "@/app/Screens/SignUp";
import Lgthome from "@/app/Screens/Lgthome";
import Home from "./Screens/Home";
import Searchs from "./Screens/Search";
import ProfileView from "./Screens/ProfileView";
import Account_Information from "./Screens/Account_Information";
import Profile from "./Screens/Profile";
import Notification from "./Screens/Notification";
import Category from "./Screens/Category";
import { useFonts } from "expo-font";
import Chat from "./Screens/Chat";
import Chatab from "./Screens/Chatab";
import Community from "./Screens/Community/index";
import Live from "./Screens/Live";
import InboxChat from "./Screens/ChatScreen";
import Balance from "./Screens/Balance";
import "../global.css";
import Recharge from "./Screens/Recharge";
import { ToastProvider } from "../context/ToastContext";
import Terms from "./Screens/Terms";
import Privacy from "./Screens/Privacy";
import PaymentPage from "./Screens/PaymentPage";
import * as NavigationBar from "expo-navigation-bar";
import ServicePage from "@/app/Screens/ServicePage";
import ServicePage1 from "@/app/Screens/ServicePage1/page";
import ServicePage2 from "@/app/Screens/ServicePage2/page";
import ServicePage3 from "@/app/Screens/ServicePage3/page";
import ServicePage4 from "@/app/Screens/ServicePage4/page";
import { StatusBar } from "expo-status-bar";
import CreateCommunity from "./Screens/Community/CreateCommunity";
import CommunityView from "./Screens/Community/CommunityView";
import ProfileCommunity from "./Screens/Community/ProfileCommunity";
import { BottomNavigation } from "react-native-paper";
import { StyleSheet } from "react-native";
import { Feather, FontAwesome5 } from "@expo/vector-icons";
import tailwind from "twrnc";
import { PaperProvider } from "react-native-paper";
import { theme } from "./theme";
import WebViewScreen from "@/components/WebViewScreen";
const Stack = createNativeStackNavigator();
const renderScene = BottomNavigation.SceneMap({
  home: Home,
  search: Searchs,
  chatab: Chatab,
  community: Community,
  profile: Profile
});

function BottomTabs() {
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    { key: "home", title: "Home", icon: "home" },
    { key: "search", title: "Search", icon: "search" },
    { key: "chatab", title: "Live Chat", icon: "radio" },
    { key: "community", title: "Community", icon: "users" },
    { key: "profile", title: "Profile", icon: "user" }
  ]);

  const [loaded] = useFonts({
    Helvetica_bold: require("@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf"),
    "Geist-Black": require("@/assets/Geist/Geist-Black.ttf"),
    "Geist-Bold": require("@/assets/Geist/Geist-Bold.ttf"),
    "Geist-ExtraLight": require("@/assets/Geist/Geist-ExtraLight.ttf"),
    "Geist-Regular": require("@/assets/Geist/Geist-Regular.ttf"),
    "Geist-SemiBold": require("@/assets/Geist/Geist-SemiBold.ttf"),
    "Geist-Thin": require("@/assets/Geist/Geist-Thin.ttf"),
    "Geist-ExtraBold": require("@/assets/Geist/Geist-ExtraBold.ttf"),
    "Geist-Light": require("@/assets/Geist/Geist-Light.ttf"),
    "Geist-Medium": require("@/assets/Geist/Geist-Medium.ttf"),
    "ProductSans-Black": require("@/assets/Geist/googlefont/ProductSans-Black.ttf"),
    "ProductSans-Medium": require("@/assets/Geist/googlefont/ProductSans-Medium.ttf"),
    "GoogleSans-Medium": require("@/assets/Google-Sans-Font/GoogleSans-Medium.ttf"),
    "GoogleSans-Bold": require("@/assets/Google-Sans-Font/GoogleSans-Bold.ttf"),
    "GoogleSans-Regular": require("@/assets/Google-Sans-Font/GoogleSans-Regular.ttf"),
    "Familiar_Pro": require("@/assets/familiar-pro.ttf")
  });

  if (!loaded) {
    return null; // You can add a loader or fallback UI here.
  }


  return (
    <BottomNavigation
      navigationState={{ index, routes }}
      onIndexChange={setIndex}
      renderScene={renderScene}
      renderIcon={({ route, color }) =>
        route.key === "other" ? (
          <FontAwesome5 name="users" size={23} color={color} />
        ) : (
          <Feather name={route.icon} size={23} color={color} />
        )
      }
      style={[tailwind``, { fontFamily: "GoogleSans-Regular" }]}
      barStyle={styles.bottomBar}
      activeColor="#231f06"
      inactiveColor="#4c483f"
      keyboardHidesNavigationBar={true} // Ensures it hides on keyboard open
    />
  );
}

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  bottomBar: {
    zIndex: 1000,
    backgroundColor: "#FFF8E1"
  }
});

export default function App() {
  useEffect(() => {
    // Hide the navigation bar when the app starts
    NavigationBar.setVisibilityAsync("hidden");
  }, []);

  return (
    <PaperProvider theme={theme}>
      <ToastProvider>
        <Stack.Navigator
          screenOptions={{ headerShown: false }}
          initialRouteName="BottomTabs"
        >
          <Stack.Screen
            name="Welcome"
            component={Welcome}
            options={{ title: "Welcome" }}
          />
          <Stack.Screen
            name="Login"
            component={Login}
            options={{ title: "Login" }}
          />
          <Stack.Screen
            name="SignUp"
            component={SignUp}
            options={{ title: "SignUp" }}
          />
          <Stack.Screen
            name="InboxChat"
            component={InboxChat}
            options={{ title: "InboxChat" }}
          />
          <Stack.Screen
            name="Lgthome"
            component={Lgthome}
            options={{ title: "Lgthome" }}
          />
          <Stack.Screen
            name="Home"
            component={Home}
            options={{ title: "Home", headerShown: false }}
          />
          <Stack.Screen
            name="Account_Information"
            component={Account_Information}
            options={{ title: "Account Information", headerShown: false }}
          />
          <Stack.Screen
            name="Notification"
            component={Notification}
            options={{ title: "Notification", headerShown: false }}
          />
          <Stack.Screen
            name="ProfileView"
            component={ProfileView}
            options={{ title: "Profile View", headerShown: false }}
          />
          <Stack.Screen
            name="Chat"
            component={Chat}
            options={{ title: "Chat", headerShown: false }}
          />
          <Stack.Screen
            name="Balance"
            component={Balance}
            options={{ title: "Add money to wallet", headerShown: false }}
          />
          <Stack.Screen
            name="Recharge"
            component={Recharge}
            options={{ title: "Payment Information", headerShown: false }}
          />
          <Stack.Screen
            name="Category"
            component={Category}
            options={{ title: "Category", headerShown: false }}
          />
          <Stack.Screen
            name="Live"
            component={Live}
            options={{ title: "Live", headerShown: false }}
          />
          <Stack.Screen
            name="Terms"
            component={Terms}
            options={{ title: "Terms", headerShown: false }}
          />
          <Stack.Screen
            name="Privacy"
            component={Privacy}
            options={{ title: "Privacy", headerShown: false }}
          />
          <Stack.Screen
            name="BottomTabs"
            component={BottomTabs}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="PaymentPage"
            component={PaymentPage}
            options={{ title: "PaymentPage", headerShown: false }}
          />
          <Stack.Screen
            name="CreateCommunity"
            component={CreateCommunity}
            options={{ title: "CreateCommunity", headerShown: false }}
          />
          <Stack.Screen
            name="Profile"
            component={Profile}
            options={{ title: "Profile", headerShown: false }}
          />
          <Stack.Screen
            name="ServicePage"
            component={ServicePage}
            options={{ title: "ServicePage", headerShown: false }}
          />
          <Stack.Screen
            name="ServicePage1"
            component={ServicePage1}
            options={{ title: "ServicePage1", headerShown: false }}
          />
          <Stack.Screen
            name="ServicePage2"
            component={ServicePage2}
            options={{ title: "ServicePage2", headerShown: false }}
          />
          <Stack.Screen
            name="ServicePage3"
            component={ServicePage3}
            options={{ title: "ServicePage3", headerShown: false }}
          />
          <Stack.Screen
            name="ServicePage4"
            component={ServicePage4}
            options={{ title: "ServicePage4", headerShown: false }}
          />
          <Stack.Screen
            name="CommunityView"
            component={CommunityView}
            options={{ title: "CommunityView", headerShown: false }}
          />
          <Stack.Screen
            name="ProfileCommunity"
            component={ProfileCommunity}
            options={{ title: "ProfileCommunity", headerShown: false }}
          />
          <Stack.Screen
            name="WebViewScreen"
            component={WebViewScreen}
            options={{ title: "WebViewScreen", headerShown: false }}
          />
        </Stack.Navigator>
      </ToastProvider>
    </PaperProvider>
  );
}