import { MD3LightTheme } from "react-native-paper"

export const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: "#231f06",
    onPrimary: "#ffffff",
    primaryContainer: "#FFF8E1",
    onPrimaryContainer: "#231f06",
    secondary: "#4c483f",
    onSecondary: "#ffffff",
    secondaryContainer: "#fafafa",
    onSecondaryContainer: "#4c483f",
    background: "#ffffff",
    surface: 'transparent',   // override Paper's surface background
  },
  fonts: {
    ...MD3LightTheme.fonts,
    regular: {
      fontFamily: "GoogleSans-Regular",
    },
    medium: {
      fontFamily: "GoogleSans-Medium",
    },
    bold: {
      fontFamily: "GoogleSans-Bold",
    },
  },
}

// Add default export to fix warning
export default theme;
