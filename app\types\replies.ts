// Reply interface for post replies
export interface Reply {
  id: number | string;
  post_id: number | string;
  user_id: number | string;
  user_name?: string;
  user_avatar?: string;
  content: string;
  created_at: string;
  parent_reply_id?: number | string | null;
  replies?: Reply[];
}

// Reply request interface for creating a new reply
export interface ReplyRequest {
  user_id: number | string;
  content: string;
  parent_reply_id?: number | string;
}

// Navigation params interface
export interface PostScreenParams {
  post: {
    id: number | string;
    author_name: string;
    content: string;
    pfp?: string;
    view_count?: number;
    reply_count?: number;
  };
}

// Add a default export
const replies = {
  // Export any existing types or interfaces
};

export default replies;
