/**
 * Utility functions for phone number handling
 */

/**
 * Standardized phone number cleaning function
 * Removes all non-digit characters, handles country codes, and removes leading zeros
 *
 * @param phoneNumber The phone number to clean
 * @returns A standardized phone number string
 */
export const cleanPhoneNumber = (phoneNumber: string | null | undefined): string => {
  if (!phoneNumber) return '';

  // Step 1: Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // Step 2: Handle country codes
  // Common country codes to handle
  const countryCodes = [
    { code: '91', country: 'India' },
    { code: '1', country: 'US/Canada' },
    { code: '44', country: 'UK' },
    { code: '61', country: 'Australia' },
    { code: '86', country: 'China' },
    { code: '49', country: 'Germany' },
    { code: '33', country: 'France' },
    { code: '81', country: 'Japan' },
    { code: '7', country: 'Russia' },
    { code: '55', country: 'Brazil' },
    { code: '52', country: 'Mexico' },
    { code: '82', country: 'South Korea' },
    { code: '39', country: 'Italy' },
    { code: '34', country: 'Spain' },
  ];

  // Try to match and remove country codes
  for (const { code, country } of countryCodes) {
    if (cleaned.startsWith(code) && cleaned.length > 10) {
      // Only remove the country code if the resulting number would be valid
      const withoutCode = cleaned.substring(code.length);
      if (withoutCode.length >= 10) {
        console.log(`Removed ${country} country code +${code} from ${cleaned}`);
        cleaned = withoutCode;
        break; // Stop after finding the first match
      }
    }
  }

  // Step 3: Remove leading zeros
  cleaned = cleaned.replace(/^0+/, '');

  // Step 4: Ensure we have a valid number (at least 10 digits for most countries)
  if (cleaned.length < 10) {
    console.log(`Warning: Number ${phoneNumber} cleaned to ${cleaned} is less than 10 digits`);
  }

  return cleaned;
};

/**
 * Formats a phone number for display
 *
 * @param phoneNumber The phone number to format
 * @returns A formatted phone number string (e.g., "************")
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  const cleaned = cleanPhoneNumber(phoneNumber);

  // For 10-digit numbers (standard in many countries)
  if (cleaned.length === 10) {
    return `${cleaned.substring(0, 3)}-${cleaned.substring(3, 6)}-${cleaned.substring(6)}`;
  }

  // For other lengths, just return the cleaned number
  return cleaned;
};

/**
 * Checks if a phone number is valid (10 digits for India)
 *
 * @param phoneNumber The phone number to validate
 * @returns True if the phone number is valid
 */
export const isValidPhoneNumber = (phoneNumber: string): boolean => {
  const cleaned = cleanPhoneNumber(phoneNumber);
  return /^\d{10,15}$/.test(cleaned); // Allow for international numbers (up to 15 digits)
};

/**
 * Normalizes two phone numbers for comparison
 *
 * @param phone1 First phone number
 * @param phone2 Second phone number
 * @returns True if the phone numbers match after normalization
 */
export const phoneNumbersMatch = (phone1: string | null | undefined, phone2: string | null | undefined): boolean => {
  if (!phone1 || !phone2) return false;

  const cleaned1 = cleanPhoneNumber(phone1);
  const cleaned2 = cleanPhoneNumber(phone2);

  // If either number is empty after cleaning, they don't match
  if (!cleaned1 || !cleaned2) return false;

  // Check for exact match after cleaning
  if (cleaned1 === cleaned2) return true;

  // Check for match with the last 10 digits (most significant part)
  // This helps match numbers with different country codes
  const last10Digits1 = cleaned1.slice(-10);
  const last10Digits2 = cleaned2.slice(-10);

  if (last10Digits1.length === 10 && last10Digits2.length === 10 && last10Digits1 === last10Digits2) {
    console.log(`Phone numbers match by last 10 digits: ${phone1} and ${phone2}`);
    return true;
  }

  return false;
};

// Add a default export
const phoneUtils = {
  // Export any existing functions or utilities
};

export default phoneUtils;
