/**
 * Word Filter Utility
 *
 * This utility provides functions to detect and filter illegal or inappropriate words
 * from user input. It can be used to prevent users from sending offensive content.
 */

// Default list of banned words (this is a basic example - should be expanded)
const DEFAULT_BANNED_WORDS = [
  // Profanity
  'fuck', 'shit', 'asshole', 'bitch', 'bastard', 'cunt', 'dick', 'pussy',
  // Slurs and hate speech
  'nigger', 'faggot', 'retard', 'spic', 'chink', 'kike', 'tranny',
  // Violence
  'kill yourself', 'kys', 'suicide',
  // Illegal activities
  'cocaine', 'heroin', 'meth', 'child porn', 'cp', 'csam','die'
];

// Filter severity levels
export enum FilterSeverity {
  WARN = 'warn',     // Just warn the user but allow the message
  BLOCK = 'block',   // Block the message from being sent
}

// Filter configuration
export interface WordFilterConfig {
  severity: FilterSeverity;
  customBannedWords?: string[];
  enableFilter: boolean;
}

// Default configuration
export const DEFAULT_FILTER_CONFIG: WordFilterConfig = {
  severity: FilterSeverity.BLOCK,
  enableFilter: true,
};

/**
 * Check if text contains any banned words
 *
 * @param text The text to check
 * @param config Optional filter configuration
 * @returns Object with result and matched words
 */
export const checkForIllegalWords = (
  text: string,
  config: WordFilterConfig = DEFAULT_FILTER_CONFIG
): { hasIllegalWords: boolean; matches: string[] } => {
  if (!config.enableFilter) {
    return { hasIllegalWords: false, matches: [] };
  }

  // Use custom banned words list if provided, otherwise use default
  const bannedWords = config.customBannedWords || DEFAULT_BANNED_WORDS;

  // Convert text to lowercase for case-insensitive matching
  const lowerText = text.toLowerCase();

  // Find all matches
  const matches: string[] = [];

  for (const word of bannedWords) {
    // Use word boundary for whole word matching when possible
    const regex = new RegExp(`\\b${word}\\b|${word}`, 'i');
    if (regex.test(lowerText)) {
      matches.push(word);
    }
  }

  return {
    hasIllegalWords: matches.length > 0,
    matches,
  };
};

/**
 * Filter illegal words from text by replacing them with asterisks
 *
 * @param text The text to filter
 * @param config Optional filter configuration
 * @returns Filtered text with illegal words replaced by asterisks
 */
export const filterIllegalWords = (
  text: string,
  config: WordFilterConfig = DEFAULT_FILTER_CONFIG
): string => {
  if (!config.enableFilter) {
    return text;
  }

  // Use custom banned words list if provided, otherwise use default
  const bannedWords = config.customBannedWords || DEFAULT_BANNED_WORDS;

  let filteredText = text;

  for (const word of bannedWords) {
    // Create a regular expression that matches the word with word boundaries
    const regex = new RegExp(`\\b${word}\\b|${word}`, 'gi');

    // Replace the word with asterisks of the same length
    filteredText = filteredText.replace(regex, match => '*'.repeat(match.length));
  }

  return filteredText;
};

export default {
  checkForIllegalWords,
  filterIllegalWords,
  FilterSeverity,
  DEFAULT_FILTER_CONFIG,
};
