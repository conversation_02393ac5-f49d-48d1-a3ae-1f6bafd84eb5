import { StatusBar, TouchableOpacity, Text, ScrollView, TextInput, ActivityIndicator, Alert, Clipboard, ImageBackground, Keyboard, KeyboardEvent } from 'react-native'
import React, { useState, useRef, useEffect } from 'react'
import { SafeAreaView, View, MotiView, Image } from 'moti';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { MoveLeft, EllipsisVertical, Send, Copy, Battery as BatteryIcon, Sun, Square } from 'lucide-react-native';
import { checkForIllegalWords } from '@/utils/wordFilter';
import * as ExpoBattery from 'expo-battery';
import * as Brightness from 'expo-brightness';
// No explicit import needed for the global WebSocket object

// Battery and Brightness Info Component
const SystemInfo = () => {
  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
  const [brightness, setBrightness] = useState<number | null>(null);

  useEffect(() => {
    const getBatteryLevel = async () => {
      const level = await ExpoBattery.getBatteryLevelAsync();
      setBatteryLevel(level);
    };

    const getBrightness = async () => {
      const level = await Brightness.getBrightnessAsync();
      setBrightness(level);
    };

    getBatteryLevel();
    getBrightness();

    // Update battery level every minute
    const batteryInterval = setInterval(getBatteryLevel, 60000);
    // Update brightness every 5 seconds
    const brightnessInterval = setInterval(getBrightness, 5000);

    return () => {
      clearInterval(batteryInterval);
      clearInterval(brightnessInterval);
    };
  }, []);

  return (
    <View style={tw`flex-row justify-end items-center gap-2 px-4 py-2`}>
      <View style={tw`flex-row items-center gap-1 bg-black/10 px-2 py-1 rounded-full`}>
        <BatteryIcon size={16} color="#000" />
        <Text style={tw`text-black text-sm`}>
          {batteryLevel !== null ? `${Math.round(batteryLevel * 100)}%` : '...'}
        </Text>
      </View>
      <View style={tw`flex-row items-center gap-1 bg-black/10 px-2 py-1 rounded-full`}>
        <Sun size={16} color="#000" />
        <Text style={tw`text-black text-sm`}>
          {brightness !== null ? `${Math.round(brightness * 100)}%` : '...'}
        </Text>
      </View>
    </View>
  );
};

// Message component to display chat messages
// Removed setMessages prop as it's no longer needed for toggleThinkingVisibility
const MessageBubble = React.memo(({ message, isUser }: any) => {

  // Display text - Simplified to always show message.text
  // Removed conditional logic for isThinking and showThinking
  const displayText = isUser ? (message.text || '') : (message.text ? String(message.text) : '');


  return (
    <MotiView
      from={isUser
        ? { opacity: 0, translateY: 10 }
        : { opacity: 0, translateY: 20, scale: 0.9 }
      }
      animate={isUser
        ? { opacity: 1, translateY: 0 }
        : { opacity: 1, translateY: 0, scale: 1 }
      }
      transition={isUser
        ? { type: 'timing', duration: 300 }
        : { type: 'spring', damping: 15, stiffness: 150 }
      }
      style={[
        tw`my-2 max-w-[80%]`,
        isUser ? tw`self-end` : tw`self-start flex-row`
      ]}
    >
      <View
        style={[
          tw`p-3 rounded-[20px] py-3.5`,
          isUser
            ? tw`bg-[#BCFEFE] border-[#A2F6F7] border rounded-br-none`
            : tw`bg-[#F6F6F6] rounded-bl-none`
        ]}
      >
         {/* Always render the main text */}
             <Text
                style={[
                  tw`text-base`,
                  isUser ? tw`text-black` : tw`text-black`,
                  { fontFamily: 'GoogleSans-Regular' }
                ]}
              >
                {displayText}
              </Text>


        {/* Time stamp, displayed only if time is available */}
         {message.time && (
          <Text
            style={[
              tw`text-xs text-gray-500 mt-1`,
              isUser ? tw`text-right` : tw`text-left`
            ]}
          >
            {message.time}
          </Text>
        )}
      </View>
    </MotiView>
  );
});

// Typing indicator component
const TypingIndicator = ({ isVisible }: { isVisible: boolean }) => {
  if (!isVisible) return null;

  return (
    <MotiView
      from={{ opacity: 0, translateY: 10 }}
      animate={{ opacity: 1, translateY: 0 }}
      transition={{ type: 'timing', duration: 300 }}
      style={tw`my-2 self-start`}
    >
      <View style={tw`p-3 rounded-[20px] bg-[#F6F6F6] rounded-bl-none flex-row items-center`}>
        <ActivityIndicator size="small" color="#000" />
        <Text style={tw`ml-2 text-sm text-gray-600`}>Typing...</Text>
      </View>
    </MotiView>
  );
};

// WebSocket endpoint for AI chat
const AI_WS_URL = 'ws://nx.ai.streaming.api.nityasha.com/ws/chat';

export default function AI({ }: any) {
  const navigation = useNavigation();
  const [inputText, setInputText] = useState('');
  // Define message type
  type Message = {
    id: number;
    text: string; // Main response text
    isUser: boolean;
    time?: string;
    // Removed: isThinking?: boolean;
    // Removed: thinkingText?: string;
    // Removed: showThinking?: boolean;
  };

  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  // State to control the visibility of the stop button
  const [showStopButton, setShowStopButton] = useState(false);

  const scrollViewRef = useRef<ScrollView>(null);
  const inputRef = useRef<TextInput>(null);
  // Ref to hold the WebSocket instance
  const wsRef = useRef<WebSocket | null>(null);
  // Ref to hold the index of the AI message being streamed
  const streamingMessageIndex = useRef<number | null>(null);
   // Ref to accumulate raw chunks before processing
  const rawResponseBuffer = useRef<string>('');
    // Ref to track if the thinking phase has finished
  const hasFinishedThinking = useRef<boolean>(false);


  // Function to check battery level
  const checkBatteryLevel = async () => {
    try {
      const batteryLevel = await ExpoBattery.getBatteryLevelAsync();
      return `Your battery level is ${Math.round(batteryLevel * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't check your battery level.";
    }
  };

  // Function to check brightness
  const checkBrightness = async () => {
    try {
      const brightness = await Brightness.getBrightnessAsync();
      return `Your screen brightness is set to ${Math.round(brightness * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't check your screen brightness.";
    }
  };

  // Function to set brightness
  const setBrightness = async (level: number) => {
    try {
      await Brightness.setSystemBrightnessAsync(level);
      return `Screen brightness has been set to ${Math.round(level * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't adjust your screen brightness.";
    }
  };

  // Function to scroll to bottom of messages
  const scrollToBottom = (delay = 100) => {
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }
    }, delay);
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    // First immediate scroll attempt
    scrollToBottom(10);
    // Second attempt after animations likely complete
    scrollToBottom(300);
    // Final attempt after everything should be rendered
    scrollToBottom(600);
  }, [messages]);

  // Handle keyboard events to ensure scrolling when keyboard appears
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      scrollToBottom(100);
    });

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

   // Effect to close WebSocket on unmount
  useEffect(() => {
      return () => {
          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
              wsRef.current.close();
          }
      };
  }, []);

    // Function to handle stopping the AI response
  const handleStopResponse = () => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
          console.log('Stopping WebSocket response.');
          wsRef.current.close(); // Close the WebSocket connection
          setIsLoading(false);
          setShowStopButton(false);
           // Update the message to indicate interruption
           setMessages(prevMessages => {
                const updated = [...prevMessages];
                 if (streamingMessageIndex.current !== null && updated[streamingMessageIndex.current]) {
                     updated[streamingMessageIndex.current] = {
                         ...updated[streamingMessageIndex.current],
                         // Append interruption text to the main text
                          text: (updated[streamingMessageIndex.current].text || '') + "\n\n(Response interrupted)",
                     };
                 }
                return updated;
           });
          streamingMessageIndex.current = null; // Reset streaming index
          rawResponseBuffer.current = ''; // Clear buffer on stop
          hasFinishedThinking.current = false; // Reset thinking state
      }
  };


  // Function to send message to AI API (using built-in WebSocket)
  const sendMessageToAI = async (userMessage: string) => {
    // Close any existing WebSocket connection before starting a new one
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.close();
    }
     // Always add an initial empty message placeholder for the AI's response
     // This ensures there's a message to update, even if an error occurs before the WS opens
    const aiMsg: Message = {
        id: Date.now() + 1,
        text: '', // Main response starts empty
        isUser: false,
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
         // Removed: isThinking: true,
         // Removed: thinkingText: 'Thinking...',
         // Removed: showThinking: false,
    };
     // We need to add the message and get the new length *before* setting the state
    setMessages(prev => {
        const updatedMessages = [...prev, aiMsg];
        streamingMessageIndex.current = updatedMessages.length - 1; // index of this AI message
        return updatedMessages;
    });
    scrollToBottom(10); // Scroll to show the new empty message
    rawResponseBuffer.current = ''; // Clear buffer for new message
    hasFinishedThinking.current = false; // Reset thinking state for new message


    try {
      setIsLoading(true);

      // Check for illegal words
      const { hasIllegalWords } = checkForIllegalWords(userMessage);
      if (hasIllegalWords) {
        Alert.alert(
          'Inappropriate Content',
          `Your message contains inappropriate content that violates our terms of service. Please revise your message.`,
          [{ text: 'OK' }]
        );
        setIsLoading(false);
         // Remove the empty AI message if we don't proceed to send
        setMessages(prevMessages => prevMessages.filter(msg => msg.id !== aiMsg.id));
        streamingMessageIndex.current = null; // Reset index
         rawResponseBuffer.current = ''; // Clear buffer
         hasFinishedThinking.current = false; // Reset thinking state
        return;
      }

      // Check for battery and brightness commands
      const lowerMessage = userMessage.toLowerCase();
      let response = '';

      if (lowerMessage.includes('battery') || lowerMessage.includes('battery level')) {
        response = await checkBatteryLevel();
         // Update the empty AI message with the response
        setMessages(prevMessages =>
            prevMessages.map(msg =>
                msg.id === aiMsg.id ? { ...msg, text: response } : msg
            )
        );
        setIsLoading(false); // Stop loading for these commands
        streamingMessageIndex.current = null; // Not a streaming message
         rawResponseBuffer.current = ''; // Clear buffer
         hasFinishedThinking.current = false; // Reset thinking state
      } else if (lowerMessage.includes('brightness') || lowerMessage.includes('screen brightness')) {
        if (lowerMessage.includes('set') || lowerMessage.includes('change')) {
          // Extract number from message (0-100)
          const match = lowerMessage.match(/\d+/);
          if (match) {
            const level = parseInt(match[0]) / 100;
            response = await setBrightness(Math.min(Math.max(level, 0), 1));
          } else {
            response = "Please specify a brightness level between 0 and 100.";
          }
        } else {
          response = await checkBrightness();
        }
         // Update the empty AI message with the response
        setMessages(prevMessages =>
            prevMessages.map(msg =>
                msg.id === aiMsg.id ? { ...msg, text: response } : msg
            )
        );
        setIsLoading(false); // Stop loading for these commands
        streamingMessageIndex.current = null; // Not a streaming message
         rawResponseBuffer.current = ''; // Clear buffer
         hasFinishedThinking.current = false; // Reset thinking state
      } else {
        // Use built-in WebSocket for streaming API call
        const ws = new WebSocket(AI_WS_URL);
        wsRef.current = ws; // Store the WebSocket instance

        ws.onopen = () => {
          console.log('WebSocket connection opened.');
           // Send the message once the connection is open
           // Modified payload to match wscode example
          ws.send(JSON.stringify({
              message: `${userMessage}` // Removed prefix based on the latest logs
          }));
          setShowStopButton(true); // Show stop button when streaming starts
        };

        ws.onmessage = (event) => {
          try {
             // Assuming the data is a stringified JSON object
            const data = JSON.parse(event.data);
            // console.log('WebSocket message received:', data); // Log received data

            if (data.chunk) {
                rawResponseBuffer.current += data.chunk;

                let processedText = '';
                let buffer = rawResponseBuffer.current;
                let inThinking = !hasFinishedThinking.current && buffer.includes('<think>'); // Check if we might be in a thinking block


                 while (buffer.length > 0) {
                     const thinkStartIndex = buffer.indexOf('<think>');
                     const thinkEndIndex = buffer.indexOf('</think>');

                     if (inThinking && thinkEndIndex !== -1) {
                          // Found closing tag while in thinking block, discard thinking content
                          // We take the part *after* the closing tag and append it to processedText
                          const contentAfterThinkEnd = buffer.substring(thinkEndIndex + '</think>'.length);
                           processedText += contentAfterThinkEnd;
                           buffer = ''; // Clear buffer after finding end tag
                           inThinking = false;
                           hasFinishedThinking.current = true; // Thinking phase is definitely over
                           break; // Exit loop after processing the end of thinking block
                     } else if (!inThinking && thinkStartIndex !== -1) {
                          // Found opening tag while not in thinking block
                           // We take the part *before* the opening tag and append it to processedText
                           const contentBeforeThinkStart = buffer.substring(0, thinkStartIndex);
                           processedText += contentBeforeThinkStart;
                           // The buffer is set to the content *after* the opening tag (start of think text)
                           buffer = buffer.substring(thinkStartIndex + '<think>'.length);
                           inThinking = true; // Now entering thinking block
                     } else if (inThinking && thinkEndIndex === -1) {
                         // In thinking block but no closing tag yet, discard the whole buffer for now
                         // This means any text received within the <think> block so far is dropped
                         buffer = '';
                         break; // Wait for the closing tag in the next chunk
                     } else if (!inThinking && thinkStartIndex === -1) {
                         // Not in thinking block and no opening tag, append everything
                         processedText += buffer;
                         buffer = ''; // Clear buffer
                         break; // Processed the whole buffer
                     } else {
                         // Should not happen in a valid sequence, but just in case, append remaining and break
                         processedText += buffer;
                         buffer = '';
                         break;
                     }
                 }

                 rawResponseBuffer.current = buffer; // Keep any incomplete parts in buffer


                 if (processedText.length > 0) {
                     setMessages(prevMessages => {
                         const updated = [...prevMessages];
                         if (streamingMessageIndex.current !== null && updated[streamingMessageIndex.current]) {
                              updated[streamingMessageIndex.current] = {
                                  ...updated[streamingMessageIndex.current],
                                  text: (updated[streamingMessageIndex.current].text || '') + processedText,
                              };
                         }
                         return updated;
                     });
                 }


                 scrollToBottom(50); // Scroll as new text arrives
            }


            // Check for 'done' signal
            if (data.done) {
              console.log('Stream done.');
              setIsLoading(false);
              setShowStopButton(false); // Hide stop button when done
               // Ensure the final message state is correct on done
               setMessages(prevMessages => {
                    const updated = [...prevMessages];
                     if (streamingMessageIndex.current !== null && updated[streamingMessageIndex.current]) {
                          // Append any remaining buffer content (which shouldn't contain think tags at this point)
                          const finalResponseText = (updated[streamingMessageIndex.current].text || '') + rawResponseBuffer.current;
                          updated[streamingMessageIndex.current] = {
                              ...updated[streamingMessageIndex.current],
                              text: finalResponseText,
                              // Removed thinking related properties
                          };
                     }
                     return updated;
               });

              streamingMessageIndex.current = null; // Reset streaming index
              rawResponseBuffer.current = ''; // Clear buffer on done
              hasFinishedThinking.current = false; // Reset thinking state
               // Check if ws is still open before closing to avoid errors
              if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
                 ws.close(); // Close the connection when done
              }
              scrollToBottom(300); // Final scroll
              return;
            }


          } catch (e) {
            console.error('WebSocket parse error:', e);
             // Update the message with a parsing error indicator
             setMessages(prevMessages => {
                  const updated = [...prevMessages];
                   if (streamingMessageIndex.current !== null && updated[streamingMessageIndex.current]) {
                       updated[streamingMessageIndex.current] = {
                           ...updated[streamingMessageIndex.current],
                           // Append error to the main text
                            text: (updated[streamingMessageIndex.current].text || '') + "\n\n(Error processing stream chunk)",
                       };
                   } else {
                        // If no streaming message was being tracked, add a new error message
                        return [
                           ...prevMessages,
                           {
                             id: Date.now(), // New ID for this error message
                             text: "I'm sorry, a WebSocket error occurred during parsing.",
                             isUser: false,
                             time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                           }
                        ];
                   }
                    rawResponseBuffer.current = ''; // Clear buffer on error
                    hasFinishedThinking.current = false; // Reset thinking state on error
                   return updated;
             });
            setIsLoading(false); // Stop loading on error
            setShowStopButton(false); // Hide stop button on error
            streamingMessageIndex.current = null;
             // Check if ws is still open before closing to avoid errors
            if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
               ws.close(); // Close the connection on error
            }
          }
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
           // Update the message with an error indicator
           setMessages(prevMessages => {
                const updated = [...prevMessages];
                 if (streamingMessageIndex.current !== null && updated[streamingMessageIndex.current]) {
                     updated[streamingMessageIndex.current] = {
                         ...updated[streamingMessageIndex.current],
                         // Append error to the main text
                         text: (updated[streamingMessageIndex.current].text || '') + "\n\nError receiving response." + (error ? ` ${error.message}` : ""),
                     };
                 } else {
                      // If no streaming message was being tracked, add a new error message
                      return [
                         ...prevMessages,
                         {
                           id: Date.now(), // New ID for this error message
                           text: "I'm sorry, a WebSocket error occurred." + (error ? ` ${error.message}` : ""),
                           isUser: false,
                           time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                         }
                      ];
                 }
                 rawResponseBuffer.current = ''; // Clear buffer on error
                 hasFinishedThinking.current = false; // Reset thinking state on error
                 return updated;
           });
          setIsLoading(false); // Stop loading on error
          setShowStopButton(false); // Hide stop button on error
          streamingMessageIndex.current = null;
           // Check if ws is still open before closing to avoid errors
          if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
             ws.close(); // Close the connection on error
          }
        };

        ws.onclose = (event) => {
          console.log('WebSocket connection closed:', event.code, event.reason);
           // Only set loading to false if it wasn't already set by done or error
           if(isLoading) {
                setIsLoading(false);
           }
           setShowStopButton(false); // Hide stop button on close
           // If streaming message wasn't marked done and no error occurred, add a closing indicator
           // Check if the streaming index is still valid before updating
           if (streamingMessageIndex.current !== null) {
                setMessages(prevMessages => {
                    const updated = [...prevMessages];
                     if (streamingMessageIndex.current !== null && updated[streamingMessageIndex.current]) {
                          const currentMessage = updated[streamingMessageIndex.current];
                          let finalResponseText = currentMessage.text || '';

                           // Append any remaining buffer content to the main text on close,
                           // but first remove any potential incomplete think tags from the buffer.
                           let remainingBuffer = rawResponseBuffer.current;
                           const incompleteThinkMatch = remainingBuffer.match(/<think>.*?(?!<\/think>)/s); // Match <think> without a closing tag
                           if (incompleteThinkMatch) {
                                // Remove the incomplete think tag and its content
                                remainingBuffer = remainingBuffer.replace(incompleteThinkMatch[0], '');
                           }
                            // Also remove any lone closing tags just in case
                            remainingBuffer = remainingBuffer.replace(/<\/think>/g, '');


                           finalResponseText = (finalResponseText || '') + remainingBuffer;


                         updated[streamingMessageIndex.current] = {
                             ...updated[streamingMessageIndex.current],
                             // Append closing indicator
                             text: (finalResponseText || '') + "\n\n(Connection closed)",
                         };
                     }
                     return updated;
                });
           }

          streamingMessageIndex.current = null; // Reset index on close
          rawResponseBuffer.current = ''; // Clear buffer on close
          hasFinishedThinking.current = false; // Reset thinking state on close
        };
      }

    } catch (error) {
      console.error('Error in sendMessageToAI:', error);

      // Update the already added empty AI message with an error
       setMessages(prevMessages =>
           prevMessages.map(msg =>
               msg.id === aiMsg.id ? { ...msg, text: "I'm sorry, I encountered an error processing your request." } : msg
           )
       );
      setIsLoading(false); // Stop loading on error
      setShowStopButton(false); // Hide stop button on error
      streamingMessageIndex.current = null; // Reset index on error
       rawResponseBuffer.current = ''; // Clear buffer on error
       hasFinishedThinking.current = false; // Reset thinking state on error
    }
    // Note: setIsLoading(false) is now handled in WebSocket onmessage (when done), onerror, or onclose
  };

  // Handle sending a message
  const handleSendMessage = () => {
    if (!inputText.trim() || isLoading) return;

    // Add user message to chat immediately
    const userMessage: Message = {
      id: Date.now(),
      text: inputText.trim(),
      isUser: true,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
    setMessages(prevMessages => [...prevMessages, userMessage]);

    // Ensure scroll happens after user message is added
    scrollToBottom(10);

    // Clear input
    const messageToSend = inputText.trim();
    setInputText('');

    // Focus the input field for next message
    inputRef.current?.focus();

    // Send to AI API (which now uses WebSocket internally for streaming)
    sendMessageToAI(messageToSend);

    // Additional scroll after a short delay to ensure UI has updated
    scrollToBottom(300); // Keep this for initial message and input clearing
  };

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`h-full`}>
      <View style={tw`h-20 flex items-center justify-between px-3 flex-row`}>
        <TouchableOpacity
          style={tw`bg-[#F6F6F6] h-15 w-15 rounded-full items-center justify-center`}
          onPress={() => navigation.goBack()}
        >
          <MoveLeft size={24} color="#000" />
        </TouchableOpacity>
        <Text style={[tw`text-black text-xl`, { fontFamily: 'Helvetica_bold' }]}>Nityasha Ai</Text>
         {/* Stop button, visible only when showStopButton is true */}
        {showStopButton ? (
            <TouchableOpacity
                style={tw`bg-red-500 h-15 w-15 rounded-full items-center justify-center`}
                onPress={handleStopResponse}
            >
                <Square size={24} color="#fff" />
            </TouchableOpacity>
        ) : (
             // Placeholder or the original options button when not streaming
             <TouchableOpacity style={tw`bg-[#F6F6F6] h-15 w-15 rounded-full items-center justify-center`}>
               <EllipsisVertical size={24} color="#000" />
             </TouchableOpacity>
        )}
      </View>


      {/* Chat messages */}
      <ScrollView
        ref={scrollViewRef}
        style={tw`flex-1 px-4`}
        contentContainerStyle={[tw`pb-4`, messages.length === 0 && !isLoading ? tw`flex-1 justify-center items-center` : {}]}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => scrollToBottom(50)}
        keyboardShouldPersistTaps="handled"
      >
        {messages.length === 0 && !isLoading ? (
          <View style={tw`flex items-center justify-center`}>
            <Image source={require('@/assets/images/icon.png')} style={tw`w-15 h-15 border-2 border-white rounded-full`} />
          </View>
        ) : (
          <>
            {messages.map(message => (
              <MessageBubble
                key={message.id}
                message={message}
                isUser={message.isUser}
              />
            ))}
          </>
        )}

        {/* Typing indicator */}
         {/* Show typing indicator only when loading */}
        <TypingIndicator isVisible={isLoading} />
      </ScrollView>

      {/* Input area */}
      <View style={tw`bg-[#F6F6F6] w-[90%] mb-4 py-2 rounded-full px-3 flex-row mx-5`}>
        <TextInput
          ref={inputRef}
          style={tw`flex-1 bg-gray-100 rounded-full px-4 py-2 mr-2`}
          placeholder="Type a message..."
          value={inputText}
          onChangeText={setInputText}
          editable={!isLoading}
          onSubmitEditing={handleSendMessage}
          onFocus={() => scrollToBottom(100)}
        />
        <TouchableOpacity
          style={[
            tw`h-13 w-13 rounded-full items-center justify-center`,
            isLoading ? tw`bg-gray-300` : tw`bg-white`
          ]}
          onPress={handleSendMessage}
          disabled={isLoading || !inputText.trim()}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <Send size={20} color="#000000" />
          )}
        </TouchableOpacity>
      </View>
    </ImageBackground>
  )
}