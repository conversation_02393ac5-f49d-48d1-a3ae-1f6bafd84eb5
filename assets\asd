<svg fill="currentColor" viewBox="0 0 40 20" xmlns="http://www.w3.org/2000/svg" class="h-full min-w-[25px] -translate-x-px text-black transition-opacity duration-500 opacity-100"><path d="M23.3919 0H32.9188C36.7819 0 39.9136 3.13165 39.9136 6.99475V16.0805H36.0006V6.99475C36.0006 6.90167 35.9969 6.80925 35.9898 6.71766L26.4628 16.079C26.4949 16.08 26.5272 16.0805 26.5595 16.0805H36.0006V19.7762H26.5595C22.6964 19.7762 19.4788 16.6139 19.4788 12.7508V3.68923H23.3919V12.7508C23.3919 12.9253 23.4054 13.0977 23.4316 13.2668L33.1682 3.6995C33.0861 3.6927 33.003 3.68923 32.9188 3.68923H23.3919V0Z"></path><path d="M13.7688 19.0956L0 3.68759H5.53933L13.6231 12.7337V3.68759H17.7535V17.5746C17.7535 19.6705 15.1654 20.6584 13.7688 19.0956Z"></path></svg>






.duration-500 {
    animation-duration: .5s;
}
.duration-500 {
    transition-duration: .5s;
}
.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .15s;
}
.opacity-100 {
    opacity: 1;
}
.text-black {
    color: hsl(var(--black));
}
.-translate-x-px {
    --tw-translate-x: -1px;
}
.-translate-x-full, .-translate-x-px {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.min-w-\[25px\] {
    min-width: 25px;
}
.h-full {
    height: 100%;
}
a svg, button svg {
    pointer-events: none;
}
svg {
    shape-rendering: crispEdges;
}
audio, canvas, embed, iframe, img, object, svg, video {
    display: block;
    vertical-align: middle;
}
*, :after, :before {
    box-sizing: border-box;
}
* {
    font-feature-settings: "zero";
}
* {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--gray-300)) transparent;
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
* {
    border-color: hsl(var(--alpha-400));
}
*, :after, :before {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: hsl(var(--gray-200));
}
svg[Attributes Style] {
    fill: currentcolor;
}
user agent stylesheet
svg:not(:root) {
    overflow-clip-margin: content-box;
    overflow: hidden;
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
a, details summary {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
a {
    -webkit-tap-highlight-color: transparent;
}
a {
    color: inherit;
    text-decoration: inherit;
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
user agent stylesheet
a:-webkit-any-link {
    color: -webkit-link;
    cursor: pointer;
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
style attribute {
    position: absolute;
    width: 100%;
    left: 0px;
    top: 3747.83px;
    visibility: visible;
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
style attribute {
    overflow-anchor: none;
    flex: 0 0 auto;
    position: relative;
    visibility: hidden;
    width: 100%;
    height: 5409.7px;
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
.__variable_3db593 {
    --font-geist-mono: "GeistMono", ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace;
}
.__variable_ad2de5 {
    --font-geist-sans: "GeistSans", "GeistSans Fallback";
}
.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.text-gray-900 {
    color: hsl(var(--gray-900));
}
.font-sans {
    font-family: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
:host, html, html body {
    font-family: var(--font-sans, var(--font-sans-fallback));
}
:host, body, html {
    text-rendering: optimizeLegibility;
    background-color: var(--geist-background);
    color: var(--ds-gray-1000);
    scroll-padding-top: var(--header-height);
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
body {
    position: relative;
    font-size: 100%;
    min-height: 100%;
    max-width: 100vw;
    margin: 0;
}
body {
    font-synthesis: none;
}
body {
    background-color: hsl(var(--white));
    color: hsl(var(--gray-900));
}
body {
    margin: 0;
    line-height: inherit;
}
*, :after, :before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
Show all properties (1 more)
}
style attribute {
    color-scheme: dark;
}
<style>
:root {
    --vscode-icon-add-content: '\ea60';
    --vscode-icon-add-font-family: 'codicon';
    --vscode-icon-plus-content: '\ea60';
    --vscode-icon-plus-font-family: 'codicon';
    --vscode-icon-gist-new-content: '\ea60';
    --vscode-icon-gist-new-font-family: 'codicon';
    --vscode-icon-repo-create-content: '\ea60';
    --vscode-icon-repo-create-font-family: 'codicon';
    --vscode-icon-lightbulb-content: '\ea61';
    --vscode-icon-lightbulb-font-family: 'codicon';
    --vscode-icon-light-bulb-content: '\ea61';
    --vscode-icon-light-bulb-font-family: 'codicon';
    --vscode-icon-repo-content: '\ea62';
    --vscode-icon-repo-font-family: 'codicon';
    --vscode-icon-repo-delete-content: '\ea62';
    --vscode-icon-repo-delete-font-family: 'codicon';
    --vscode-icon-gist-fork-content: '\ea63';
    --vscode-icon-gist-fork-font-family: 'codicon';
    --vscode-icon-repo-forked-content: '\ea63';
    --vscode-icon-repo-forked-font-family: 'codicon';
    --vscode-icon-git-pull-request-content: '\ea64';
    --vscode-icon-git-pull-request-font-family: 'codicon';
    --vscode-icon-git-pull-request-abandoned-content: '\ea64';
    --vscode-icon-git-pull-request-abandoned-font-family: 'codicon';
    --vscode-icon-record-keys-content: '\ea65';
    --vscode-icon-record-keys-font-family: 'codicon';
    --vscode-icon-keyboard-content: '\ea65';
    --vscode-icon-keyboard-font-family: 'codicon';
    --vscode-icon-tag-content: '\ea66';
    --vscode-icon-tag-font-family: 'codicon';
    --vscode-icon-git-pull-request-label-content: '\ea66';
    --vscode-icon-git-pull-request-label-font-family: 'codicon';
    --vscode-icon-tag-add-content: '\ea66';
    --vscode-icon-tag-add-font-family: 'codicon';
    --vscode-icon-tag-remove-content: '\ea66';
    --vscode-icon-tag-remove-font-family: 'codicon';
    --vscode-icon-person-content: '\ea67';
    --vscode-icon-person-font-family: 'codicon';
    --vscode-icon-person-follow-content: '\ea67';
    --vscode-icon-person-follow-font-family: 'codicon';
    --vscode-icon-person-outline-content: '\ea67';
    --vscode-icon-person-outline-font-family: 'codicon';
    --vscode-icon-person-filled-content: '\ea67';
    --vscode-icon-person-filled-font-family: 'codicon';
    --vscode-icon-git-branch-content: '\ea68';
    --vscode-icon-git-branch-font-family: 'codicon';
    --vscode-icon-git-branch-create-content: '\ea68';
    --vscode-icon-git-branch-create-font-family: 'codicon';
    --vscode-icon-git-branch-delete-content: '\ea68';
    --vscode-icon-git-branch-delete-font-family: 'codicon';
Show all properties (1294 more)
}
<style>
:root {
    --vscode-sash-size: 4px;
    --vscode-sash-hover-size: 4px;
}
.dark, .dark-theme, .invert-theme {
    --ds-gray-100-value: 0, 0%, 10%;
    --ds-gray-200-value: 0, 0%, 12%;
    --ds-gray-300-value: 0, 0%, 16%;
    --ds-gray-400-value: 0, 0%, 18%;
    --ds-gray-500-value: 0, 0%, 27%;
    --ds-gray-600-value: 0, 0%, 53%;
    --ds-gray-700-value: 0, 0%, 56%;
    --ds-gray-800-value: 0, 0%, 49%;
    --ds-gray-900-value: 0, 0%, 63%;
    --ds-gray-1000-value: 0, 0%, 93%;
    --ds-blue-100-value: 216, 50%, 12%;
    --ds-blue-200-value: 214, 59%, 15%;
    --ds-blue-300-value: 213, 71%, 20%;
    --ds-blue-400-value: 212, 78%, 23%;
    --ds-blue-500-value: 211, 86%, 27%;
    --ds-blue-600-value: 206, 100%, 50%;
    --ds-blue-700-value: 212, 100%, 48%;
    --ds-blue-800-value: 212, 100%, 41%;
    --ds-blue-900-value: 210, 100%, 66%;
    --ds-blue-1000-value: 206, 100%, 96%;
    --ds-red-100-value: 357, 37%, 12%;
    --ds-red-200-value: 357, 46%, 16%;
    --ds-red-300-value: 356, 54%, 22%;
    --ds-red-400-value: 357, 55%, 26%;
    --ds-red-500-value: 357, 60%, 32%;
    --ds-red-600-value: 358, 75%, 59%;
    --ds-red-700-value: 358, 75%, 59%;
    --ds-red-800-value: 358, 69%, 52%;
    --ds-red-900-value: 358, 100%, 69%;
    --ds-red-1000-value: 353, 90%, 96%;
    --ds-amber-100-value: 35, 100%, 8%;
    --ds-amber-200-value: 32, 100%, 10%;
    --ds-amber-300-value: 33, 100%, 15%;
    --ds-amber-400-value: 35, 100%, 17%;
    --ds-amber-500-value: 35, 91%, 22%;
    --ds-amber-600-value: 39, 85%, 49%;
    --ds-amber-700-value: 39, 100%, 57%;
    --ds-amber-800-value: 35, 100%, 52%;
    --ds-amber-900-value: 39, 90%, 50%;
    --ds-amber-1000-value: 40, 94%, 93%;
    --ds-green-100-value: 136, 50%, 9%;
    --ds-green-200-value: 137, 50%, 12%;
    --ds-green-300-value: 136, 50%, 14%;
    --ds-green-400-value: 135, 70%, 16%;
    --ds-green-500-value: 135, 70%, 23%;
    --ds-green-600-value: 135, 70%, 34%;
    --ds-green-700-value: 131, 41%, 46%;
    --ds-green-800-value: 132, 43%, 39%;
    --ds-green-900-value: 131, 43%, 57%;
    --ds-green-1000-value: 136, 73%, 94%;
Show all properties (45 more)
}
.dark, .dark-theme, .invert-theme, :host, :root {
    --ds-gray-100: hsla(var(--ds-gray-100-value), 1);
    --ds-gray-200: hsla(var(--ds-gray-200-value), 1);
    --ds-gray-300: hsla(var(--ds-gray-300-value), 1);
    --ds-gray-400: hsla(var(--ds-gray-400-value), 1);
    --ds-gray-500: hsla(var(--ds-gray-500-value), 1);
    --ds-gray-600: hsla(var(--ds-gray-600-value), 1);
    --ds-gray-700: hsla(var(--ds-gray-700-value), 1);
    --ds-gray-800: hsla(var(--ds-gray-800-value), 1);
    --ds-gray-900: hsla(var(--ds-gray-900-value), 1);
    --ds-gray-1000: hsla(var(--ds-gray-1000-value), 1);
    --ds-blue-100: hsla(var(--ds-blue-100-value), 1);
    --ds-blue-200: hsla(var(--ds-blue-200-value), 1);
    --ds-blue-300: hsla(var(--ds-blue-300-value), 1);
    --ds-blue-400: hsla(var(--ds-blue-400-value), 1);
    --ds-blue-500: hsla(var(--ds-blue-500-value), 1);
    --ds-blue-600: hsla(var(--ds-blue-600-value), 1);
    --ds-blue-700: hsla(var(--ds-blue-700-value), 1);
    --ds-blue-800: hsla(var(--ds-blue-800-value), 1);
    --ds-blue-900: hsla(var(--ds-blue-900-value), 1);
    --ds-blue-1000: hsla(var(--ds-blue-1000-value), 1);
    --ds-amber-100: hsla(var(--ds-amber-100-value), 1);
    --ds-amber-200: hsla(var(--ds-amber-200-value), 1);
    --ds-amber-300: hsla(var(--ds-amber-300-value), 1);
    --ds-amber-400: hsla(var(--ds-amber-400-value), 1);
    --ds-amber-500: hsla(var(--ds-amber-500-value), 1);
    --ds-amber-600: hsla(var(--ds-amber-600-value), 1);
    --ds-amber-700: hsla(var(--ds-amber-700-value), 1);
    --ds-amber-800: hsla(var(--ds-amber-800-value), 1);
    --ds-amber-900: hsla(var(--ds-amber-900-value), 1);
    --ds-amber-1000: hsla(var(--ds-amber-1000-value), 1);
    --ds-red-100: hsla(var(--ds-red-100-value), 1);
    --ds-red-200: hsla(var(--ds-red-200-value), 1);
    --ds-red-300: hsla(var(--ds-red-300-value), 1);
    --ds-red-400: hsla(var(--ds-red-400-value), 1);
    --ds-red-500: hsla(var(--ds-red-500-value), 1);
    --ds-red-600: hsla(var(--ds-red-600-value), 1);
    --ds-red-700: hsla(var(--ds-red-700-value), 1);
    --ds-red-800: hsla(var(--ds-red-800-value), 1);
    --ds-red-900: hsla(var(--ds-red-900-value), 1);
    --ds-red-1000: hsla(var(--ds-red-1000-value), 1);
    --ds-green-100: hsla(var(--ds-green-100-value), 1);
    --ds-green-200: hsla(var(--ds-green-200-value), 1);
    --ds-green-300: hsla(var(--ds-green-300-value), 1);
    --ds-green-400: hsla(var(--ds-green-400-value), 1);
    --ds-green-500: hsla(var(--ds-green-500-value), 1);
    --ds-green-600: hsla(var(--ds-green-600-value), 1);
    --ds-green-700: hsla(var(--ds-green-700-value), 1);
    --ds-green-800: hsla(var(--ds-green-800-value), 1);
    --ds-green-900: hsla(var(--ds-green-900-value), 1);
    --ds-green-1000: hsla(var(--ds-green-1000-value), 1);
Show all properties (30 more)
}
.dark .invert-theme, .dark-theme .invert-theme, :host, :root {
    --ds-gray-100-value: 0, 0%, 95%;
    --ds-gray-200-value: 0, 0%, 92%;
    --ds-gray-300-value: 0, 0%, 90%;
    --ds-gray-400-value: 0, 0%, 92%;
    --ds-gray-500-value: 0, 0%, 79%;
    --ds-gray-600-value: 0, 0%, 66%;
    --ds-gray-700-value: 0, 0%, 56%;
    --ds-gray-800-value: 0, 0%, 49%;
    --ds-gray-900-value: 0, 0%, 40%;
    --ds-gray-1000-value: 0, 0%, 9%;
    --ds-blue-100-value: 212, 100%, 97%;
    --ds-blue-200-value: 210, 100%, 96%;
    --ds-blue-300-value: 210, 100%, 94%;
    --ds-blue-400-value: 209, 100%, 90%;
    --ds-blue-500-value: 209, 100%, 80%;
    --ds-blue-600-value: 208, 100%, 66%;
    --ds-blue-700-value: 212, 100%, 48%;
    --ds-blue-800-value: 212, 100%, 41%;
    --ds-blue-900-value: 211, 100%, 42%;
    --ds-blue-1000-value: 211, 100%, 15%;
    --ds-red-100-value: 0, 100%, 97%;
    --ds-red-200-value: 0, 100%, 96%;
    --ds-red-300-value: 0, 100%, 95%;
    --ds-red-400-value: 0, 90%, 92%;
    --ds-red-500-value: 0, 82%, 85%;
    --ds-red-600-value: 359, 90%, 71%;
    --ds-red-700-value: 358, 75%, 59%;
    --ds-red-800-value: 358, 70%, 52%;
    --ds-red-900-value: 358, 66%, 48%;
    --ds-red-1000-value: 355, 49%, 15%;
    --ds-amber-100-value: 39, 100%, 95%;
    --ds-amber-200-value: 44, 100%, 92%;
    --ds-amber-300-value: 43, 96%, 90%;
    --ds-amber-400-value: 42, 100%, 78%;
    --ds-amber-500-value: 38, 100%, 71%;
    --ds-amber-600-value: 36, 90%, 62%;
    --ds-amber-700-value: 39, 100%, 57%;
    --ds-amber-800-value: 35, 100%, 52%;
    --ds-amber-900-value: 30, 100%, 32%;
    --ds-amber-1000-value: 20, 79%, 17%;
    --ds-green-100-value: 120, 60%, 96%;
    --ds-green-200-value: 120, 60%, 95%;
    --ds-green-300-value: 120, 60%, 91%;
    --ds-green-400-value: 122, 60%, 86%;
    --ds-green-500-value: 124, 60%, 75%;
    --ds-green-600-value: 125, 60%, 64%;
    --ds-green-700-value: 131, 41%, 46%;
    --ds-green-800-value: 132, 43%, 39%;
    --ds-green-900-value: 133, 50%, 32%;
    --ds-green-1000-value: 128, 29%, 15%;
Show all properties (48 more)
}
.dark, .dark-theme, .dark-theme .geist-disabled .geist-disabled-skip, .invert-theme {
    --geist-foreground: #fff;
    --geist-background: #000;
    --accents-8: #fafafa;
    --accents-7: #eaeaea;
    --accents-6: #999999;
    --accents-5: #888888;
    --accents-4: #666666;
    --accents-3: #444444;
    --accents-2: #333333;
    --accents-1: #111111;
    --geist-secondary-lighter: var(--accents-2);
    --geist-secondary-light: var(--accents-3);
    --geist-secondary: var(--accents-5);
    --geist-secondary-dark: var(--accents-7);
    --geist-link-color: var(--ds-blue-900);
    --geist-marketing-gray: var(--accents-1);
    --geist-code: var(--geist-foreground);
    --geist-error-light: #ff3333;
    --geist-error: #ff0000;
    --geist-error-dark: #e60000;
    --geist-text-gradient: linear-gradient(180deg, #ffffff, rgba(255, 255, 255, 0.75));
    --dropdown-box-shadow: 0 0 0 1px var(--accents-2);
    --dropdown-triangle-stroke: #333;
    --scroller-start: rgba(0, 0, 0, 1);
    --scroller-end: rgba(0, 0, 0, 0);
    --header-border-bottom: 0 1px 0 0 rgba(255, 255, 255, 0.1);
    --header-import-flow-background: rgba(17, 17, 17, 0.8);
    --shadow-smallest: 0 0 0 1px var(--accents-2);
    --shadow-extra-small: 0 0 0 1px var(--accents-2);
    --shadow-small: 0 0 0 1px var(--accents-2);
    --shadow-medium: 0 0 0 1px var(--accents-2);
    --shadow-large: 0 0 0 1px var(--accents-2);
    --shadow-sticky: 0 0 0 1px var(--accents-2);
    --shadow-hover: 0 0 0 1px var(--geist-foreground);
    --portal-opacity: 0.75;
    --geist-violet-background: #291d3a;
    --geist-violet-background-secondary: #211830;
    --geist-violet-background-tertiary: #211830;
    --geist-background-rgb: 0, 0, 0;
    --geist-foreground-rgb: 255, 255, 255;
    --geist-console-header: #0f0310;
    --geist-console-purple: #8a63d2;
    --geist-console-text-color-default: var(--geist-foreground);
    --geist-console-text-color-blue: #3291ff;
    --geist-console-text-color-pink: #eb367f;
    --next-icon-border: #fff;
    --ds-shadow-border: 0 0 0 1px rgba(255, 255, 255, 0.145);
    --ds-shadow-small: 0px 1px 2px rgba(0, 0, 0, 0.16);
    --ds-shadow-border-small: var(--ds-shadow-border), 0px 1px 2px rgba(0, 0, 0, 0.16);
    --ds-shadow-medium: 0px 2px 2px rgba(0, 0, 0, 0.32), 0px 8px 8px -8px rgba(0, 0, 0, 0.16);
Show all properties (8 more)
}
.dark .invert-theme, .dark-theme .invert-theme, .geist-disabled .geist-disabled-skip, :host, :root {
    --next-icon-border: #000;
}
.dark .invert-theme, .dark-theme .invert-theme, .geist-disabled .geist-disabled-skip, :host, :root {
    --geist-success-lighter: #d3e5ff;
    --geist-success-light: #3291ff;
    --geist-success: #0070f3;
    --geist-success-dark: #0761d1;
    --geist-error-lighter: #f7d4d6;
    --geist-error-light: #ff1a1a;
    --geist-error: #ee0000;
    --geist-error-dark: #c50000;
    --geist-warning-lighter: #ffefcf;
    --geist-warning-light: #f7b955;
    --geist-warning: #f5a623;
    --geist-warning-dark: #ab570a;
    --geist-violet-lighter: #d8ccf1;
    --geist-violet-light: #8a63d2;
    --geist-violet: #7928ca;
    --geist-violet-dark: #4c2889;
    --geist-violet-background: #fff;
    --geist-violet-background-secondary: #291c3a;
    --geist-violet-background-tertiary: #eae5f4;
    --geist-background-rgb: 255, 255, 255;
    --geist-foreground-rgb: 0, 0, 0;
    --geist-console-header: #efe7ed;
    --geist-console-purple: #7928ca;
    --geist-console-text-color-default: var(--geist-foreground);
    --geist-console-text-color-blue: #0070f3;
    --geist-console-text-color-pink: #eb367f;
    --geist-console-text-color-purple: #7928ca;
    --geist-cyan-lighter: #aaffec;
    --geist-cyan-light: #79ffe1;
    --geist-cyan: #50e3c2;
    --geist-cyan-dark: #29bc9b;
    --geist-highlight-purple: #f81ce5;
    --geist-highlight-magenta: #eb367f;
    --geist-highlight-pink: #ff0080;
    --geist-highlight-yellow: #fff500;
    --geist-foreground: #000;
    --geist-background: #fff;
    --geist-selection: var(--ds-blue-800);
    --geist-selection-text-color: #fff;
    --accents-1: #fafafa;
    --accents-2: #eaeaea;
    --accents-3: #999999;
    --accents-4: #888888;
    --accents-5: #666666;
    --accents-6: #444444;
    --accents-7: #333333;
    --accents-8: #111111;
    --geist-link-color: var(--ds-blue-700);
    --geist-marketing-gray: #fafbfc;
    --geist-code: var(--geist-foreground);
Show all properties (31 more)
}
:host, :root {
    font-size: 16px;
    --geist-space: 4px;
    --geist-space-2x: 8px;
    --geist-space-3x: 12px;
    --geist-space-4x: 16px;
    --geist-space-6x: 24px;
    --geist-space-8x: 32px;
    --geist-space-10x: 40px;
    --geist-space-16x: 64px;
    --geist-space-24x: 96px;
    --geist-space-32x: 128px;
    --geist-space-48x: 192px;
    --geist-space-64x: 256px;
    --geist-space-small: 32px;
    --geist-space-medium: 40px;
    --geist-space-large: 48px;
    --geist-space-gap: 24px;
    --geist-space-gap-half: 12px;
    --geist-space-gap-quarter: var(--geist-space-2x);
    --geist-gap: var(--geist-space-gap);
    --geist-gap-half: var(--geist-space-gap-half);
    --geist-gap-quarter: var(--geist-space-gap-quarter);
    --geist-gap-double: var(--geist-space-large);
    --geist-gap-section: var(--geist-space-small);
    --geist-space-negative: -4px;
    --geist-space-2x-negative: -8px;
    --geist-space-4x-negative: -16px;