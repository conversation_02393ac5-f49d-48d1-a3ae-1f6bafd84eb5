# Production Build Script for Nityasha App
# This script helps build the app for production using EAS Build

Write-Host "Starting Nityasha Production Build Process..." -ForegroundColor Green

# Check if EAS CLI is installed
$easVersion = npx eas --version
if ($LASTEXITCODE -ne 0) {
    Write-Host "EAS CLI is not installed. Installing now..." -ForegroundColor Yellow
    npm install -g eas-cli
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install EAS CLI. Please install it manually with 'npm install -g eas-cli'" -ForegroundColor Red
        exit 1
    }
}

# Check if user is logged in to EAS
$easWhoami = npx eas whoami
if ($LASTEXITCODE -ne 0) {
    Write-Host "You are not logged in to EAS. Please log in first." -ForegroundColor Yellow
    npx eas login
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to log in to EAS. Please try again manually with 'npx eas login'" -ForegroundColor Red
        exit 1
    }
}

# Verify the current working directory has package.json
if (-not (Test-Path "package.json")) {
    Write-Host "Error: package.json not found. Make sure you're in the project root directory." -ForegroundColor Red
    exit 1
}

# Check if there are any uncommitted changes
$gitStatus = git status --porcelain
if ($gitStatus) {
    Write-Host "Warning: You have uncommitted changes in your repository." -ForegroundColor Yellow
    Write-Host "It's recommended to commit your changes before building for production." -ForegroundColor Yellow
    
    $confirmation = Read-Host "Do you want to continue anyway? (y/n)"
    if ($confirmation -ne "y") {
        Write-Host "Build process cancelled." -ForegroundColor Red
        exit 0
    }
}

# Update app version if needed
$updateVersion = Read-Host "Do you want to update the app version in app.json? (y/n)"
if ($updateVersion -eq "y") {
    # Get current version from app.json
    $appJson = Get-Content "app.json" -Raw | ConvertFrom-Json
    $currentVersion = $appJson.expo.version
    $currentVersionCode = $appJson.expo.android.versionCode
    
    Write-Host "Current version: $currentVersion (versionCode: $currentVersionCode)" -ForegroundColor Cyan
    
    # Ask for new version
    $newVersion = Read-Host "Enter new version (leave empty to keep $currentVersion)"
    if (-not [string]::IsNullOrWhiteSpace($newVersion)) {
        $appJson.expo.version = $newVersion
        
        # Increment versionCode
        $appJson.expo.android.versionCode = $currentVersionCode + 1
        
        # Save updated app.json
        $appJson | ConvertTo-Json -Depth 10 | Set-Content "app.json"
        Write-Host "Updated to version $newVersion (versionCode: $($appJson.expo.android.versionCode))" -ForegroundColor Green
    }
}

# Start the build process
Write-Host "Starting production build with EAS..." -ForegroundColor Green
npx eas build --platform android --profile production

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build process initiated successfully!" -ForegroundColor Green
    Write-Host "You can monitor the build progress on the EAS dashboard." -ForegroundColor Cyan
} else {
    Write-Host "Build process failed. Please check the error messages above." -ForegroundColor Red
}
