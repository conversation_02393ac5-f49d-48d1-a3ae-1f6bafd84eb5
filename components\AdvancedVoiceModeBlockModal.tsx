import React, { useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Pressable, useColorScheme, Animated, Dimensions } from 'react-native';
import { Portal, Surface } from 'react-native-paper';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { X } from 'lucide-react-native';
import tw from 'twrnc';

interface AdvancedVoiceModeBlockModalProps {
  visible: boolean;
  onNewChat: () => void;
  onClose?: () => void;
}

const AdvancedVoiceModeBlockModal: React.FC<AdvancedVoiceModeBlockModalProps> = ({ visible, onNewChat, onClose }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const screenHeight = Dimensions.get('window').height;

  useEffect(() => {
    if (visible) {
      // Animate in
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset values
      translateY.setValue(0);
      opacity.setValue(0);
    }
  }, [visible]);

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: translateY } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationY } = event.nativeEvent;
      
      if (translationY > 100) {
        // Slide down threshold met, close modal
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: screenHeight,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start(() => {
          onClose?.();
        });
      } else {
        // Snap back to original position
        Animated.spring(translateY, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  if (!visible) return null;
  
  return (
    <Portal>
      <Animated.View
        style={[
          tw`flex-1 bg-black/40 items-center justify-center`,
          { opacity }
        ]}
      >
        <Pressable
          style={tw`flex-1 w-full`}
          onPress={onClose}
        />
        
        <PanGestureHandler
          onGestureEvent={onGestureEvent}
          onHandlerStateChange={onHandlerStateChange}
        >
          <Animated.View style={{ transform: [{ translateY }] }}>
            <Surface
              style={[
                tw`w-11/12 max-w-xl rounded-3xl items-center px-6 py-10 mx-auto`,
                {
                  backgroundColor: isDark ? '#18181b' : '#fff',
                  elevation: 8, // Paper elevation
                }
              ]}
            >
              {/* Drag indicator */}
              <View style={tw`w-12 h-1 bg-gray-400 rounded-full mb-4`} />

              {/* Close button */}
              <TouchableOpacity
                onPress={onClose}
                style={tw`absolute top-4 right-4 p-2 z-20`}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X size={28} color={isDark ? '#fff' : '#000'} />
              </TouchableOpacity>

              {/* Title */}
              <Text style={tw`text-center text-[28px] font-bold mb-4 ${isDark ? 'text-white' : 'text-black'}`}>
                Start a new chat to use{"\n"}advanced voice mode
              </Text>

              {/* Subtitle */}
              <Text style={tw`text-center text-[18px] mb-10 ${isDark ? 'text-zinc-300' : 'text-zinc-700'}`}>
                Your current chat has content that {""} advanced voice mode can't handle just yet.
              </Text>

              {/* New Chat button */}
              <TouchableOpacity
                onPress={onNewChat}
                style={tw`w-full rounded-full py-4 bg-black ${isDark ? 'bg-white' : 'bg-black'} items-center mt-2`}
                activeOpacity={0.85}
              >
                <Text style={tw`text-[20px] font-bold ${isDark ? 'text-black' : 'text-white'}`}>New Chat</Text>
              </TouchableOpacity>
            </Surface>
          </Animated.View>
        </PanGestureHandler>
      </Animated.View>
    </Portal>
  );
};

export default AdvancedVoiceModeBlockModal;