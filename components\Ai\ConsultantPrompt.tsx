"use client"

import React from "react"
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from "react-native"
import { <PERSON><PERSON><PERSON>ie<PERSON> } from "moti"
import { LinearGradient } from "expo-linear-gradient"
import { Feather } from "@expo/vector-icons"

// Theme colors (matching the existing app)
const colors = {
  primary: "#6366F1",
  primaryDark: "#4F46E5",
  primaryLight: "#A5B4FC",
  secondary: "#EC4899",
  background: "#FFFFFF",
  card: "#F9FAFB",
  surface: "#FFFFFF",
  text: "#1F2937",
  textSecondary: "#6B7280",
  border: "#E5E7EB",
}

const { width } = Dimensions.get("window")

interface ConsultantPromptProps {
  onYes: () => void
  onNo: () => void
}

const ConsultantPrompt: React.FC<ConsultantPromptProps> = ({ onYes, onNo }) => {
  return (
    <MotiView
      from={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: "timing", duration: 300 }}
      style={styles.container}
    >
      <LinearGradient
        colors={[colors.primary, colors.secondary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBorder}
      >
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <Feather name="users" size={28} color={colors.primary} />
          </View>
          
          <Text style={styles.title}>Chat with a Consultant</Text>
          
          <Text style={styles.description}>
            Would you like to connect with one of our expert consultants who can help you with your specific needs?
          </Text>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[styles.button, styles.noButton]} 
              onPress={onNo}
              activeOpacity={0.8}
            >
              <Text style={styles.noButtonText}>No, Thanks</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.button, styles.yesButton]} 
              onPress={onYes}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[colors.primary, colors.secondary]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.yesButtonGradient}
              >
                <Text style={styles.yesButtonText}>Yes, Connect Me</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </MotiView>
  )
}

const styles = StyleSheet.create({
  container: {
    width: width - 32,
    alignSelf: "center",
    marginVertical: 16,
    borderRadius: 16,
    overflow: "hidden",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  gradientBorder: {
    padding: 2,
    borderRadius: 16,
  },
  content: {
    backgroundColor: colors.background,
    borderRadius: 14,
    padding: 20,
    alignItems: "center",
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primaryLight + "20", // 20% opacity
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  description: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  button: {
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    minWidth: "45%",
    alignItems: "center",
    justifyContent: "center",
  },
  noButton: {
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
  },
  noButtonText: {
    color: colors.textSecondary,
    fontWeight: "600",
    fontSize: 14,
  },
  yesButton: {
    overflow: "hidden",
  },
  yesButtonGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    height: "100%",
  },
  yesButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 14,
  },
})

export default ConsultantPrompt
