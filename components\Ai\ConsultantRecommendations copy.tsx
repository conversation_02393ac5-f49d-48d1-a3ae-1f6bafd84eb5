"use client"

import { useState, useEffect, useCallback } from "react"
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { Search, Filter, ChevronRight } from "lucide-react-native"
import ConsultantCard from "./consultant-card"

interface Consultant {
  id: number
  name: string
  per_minute_rate: string
  birth_date: string
  pfp: string
  category_name: string
  exp: number
  isLive: number
  isChatOn: number
  [key: string]: any
}

interface CategoryData {
  name: string
  consultants: Consultant[]
}

const { width } = Dimensions.get("window")

export default function ConsultantRecommendations() {
  const [consultants, setConsultants] = useState<Consultant[]>([])
  const [categories, setCategories] = useState<CategoryData[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const fetchConsultants = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("https://api.nityasha.com/api/v1/consultants")

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }

      const data = await response.json()
      setConsultants(data)

      // Group consultants by category
      const categoryMap = new Map<string, Consultant[]>()

      data.forEach((consultant: Consultant) => {
        if (!categoryMap.has(consultant.category_name)) {
          categoryMap.set(consultant.category_name, [])
        }
        categoryMap.get(consultant.category_name)?.push(consultant)
      })

      // Convert map to array for rendering
      const categoryData: CategoryData[] = []
      categoryMap.forEach((consultants, name) => {
        categoryData.push({ name, consultants })
      })

      setCategories(categoryData)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch consultants")
      console.error("Error fetching consultants:", err)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [])

  useEffect(() => {
    fetchConsultants()
  }, [fetchConsultants])

  const onRefresh = useCallback(() => {
    setRefreshing(true)
    fetchConsultants()
  }, [fetchConsultants])

  const handleConsultantPress = (id: number) => {
    console.log(`Consultant selected: ${id}`)
    // Navigate to consultant detail or start chat
  }

  const renderCategoryTabs = () => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryTabsContainer}
      >
        <TouchableOpacity
          style={[styles.categoryTab, selectedCategory === null && styles.selectedCategoryTab]}
          onPress={() => setSelectedCategory(null)}
        >
          <Text style={[styles.categoryTabText, selectedCategory === null && styles.selectedCategoryTabText]}>All</Text>
        </TouchableOpacity>

        {categories.map((category) => (
          <TouchableOpacity
            key={category.name}
            style={[styles.categoryTab, selectedCategory === category.name && styles.selectedCategoryTab]}
            onPress={() => setSelectedCategory(category.name)}
          >
            <Text
              style={[styles.categoryTabText, selectedCategory === category.name && styles.selectedCategoryTabText]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    )
  }

  const renderCategorySection = (category: CategoryData) => {
    // Skip rendering if a category is selected and it's not this one
    if (selectedCategory !== null && selectedCategory !== category.name) {
      return null
    }

    return (
      <View key={category.name} style={styles.categorySection}>
        <View style={styles.categoryHeader}>
          <Text style={styles.categoryTitle}>{category.name}</Text>
          <TouchableOpacity style={styles.viewAllButton}>
            <Text style={styles.viewAllText}>View All</Text>
            <ChevronRight size={16} color="#6366F1" />
          </TouchableOpacity>
        </View>

        <FlatList
          data={category.consultants}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => <ConsultantCard consultant={item} onPress={handleConsultantPress} />}
          horizontal={false}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          contentContainerStyle={styles.consultantGrid}
        />
      </View>
    )
  }

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>Loading consultants...</Text>
      </View>
    )
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchConsultants}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Find Consultants</Text>
        <Text style={styles.subtitle}>Connect with experts in various fields</Text>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Search size={20} color="#6B7280" />
          <Text style={styles.searchPlaceholder}>Search consultants...</Text>
        </View>
        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color="#1F2937" />
        </TouchableOpacity>
      </View>

      {renderCategoryTabs()}

      <ScrollView
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Featured Consultants</Text>
          <FlatList
            data={consultants.filter((c) => c.isLive === 1).slice(0, 5)}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => <ConsultantCard consultant={item} onPress={handleConsultantPress} />}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredList}
          />
        </View>

        {selectedCategory === null
          ? categories.map(renderCategorySection)
          : categories.filter((category) => category.name === selectedCategory).map(renderCategorySection)}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: "#6B7280",
  },
  searchContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: "center",
  },
  searchBar: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginRight: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  searchPlaceholder: {
    marginLeft: 8,
    color: "#9CA3AF",
    fontSize: 14,
  },
  filterButton: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 10,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  categoryTabsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#FFFFFF",
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  selectedCategoryTab: {
    backgroundColor: "#6366F1",
    borderColor: "#6366F1",
  },
  categoryTabText: {
    fontSize: 14,
    color: "#4B5563",
    fontWeight: "500",
  },
  selectedCategoryTabText: {
    color: "#FFFFFF",
    fontWeight: "600",
  },
  scrollContent: {
    paddingBottom: 24,
  },
  featuredSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1F2937",
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  featuredList: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1F2937",
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    color: "#6366F1",
    fontWeight: "500",
  },
  consultantGrid: {
    paddingHorizontal: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F9FAFB",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: "#6B7280",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F9FAFB",
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: "#EF4444",
    marginBottom: 16,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: "#6366F1",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
})

