"use client"

import { useState, useEffect, useCallback } from "react"
import {
  View,
  Text,
  Image,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
  RefreshControl,
} from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { Mo<PERSON><PERSON>ie<PERSON> } from "moti"
import { Feather } from "@expo/vector-icons"

// Theme colors (matching your existing app)
const colors = {
  primary: "#6366F1",
  primaryDark: "#4F46E5",
  primaryLight: "#A5B4FC",
  secondary: "#EC4899",
  background: "#FFFFFF",
  card: "#F9FAFB",
  surface: "#FFFFFF",
  text: "#1F2937",
  textSecondary: "#6B7280",
  border: "#E5E7EB",
}

const { width } = Dimensions.get("window")

// Consultant card component
const ConsultantCard = ({ consultant, onPress }) => {
  return (
    <MotiView
      from={{ opacity: 0, translateY: 20 }}
      animate={{ opacity: 1, translateY: 0 }}
      transition={{ type: "timing", duration: 300, delay: 100 }}
      style={styles.cardContainer}
    >
      <TouchableOpacity style={styles.card} onPress={() => onPress(consultant)} activeOpacity={0.8}>
        <View style={styles.cardHeader}>
          <Image source={{ uri: consultant.pfp || "https://via.placeholder.com/100" }} style={styles.avatar} />
          <View style={styles.statusIndicator}>
            <View
              style={[
                styles.statusDot,
                { backgroundColor: consultant.isLive || consultant.isChatOn ? "#10B981" : "#6B7280" },
              ]}
            />
          </View>
        </View>

        <View style={styles.cardBody}>
          <Text style={styles.name} numberOfLines={1}>
            {consultant.name}
          </Text>
          <View style={styles.categoryContainer}>
            <Text style={styles.category} numberOfLines={1}>
              {consultant.category_name}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Feather name="star" size={14} color={colors.primary} />
              <Text style={styles.infoText}>{consultant.exp || 0} yrs</Text>
            </View>
            <View style={styles.infoItem}>
              <Feather name="clock" size={14} color={colors.primary} />
              <Text style={styles.infoText}>₹{consultant.per_minute_rate}/min</Text>
            </View>
          </View>
        </View>

        <LinearGradient
          colors={[colors.primary, colors.secondary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.chatButton}
        >
          <Feather name="message-circle" size={16} color="white" />
          <Text style={styles.chatButtonText}>Chat</Text>
        </LinearGradient>
      </TouchableOpacity>
    </MotiView>
  )
}

// Category section component
const CategorySection = ({ category, consultants, onConsultantPress }) => {
  return (
    <View style={styles.categorySection}>
      <View style={styles.categoryHeader}>
        <Text style={styles.categoryTitle}>{category}</Text>
        <TouchableOpacity style={styles.viewAllButton}>
          <Text style={styles.viewAllText}>View All</Text>
          <Feather name="chevron-right" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <FlatList
        data={consultants}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => <ConsultantCard consultant={item} onPress={onConsultantPress} />}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.consultantList}
      />
    </View>
  )
}

// Main component
const ConsultantRecommendations = ({ onConsultantSelect }) => {
  const [consultants, setConsultants] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [refreshing, setRefreshing] = useState(false)
  const [categorizedConsultants, setCategorizedConsultants] = useState({})

  // Fetch consultants from API
  const fetchConsultants = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("https://api.nityasha.com/api/v1/consultants")

      if (!response.ok) {
        throw new Error("Failed to fetch consultants")
      }

      const data = await response.json()
      setConsultants(data)

      // Group consultants by category
      const grouped = data.reduce((acc, consultant) => {
        const category = consultant.category_name || "Other"
        if (!acc[category]) {
          acc[category] = []
        }
        acc[category].push(consultant)
        return acc
      }, {})

      setCategorizedConsultants(grouped)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [])

  useEffect(() => {
    fetchConsultants()
  }, [fetchConsultants])

  const handleRefresh = () => {
    setRefreshing(true)
    fetchConsultants()
  }

  const handleConsultantPress = (consultant) => {
    if (onConsultantSelect) {
      onConsultantSelect(consultant)
    }
  }

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading consultants...</Text>
      </View>
    )
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Feather name="alert-circle" size={48} color={colors.textSecondary} />
        <Text style={styles.errorText}>Failed to load consultants</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchConsultants}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={Object.keys(categorizedConsultants)}
        keyExtractor={(item) => item}
        renderItem={({ item: category }) => (
          <CategorySection
            category={category}
            consultants={categorizedConsultants[category]}
            onConsultantPress={handleConsultantPress}
          />
        )}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.categoriesList}
        ListHeaderComponent={
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Recommended Consultants</Text>
            <Text style={styles.headerSubtitle}>Find the right expert for your needs</Text>
          </View>
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "white",
    fontWeight: "600",
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: colors.text,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  categoriesList: {
    paddingBottom: 20,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    color: colors.primary,
    marginRight: 4,
  },
  consultantList: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  cardContainer: {
    width: width * 0.7,
    maxWidth: 280,
    marginRight: 12,
  },
  card: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.border,
  },
  cardHeader: {
    position: "relative",
    alignItems: "center",
    marginBottom: 12,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.border,
  },
  statusIndicator: {
    position: "absolute",
    bottom: 0,
    right: width * 0.25,
    backgroundColor: "white",
    borderRadius: 10,
    padding: 2,
    borderWidth: 1,
    borderColor: colors.border,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  cardBody: {
    alignItems: "center",
  },
  name: {
    fontSize: 16,
    fontWeight: "bold",
    color: colors.text,
    marginBottom: 4,
    textAlign: "center",
  },
  categoryContainer: {
    backgroundColor: colors.primaryLight + "30",
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  category: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: "500",
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 6,
  },
  infoText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginLeft: 4,
  },
  chatButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
    borderRadius: 8,
  },
  chatButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 6,
  },
})

export default ConsultantRecommendations

