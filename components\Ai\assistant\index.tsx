import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  Modal,
  Pressable,
  TouchableWithoutFeedback,
  TextInput,
  Keyboard as RNKeyboard,
} from 'react-native';
import { theme } from '../../../app/theme';
import tw from 'twrnc';
import { Keyboard as KeyboardIcon, Plus, Send } from 'lucide-react-native';

export default function Assistant() {
  const [showPopup, setShowPopup] = useState(false);
  const [inputText, setInputText] = useState('');
  const inputRef = useRef<TextInput>(null);

  // Auto-focus the input field when the popup opens
  useEffect(() => {
    if (showPopup) {
      // Use a longer delay to ensure the modal is fully visible before focusing
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          // Force keyboard to open
          RNKeyboard.dismiss();
          setTimeout(() => {
            if (inputRef.current) {
              inputRef.current.focus();
            }
          }, 50);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [showPopup]);

  // Function to force keyboard to open
  const forceKeyboardOpen = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Handle sending a message
  const handleSendMessage = () => {
    if (inputText.trim()) {
      // Here you would typically send the message to your AI service
      console.log('Sending message:', inputText);

      // Clear the input field after sending
      setInputText('');

      // Keep keyboard open and focus on input
      setTimeout(forceKeyboardOpen, 50);

      // You could also close the popup if desired
      // setShowPopup(false);
    }
  };

  return (
    <>
      <TouchableWithoutFeedback
        onLongPress={() => {
          setShowPopup(true);
          // Force keyboard to open after a delay
          setTimeout(forceKeyboardOpen, 500);
        }}
        delayLongPress={300}
      >
        <View
          style={{
            backgroundColor: theme.colors.primary,
            width: 50,
            height: 50,
            marginTop: 10,
            borderRadius: 18,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image
            source={require('@/assets/images/image.png')}
            style={{
              width: 50,
              height: 50,
              resizeMode: 'contain',
              tintColor: theme.colors.onPrimary,
            }}
          />
        </View>
      </TouchableWithoutFeedback>

      <Modal
        transparent
        visible={showPopup}
        animationType="fade"
        onShow={() => {
          // Force focus when modal is shown
          setTimeout(() => {
            if (inputRef.current) {
              inputRef.current.focus();
            }
          }, 500);
        }}
        onRequestClose={() => setShowPopup(false)}
      >
        <Pressable
          style={tw`flex-1 bg-black/10 justify-end items-center px-2`}
          onPress={(event) => {
            // Only close if tapping outside the input area
            if (event.target !== inputRef.current) {
              RNKeyboard.dismiss();
              setShowPopup(false);
            }
          }}
        >
          <View
            style={tw`bg-white rounded-[31px] mb-20 p-4 w-full shadow-lg`}
          >
            <View style={tw`flex-row justify-between items-center mb-2`}>
              <View style={tw`flex-row items-center justify-center`}>
                <Plus size={24} color={"#000"} />
                <Text style={tw`text-[12px] font-bold ml-2 w-[12.5rem]`}>Hello I'm Nityasha Your Personal assistant Can I Help You</Text>
              </View>
              <View style={tw`rounded-full border-2 w-[35px] h-[35px] items-center justify-center`}>
                <KeyboardIcon size={20} color={"#000"} />
              </View>
            </View>

            {/* Input field with send button that automatically gets focus */}
            <View style={tw`flex-row items-center mt-1`}>
              <TextInput
                ref={inputRef}
                style={tw`flex-1 border border-gray-300 rounded-full px-4 py-2 mr-2`}
                placeholder="Type your message..."
                value={inputText}
                onChangeText={setInputText}
                autoFocus={true}
                showSoftInputOnFocus={true}
                onFocus={() => console.log('Input focused')}
                onSubmitEditing={() => {
                  handleSendMessage();
                  // Re-focus after submission to keep keyboard open
                  setTimeout(forceKeyboardOpen, 50);
                }}
                returnKeyType="send"
                keyboardType="default"
                keyboardAppearance="default"
              />
              <TouchableWithoutFeedback onPress={handleSendMessage}>
                <View style={tw`bg-primary rounded-full w-10 h-10 items-center justify-center`}>
                  <Send size={20} color="#fff" />
                </View>
              </TouchableWithoutFeedback>
            </View>
          </View>
        </Pressable>
      </Modal>
    </>
  );
}
