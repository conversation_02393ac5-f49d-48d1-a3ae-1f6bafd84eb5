import { View, Text, Image, TouchableOpacity, StyleSheet, Dimensions } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { Star, MessageCircle } from "lucide-react-native"

interface ConsultantCardProps {
  consultant: {
    id: number
    name: string
    per_minute_rate: string
    pfp: string
    category_name: string
    exp: number
    isLive: number
    isChatOn: number
  }
  onPress: (id: number) => void
}

const { width } = Dimensions.get("window")
const cardWidth = width / 2 - 24 // 2 cards per row with padding

export default function ConsultantCard({ consultant, onPress }: ConsultantCardProps) {
  return (
    <TouchableOpacity style={styles.cardContainer} onPress={() => onPress(consultant.id)} activeOpacity={0.9}>
      <View style={styles.card}>
        {/* Consultant Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: consultant.pfp || "https://placeholder.svg?height=120&width=120" }}
            style={styles.image}
            resizeMode="cover"
          />
          {consultant.isLive === 1 && (
            <View style={styles.liveIndicator}>
              <Text style={styles.liveText}>LIVE</Text>
            </View>
          )}
        </View>

        {/* Consultant Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.name} numberOfLines={1}>
            {consultant.name}
          </Text>

          <View style={styles.categoryContainer}>
            <Text style={styles.category} numberOfLines={1}>
              {consultant.category_name}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <View style={styles.experienceContainer}>
              <Star size={12} color="#FFD700" />
              <Text style={styles.experience}>{consultant.exp} yrs</Text>
            </View>

            <View style={styles.rateContainer}>
              <Text style={styles.rate}>₹{consultant.per_minute_rate}/min</Text>
            </View>
          </View>

          <LinearGradient
            colors={["#6366F1", "#EC4899"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.chatButton}
          >
            <MessageCircle size={14} color="#FFFFFF" />
            <Text style={styles.chatButtonText}>{consultant.isChatOn ? "Chat Now" : "Book Chat"}</Text>
          </LinearGradient>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  cardContainer: {
    width: cardWidth,
    margin: 8,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#FFFFFF",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  card: {
    flex: 1,
  },
  imageContainer: {
    position: "relative",
    height: 140,
    width: "100%",
  },
  image: {
    height: "100%",
    width: "100%",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  liveIndicator: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#EF4444",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  liveText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "bold",
  },
  infoContainer: {
    padding: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: "700",
    color: "#1F2937",
    marginBottom: 4,
  },
  categoryContainer: {
    marginBottom: 8,
  },
  category: {
    fontSize: 12,
    color: "#6B7280",
    fontWeight: "500",
  },
  detailsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  experienceContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  experience: {
    fontSize: 12,
    color: "#6B7280",
    marginLeft: 4,
  },
  rateContainer: {
    backgroundColor: "#F3F4F6",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  rate: {
    fontSize: 11,
    fontWeight: "600",
    color: "#4F46E5",
  },
  chatButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
    borderRadius: 8,
  },
  chatButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 6,
  },
})

