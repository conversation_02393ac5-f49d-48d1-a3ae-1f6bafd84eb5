import React, { useRef, useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions, PanResponder } from 'react-native';
import tw from 'twrnc';
import { Laugh, Heart ,HeartCrack,CircleDollarSign,Users   } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

const IconComponents = { Heart, Laugh,HeartCrack,CircleDollarSign,Users }; // Map icons

type Card = {
  id: string;
  title: string;
  icon: keyof typeof IconComponents;
};

type ScrollViewRef = {
  scrollTo: (options: { x: number; animated: boolean }) => void;
};

const App = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [cards, setCards] = useState<Card[]>([]);
  const scrollX = useRef(new Animated.Value(0)).current;
  const scrollViewRef = useRef<ScrollViewRef | null>(null);

  useEffect(() => {
    const fetchCards = async () => {
      try {
        const response = await fetch('https://j3zs8egtxg23.nityasha.com/storage/silder.json');
        const data = await response.json();
        if (Array.isArray(data)) {
          setCards(data as Card[]);
        }
      } catch (error) {
        console.error('Error fetching cards:', error);
      }
    };

    fetchCards();
  }, []);

  useEffect(() => {
    if (cards.length === 0) return;

    const goToNextCard = () => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = prevIndex < cards.length - 1 ? prevIndex + 1 : 0;
        scrollViewRef.current?.scrollTo({ x: width * nextIndex, animated: true });
        return nextIndex;
      });
    };

    const autoSlideInterval = setInterval(goToNextCard, 3888);
    return () => clearInterval(autoSlideInterval);
  }, [cards.length]);

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: () => false,
    onStartShouldSetPanResponder: () => false,
  });

  return (
    <View style={[tw`rounded-lg mt-2 border-2`, styles.container]} {...panResponder.panHandlers}>
      <Animated.ScrollView
        horizontal
        pagingEnabled
        ref={scrollViewRef}
        scrollEnabled={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
      >
        {cards.map((card) => {
          const Icon = IconComponents[card.icon];
          return (
            <LinearGradient
              key={card.id}
              style={[tw`px-2 rounded-lg justify-between items-center flex flex-row pr-10`, styles.card]}
              colors={['#189e7f1c', '#fff']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={tw`text-[#000] font-bold text-[15px]`}>{card.title}</Text>
              {Icon ? <Icon size={40} color={'#000'} /> : null}
            </LinearGradient>
          );
        })}
      </Animated.ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    width: width,
    height: 60,
  },
});

export default App;