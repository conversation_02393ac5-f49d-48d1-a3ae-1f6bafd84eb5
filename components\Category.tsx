import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity, ActivityIndicator, Text, ScrollView } from 'react-native';
import tw from 'twrnc'; // Ensure you're using Tailwind CSS with React Native
import Headingsixth from '@/components/ui/Headingsixth'; // Import your Headingsixth component

const Category = ({ navigation }) => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedConsultant, setSelectedConsultant] = useState(null);

    // Fetch consultants from API
    const fetchConsultants = async () => {
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/categories/get'); // Replace with your API URL
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchConsultants(); // Initial fetch
        const intervalId = setInterval(fetchConsultants, 9000); // Set interval for 30 seconds

        return () => clearInterval(intervalId); // Cleanup interval on component unmount
    }, []);

    if (error) {
        return (
            <View style={tw`flex items-center justify-center h-full`}>
                <Text style={tw`text-red-500`}>{error}</Text>
            </View>
        );
    }

    return (
        <View style={tw`flex items-center justify-center px-2 py-3`}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <TouchableOpacity
                    style={[tw`flex px-2 py-1 border-2 bg-[#000] rounded-[10px] mr-2`,]}>
                    <View>
                        <Text style={{ fontFamily: 'Helvetica_bold', color: '#fff' }}>
                            All
                        </Text>
                    </View>
                </TouchableOpacity>
                {consultants.map((consultant, index) => {
                    return (
                        <TouchableOpacity
                            key={index}
                            onPress={() => setSelectedConsultant(consultant.id)} // Set selected consultant ID
                            style={[
                                tw`flex px-2 py-1 border-2 border-[#D7D9DC] rounded-[10px] mr-2`,
                                selectedConsultant === consultant.id && tw`bg-[#FFDACC] border-[#000]`, // Change bg color if selected
                            ]}
                        >
                            <View>
                                <Text style={{ fontFamily: 'Helvetica_bold', color: '#39434F' }}>
                                    {consultant.name}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    );
                })}
            </ScrollView>
        </View>
    );
};

export default Category;
