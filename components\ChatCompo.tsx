import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc'; // Corrected Tailwind CSS import
import { useNavigation } from '@react-navigation/native';

const TopConsultants = ({ consultant }) => {
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [balance, setBalance] = useState(1000); // Example starting balance
  const [chatDuration, setChatDuration] = useState(5); // Duration in minutes
  const navigation = useNavigation();

  useEffect(() => {
    const fetchConsultants = async () => {
      try {
        const response = await fetch(`https://nityasha.vercel.app/api/v1/consultants/${consultant}`);
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        const data = await response.json();
        setConsultants(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchConsultants();

    // Cleanup (if needed) - this depends on whether you have any cleanup to do (like clearing intervals or subscriptions)
    // return () => { cleanup code };
  }, [consultant]); // Dependency array ensures useEffect runs only when consultant changes

  if (loading) {
    return <ActivityIndicator style={tw`m-4`} size="large" color="#0000ff" />;
  }

  if (error) {
    return <Text style={tw`m-4 text-red-500`}>Error: {error}</Text>;
  }

  return (
    <View>
      {consultants.map((consultant, index) => (
        <View key={index} style={tw`flex items-center py-3 px-1 mr-[5px] overflow-hidden`}>
          <Text style={tw`mt-2 text-base text-gray-700`}>{consultant.name}</Text>
        </View>
      ))}
    </View>
  );
};

export default TopConsultants;
