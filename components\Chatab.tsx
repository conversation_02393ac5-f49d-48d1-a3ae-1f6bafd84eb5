import React, { useEffect, useState, useCallback } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    ActivityIndicator,
    Text,
    Modal,
    TextInput,
    Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { Star, BadgeCheck } from 'lucide-react-native';
import PaymentPopup from './paymentpopup';
const TopConsultants = ({ consultant }) => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [balance, setBalance] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
    const [isNewUser, setIsNewUser] = useState(null);
    const navigation = useNavigation();
    const rechargeMinimumAmount = 1; // Set a minimum recharge amount
    const [showPaymentPopup, setShowPaymentPopup] = useState(false)
    const [Username, setUsername] = useState('')
    const [selectedRate, setSelectedRate] = useState(0);
    const [rechargeAmount, setRechargeAmount] = useState('');

    // Fetch consultants from the API
    const fetchConsultants = useCallback(async () => {
        setLoading(true);
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/consultants');
            if (!response.ok) throw new Error('Failed to fetch consultants.');
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, []);

    // Fetch user balance
    const fetchUserBalance = useCallback(async (userId) => {
        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
            if (!response.ok) throw new Error('Failed to fetch balance');
            const userData = await response.json();
            setBalance(parseFloat(userData.balance) || 0);
        } catch (err) {
            setError(err.message);
        }
    }, []);

    // Check login status and fetch consultants
    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (userSession) {
                    const userId = JSON.parse(userSession).userId;
                    await fetchUserBalance(userId);
                    const userStatus = await AsyncStorage.getItem('isNewUser');
                    setIsNewUser(userStatus === '0');
                } else {
                    navigation.navigate('Welcome');
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        checkLoginStatus();
        fetchConsultants();

        const intervalId = setInterval(fetchConsultants, 6000);
        return () => clearInterval(intervalId);
    }, [fetchConsultants, fetchUserBalance, navigation]);

    // Update user balance
    const updateBalance = async (newBalance) => {
        const userSession = await AsyncStorage.getItem('userSession');
        const userId = JSON.parse(userSession).userId;

        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ balance: newBalance }),
            });

            if (response.ok) {
                if (newBalance !== balance) {
                    setBalance(newBalance);
                }
            } else {
                const errorData = await response.json();
                Alert.alert(`Error updating balance: ${errorData.message || 'Unknown error'}`);
            }
        } catch (error) {
            Alert.alert("Error updating balance.");
        }
    };

    // Handle recharge action
    const handleRecharge = () => {
        const rechargeValue = parseFloat(rechargeAmount);
        if (!isNaN(rechargeValue) && rechargeValue >= rechargeMinimumAmount) {
            const newBalance = balance + rechargeValue;
            updateBalance(newBalance);
            setModalVisible(false);
            setRechargeAmount('');
        } else {
            Alert.alert("Please enter a valid amount to recharge.");
        }
    };

  // Handle Chat
  const handleChatNow = async (consultantId: string, perMinuteRate: number, categoryName: string) => {
    // Pehle recharge check karein
    if (balance === 0) {
      setSelectedRate(perMinuteRate);
      setShowPaymentPopup(true);
      return;
    }

    if (categoryName.toLowerCase() === "astrology" && balance < 3 * perMinuteRate) {
        setSelectedRate(perMinuteRate);
        setShowPaymentPopup(true);
        return;
      }
  
    const userSession = await AsyncStorage.getItem('userSession');
    if (!userSession) {
      setError('User session not found');
      return;
    }
  
    const userId = JSON.parse(userSession).userId;
  
    // Check karein agar user free chat ka use kar chuka hai
    const freeChatUsed = await AsyncStorage.getItem(`freeChat_${userId}`);
  
    if (!freeChatUsed) {
      setIsNewUser(false);
      await AsyncStorage.setItem(`freeChat_${userId}`, 'true');
      navigation.navigate('Chat', { consultantId, balance: '0', chatDuration: 3 });
      return;
    }
  
    // Balance check karein
    if (balance === 0) {
      setSelectedRate(perMinuteRate);
      setShowPaymentPopup(true);
    } else {
      const maxChatDuration = Math.floor(balance / perMinuteRate);
  
      if (maxChatDuration <= 0) {
        setSelectedRate(perMinuteRate);
        setShowPaymentPopup(true);
      } else {
        const totalCost = maxChatDuration * perMinuteRate;
        const chatDuration = maxChatDuration;
        const newBalance = balance - totalCost;
  
        if (newBalance >= 0) {
          await updateBalance(newBalance);
          navigation.navigate('Chat', { consultantId, balance: balance, chatDuration });
        } else {
          setSelectedRate(perMinuteRate);
          setShowPaymentPopup(true);
        }
      }
    }
  };

    if (error) {
        return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
    }

    return (
        <View>
            <View key={consultant.id} style={tw`w-full h-35 mt-2 flex-row flex justify-between rounded-[8px] py-3 border-2 border-gray-500 px-3 mr-[5px] overflow-hidden`}>
                <View style={tw`flex flex-row`}>
                    <View style={tw`flex flex-col items-center justify-center`}>
                        <TouchableOpacity >
                            <Image
                                source={{
                                    uri: consultant.pfp || `https://ui-avatars.com/api/?name=${consultant.username.slice(0, 1)}...`,
                                }}
                                style={tw`w-[75px] h-[75px] rounded-full border-2`}
                            />
                            {consultant.active === 1 && (
                    <View
                      style={tw.style(
                        `absolute top-0 right-0 p-2 rounded-full bg-green-500`
                      )}
                    ></View>
                  )}
                        </TouchableOpacity>
                        <View style={tw`flex flex-row mt-3`}>
                            <Star color={'orange'} size={14} />
                            <Star color={'orange'} size={14} />
                            <Star color={'orange'} size={14} />
                            <Star color={'orange'} size={14} />
                            <Star color={'orange'} size={14} />
                        </View>
                    </View>
                    <View style={tw`items-start justify-center gap-2 pl-2`}>
                        <Text style={tw`text-[15px] font-[#39434F] h-5 font-bold pl-2 -mt-1`}>{consultant.name}</Text>
                        <Text style={tw` text-[13px] text-[#6e6e6e] font-bold pl-2`}>{consultant.category_name}</Text>
                        <Text style={tw`text-[13px] text-[#6e6e6e] font-bold pl-2`}>Exp: {consultant.exp} Year</Text>
                        <Text style={tw`text-[15px] font-bold pl-2`}>₹{consultant.per_minute_rate}/min</Text>
                    </View>
                </View>
                <View style={tw`items-end justify-between`}>
                    <BadgeCheck color={'blue'} size={30} />
                    <TouchableOpacity disabled={consultant.active === 0}  style={tw`py-1.5 border-2 px-6 rounded-xl`} onPress={() => handleChatNow(consultant.id, consultant.per_minute_rate, consultant.category_name)}><Text style={tw`font-bold text-base`}>Chat</Text></TouchableOpacity>
                </View>
            </View>


            <PaymentPopup
                visible={showPaymentPopup}
                onClose={() => setShowPaymentPopup(false)}
                rate={selectedRate}
                username={Username}
            />
        </View>
    );
};


export default TopConsultants;
