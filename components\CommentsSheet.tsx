import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  Modal,
  Dimensions,
  StyleSheet,
  TextInput, KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { X, Send } from 'lucide-react-native';
import tw from 'twrnc';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@/utils/supabase';

const { height } = Dimensions.get('window');

const Comment = ({ comment, onReply, level = 0 }) => {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyText, setReplyText] = useState('');

  const handleReply = () => {
    if (replyText.trim()) {
      onReply({
        post_id: comment.post_id,
        content: replyText.trim(),
        parent_id: comment.id
      });
      setReplyText('');
      setShowReplyInput(false);
    }
  };

  return (
    <View style={tw`mb-4 ml-${level * 4}`}>
      <View style={tw`flex-row relative p-2 border-2 rounded-xl`}>
        <Image
          source={{ uri: comment.user_pfp }}
          style={tw`w-10 h-10 rounded-full mr-3`}
        />
        <View style={tw`flex-1`}>
          <Text style={tw`font-bold mb-1`}>
            {comment.username}{" "}
            {new Date(comment.created_at).toLocaleDateString()}
          </Text>
          <Text style={tw`text-gray-700`}>{comment.content}</Text>
        </View>
      </View>

      {showReplyInput && (
        <View style={tw`mt-2 flex-row items-center`}>
          <TextInput
            value={replyText}
            onChangeText={setReplyText}
            placeholder="Write a reply..."
            style={tw`flex-1 bg-gray-100 rounded-full px-4 py-2 mr-2`}
          />
        </View>
      )}

      {comment.replies?.map((reply) => (
        <Comment
          key={reply.id}
          comment={reply}
          onReply={onReply}
          level={level + 1}
        />
      ))}
    </View>
  );
};

const CommentsSheet = ({ visible, onClose, comments: initialComments, postId, onAddComment = () => {} }) => {
  const [newComment, setNewComment] = useState('');
  const [comments, setComments] = useState(initialComments);

  const organizeComments = (comments) => {
    const commentMap = {};
    const rootComments = [];

    // First, create a map of all comments
    comments.forEach(comment => {
      commentMap[comment.id] = { ...comment, replies: [] };
    });

    // Then, organize them into a tree structure
    comments.forEach(comment => {
      if (comment.parent_id) {
        // This is a reply - add it to its parent's replies array
        if (commentMap[comment.parent_id]) {
          commentMap[comment.parent_id].replies.push(commentMap[comment.id]);
        }
      } else {
        // This is a root comment
        rootComments.push(commentMap[comment.id]);
      }
    });

    // Sort root comments by creation date in descending order (newest first)
    rootComments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // Sort replies for each comment
    rootComments.forEach(comment => {
      if (comment.replies && comment.replies.length > 0) {
        comment.replies.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      }
    });

    return rootComments;
  };

  useEffect(() => {
    const subscription = supabase
      .channel('comments')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'comments', filter: `post_id=eq.${postId}` },
        async (payload) => {
          if (payload.eventType === 'INSERT') {
            try {
              const response = await fetch(
                `https://nityasha.vercel.app/api/v1/users/${payload.new.user_id}`
              );
              const userData = await response.json();

              const newComment = {
                ...payload.new,
                username: userData.username || 'Anonymous',
                user_pfp: userData.pfp || `https://ui-avatars.com/api/?name=${userData.username?.charAt(0) || 'A'}`,
              };

              setComments(prev => {
                const exists = prev.some(comment => comment.id === newComment.id);
                if (!exists) {
                  const updatedComments = [...prev, newComment];
                  return organizeComments(updatedComments);
                }
                return prev;
              });
            } catch (error) {
              console.error('Error fetching user data for new comment:', error);
            }
          } else if (payload.eventType === 'DELETE') {
            setComments(prev => {
              const updatedComments = prev.filter(comment => comment.id !== payload.old.id);
              return organizeComments(updatedComments);
            });
          } else if (payload.eventType === 'UPDATE') {
            setComments(prev => {
              const updatedComments = prev.map(comment => 
                comment.id === payload.new.id ? { ...comment, ...payload.new } : comment
              );
              return organizeComments(updatedComments);
            });
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [postId]);

  // Sync with initialComments when they change
  useEffect(() => {
    if (initialComments?.length > 0) {
      setComments(initialComments);
    }
  }, [initialComments]);

  useEffect(() => {
    setComments(initialComments);
  }, [initialComments]);

  const handleAddComment = async () => {
    const userSession = await AsyncStorage.getItem("userSession");
    if (!userSession) {
      Alert.alert('Error', 'Please log in to comment');
      return;
    }

    const parsedSession = JSON.parse(userSession);
    const userId = parsedSession?.userId || userSession;

    if (!newComment.trim() || !userId) {
      Alert.alert('Error', 'Please enter a comment and ensure you are logged in.');
      return;
    }

    try {
      const { data: userData } = await supabase
        .from('users')
        .select('username, pfp')
        .eq('id', userId)
        .single();

      const { data, error } = await supabase
        .from("comments")
        .insert([
          {
            post_id: postId,
            content: newComment.trim(),
            user_id: userId,
            created_at: new Date().toISOString(),
            parent_comment_id: null // Set parent_id to null for top-level comments
          }
        ])
        .select()
        .single();

      if (error) throw error;

      if (onAddComment) {
        onAddComment(data);
      }
      
      setNewComment('');
    } catch (error) {
      console.error('Error creating comment:', error);
      Alert.alert('Error', 'Failed to create comment');
    }
  };

  const handleReplyToComment = async (parentComment, replyContent) => {
    const userSession = await AsyncStorage.getItem("userSession");
    if (!userSession) {
      Alert.alert('Error', 'Please log in to reply');
      return;
    }

    const parsedSession = JSON.parse(userSession);
    const userId = parsedSession?.userId || userSession;

    try {
      const { data, error } = await supabase
        .from('comments')
        .insert([{
          post_id: postId,
          content: replyContent,
          user_id: userId,
          created_at: new Date().toISOString(),
          parent_comment_id: parentComment.id // Set parent_id to the parent comment's id
        }])
        .select()
        .single();

      if (error) throw error;

      if (onAddComment) {
        onAddComment(data);
      }
    } catch (error) {
      console.error('Error creating reply:', error);
      Alert.alert('Error', 'Failed to add reply');
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.modalContainer}
      >
        <View style={styles.sheetContainer}>
          <View style={tw`flex-row justify-between items-center p-4 border-b border-gray-200`}>
            <Text style={tw`text-lg font-bold`}>Comments</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color="black" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={tw`flex-1 p-4`}>
            {comments?.map((comment) => (
              <Comment
                key={comment.id}
                comment={comment}
                onReply={(replyData) => handleReplyToComment(comment, replyData.content)}
              />
            ))}
          </ScrollView>

          <View style={tw`p-4 border-t border-gray-200`}>
            <View style={tw`flex-row items-center`}>
              <TextInput
                value={newComment}
                onChangeText={setNewComment}
                placeholder="Add a comment..."
                style={tw`flex-1 bg-gray-100 rounded-full px-4 py-2 mr-2`}
                multiline
              />
              <TouchableOpacity
                onPress={handleAddComment}
                style={tw`bg-emerald-500 p-2 rounded-full`}
                disabled={!newComment.trim()}
              >
                <Send size={20} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sheetContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: height * 0.8,
    paddingBottom: Platform.OS === 'ios' ? 20 : 0,
  },
});

export default CommentsSheet;