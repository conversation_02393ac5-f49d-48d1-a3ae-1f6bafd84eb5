import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Image,
  TouchableOpacity,
  Text,
  ScrollView,
  StyleSheet,
  Animated,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useNavigation } from "@react-navigation/native";
import { useTheme } from "react-native-paper";
import { BlurView } from "expo-blur";
import { MessageSquareMore, Star } from "lucide-react-native";
import SkeletonLoader from "./ui/SkeletonLoader";
import PaymentPopup from "./paymentpopup";

const TopConsultants = () => {
  const theme = useTheme();
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [balance, setBalance] = useState(0);
  const [showPaymentPopup, setShowPaymentPopup] = useState(false);
  const [selectedRate, setSelectedRate] = useState(0);
  const [username, setUsername] = useState("");
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const navigation = useNavigation();
  const socketRef = useRef(null);

  // Fetch Consultants
  const fetchConsultants = async () => {
    setLoading(true);
    try {
      const response = await fetch("https://nityasha.vercel.app/api/v1/home/<USER>");
      if (!response.ok) throw new Error("Failed to fetch consultants.");
      const data = await response.json();
      setConsultants(data);
      
      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // WebSocket Connection for Balance
  const connectWebSocket = (userId) => {
    if (socketRef.current) {
      socketRef.current.close();
    }

    const socket = new WebSocket("wss://balance-app-api.nityasha.com");
    socketRef.current = socket;

    socket.onopen = () => {
      socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.error) {
          setError(data.error);
        } else if (data.balance !== undefined) {
          setBalance(parseFloat(data.balance) || 0);
        }
      } catch (err) {
        setError("Invalid data received");
      }
    };

    socket.onerror = (err) => {
      console.error("WebSocket Error:", err);
      setError("WebSocket connection failed");
    };

    socket.onclose = () => {
      console.log("WebSocket disconnected");
    };
  };

  // Check Login Status & Fetch Balance
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const userId = JSON.parse(userSession).userId;
          connectWebSocket(userId);
  
          // Balance update every 10s
          const balanceInterval = setInterval(() => {
            if (socketRef.current?.readyState === WebSocket.OPEN) {
              socketRef.current.send(JSON.stringify({ action: "GET_BALANCE", userId }));
            }
          }, 10000);
  
          return () => clearInterval(balanceInterval);
        } else {
          navigation.navigate("Welcome");
        }
      } catch (error) {
        console.error("Error checking login status:", error);
      }
    };
  
    checkLoginStatus();
    fetchConsultants();
  
    return () => {
      if (socketRef.current) socketRef.current.close();
    };
  }, [navigation]);
  
  const handleChatNow = async (consultantId, perMinuteRate, categoryName) => {
    const userSession = await AsyncStorage.getItem("userSession");
    if (!userSession) {
      setError("User session not found");
      return;
    }
  
    const userId = JSON.parse(userSession).userId;
    const freeChatUsed = await AsyncStorage.getItem(`freeChat_${userId}`);
  
    // Astrology category with sufficient balance
    if (String(categoryName).toLowerCase().trim() === "astrology") {
      if (balance >= 3 * perMinuteRate) { 
        console.log("Balance is sufficient, navigating to ServicePage...");
        navigation.navigate("ServicePage", { consultantId });
        return;
      }
    }
  
    // Free Chat Check
    if (!freeChatUsed) {
      await AsyncStorage.setItem(`freeChat_${userId}`, "true");
      console.log("Navigating to Chat (free chat)");
      navigation.navigate("Chat", { consultantId, balance: "0", chatDuration: 3 });
      return;
    }
  
    const chatDuration = Math.max(3, Math.min(Math.floor(balance / perMinuteRate), 30));
  
    if (balance < 3 * perMinuteRate) {
      console.log("Opening payment popup");
      setSelectedRate(perMinuteRate);
      setShowPaymentPopup(true);
    } else {
      console.log("Navigating to Chat");
      navigation.navigate("Chat", { consultantId, balance, chatDuration });
    }
  };

  if (loading) return (
    <View style={styles.skeletonContainer}>
      <SkeletonLoader borderRadius={16} height={220} width={150} />
      <SkeletonLoader borderRadius={16} height={220} width={150} />
      <SkeletonLoader borderRadius={16} height={220} width={150} />
    </View>
  );

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.cardsContainer}>
          {consultants.length === 0 ? (
            <Text style={styles.noConsultantsText}>No consultants available.</Text>
          ) : (
            consultants.map((consultant) => (
              <TouchableOpacity
                key={consultant.id}
                disabled={consultant.active === 0}
                onPress={() => {
                  setUsername(consultant.name);
                }}
                style={[styles.card, { backgroundColor: theme.colors.surface }]}
              >
                <View style={styles.cardContent}>
                  <View style={styles.profileSection}>
                    <View style={styles.profileImageContainer}>
                      <Image source={{ uri: consultant.pfp }} style={styles.profileImage} />
                      {consultant.active === 1 && (
                        <View style={styles.activeIndicator} />
                      )}
                    </View>
                    
                    <View style={styles.nameContainer}>
                      <Text style={[styles.name, { fontFamily: 'GoogleSans-Medium' }]}>
                        {consultant.name.length > 12 
                          ? `${consultant.name.substring(0, 12)}...` 
                          : consultant.name}
                      </Text>
                      <View style={styles.categoryBadge}>
                        <Text style={styles.categoryText}>
                          {consultant.category_name}
                        </Text>
                      </View>
                    </View>
                  </View>
                  
                  <View style={styles.ratingContainer}>
                    <Star size={14} color={theme.colors.primary} fill={theme.colors.primary} />
                    <Text style={styles.ratingText}>
                      {(Math.random() * 1 + 4).toFixed(1)} • {consultant.exp}+ yrs
                    </Text>
                  </View>
                  
                  <View style={styles.priceContainer}>
                    <Text style={[styles.price, { color: theme.colors.primary, fontFamily: 'GoogleSans-Bold' }]}>
                      ₹{consultant.per_minute_rate}
                    </Text>
                    <Text style={styles.perMinute}>/min</Text>
                  </View>
                  
                  <TouchableOpacity
                    style={[styles.chatButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => {
                      setUsername(consultant.name);
                      handleChatNow(consultant.id, consultant.per_minute_rate, consultant.category_name);
                    }}
                  >
                    <MessageSquareMore size={14} color={theme.colors.onPrimary} />
                    <Text style={[styles.chatButtonText, { color: theme.colors.onPrimary }]}>
                      Chat Now
                    </Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>
      <PaymentPopup 
        visible={showPaymentPopup} 
        onClose={() => setShowPaymentPopup(false)} 
        rate={selectedRate} 
        username={username} 
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  cardsContainer: {
    flexDirection: 'row',
    paddingRight: 16,
  },
  card: {
    width: 150,
    height: 225,
    marginRight: 12,
    borderRadius: 16,
    marginBottom: 3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  cardContent: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 12,
    height: '100%',
    justifyContent: 'space-between',
  },
  profileSection: {
    alignItems: 'center',
    marginBottom: 8,
  },
  profileImageContainer: {
    position: 'relative',
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    marginBottom: 8,
    borderWidth: 2,
    borderColor: '#FFF8E1',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  activeIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
  },
  nameContainer: {
    alignItems: 'center',
    width: '100%',
  },
  name: {
    fontSize: 14,
    textAlign: 'center',
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: '#F3F0E8',
    marginTop: 4,
  },
  categoryText: {
    fontSize: 10,
    fontWeight: '500',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  ratingText: {
    fontSize: 12,
    marginLeft: 4,
    color: '#666',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  perMinute: {
    fontSize: 10,
    color: '#666',
    marginLeft: 2,
  },
  chatButton: {
    height: 36,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  chatButtonText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  noConsultantsText: {
    textAlign: 'center',
    width: '100%',
    padding: 20,
  },
});

export default TopConsultants;