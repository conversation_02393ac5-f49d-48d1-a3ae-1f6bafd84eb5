import {
  View,
  Text,
  Image,
  Pressable,
  TouchableOpacity,
  Alert
} from "react-native";
import React, { useEffect, useState, useCallback } from "react";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring
} from "react-native-reanimated";
import tw from "twrnc";
import { supabase } from "@/utils/supabase";
import AsyncStorage from "@react-native-async-storage/async-storage";

const sortCommunitiesByMembers = (communities) => {
  return [...communities].sort(
    (a, b) => (b.member_count || 0) - (a.member_count || 0)
  );
};

const CommunityCardItem = React.memo(({ community, isFlipped, onFlip }) => {
  const rotateYValue = useSharedValue(isFlipped ? 180 : 0);

  const animatedFlipStyle = useAnimatedStyle(() => ({
    transform: [{ rotateY: `${rotateYValue.value}deg` }]
  }));

  
  
  

  const joinCommunity = async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (!userSession) {
        Alert.alert("Please login to join communities");
        return;
      }
      const userId = JSON.parse(userSession)?.userId || userSession;

      const { error } = await supabase
        .from("community_members")
        .insert([{ community_id: community.id, user_id: userId, role: "member" }]);

      if (error) {
        Alert.alert(error.code === "23505" ? "You are already a member" : "Failed to join");
      } else {
        Alert.alert("Successfully joined the community!");
        onFlip();
      }
    } catch (error) {
      console.error("Error:", error);
      Alert.alert("An error occurred");
    }
  };

  return (
    <Pressable onPress={onFlip}>
      <Animated.View
        style={[
          tw`bg-[#FFFBF1] h-40 w-36 rounded-3xl overflow-hidden p-2 flex items-center`,
          animatedFlipStyle
        ]}
      >
        {!isFlipped ? (
          <>
            <Image
              style={tw`h-15 w-full rounded-[10px]`}
              source={{
                uri: community.banner || "https://via.placeholder.com/150"
              }}
            />
            <View
              style={tw`bg-black -mt-8 h-[68px] w-[68px] rounded-full border-[4px] border-[#FFFBF1] relative`}
            >
              <Image
                style={tw`w-full h-full rounded-full`}
                source={{
                  uri: community.pfp || "https://via.placeholder.com/150"
                }}
                resizeMode="cover"
              />
            </View>
            <Text style={tw`text-base font-bold`} numberOfLines={1}>
              {community.name}
            </Text>
            <Text style={tw`text-xs text-gray-500 font-regular`}>
              {community.member_count} members
            </Text>
          </>
        ) : (
          <TouchableOpacity
            style={tw`flex-1 justify-center items-center`}
            onPress={joinCommunity}
          >
            <Text style={tw`text-lg mb-2 font-bold rotate-180`}>
              Join Now
            </Text>
          </TouchableOpacity>
        )}
      </Animated.View>
    </Pressable>
  );
});

export default function CommunityCard() {
  const [communities, setCommunities] = useState(null);
  const [flippedId, setFlippedId] = useState(null);

  useEffect(() => {
    let isMounted = true;
    const fetchCommunities = async () => {
      try {
        const { data, error } = await supabase
          .from("communities")
          .select("*, community_members(count)");

        if (error) throw error;

        const processedCommunities = data.map((community) => ({
          ...community,
          member_count: community.community_members[0]?.count || 0,
          banner:
            community.banner ||
            `https://api.dicebear.com/7.x/shapes/png?seed=${encodeURIComponent(
              community.name
            )}`
        }));

        if (isMounted) setCommunities(sortCommunitiesByMembers(processedCommunities));
      } catch (error) {
        console.error("Error fetching communities:", error);
      }
    };

    if (!communities) fetchCommunities();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleFlip = useCallback(
    (id) => {
      setFlippedId(flippedId === id ? null : id);
    },
    [flippedId]
  );

  return (
    <View style={tw`flex-row flex-wrap gap-3`}>
      {communities?.map((community) => (
        <CommunityCardItem
          key={community.id}
          community={community}
          isFlipped={flippedId === community.id}
          onFlip={() => handleFlip(community.id)}
        />
      ))}
    </View>
  );
}
