import {
  View,
  Text,
  TouchableOpacity,
  Share,
  Image,
  FlatList,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import React, { useState, useEffect, useCallback } from "react";
import { Pin, Share2, ThumbsDown, ThumbsUp } from "lucide-react-native";
import tw from "twrnc";
import { supabase } from "@/utils/supabase";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CommentsSheet from "@/components/CommentsSheet";
import TopConsultants from "../Consultant";
import { MotiScrollView } from "moti";
import SkeletonLoader from "@/components/ui/SkeletonLoader";

const PostSkeleton = () => {
  return (
    <View style={tw`mb-4 p-4 bg-white rounded-lg shadow-md`}>
      <View style={tw`flex-row items-center justify-between mb-4`}>
        <View style={tw`flex-row items-center`}>
          <SkeletonLoader width={40} height={40} borderRadius={20} />
          <View style={tw`ml-2`}>
            <SkeletonLoader width={120} height={20} borderRadius={4} />
            <View style={tw`mt-1`}>
              <SkeletonLoader width={80} height={16} borderRadius={4} />
            </View>
          </View>
        </View>
        <SkeletonLoader width={24} height={24} borderRadius={4} />
      </View>
      <View style={tw`mb-4`}>
        <SkeletonLoader width={'100%'} height={60} borderRadius={4} />
      </View>
      <View style={tw`flex-row justify-between items-center`}>
        <View style={tw`flex-row gap-2`}>
          <SkeletonLoader width={100} height={40} borderRadius={8} />
          <SkeletonLoader width={40} height={40} borderRadius={8} />
        </View>
        <SkeletonLoader width={80} height={30} borderRadius={4} />
      </View>
    </View>
  );
};

export default function Posts() {
  const [posts, setPosts] = useState([]);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [expandedPosts, setExpandedPosts] = useState({});
  const [showComments, setShowComments] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState(null);
  const [selectedComments, setSelectedComments] = useState([]);

  const characterLimit = 150;

  useEffect(() => {
    const initializePosts = async () => {
      setLoading(true);
      try {
        await fetchUserData();
        await fetchPosts();
      } finally {
        setLoading(false);
      }
    };

    initializePosts();

    const subscription = supabase
      .channel("posts")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "posts" },
        async () => {
          // Don't set loading true for real-time updates
          await fetchPosts();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []); // Empty dependency array since we only want this to run once on mount

  const fetchUserData = async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (userSession) {
        const parsedSession = JSON.parse(userSession);
        setUserId(parsedSession?.userId || userSession);
      }
    } catch (error) {
      console.error("Error getting user session:", error);
    }
  };

  const fetchPosts = async () => {
    try {
      if (!userId) {
        // For new users, fetch random recent posts
        const { data: randomPosts } = await supabase
          .from("posts")
          .select(`*, likes:likes(count), comments:comments(*)`)
          .order('created_at', { ascending: false })
          .limit(50);

        return processAndSetPosts(randomPosts);
      }

      // Get user's liked posts to find preferred authors
      const { data: userLikes } = await supabase
        .from("likes")
        .select('post_id, posts(user_id)')
        .eq('user_id', userId);

      if (!userLikes?.length) {
        // If user has no likes yet, show random posts
        const { data: randomPosts } = await supabase
          .from("posts")
          .select(`*, likes:likes(count), comments:comments(*)`)
          .order('created_at', { ascending: false })
          .limit(50);

        return processAndSetPosts(randomPosts);
      }

      // Get unique author IDs from liked posts
      const preferredAuthors = [...new Set(userLikes.map(like => like.posts.user_id))];

      // Fetch posts with preference to liked authors
      const { data: recommendedPosts } = await supabase
        .from("posts")
        .select(`*, likes:likes(count), comments:comments(*)`)
        .in('user_id', preferredAuthors)
        .order('created_at', { ascending: false })
        .limit(30);

      // Also fetch some random posts for variety
      const { data: randomPosts } = await supabase
        .from("posts")
        .select(`*, likes:likes(count), comments:comments(*)`)
        .not('user_id', 'in', preferredAuthors)
        .order('created_at', { ascending: false })
        .limit(20);

      // Combine and shuffle posts
      const allPosts = [...(recommendedPosts || []), ...(randomPosts || [])]
        .sort(() => Math.random() - 0.5)
        .slice(0, 10);

      return processAndSetPosts(allPosts);
    } catch (error) {
      console.error("Error fetching posts:", error);
    }
  };

  const processAndSetPosts = async (posts) => {
    const postsWithDetails = await Promise.all(
      (posts || []).map(async (post) => {
        try {
          const response = await fetch(
            `https://nityasha.vercel.app/api/v1/users/${post.user_id}`
          );
          const userData = await response.json();

          return {
            ...post,
            author_name: userData.username || "Anonymous",
            author_title: userData.title || "User",
            pfp: userData.pfp || "https://ui-avatars.com/api/?name=Anonymous"
          };
        } catch (error) {
          console.error("Error fetching user data:", error);
          return { ...post, author_name: "Anonymous", author_title: "User" };
        }
      })
    );
    setPosts(postsWithDetails);
  };

  const handleLike = async (postId) => {
    if (!userId) return;
    try {
      const { data: existingLike } = await supabase
        .from("likes")
        .select()
        .eq("post_id", postId)
        .eq("user_id", userId)
        .single();
      if (existingLike) {
        await supabase
          .from("likes")
          .delete()
          .eq("post_id", postId)
          .eq("user_id", userId);
      } else {
        await supabase
          .from("likes")
          .insert([{ post_id: postId, user_id: userId }]);
      }
    } catch (error) {
      console.error("Error handling like:", error);
    }
  };

  const handleShare = async (post) => {
    try {
      await Share.share({
        message: post.content,
        title: `Post by ${post.author_name}`
      });
    } catch (error) {
      console.error("Error sharing post:", error);
    }
  };

  const handleOpenComments = (postId, comments) => {
    setSelectedPostId(postId);
    setSelectedComments(comments || []);
    setShowComments(true);
  };

  const renderItem = useCallback(({ item }) => {
    if (item.isAd) return ;

    return (
      <View key={item.id} style={tw`mb-4 p-4 bg-white rounded-lg shadow-md`}>
        {/* Post content */}
        <View style={tw`flex-row items-center justify-between`}>
          <View style={tw`flex-row items-center mb-2`}>
            <Image
              source={{ uri: `https://api.dicebear.com/7.x/initials/png?seed=${item.author_name}` }}
              style={tw`w-10 h-10 rounded-full mr-2`}
            />
            <View>
              <Text style={tw`font-bold`}>
                {item.author_name || "Anonymous"}
              </Text>
              <Text style={tw`text-gray-500 text-sm`}>
                {item.author_title || "User"}
              </Text>
            </View>
          </View>
          <Pin size={24} color={"#000"} />
        </View>
        <Text>
          {item.content.length > characterLimit && !expandedPosts[item.id]
            ? `${item.content.slice(0, characterLimit)}...`
            : item.content}
        </Text>
        {item.content.length > characterLimit && (
          <TouchableOpacity
            onPress={() =>
              setExpandedPosts((prev) => ({
                ...prev,
                [item.id]: !prev[item.id]
              }))
            }
            style={tw`text-blue-800`}
          >
            <Text>{expandedPosts[item.id] ? "Show less" : "Show more"}</Text>
          </TouchableOpacity>
        )}
        <View style={tw`flex-row mt-2 justify-between`}>
          <View style={tw`flex-row gap-2`}>
            <View
              style={tw`bg-[#F3F0E8] rounded-lg p-2 flex-row items-center justify-center gap-2`}
            >
              <TouchableOpacity onPress={() => handleLike(item.id)}>
                <ThumbsUp color={item.likes?.count > 0 ? "#189e7f" : "#000"} />
              </TouchableOpacity>
              <View style={tw`h-[20px] bg-black w-[2px]`} />
              <TouchableOpacity>
                <ThumbsDown color={"#000"} />
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              onPress={() => handleShare(item)}
              style={tw`p-2 rounded-lg bg-[#F3F0E8]`}
            >
              <Share2 size={24} color={"#000"} />
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            onPress={() => handleOpenComments(item.id, item.comments)}
            style={tw`ml-4 flex-row items-center justify-center `}
          >
            <Text>
              Comments
            </Text>
              <Text style={tw`rounded-sm ml-2`}>{item.comments?.length}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }, [expandedPosts]);

  if (loading) {
    return (
      <View style={tw`flex-1 mt-5`}>
        <PostSkeleton />
        <PostSkeleton />
        <PostSkeleton />
        <PostSkeleton />
      </View>
    );
  }

  return (
    <View style={tw`flex-1 mt-5`}>
      <FlatList
      data={posts}
      keyExtractor={(item, index) => item.isAd ? `ad-${index}` : item.id.toString()}
      renderItem={renderItem}
      showsVerticalScrollIndicator={false}
    />
      <CommentsSheet
        visible={showComments}
        onClose={() => setShowComments(false)}
        comments={selectedComments}
        postId={selectedPostId}
      />
    </View>
  );
}



