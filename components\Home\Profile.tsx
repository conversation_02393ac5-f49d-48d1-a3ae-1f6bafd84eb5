import { TouchableOpacity } from 'react-native';
import React from 'react';
import { useNavigation } from '@react-navigation/native';
import { EllipsisVertical } from 'lucide-react-native';

export default function Profile() {
  const navigation = useNavigation();

  return (
    <TouchableOpacity onPress={() => navigation.navigate('Profile')}>
      <EllipsisVertical size={24} color={"black"} />
    </TouchableOpacity>
  );
}
