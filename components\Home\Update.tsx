"use client"

import { useEffect, useState, useCallback } from "react"
import { View, TouchableOpacity, Alert, ScrollView, RefreshControl } from "react-native"
import { Avatar, Button, Card, IconButton, Text, Chip } from "react-native-paper"
import { Bell, Clock, Star, ChevronRight } from "lucide-react-native"
import { supabase } from "@/utils/supabase"
import AsyncStorage from "@react-native-async-storage/async-storage"
import tw from "twrnc"
import { useNavigation } from "@react-navigation/native"
import LinkText from "@/components/LinkText"

// Skeleton loader component with animation
const UpdateSkeleton = () => {
  const [opacity, setOpacity] = useState(0.5)

  useEffect(() => {
    const interval = setInterval(() => {
      setOpacity((prev) => (prev === 0.5 ? 0.8 : 0.5))
    }, 800)

    return () => clearInterval(interval)
  }, [])

  return (
    <View style={tw`mx-4 my-2 rounded-xl overflow-hidden`}>
      <View style={[tw`-gray-200 p-4 rounded-t-xl flex-row items-center`, { opacity }]}>
        <View style={tw`h-12 w-12 rounded-full -gray-300 mr-3`} />
        <View>
          <View style={tw`h-5 w-40 -gray-300 rounded-md mb-2`} />
          <View style={tw`h-4 w-24 -gray-300 rounded-md`} />
        </View>
      </View>
      <View style={[tw`-gray-100 p-4`, { opacity }]}>
        <View style={tw`h-4 w-full -gray-200 rounded-md mb-2`} />
        <View style={tw`h-4 w-full -gray-200 rounded-md mb-2`} />
        <View style={tw`h-4 w-3/4 -gray-200 rounded-md mb-4`} />
        <View style={tw`h-10 w-full -gray-200 rounded-md`} />
      </View>
    </View>
  )
}



const Updates = () => {
  const navigation = useNavigation()
  const [messages, setMessages] = useState([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [userId, setUserId] = useState(null)
  const [joinedCommunities, setJoinedCommunities] = useState([])
  const [communityMessages, setCommunityMessages] = useState({})
  const [savedUpdates, setSavedUpdates] = useState({})

  const fetchMessages = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from("community_messages")
        .select("*")
        .order("created_at", { ascending: false })

      if (error) throw error

      const messagesWithUserDetails = await Promise.all(
        data.map(async (message) => {
          try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${message.user_id}`)
            const userData = await response.json()

            // Get community details
            const { data: communityData } = await supabase
              .from("communities")
              .select("name, pfp")
              .eq("id", message.community_id)
              .single()

            return {
              ...message,
              username: userData.username || "Anonymous",
              pfp: userData.pfp || `https://ui-avatars.com/api/?name=${userData.username?.charAt(0) || "A"}`,
              communityName: communityData?.name || "Community",
              communityImage:
                communityData?.pfp || `https://ui-avatars.com/api/?name=${communityData?.name?.charAt(0) || "C"}`,
            }
          } catch (error) {
            console.error("Error fetching user data:", error)
            return {
              ...message,
              username: "Anonymous",
              pfp: `https://ui-avatars.com/api/?name=A`,
              communityName: "Community",
              communityImage: `https://ui-avatars.com/api/?name=C`,
            }
          }
        }),
      )

      setMessages(messagesWithUserDetails)
    } catch (error) {
      console.error("Error fetching messages:", error)
      Alert.alert("Error", "Failed to load messages")
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchJoinedCommunities = useCallback(async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession")
      if (!userSession) return

      const parsedSession = JSON.parse(userSession)
      const userId = parsedSession?.userId || userSession

      const { data: memberData, error: memberError } = await supabase
        .from("community_members")
        .select("community_id")
        .eq("user_id", userId)

      if (memberError) throw memberError

      if (memberData && memberData.length > 0) {
        const communityIds = memberData.map((item) => item.community_id)

        const { data: communities, error: commError } = await supabase
          .from("communities")
          .select("id, name, pfp")
          .in("id", communityIds)

        if (commError) throw commError

        const formattedCommunities = communities.map((comm) => ({
          id: comm.id,
          name: comm.name,
          image: comm.pfp || `https://ui-avatars.com/api/?name=${comm.name.slice(0, 1)}`,
        }))

        setJoinedCommunities(formattedCommunities)
      }
    } catch (error) {
      console.error("Error fetching joined communities:", error)
    }
  }, [])

  const fetchCommunityMessages = useCallback(async () => {
    const { data, error } = await supabase.from("community_messages").select("community_id, content")

    if (!error && data) {
      const messageCount = data.reduce((acc, msg) => {
        if (msg.content.length >= 150) {
          acc[msg.community_id] = (acc[msg.community_id] || 0) + 1
        }
        return acc
      }, {})
      setCommunityMessages(messageCount)
    }
  }, [])

  const onRefresh = useCallback(async () => {
    setRefreshing(true)
    await Promise.all([fetchMessages(), fetchJoinedCommunities(), fetchCommunityMessages()])
    setRefreshing(false)
  }, [fetchMessages, fetchJoinedCommunities, fetchCommunityMessages])

  useEffect(() => {
    const fetchUserData = async () => {
      const userSession = await AsyncStorage.getItem("userSession")
      if (userSession) {
        const parsedSession = JSON.parse(userSession)
        const currentUserId = parsedSession?.userId || userSession
        setUserId(currentUserId)
      }
    }

    const fetchSavedUpdates = async () => {
      try {
        const saved = await AsyncStorage.getItem("savedUpdates")
        if (saved) {
          setSavedUpdates(JSON.parse(saved))
        }
      } catch (error) {
        console.error("Error fetching saved updates:", error)
      }
    }

    fetchUserData()
    fetchMessages()
    fetchJoinedCommunities()
    fetchCommunityMessages()
    fetchSavedUpdates()

    // Subscribe to messages changes
    const messagesSubscription = supabase
      .channel("public:community_messages")
      .on("postgres_changes", { event: "*", schema: "public", table: "community_messages" }, () => {
        fetchMessages()
      })
      .subscribe()

    return () => {
      messagesSubscription.unsubscribe()
    }
  }, [fetchMessages, fetchJoinedCommunities, fetchCommunityMessages])

  const toggleSaveUpdate = async (messageId) => {
    try {
      const newSavedUpdates = { ...savedUpdates }
      newSavedUpdates[messageId] = !savedUpdates[messageId]

      setSavedUpdates(newSavedUpdates)
      await AsyncStorage.setItem("savedUpdates", JSON.stringify(newSavedUpdates))

      Alert.alert(
        savedUpdates[messageId] ? "Update Removed" : "Update Saved",
        savedUpdates[messageId] ? "Update removed from saved items" : "Update saved for later",
      )
    } catch (error) {
      console.error("Error saving update:", error)
    }
  }

  const renderHeader = () => (
    <View style={tw`px-4 py-3 -[#F7F4E7]`}>
      <Text style={tw`text-2xl font-bold text-[#3D3A2E]`}>Updates</Text>
      <Text style={tw`text-sm text-[#767062] mt-1`}>
        Stay informed with the latest announcements from your communities
      </Text>


    </View>
  )

  const renderEmptyState = () => (
    <View style={tw`flex-1 items-center justify-center p-8 mt-8`}>
      <Bell size={48} color="#767062" style={tw`mb-4 opacity-50`} />
      <Text style={tw`text-lg text-[#3D3A2E] text-center font-medium mb-2`}>No updates yet</Text>
      <Text style={tw`text-base text-[#767062] text-center mb-6`}>
        It seems there are no updates today. Check back later or join more communities!
      </Text>
      <Button
        mode="contained"
        style={tw`w-full`}
        buttonColor="#EAE5D0"
        textColor="#3D3A2E"
        onPress={() => navigation.navigate("Communities")}
      >
        Explore Communities
      </Button>
    </View>
  )

  return (
    <View style={tw`flex-1 -[#FFFBF1]`}>
      {renderHeader()}

      <ScrollView style={tw`flex-1`} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}>
        {loading ? (
          <>
            <UpdateSkeleton />
            <UpdateSkeleton />
            <UpdateSkeleton />
          </>
        ) : messages.length === 0 ? (
          renderEmptyState()
        ) : (
          messages
            .filter((msg) => msg.content.length >= 150)
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
            .map((message) => (
              <Card
                key={message.id}
                style={tw`mx-4 my-2 -white rounded-xl overflow-hidden border border-[#EAE5D0]`}
                elevation={2}
              >
                <Card.Title
                  title={message.communityName}
                  left={(props) => (
                    <Avatar.Image {...props} source={{ uri: message.communityImage }} style={tw`-[#F7F4E7]`} />
                  )}
                  right={(props) => (
                    <IconButton
                      {...props}
                      icon={savedUpdates[message.id] ? "star" : "star-outline"}
                      iconColor={savedUpdates[message.id] ? "#F59E0B" : "#767062"}
                      style={tw`-[#F7F4E7] mr-2`}
                      onPress={() => toggleSaveUpdate(message.id)}
                    />
                  )}
                  titleStyle={tw`font-bold text-[#3D3A2E]`}
                  subtitleStyle={tw`text-[#767062]`}
                />

                <Card.Content style={tw`px-4 py-2`}>
                  <LinkText text={message.content} style={tw`text-[#3D3A2E]`} numberOfLines={5} />

                </Card.Content>

                <View style={tw`px-4 py-3 -[#F7F4E7] justify-between items-center w-full flex-row`}>
                  <View style={tw`flex-row items-center mr-20`}>
                    <Avatar.Image
                      size={24}
                      source={{ uri: message.pfp ? message.pfp : `https://api.dicebear.com/7.x/initials/png?seed=${message.username}` }}
                      style={tw`mr-2`}
                    />
                    <Text style={tw`text-sm text-[#3D3A2E]`}>
                      {message.username.length > 10 ? message.username.slice(0, 10) + "..." : message.username}
                    </Text>
                  </View>

                  <Button
                    mode="contained"
                    compact
                    style={tw`rounded-full`}
                    buttonColor="#EAE5D0"
                    textColor="#3D3A2E"
                    icon={() => <ChevronRight size={16} color="#3D3A2E" />}
                    contentStyle={tw`flex-row-reverse`}
                    onPress={() => {
                      navigation.navigate("CommunityView", {
                        communityId: message.community_id,
                      })
                    }}
                  >
                    View Community
                  </Button>
                </View>
              </Card>
            ))
        )}

        <View style={tw`h-4`} />
      </ScrollView>
    </View>
  )
}

export default Updates

