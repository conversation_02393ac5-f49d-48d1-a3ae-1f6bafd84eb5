import { View, Text, Image, ActivityIndicator, FlatList } from "react-native";
import tw from "twrnc";
import { useState } from "react";

interface User {
  id: number;
  pfp?: string;
  name: string;
  email: string;
  number: string;
}

interface UserCardProps {
    users: User[];
}

const DEFAULT_AVATAR = 'https://www.gravatar.com/avatar/default?d=mp';

const UserCard = ({ users = [] }: UserCardProps) => {
  const [loadingStates, setLoadingStates] = useState<{[key: number]: boolean}>({});
  const [imageErrors, setImageErrors] = useState<{[key: number]: boolean}>({});

  const renderUser = ({ item: user }: { item: User }) => (
    <View style={tw`bg-white p-4 rounded-lg shadow-md mb-3 flex-row items-center`}>
      <Image 
        source={{ 
          uri: imageErrors[user.id] ? DEFAULT_AVATAR : (user.pfp || DEFAULT_AVATAR) 
        }}
        style={tw`w-12 h-12 rounded-full mr-3`}
        onError={() => setImageErrors(prev => ({ ...prev, [user.id]: true }))}
        onLoadStart={() => setLoadingStates(prev => ({ ...prev, [user.id]: true }))}
        onLoadEnd={() => setLoadingStates(prev => ({ ...prev, [user.id]: false }))}
      />
      {loadingStates[user.id] && (
        <ActivityIndicator 
          style={tw`absolute left-4`}
          size="small"
        />
      )}
      <View style={tw`flex-1`}>
        <Text style={tw`text-lg font-bold`}>{user.name}</Text>
        <Text style={tw`text-sm text-gray-600`}>{user.email}</Text>
        <Text style={tw`text-sm text-gray-500`}>{user.number}</Text>
      </View>
    </View>
  );

  return (
    <FlatList
      data={users}
      renderItem={renderUser}
      keyExtractor={(user) => user.id.toString()}
    />
  );
};

export default UserCard;
