import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getLastVisitedScreen } from '@/utils/navigationState';

/**
 * A component that displays the last visited screen
 * This is for debugging purposes only
 */
const LastScreenIndicator = () => {
  const [lastScreen, setLastScreen] = useState<string | null>(null);

  useEffect(() => {
    const checkLastScreen = async () => {
      const screenData = await getLastVisitedScreen();
      if (screenData) {
        setLastScreen(screenData.name);
      }
    };

    checkLastScreen();
  }, []);

  if (!lastScreen) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.text}>Last screen: {lastScreen}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 100,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 5,
    borderRadius: 5,
    zIndex: 9999,
  },
  text: {
    color: 'white',
    fontSize: 10,
  },
});

export default LastScreenIndicator;
