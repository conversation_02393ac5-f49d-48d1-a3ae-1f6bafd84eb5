import React, { useEffect, useState } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    Text,
    ScrollView,
    Alert,
    Modal,
    TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { BlurView } from "expo-blur";

const TopConsultants = () => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [balance, setBalance] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
    const [rechargeAmount, setRechargeAmount] = useState('');
    const [isNewUser, setIsNewUser] = useState(null);
    const navigation = useNavigation();
    const rechargeMinimumAmount = 1; // Set a minimum recharge amount

    const fetchConsultants = async () => {
        setLoading(true);
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/consultants');
            if (!response.ok) throw new Error('Failed to fetch consultants.');
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
            Alert.alert("Error", err.message); // Show error to the user
        } finally {
            setLoading(false);
        }
    };

    const fetchUserBalance = async (userId) => {
        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
            if (!response.ok) throw new Error('Failed to fetch balance');
            const userData = await response.json();
            setBalance(parseFloat(userData.balance) || 0);
        } catch (err) {
            setError(err.message);
            Alert.alert("Error", err.message); // Show error to the user
        }
    };

    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (userSession) {
                    const userId = JSON.parse(userSession).userId;
                    await fetchUserBalance(userId);
                    const userStatus = await AsyncStorage.getItem('isNewUser');
                    setIsNewUser(userStatus === '0');
                } else {
                    navigation.navigate('Welcome');
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        const fetchData = async () => {
            await checkLoginStatus();
            await fetchConsultants();
        };

        fetchData();

        const intervalId = setInterval(async () => {
            await fetchConsultants();
            const userSession = await AsyncStorage.getItem('userSession');
            const userId = JSON.parse(userSession).userId;
            await fetchUserBalance(userId);
        }, 6000); // 6 seconds interval

        return () => clearInterval(intervalId);
    }, [navigation]);

    const updateBalance = async (newBalance) => {
        const userSession = await AsyncStorage.getItem('userSession');
        const userId = JSON.parse(userSession).userId;

        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ balance: newBalance }),
            });

            if (response.ok) {
                Alert.alert(`Balance updated. Your new balance is ₹${newBalance}.`);
                setBalance(newBalance);
            } else {
                const errorData = await response.json();
                Alert.alert(`Error updating balance: ${errorData.message || 'Unknown error'}`);
            }
        } catch (error) {
            Alert.alert("Error updating balance.");
        }
    };

    const handleRecharge = () => {
        const rechargeValue = parseFloat(rechargeAmount);
        if (!isNaN(rechargeValue) && rechargeValue >= rechargeMinimumAmount) {
            const newBalance = balance + rechargeValue;
            updateBalance(newBalance);
            setModalVisible(false);
            setRechargeAmount('');
        } else {
            Alert.alert("Please enter a valid amount to recharge.");
        }
    };

    const handleChatNow = async (consultantId, perMinuteRate) => {
        const maxChatDuration = Math.floor(balance / perMinuteRate); // Calculate maximum chat duration
        const freeChatDuration = isNewUser ? 5 : 0; // 5 free minutes for new users


        if (isNewUser && freeChatDuration > 0) {
            Alert.alert(`Enjoy your 5 minutes of free chat!`);
            await AsyncStorage.setItem('isNewUser', '1'); // Mark the user as old after the free chat
            navigation.navigate('Chat', { consultantId, balance, chatDuration: freeChatDuration });
        } else if (maxChatDuration <= 0) {
            navigation.navigate('Balance') // Show insufficient balance modal
        } else {
            const totalCost = maxChatDuration * perMinuteRate;
            const chatDuration = maxChatDuration;

            Alert.alert(
                "Confirm Chat",
                `You are about to start a chat for ₹${totalCost} for ${chatDuration} minute(s). Do you want to proceed?`,
                [
                    {
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "OK",
                        onPress: async () => {
                            const newBalance = balance - totalCost; // Deduct total cost from balance
                            if (newBalance < 0) {
                                Alert.alert("Insufficient balance for this chat duration.");
                            } else {
                                await updateBalance(newBalance); // Update the balance
                                navigation.navigate('Welcome', { consultantId, balance: newBalance, chatDuration }); // Navigate to chat
                            }
                        }
                    }
                ]
            );
        }
    };

    if (loading) {
        return <Text style={tw`text-center`}>Loading...</Text>; // Show loading state
    }

    if (error) {
        return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
    }

    return (
        <View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={tw`flex-row flex-wrap`}>
                    {consultants.length === 0 ? (
                        <Text style={tw`text-center w-full`}>No consultants available.</Text>
                    ) : (
                        consultants.map((consultant) => (
                            <TouchableOpacity key={consultant.id} onPress={navigation.navigate('Welcome')} style={tw`flex items-center justify-center mr-3 relative w-[190px]`}>
                                <View style={tw`w-[190px] h-[115px] rounded-[8px] relative`}>
                                    <View style={tw`flex items-center justify-center absolute px-1 py-0.5 top-2 z-10 left-2 bg-white rounded-[6px]`}>
                                        <Text style={[tw`text-[10px] font-bold text-[#000]`, { fontFamily: 'Helvetica_bold' }]}>₹{consultant.per_minute_rate}/min</Text>
                                    </View>
                                    <Image source={{ uri: consultant.thumnel }} style={tw`w-full h-full rounded-[8px]`} />
                                </View>
                                <View style={tw`flex items-center justify-between flex-row w-full px-1 h-[2rem]`}>
                                    <Text style={[tw`mt-1 text-[16px] text-[#39434F]`, { fontFamily: 'Helvetica_bold' }]}>{consultant.name}</Text>
                                    <TouchableOpacity
                                        onPress={() => handleChatNow(consultant.id, consultant.per_minute_rate)}
                                        style={tw`mt-2 ${consultant.isChatOn === 1 ? 'bg-red-600' : 'bg-[#000]'} flex items-center justify-center p-1 px-1.5 rounded-full`}
                                        disabled={consultant.isChatOn === 1}
                                    >
                                        <Text style={[tw`text-white font-bold text-[10px]`, { fontFamily: 'Helvetica_bold' }]}>{consultant.isChatOn === 1 ? 'Waiting' : 'Chat Now'}</Text>
                                    </TouchableOpacity>
                                </View>
                            </TouchableOpacity>
                        ))
                    )}
                </View>
            </ScrollView>

            <Modal visible={modalVisible} animationType="slide" transparent={true}>
                <BlurView experimentalBlurMethod="dimezisBlurView"
                    intensity={50}
                    tint="light"
                    style={{
                        height: "21%",
                        width: "100%",
                        position: "absolute",
                        bottom: 0,
                        elevation: 8,
                    }}>
                    <View style={tw`flex-1 justify-center items-center absolute bottom-0 w-full`}>
                        <View style={tw`p-6 rounded-lg w-full`}>
                            <Text style={[tw`text-lg font-semibold mb-4 w-full text-center`, { fontFamily: 'Helvetica_bold' }]}>Your Balance is ₹{balance.toFixed(2)}</Text>
                            <TextInput
                                style={[tw`border-2 p-2 px-3 rounded-xl font-bold`, { fontFamily: 'Helvetica_bold' }]}
                                placeholder="Enter recharge amount"
                                keyboardType="numeric"
                                value={rechargeAmount}
                                onChangeText={setRechargeAmount}
                            />
                            <View style={tw`flex flex-row gap-2 w-full mt-3`}>
                                <TouchableOpacity onPress={handleRecharge} style={tw`w-[50%] flex bg-black px-5 py-2  rounded-lg items-center justify-center`}>
                                    <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>Recharge</Text>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => setModalVisible(false)} style={tw`w-[50%] flex bg-black px-5 py-2 rounded-lg items-center justify-center`}>
                                    <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>Cancel</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </BlurView>
            </Modal>
        </View>
    );
};

export default TopConsultants;
