import React, { useEffect, useState } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    Text,
    ScrollView,
    Alert,
    Modal,
    TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import Headingsixth from '@/components/ui/Headingsixth';
import { useNavigation } from '@react-navigation/native';
import { BlurView } from "expo-blur";

const ShortsCard = () => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [balance, setBalance] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
    const [rechargeAmount, setRechargeAmount] = useState('');
    const [isNewUser, setIsNewUser] = useState(null);
    const navigation = useNavigation();
    const rechargeMinimumAmount = 1; // Set a minimum recharge amount
    const reloadInterval = 5000; // 5 seconds interval for auto-reload

    const fetchConsultants = async () => {
        setLoading(true);
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/consultants');
            if (!response.ok) throw new Error('Failed to fetch consultants.');
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const fetchUserBalance = async () => {
        try {
            const userSession = await AsyncStorage.getItem('userSession');
            const userId = JSON.parse(userSession).userId;
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
            if (!response.ok) throw new Error('Failed to fetch balance');
            const userData = await response.json();
            setBalance(parseFloat(userData.balance) || 0);
        } catch (err) {
            setError(err.message);
        }
    };

    useEffect(() => {
        // Fetch data initially
        fetchConsultants();
        fetchUserBalance();

        // Set up interval to auto-reload consultants and balance
        const intervalId = setInterval(() => {
            fetchConsultants();
            fetchUserBalance();
        }, reloadInterval);

        // Clear the interval when the component unmounts
        return () => clearInterval(intervalId);
    }, []);

    const updateBalance = async (newBalance) => {
        const userSession = await AsyncStorage.getItem('userSession');
        const userId = JSON.parse(userSession).userId;

        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ balance: newBalance }),
            });

            if (response.ok) {
                if (newBalance !== balance) {
                    Alert.alert(`Balance updated. Your new balance is ₹${newBalance}.`);
                    setBalance(newBalance);
                }
            } else {
                const errorData = await response.json();
                Alert.alert(`Error updating balance: ${errorData.message || 'Unknown error'}`);
            }
        } catch (error) {
            Alert.alert("Error updating balance.");
        }
    };

    const handleRecharge = () => {
        const rechargeValue = parseFloat(rechargeAmount);
        if (!isNaN(rechargeValue) && rechargeValue >= rechargeMinimumAmount) {
            const newBalance = balance + rechargeValue;
            updateBalance(newBalance);
            setModalVisible(false);
            setRechargeAmount('');
        } else {
            Alert.alert("Please enter a valid amount to recharge.");
        }
    };

    const handleChatNow = async (consultantId, perMinuteRate) => {
        const maxChatDuration = Math.floor(balance / perMinuteRate);
        const freeChatDuration = isNewUser ? 5 : 0;

        if (isNewUser && freeChatDuration > 0) {
            Alert.alert(`Enjoy your 5 minutes of free chat!`);
            await AsyncStorage.setItem('isNewUser', '1');
            navigation.navigate('Chat', { consultantId, balance, chatDuration: freeChatDuration });
        } else if (maxChatDuration <= 0) {
            navigation.navigate('Balance')
        } else {
            const totalCost = maxChatDuration * perMinuteRate;
            const chatDuration = maxChatDuration;

            Alert.alert(
                "Confirm Chat",
                `You are about to start a chat for ₹${totalCost} for ${chatDuration} minute(s). Do you want to proceed?`,
                [
                    {
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "OK",
                        onPress: async () => {
                            const newBalance = balance - totalCost;
                            if (newBalance < 0) {
                                Alert.alert("Insufficient balance for this chat duration.");
                            } else {
                                await updateBalance(newBalance);
                                navigation.navigate('Chat', { consultantId, balance: newBalance, chatDuration });
                            }
                        }
                    }
                ]
            );
        }
    };

    if (error) {
        return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
    }

    return (
        <View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={tw`flex-row flex-wrap`}>
                    {consultants.length === 0 ? (
                        <Text style={tw`text-center w-full`}>No consultants available.</Text>
                    ) : (
                        consultants.map((consultant) => (
                            <View key={consultant.id}>
                                <TouchableOpacity
                                    onPress={() => handleChatNow(consultant.id, consultant.per_minute_rate)}
                                    style={tw`flex consultants-center relative justify-center w-[130px] mr-1 h-[195px] rounded-[10px] overflow-hidden border-2`}
                                >
                                    <Image source={{ uri: consultant.thumnel }} style={tw`w-[130px] h-[195px]`} />
                                    <View style={tw`flex absolute bottom-1 left-1 rounded-[5px] bg-[#000] px-2 py-0.5`}>
                                        <Text style={tw`text-[10px] text-white font-bold`}>Live</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        ))
                    )}
                </View>
            </ScrollView>

            <Modal visible={modalVisible} animationType="slide" transparent={true}>
                <BlurView experimentalBlurMethod="dimezisBlurView"
                    intensity={50}
                    tint="light"
                    style={{
                        height: "21%",
                        width: "100%",
                        position: "absolute",
                        bottom: 0,
                        elevation: 8,
                    }}
                >
                    <View style={tw`flex-1 justify-center consultants-center absolute bottom-0 w-full`}>
                        <View style={tw`p-6 rounded-lg w-full`}>
                            <Text style={[tw`text-lg font-semibold mb-4 w-full text-center`, { fontFamily: 'Helvetica_bold' }]}>Your Balance is ₹{balance.toFixed(2)}</Text>
                            <TextInput
                                style={[tw`border-2 p-2 px-3 rounded-xl font-bold`, { fontFamily: 'Helvetica_bold' }]}
                                placeholder="Enter recharge amount"
                                keyboardType="numeric"
                                value={rechargeAmount}
                                onChangeText={setRechargeAmount}
                            />
                            <View style={tw`flex flex-row gap-2 w-full mt-3`}>
                                <TouchableOpacity onPress={handleRecharge} style={tw`w-[50%] flex bg-black px-5 py-2  rounded-lg consultants-center justify-center`}>
                                    <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>Recharge</Text>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => setModalVisible(false)} style={tw`w-[50%] flex bg-black px-5 py-2 rounded-lg consultants-center justify-center`}>
                                    <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>Cancel</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </BlurView>
            </Modal>
        </View>
    );
};

export default ShortsCard;
