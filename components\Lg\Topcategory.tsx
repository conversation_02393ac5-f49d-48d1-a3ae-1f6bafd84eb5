import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, Text, ScrollView, TouchableOpacity } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import Headingsixth from '@/components/ui/Headingsixth';
import { useNavigation } from '@react-navigation/native';
import { Repeat2 , ShieldCheck , CodeXml , DollarSign ,HeartPulse  } from 'lucide-react-native'; // Import necessary icons

// Create a mapping of icon names to their components
const iconMapping = {
    Repeat2: Repeat2,
    ShieldCheck: ShieldCheck,
    CodeXml: CodeXml,
    DollarSign: DollarSign,
    HeartPulse: HeartPulse,
};

const TopConsultants = () => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const navigation = useNavigation();
    const reloadIntervalMs = 60000; // Auto reload every 60 seconds

    // Fetch consultants from API
    const fetchConsultants = async () => {
        setLoading(true);
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/categories/get');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (!userSession) {
                    navigation.navigate('Welcome');
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        checkLoginStatus(); // Call the function to check login status
        fetchConsultants(); // Initial fetch of consultants

        // Set up auto-reload for consultants
        const interval = setInterval(() => {
            fetchConsultants();
        }, reloadIntervalMs);

        return () => clearInterval(interval); // Cleanup interval on component unmount
    }, [navigation]);

    const handleChatNow = (id) => {
        navigation.navigate('Category', { id: id });
    };

    // Error handling UI
    if (error) {
        return (
            <View style={tw`flex-1 justify-center items-center`}>
                <Text style={tw`text-red-500 text-center`}>{error}</Text>
                <TouchableOpacity onPress={fetchConsultants} style={tw`mt-4 p-2 bg-blue-500 rounded`}>
                    <Text style={tw`text-white`}>Retry</Text>
                </TouchableOpacity>
            </View>
        );
    }

    // Loading state
    if (loading) {
        return (
            <View style={tw`flex-1 justify-center items-center`}>
                <ActivityIndicator size="large" color="#0000ff" />
            </View>
        );
    }

    return (
        <View>
            <Headingsixth title="Top Consultants" />
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={tw`flex-row flex-wrap`}>
                    {consultants.map((consultant) => {
                        const IconComponent = iconMapping[consultant.icon]; 
                        return (
                            <TouchableOpacity
                                key={consultant.id}
                                style={tw`flex items-center py-3 px-1 mr-[5px] overflow-hidden`}
                                onPress={() => handleChatNow(consultant.id)}
                            >
                                <View style={tw`w-[68px] h-[68px] rounded-full bg-[#000] flex-row items-center justify-center`}>
                                    {IconComponent ? <IconComponent size={30} color="#fff" /> : null} 
                                </View>
                                <Text style={tw`mt-2 text-[14px] text-[#39434F] h-5`}>
                                    {consultant.name ? consultant.name : 'Unknown Consultant'}
                                </Text>
                            </TouchableOpacity>
                        );
                    })}
                </View>
            </ScrollView>
        </View>
    );
};

export default TopConsultants;
