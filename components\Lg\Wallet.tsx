import { View, Text, TouchableOpacity, ActivityIndicator, Modal, Alert } from 'react-native';
import React, { useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import tw from 'twrnc';
import { useFonts } from 'expo-font';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Wallet({ navigation }) {
  const [modalVisible, setModalVisible] = useState(false);
  const [userData, setUserData] = useState(null);
  const [loaded] = useFonts({
    'Helvetica_bold': require('@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf'),
  });

  const openLogoutModal = () => {
    navigation.navigate('Balance')
  };

  const closeLogoutModal = () => {
    setModalVisible(false);
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem('userSession');
      await AsyncStorage.removeItem('userId');
      navigation.navigate('Welcome'); // Redirect to login screen
    } catch (error) {
      console.error('Error during logout:', error);
    }
    closeLogoutModal();
  };

  const fetchUserData = async () => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (userSession) {
        const loggedUserId = JSON.parse(userSession).userId;
        const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${loggedUserId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
        
        const data = await response.json();
        setUserData(data);
      } else {
        console.error('No user session found');
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      Alert.alert('Error', 'Could not load user data. Please try again.');
    }
  };

  useEffect(() => {
    fetchUserData();
    const interval = setInterval(fetchUserData, 3000); // Set interval for 30 seconds

    return () => clearInterval(interval);
  }, []);

  if (!loaded) {
    return <View style={tw`flex justify-center`}><ActivityIndicator size="small" color="#fff" /></View>; 
  }

  if (!userData) {
    return <View style={tw`flex justify-center`}><ActivityIndicator size="small" color="#fff" /></View>;
  }

  return (

      <TouchableOpacity style={tw`flex px-1.5 items-center justify-center py-0.5 rounded-[5px] border-2 flex-row`} onPress={openLogoutModal}>
        <Text style={[tw`font-bold text-[10px]`, { fontFamily: 'Helvetica_bold' }]}>
          ₹ {userData.balance}
        </Text>
      </TouchableOpacity>
  );
}
