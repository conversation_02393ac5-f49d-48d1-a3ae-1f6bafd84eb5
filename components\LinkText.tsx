import React, { useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import tw from "twrnc";

interface LinkTextProps {
  text: string;
}

const LinkText = ({ text }: LinkTextProps) => {
  const navigation = useNavigation();
  const [expanded, setExpanded] = useState(false);

  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const shouldTruncate = text.length > 250;
  const displayText =
    expanded || !shouldTruncate ? text : text.slice(0, 190) + "...";

  const parts = displayText.split(urlRegex);

  return (
    <View style={tw`w-full`}>
      <Text>
        {parts.map((part, index) => {
          if (part.match(urlRegex)) {
            return (
              <TouchableOpacity
                key={index}
                onPress={() =>
                  navigation.navigate("WebViewScreen", { url: part })
                }
              >
                <Text style={tw`text-blue-500 underline`}>{part}</Text>
              </TouchableOpacity>
            );
          }
          return (
            <Text key={index}>
              {part}
            
            </Text>
          );
        })}
      </Text>

    </View>
  );
};

export default LinkText;
