import React, { useEffect, useState } from "react";
import {
  View,
  Image,
  TouchableOpacity,
  Text,
  ScrollView,
  Modal,
  TextInput,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import tw from "twrnc";
import Headingsixth from "@/components/ui/Headingsixth";
import { useNavigation } from "@react-navigation/native";
import SkeletonLoader from "./ui/SkeletonLoader";
import { BlurView } from "expo-blur";
import PaymentPopup from "./paymentpopup";

const TopConsultants = () => {
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [balance, setBalance] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [Username, setUsername] = useState("");
  const [isNewUser, setIsNewUser] = useState<boolean | null>(null);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const navigation = useNavigation();
  const rechargeMinimumAmount = 1;
  const [rechargeAmount, setRechargeAmount] = useState("");
  const [showPaymentPopup, setShowPaymentPopup] = useState(false);
  const [selectedRate, setSelectedRate] = useState(0);

  // Fetch Consultants
  const fetchConsultants = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        "https://nityasha.vercel.app/api/v1/consultants"
      );
      if (!response.ok) throw new Error("Failed to fetch consultants.");
      const data = await response.json();

      setConsultants(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setHasLoadedOnce(true);
    }
  };

  // Fetch User Balance
  const fetchUserBalance = (userId: string) => {
    try {
      const socket = new WebSocket("wss://balance-app-api.nityasha.com");
  
      socket.onopen = () => {
        socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
      };
  
      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.error) {
            setError(data.error);
          } else if (data.balance !== undefined) {
            setBalance(parseFloat(data.balance) || 0);
          }
        } catch (err) {
          setError("Invalid data received");
        }
      };
  
      socket.onerror = (err) => {
        console.error("WebSocket Error:", err);
        setError("WebSocket connection failed");
      };
  
      socket.onclose = () => console.log("WebSocket disconnected");
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("An unknown error occurred");
      }
    }
  };
  

  // Check Login Status
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const userId = JSON.parse(userSession).userId;
          await fetchUserBalance(userId);

          // Check if the user is new by reading AsyncStorage
          const userStatus = await AsyncStorage.getItem("isNewUser");
          setIsNewUser(userStatus === "0");

          // If the user has used the free chat, mark them as not new
          const freeChatUsed = await AsyncStorage.getItem(
            `freeChast_${userId}`
          );
          if (freeChatUsed) {
            setIsNewUser(false); // Set to false after free chat is used
          }
        } else {
          navigation.navigate("Welcome");
        }
      } catch (error) {
        console.error("Error checking login status:", error);
      }
    };

    checkLoginStatus();
    fetchConsultants();

    const intervalId = setInterval(() => {
      fetchConsultants();
      const userSession = AsyncStorage.getItem("userSession").then(
        (session) => {
          const userId = JSON.parse(session).userId;
          fetchUserBalance(userId);
        }
      );
    }, 6000);

    return () => clearInterval(intervalId);
  }, [navigation]);

  // Update Balance Function
  const updateBalance = async (newBalance) => {
    const userSession = await AsyncStorage.getItem("userSession");
    const userId = JSON.parse(userSession).userId;

    try {
      const response = await fetch(
        `https://nityasha.vercel.app/api/v1/users/${userId}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ balance: newBalance }),
        }
      );

      if (response.ok) {
        setBalance(newBalance); // Update state immediately
      } else {
        const errorData = await response.json();
        setError(
          `Error updating balance: ${errorData.message || "Unknown error"}`
        );
      }
    } catch (error) {
      setError("Error updating balance.");
    }
  };

  // Handle Chat
  const handleChatNow = async (
    consultantId: string,
    perMinuteRate: number,
    categoryName: string
  ) => {
    // Phir astrology check karein
    if (categoryName.toLowerCase() === "astrology") {
      // If balance is less than minimum required for astrology service
      if (balance < 3 * perMinuteRate) {
        setSelectedRate(perMinuteRate);
        setShowPaymentPopup(true);
        return;
      }
      navigation.navigate("ServicePage", { consultantId });
      return;
    }

    const userSession = await AsyncStorage.getItem("userSession");
    if (!userSession) {
      setError("User session not found");
      return;
    }

    const userId = JSON.parse(userSession).userId;

    // Check karein agar user free chat ka use kar chuka hai
    const freeChatUsed = await AsyncStorage.getItem(`freeChat_${userId}`);

    if (!freeChatUsed) {
      setIsNewUser(false);
      await AsyncStorage.setItem(`freeChat_${userId}`, "true");
      navigation.navigate("Chat", {
        consultantId,
        balance: "0",
        chatDuration: 3,
      });
      return;
    }

    // Minimum 3 minutes ka balance check karein
    const minimumBalanceRequired = 3 * perMinuteRate;

    if (balance < minimumBalanceRequired) {
      setSelectedRate(perMinuteRate);
      setShowPaymentPopup(true);
      return;
    }

    // Calculate maximum possible chat duration
    const maxChatDuration = Math.floor(balance / perMinuteRate);

    // Ensure minimum 3 minutes chat
    const chatDuration = Math.max(3, Math.min(maxChatDuration, 30)); // Max 30 minutes for safety

    const totalCost = chatDuration * perMinuteRate;
    const newBalance = balance - totalCost;

    if (newBalance >= 0) {
      await updateBalance(newBalance);
      navigation.navigate("Chat", {
        consultantId,
        balance: newBalance,
        chatDuration,
      });
    } else {
      setSelectedRate(perMinuteRate);
      setShowPaymentPopup(true);
    }
  };

  if (error) {
    return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
  }

  if (loading && !hasLoadedOnce) {
    return (
      <View style={tw`flex-1 justify-center items-center flex-row`}>
        <SkeletonLoader width={130} height={200} borderRadius={16} />
        <SkeletonLoader width={130} height={200} borderRadius={16} />
        <SkeletonLoader width={130} height={200} borderRadius={16} />
      </View>
    );
  }

  return (
    <View style={tw`mt-2 mb-2`}>
      <View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={tw`flex-row flex-wrap`}>
            {consultants.length === 0 ? (
              <Text style={tw`text-center w-full`}>
                No consultants available.
              </Text>
            ) : (
              consultants.map((consultant) => (
                <TouchableOpacity
                  key={consultant.id}
                  disabled={consultant.active === 0} // Disable the button when active is 0
                  onPress={() => {
                    setUsername(consultant.name);
                    handleChatNow(
                      consultant.id,
                      consultant.per_minute_rate,
                      consultant.category_name
                    );
                  }}
                  style={tw`flex items-center rounded-[8px] overflow-hidden justify-center mr-3 relative h-[200px] w-[125px]`}
                >
                  <View style={tw`w-[130px] h-[200px] rounded-[8px] relative`}>
                    <Image
                      source={{ uri: consultant.thumnel }}
                      style={tw`w-full h-full rounded-[8px]`}
                    />
                  </View>
                  <BlurView
                    intensity={100}
                    style={tw`w-full h-full absolute flex items-center justify-center bg-black bg-opacity-20`}
                  >
                  <View>
                  <Image
                      source={{ uri: consultant.pfp }}
                      style={tw`w-[70px] h-[70px] rounded-full`}
                    />
                  </View>
                    <Text
                      style={[
                        tw`mt-2 font-medium text-center text-white`,
                        { fontFamily: "Helvetica_bold" },
                      ]}
                    >
                      {consultant.name}
                    </Text>
                  </BlurView>
                </TouchableOpacity>
              ))
            )}
          </View>
        </ScrollView>
      </View>
      <PaymentPopup
        visible={showPaymentPopup}
        onClose={() => setShowPaymentPopup(false)}
        rate={selectedRate}
        username={Username}
      />
    </View>
  );
};

export default TopConsultants;
