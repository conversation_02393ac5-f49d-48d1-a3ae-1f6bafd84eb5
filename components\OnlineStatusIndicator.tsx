import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useUserStatus } from '@/hooks/useUserStatus';

interface OnlineStatusIndicatorProps {
  userId: string;
  showText?: boolean;
  size?: number;
  textStyle?: object;
}

const OnlineStatusIndicator: React.FC<OnlineStatusIndicatorProps> = ({
  userId,
  showText = false,
  size = 8,
  textStyle = {},
}) => {
  const { isOnline, lastSeen, loading, error } = useUserStatus(userId);

  // Format last seen time
  const formatLastSeen = (timestamp: string | null) => {
    if (!timestamp) return 'Unknown';

    const lastSeenDate = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - lastSeenDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hr ago`;
    if (diffDays === 1) return 'Yesterday';
    return `${diffDays} days ago`;
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="small" color="#0000ff" />
        {showText && <Text style={[styles.text, textStyle]}>Loading...</Text>}
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View
          style={[
            styles.indicator,
            { backgroundColor: '#FF5252' },
            { width: size, height: size, borderRadius: size / 2 }
          ]}
        />
        {showText && <Text style={[styles.text, textStyle]}>Error</Text>}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.indicator,
          { backgroundColor: isOnline ? '#4CAF50' : '#9E9E9E' },
          { width: size, height: size, borderRadius: size / 2 }
        ]}
      />
      {showText && (
        <Text style={[styles.text, textStyle]}>
          {isOnline ? 'Online' : `Last seen ${formatLastSeen(lastSeen)}`}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  text: {
    fontSize: 12,
    color: '#757575',
  },
});

export default OnlineStatusIndicator;
