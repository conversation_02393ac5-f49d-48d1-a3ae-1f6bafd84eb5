import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, TextInput, ScrollView } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import OnlineStatusIndicator from './OnlineStatusIndicator';
import { useMultipleUserStatus } from '@/hooks/useMultipleUserStatus';

const OnlineStatusTest = () => {
  const [userId, setUserId] = useState<string>('');
  const [userIds, setUserIds] = useState<string[]>([]);
  const [newUserId, setNewUserId] = useState<string>('');
  const { userStatuses, loading, error } = useMultipleUserStatus(userIds);

  // Get current user ID from AsyncStorage
  useEffect(() => {
    const getCurrentUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          if (parsedSession?.userId) {
            setUserId(parsedSession.userId);
            // Add current user to the list of users to monitor
            setUserIds(prev => [...prev, parsedSession.userId]);
          }
        }
      } catch (error) {
        console.error('Error getting user session:', error);
      }
    };

    getCurrentUserId();
  }, []);

  const addUserId = () => {
    if (newUserId && !userIds.includes(newUserId)) {
      setUserIds(prev => [...prev, newUserId]);
      setNewUserId('');
    }
  };

  const removeUserId = (id: string) => {
    setUserIds(prev => prev.filter(userId => userId !== id));
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Online Status Test</Text>
      
      {userId ? (
        <View style={styles.currentUserContainer}>
          <Text style={styles.subtitle}>Current User (You)</Text>
          <View style={styles.userRow}>
            <Text style={styles.userId}>User ID: {userId}</Text>
            <OnlineStatusIndicator userId={userId} showText size={12} />
          </View>
        </View>
      ) : (
        <Text style={styles.noUser}>No current user found</Text>
      )}

      <View style={styles.addUserContainer}>
        <Text style={styles.subtitle}>Monitor Other Users</Text>
        <View style={styles.inputRow}>
          <TextInput
            style={styles.input}
            value={newUserId}
            onChangeText={setNewUserId}
            placeholder="Enter user ID"
            keyboardType="numeric"
          />
          <Button title="Add" onPress={addUserId} />
        </View>
      </View>

      <View style={styles.usersContainer}>
        <Text style={styles.subtitle}>Monitored Users</Text>
        {loading && <Text style={styles.loading}>Loading user statuses...</Text>}
        {error && <Text style={styles.error}>Error: {error}</Text>}
        
        {userIds.length === 0 ? (
          <Text style={styles.noUsers}>No users to monitor</Text>
        ) : (
          userIds.map(id => (
            <View key={id} style={styles.userRow}>
              <Text style={styles.userId}>User ID: {id}</Text>
              <View style={styles.statusContainer}>
                <OnlineStatusIndicator userId={id} showText size={12} />
                <Button title="Remove" onPress={() => removeUserId(id)} color="#FF5252" />
              </View>
            </View>
          ))
        )}
      </View>

      <View style={styles.debugContainer}>
        <Text style={styles.subtitle}>Debug Info</Text>
        <Text style={styles.debugText}>User Statuses:</Text>
        <Text style={styles.debugJson}>
          {JSON.stringify(userStatuses, null, 2)}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  currentUserContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
  },
  addUserContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
  },
  usersContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
  },
  debugContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
  },
  userRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  userId: {
    fontSize: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 8,
    marginRight: 8,
  },
  noUser: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  noUsers: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
  },
  loading: {
    fontSize: 16,
    color: '#2196F3',
    marginVertical: 8,
  },
  error: {
    fontSize: 16,
    color: '#F44336',
    marginVertical: 8,
  },
  debugText: {
    fontSize: 14,
    marginBottom: 4,
  },
  debugJson: {
    fontSize: 12,
    fontFamily: 'monospace',
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderRadius: 4,
  },
});

export default OnlineStatusTest;
