import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Modal,
  Animated,
  StyleSheet,
  Share,
  Dimensions,
  Platform,
  ActivityIndicator,
} from 'react-native';
// Import TouchableOpacity from gesture-handler to avoid duplicate registrations
import { TouchableOpacity as GHTouchableOpacity } from 'react-native-gesture-handler';
import { X, Share2 } from 'lucide-react-native';
import tw from 'twrnc';
import { useFonts } from 'expo-font';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ReferFriendPopupProps {
  visible: boolean;
  onClose: () => void;
}

const ReferFriendPopup = ({ visible, onClose }: ReferFriendPopupProps) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const [referralLink, setReferralLink] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [loaded] = useFonts({
    Helvetica_bold: require('@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf'),
    Satoshi: require('@/assets/fonts/Satoshi-Medium.otf'),
  });

  // Generate referral link when popup becomes visible
  useEffect(() => {
    if (visible && !referralLink && !isLoading) {
      generateReferralLink();
    }

    // Handle animations
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, slideAnim, opacityAnim, referralLink, isLoading]);

  // Function to generate a referral link
  const generateReferralLink = async () => {
    try {
      setIsLoading(true);

      // Get user session to extract user ID
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) {
        // If no user session, use a default link
        setReferralLink('https://nityasha.com/sign-up');
        setIsLoading(false);
        return;
      }

      // Parse user session to get user ID
      const userData = JSON.parse(userSession);
      const userId = userData.userId || userData;

      // Call the API to generate a referral link
      const response = await fetch(`https://status.api.nityasha.com/user/${userId}/generate-referral-link`);
      const data = await response.json();

      if (data.success && data.referralLink) {
        setReferralLink(data.referralLink);
      } else {
        // Fallback to default link if API fails
        setReferralLink('https://nityasha.com/sign-up');
      }
    } catch (error) {
      console.error('Error generating referral link:', error);
      // Fallback to default link if there's an error
      setReferralLink('https://nityasha.com/sign-up');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      // Use the generated referral link if available, otherwise use default
      const shareLink = referralLink || 'https://nityasha.com/sign-up';

      await Share.share({
        message: `Join me on Nityasha! Download the app and get exclusive benefits. ${shareLink}`,
        title: 'Refer a Friend to Nityasha',
      });
      onClose();
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  if (!loaded) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      onRequestClose={onClose}
      animationType="none"
    >
      <Animated.View
        style={[
          styles.modalOverlay,
          {
            opacity: opacityAnim,
          }
        ]}
      >
        <View
          style={styles.dismissArea}
          onTouchEnd={onClose}
        />

        <Animated.View
          style={[
            styles.modalContent,
            {
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [300, 0],
                  }),
                },
              ],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={[tw`text-lg`, { fontFamily: 'Helvetica_bold' }]}>
                Refer a Friend
              </Text>

              <Text style={[tw`text-sm text-gray-600 mt-1`, { fontFamily: 'Satoshi' }]}>
                Share Nityasha with your friends and family
              </Text>
            </View>

            <GHTouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color="#000" />
            </GHTouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={[tw`text-base mb-2`, { fontFamily: 'Satoshi' }]}>
              Invite your friends to join Nityasha and enjoy:
            </Text>

            <View style={tw`mb-4`}>
              <Text style={[tw`text-sm mb-1`, { fontFamily: 'Satoshi' }]}>• Expert consultations</Text>
              <Text style={[tw`text-sm mb-1`, { fontFamily: 'Satoshi' }]}>• Community discussions</Text>
              <Text style={[tw`text-sm mb-1`, { fontFamily: 'Satoshi' }]}>• Personalized advice</Text>
              <Text style={[tw`text-sm mb-1 font-bold`, { fontFamily: 'Satoshi' }]}>• Earn rewards when friends join!</Text>
            </View>
          </View>

          {/* Share Button */}
          <GHTouchableOpacity
            onPress={handleShare}
            disabled={isLoading}
            style={[
              styles.shareButton,
              isLoading && { opacity: 0.7 }
            ]}
          >
            {isLoading ? (
              <>
                <ActivityIndicator size="small" color="#fff" style={tw`mr-2`} />
                <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>
                  Generating Link...
                </Text>
              </>
            ) : (
              <>
                <Share2 size={20} color="#fff" style={tw`mr-2`} />
                <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold' }]}>
                  Share with Friends
                </Text>
              </>
            )}
          </GHTouchableOpacity>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const { height } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  dismissArea: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: '#FFF8E1',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16,
    maxHeight: height * 0.4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    marginBottom: 20,
  },
  shareButton: {
    backgroundColor: '#000',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
});

export default ReferFriendPopup;
