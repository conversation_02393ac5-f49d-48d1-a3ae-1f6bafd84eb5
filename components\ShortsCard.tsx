import React, { useEffect, useState } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    Text,
    ScrollView,
    Alert,
    Modal,
    TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import Headingsixth from '@/components/ui/Headingsixth';
import { useNavigation } from '@react-navigation/native';
import { MessageCircleMore } from 'lucide-react-native';
import SkeletonLoader from '@/components/ui/SkeletonLoader';
import PaymentPopup from './paymentpopup';

// Add interface for Consultant type
interface Consultant {
    id: string;
    name: string;
    per_minute_rate: number;
    category_name: string;
    thumnel: string;
    active: number;
}

const TopConsultants = () => {
    const [consultants, setConsultants] = useState<Consultant[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [balance, setBalance] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
    const [rechargeAmount, setRechargeAmount] = useState('');
    const [isNewUser, setIsNewUser] = useState<boolean | null>(null);
    const navigation = useNavigation<any>();
    const rechargeMinimumAmount = 1; // Set a minimum recharge amount
    const [showPaymentPopup, setShowPaymentPopup] = useState(false)
    const [selectedRate, setSelectedRate] = useState(0);
    const [Username, setUsername] = useState('')

     const fetchConsultants = async () => {
    setLoading(true);
    try {
      const response = await fetch('https://nityasha.vercel.app/api/v1/home/<USER>');
      if (!response.ok) throw new Error('Failed to fetch consultants.');
      const data = await response.json();
  
      // Randomizing the order of consultants
      const shuffledData = data.sort(() => Math.random() - 0.5);
  
      setConsultants(shuffledData);
    } catch (err) {
        if (err instanceof Error) {
            setError(err.message);
        } else {
            setError('An unknown error occurred');
        }
    } finally {
      setLoading(false);
    }
  };

  const fetchUserBalance = (userId) => {
    const socket = new WebSocket("wss://balance-app-api.nityasha.com");

    socket.onopen = () => {
        socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
    };

    socket.onmessage = async (event) => {
        try {
            const data = JSON.parse(event.data);
            if (data.error) {
                setError(data.error);
            } else if (data.balance !== undefined) {
                const updatedBalance = parseFloat(data.balance) || 0;
                setBalance(updatedBalance);

                // Store the updated balance in AsyncStorage
                await AsyncStorage.setItem("userBalance", JSON.stringify(updatedBalance));
            }
        } catch (err) {
            setError("Invalid data received");
        }
    };

    socket.onerror = (err) => {
        console.error("WebSocket Error:", err);
        setError("WebSocket connection failed");
    };

    socket.onclose = () => {
        console.log("WebSocket disconnected, attempting reconnect in 5s...");
        setTimeout(() => fetchUserBalance(userId), 5000);
    };
};

const checkBalanceFrequently = (userId) => {
    const intervalId = setInterval(() => {
        fetchUserBalance(userId);
    }, 30000); // Now checks balance every 30 seconds

    return () => clearInterval(intervalId);
};


    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (userSession) {
                    const userId = JSON.parse(userSession).userId;
                    await fetchUserBalance(userId);
                    const userStatus = await AsyncStorage.getItem('isNewUser');
                    setIsNewUser(userStatus === '0');
                    checkBalanceFrequently(userId); // Start checking balance frequently
                } else {
                    navigation.navigate('Welcome' as never);
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        checkLoginStatus();
        fetchConsultants();
    }, [navigation]);

    const updateBalance = async (newBalance: number) => {
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) {
            setError('User session not found');
            return;
        }
        const userId = JSON.parse(userSession).userId;

        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ balance: newBalance }),
            });

            if (response.ok) {
                if (newBalance !== balance) {
                    setBalance(newBalance);
                }
            } else {
                const errorData = await response.json();
                Alert.alert(`Error updating balance: ${errorData.message || 'Unknown error'}`);
            }
        } catch (error) {
            Alert.alert("Error updating balance.");
        }
    };

const handleRecharge = async () => {
    const rechargeValue = parseFloat(rechargeAmount);
    if (isNaN(rechargeValue) || rechargeValue < rechargeMinimumAmount) {
        Alert.alert("Please enter a valid amount to recharge.");
        return;
    }

    const newBalance = balance + rechargeValue;

    try {
        await updateBalance(newBalance);
        setModalVisible(false);
        setRechargeAmount('');

        // Fetch latest balance to reflect the change immediately
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
            const userId = JSON.parse(userSession).userId;
            await fetchUserBalance(userId);  // ✅ Get updated balance from WebSocket
        }
    } catch (error) {
        Alert.alert("Error updating balance.");
    }
};


    const handleChatNow = async (consultantId: string, perMinuteRate: number, categoryName: string) => {
        if (categoryName.toLowerCase() === 'astrology') {
            navigation.navigate('ServicePage' as never, { consultantId });
            return;
        }
    
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) {
            setError('User session not found');
            return;
        }
    
        const userId = JSON.parse(userSession).userId;
        const freeChatUsed = await AsyncStorage.getItem(`freeChat_${userId}`);
    
        if (!freeChatUsed) {
            setIsNewUser(false);
            await AsyncStorage.setItem(`freeChat_${userId}`, 'true');
            navigation.navigate('Chat' as never, { 
                consultantId, 
                balance: '0', 
                chatDuration: 3 
            });
            return;
        }
    
        const minimumChatDuration = 3;
        const minimumBalanceRequired = minimumChatDuration * perMinuteRate;
    
        if (balance < minimumBalanceRequired) {
            setSelectedRate(perMinuteRate);
            setShowPaymentPopup(true);
            return;
        }
    
        const maxChatDuration = Math.floor(balance / perMinuteRate);
        const chatDuration = Math.max(minimumChatDuration, maxChatDuration);
        const totalCost = chatDuration * perMinuteRate;
        const newBalance = balance - totalCost;
    
        if (newBalance >= 0) {
            console.log("[Before Updating Balance]:", balance);
    
            await updateBalance(newBalance); // ✅ Wait for balance update
            
            // ✅ Fetch latest balance from AsyncStorage after update
            setTimeout(async () => {
                const updatedSession = await AsyncStorage.getItem('userSession');
                const updatedBalance = updatedSession ? JSON.parse(updatedSession).balance : newBalance;
    
                console.log("[After Updating Balance]:", updatedBalance);
    
                navigation.navigate('Chat' as never, { 
                    consultantId, 
                    balance: updatedBalance, // ✅ Use the updated balance
                    chatDuration 
                });
            }, 1000); // ✅ Wait for 1 sec to ensure update reflects
        } else {
            setSelectedRate(perMinuteRate);
            setShowPaymentPopup(true);
        }
    };
    
    
    
    

    if (error) {
        return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
    }

    if (loading) {
        return (
            <View style={tw`flex-1 justify-center items-center flex-row`}>
                <SkeletonLoader width={130} height={200} borderRadius={16} />
                <SkeletonLoader width={130} height={200} borderRadius={16} />
                <SkeletonLoader width={130} height={200} borderRadius={16} />
            </View>
        );
    }

    return (
        <View style={tw`pb-5`}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={tw`flex-row flex-wrap`}>
                    {consultants.length === 0 ? (
                        <Text style={tw`text-center w-full`}>No consultants available.</Text>
                    ) : (
                        consultants.map((consultant) => (
                            <View key={consultant.id} style={tw`mb-10`}>
                                <TouchableOpacity
                             disabled={consultant.active === 0} // Disable the button when active is 0
                                    onPress={() => {
                                        handleChatNow(consultant.id, consultant.per_minute_rate, consultant.category_name);
                                        setUsername(consultant.name);
                                    }}
                                    style={tw`relative w-[120px] h-[190px] overflow-hidden mr-[16px] rounded-[12px]`}
                                >
                                    <Image source={{ uri: consultant.thumnel }} style={tw`w-full h-full`} />
                                    <View style={tw`absolute bottom-0 left-0 right-0 p-2 h-full bg-black/20 flex items-start justify-end`}>
                                        <Text style={tw`text-white text-center text-[14px] font-bold`}>{consultant.name.slice(0, 10)}</Text>
                                    </View>
                                </TouchableOpacity>
                                
                            </View>
                        ))
                    )}
                </View>
            </ScrollView>

            <Modal visible={modalVisible} animationType="slide" transparent={true}>
                <View style={tw`flex-1 justify-center items-center absolute bottom-0 w-full bg-white border-2 rounded-t-3xl border-yellow-500`}>
                    <View style={tw`p-6 rounded-lg w-full`}>
                        <Text style={tw`text-lg font-semibold text-center mb-4`}>Your Balance is ₹{balance.toFixed(2)}</Text>
                        <TextInput
                            style={tw`border-2 p-2 rounded-xl`}
                            placeholder="Enter recharge amount"
                            keyboardType="numeric"
                            value={rechargeAmount}
                            onChangeText={setRechargeAmount}
                        />
                        <View style={tw`flex flex-row gap-2 mt-3`}>
                            <TouchableOpacity onPress={handleRecharge} style={tw`w-[50%] bg-black px-5 py-2 rounded-lg`}>
                                <Text style={tw`text-white text-center`}>Recharge</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                onPress={() => setModalVisible(false)}
                                style={tw`w-[50%] bg-black px-5 py-2 rounded-lg`}
                            >
                                <Text style={tw`text-white text-center`}>Cancel</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
            <PaymentPopup
                visible={showPaymentPopup}
                onClose={() => setShowPaymentPopup(false)}
                rate={selectedRate}
                username={Username}
            />
        </View>
    );
};

export default TopConsultants;
