import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, Button, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useSocketOnlineStatus } from '@/hooks/useSocketOnlineStatus';

const SocketOnlineStatus = () => {
  const { userStatuses, connected, error, currentUserId, checkUserStatus } = useSocketOnlineStatus();
  const [newUserId, setNewUserId] = useState('');
  const [monitoredUsers, setMonitoredUsers] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addUser = async () => {
    if (!newUserId || monitoredUsers.includes(newUserId)) {
      return;
    }

    setLoading(true);
    await checkUserStatus(newUserId);
    setLoading(false);
    
    setMonitoredUsers(prev => [...prev, newUserId]);
    setNewUserId('');
  };

  const removeUser = (userId: string) => {
    setMonitoredUsers(prev => prev.filter(id => id !== userId));
  };

  const formatLastSeen = (timestamp: string | null) => {
    if (!timestamp) return 'Unknown';
    
    const lastSeenDate = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - lastSeenDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hr ago`;
    if (diffDays === 1) return 'Yesterday';
    return `${diffDays} days ago`;
  };

  const renderUserItem = ({ item }: { item: string }) => {
    const status = userStatuses[item];
    const isCurrentUser = item === currentUserId;
    
    return (
      <View style={styles.userItem}>
        <View style={styles.userInfo}>
          <View style={[styles.statusDot, { backgroundColor: status?.isOnline ? '#4CAF50' : '#9E9E9E' }]} />
          <Text style={styles.userId}>
            {item} {isCurrentUser ? '(You)' : ''}
          </Text>
        </View>
        <View style={styles.statusInfo}>
          <Text style={styles.statusText}>
            {status?.isOnline ? 'Online' : status?.lastSeen ? `Last seen ${formatLastSeen(status.lastSeen)}` : 'Offline'}
          </Text>
          {!isCurrentUser && (
            <TouchableOpacity style={styles.removeButton} onPress={() => removeUser(item)}>
              <Text style={styles.removeButtonText}>Remove</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Real-time Online Status</Text>
        <View style={[styles.connectionStatus, { backgroundColor: connected ? '#4CAF50' : '#F44336' }]}>
          <Text style={styles.connectionStatusText}>
            {connected ? 'Connected' : 'Disconnected'}
          </Text>
        </View>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <View style={styles.addUserContainer}>
        <TextInput
          style={styles.input}
          value={newUserId}
          onChangeText={setNewUserId}
          placeholder="Enter user ID to monitor"
          keyboardType="numeric"
        />
        <Button
          title={loading ? "Adding..." : "Add User"}
          onPress={addUser}
          disabled={loading || !newUserId}
        />
      </View>

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#0000ff" />
          <Text style={styles.loadingText}>Checking user status...</Text>
        </View>
      )}

      <Text style={styles.sectionTitle}>Monitored Users</Text>
      
      {currentUserId && !monitoredUsers.includes(currentUserId) && (
        <TouchableOpacity 
          style={styles.addCurrentUserButton}
          onPress={() => {
            setMonitoredUsers(prev => [...prev, currentUserId]);
            checkUserStatus(currentUserId);
          }}
        >
          <Text style={styles.addCurrentUserText}>Add yourself to the list</Text>
        </TouchableOpacity>
      )}

      {monitoredUsers.length === 0 ? (
        <Text style={styles.emptyText}>No users being monitored</Text>
      ) : (
        <FlatList
          data={monitoredUsers}
          renderItem={renderUserItem}
          keyExtractor={(item) => item}
          style={styles.userList}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  connectionStatus: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  connectionStatusText: {
    color: 'white',
    fontWeight: 'bold',
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#D32F2F',
  },
  addUserContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    backgroundColor: 'white',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  loadingText: {
    marginLeft: 8,
    color: '#666',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyText: {
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 16,
  },
  userList: {
    flex: 1,
  },
  userItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  userId: {
    fontSize: 16,
    fontWeight: '500',
  },
  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    color: '#666',
    marginRight: 8,
  },
  removeButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  removeButtonText: {
    color: 'white',
    fontSize: 12,
  },
  addCurrentUserButton: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  addCurrentUserText: {
    color: '#1976D2',
    fontWeight: '500',
  },
});

export default SocketOnlineStatus;
