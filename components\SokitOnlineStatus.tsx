import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SokitOnlineStatusProps {
  userId: string;
  size?: number;
  showText?: boolean;
  textStyle?: object;
}

const SokitOnlineStatus: React.FC<SokitOnlineStatusProps> = ({
  userId,
  size = 8,
  showText = false,
  textStyle = {},
}) => {
  const [isOnline, setIsOnline] = useState(false);
  const [lastSeen, setLastSeen] = useState<string | null>(null);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let ws: WebSocket | null = null;
    let cleanup: (() => void) | undefined;

    const connectWebSocket = async () => {
      try {
        // Get current user ID from AsyncStorage
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) {
          console.log('No user session found');
          return;
        }

        const parsedSession = JSON.parse(userSession);
        const currentUserId = parsedSession?.userId;

        if (!currentUserId) {
          console.log('No user ID found in session');
          return;
        }

        // Initial fetch of user status
        fetchUserStatus();

        // Connect to WebSocket server
        ws = new WebSocket(`wss://status.api.nityasha.com/ws?userId=${currentUserId}&type=status`);

        ws.onopen = () => {
          console.log('WebSocket connected');
          setConnected(true);
          setError(null);
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            if (data.type === 'user_status_update' && data.userId === userId) {
              // Update user status
              setIsOnline(data.isOnline);
              setLastSeen(data.timestamp);
            }
          } catch (err) {
            console.error('Error parsing WebSocket message:', err);
          }
        };

        ws.onerror = (event) => {
          console.error('WebSocket error:', event);
          setError('WebSocket connection error');
        };

        ws.onclose = () => {
          console.log('WebSocket disconnected');
          setConnected(false);
        };

        // Set up cleanup function
        cleanup = () => {
          console.log('Cleaning up WebSocket connection');
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.close();
          }
        };
      } catch (err) {
        console.error('Error initializing WebSocket:', err);
        setError('Failed to initialize WebSocket connection');
      }
    };

    const fetchUserStatus = async () => {
      try {
        const response = await fetch(`https://status.api.nityasha.com/user/${userId}/online-status`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user status');
        }
        
        const data = await response.json();
        setIsOnline(data.isOnline);
        setLastSeen(data.lastSeen);
      } catch (err) {
        console.error('Error fetching user status:', err);
      }
    };

    connectWebSocket();

    // Set up interval to fetch status if WebSocket is not connected
    const intervalId = setInterval(() => {
      if (!connected) {
        fetchUserStatus();
      }
    }, 30000); // Every 30 seconds

    // Clean up WebSocket connection and interval when component unmounts
    return () => {
      if (cleanup) {
        cleanup();
      }
      clearInterval(intervalId);
    };
  }, [userId]);

  // Format last seen time
  const formatLastSeen = (timestamp: string | null) => {
    if (!timestamp) return 'Unknown';
    
    const lastSeenDate = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - lastSeenDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hr ago`;
    if (diffDays === 1) return 'Yesterday';
    return `${diffDays} days ago`;
  };

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.indicator,
          { backgroundColor: isOnline ? '#4CAF50' : '#9E9E9E' },
          { width: size, height: size, borderRadius: size / 2 }
        ]}
      />
      {showText && (
        <Text style={[styles.text, textStyle]}>
          {isOnline ? 'Online' : `Last seen ${formatLastSeen(lastSeen)}`}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  text: {
    fontSize: 12,
    color: '#757575',
  },
});

export default SokitOnlineStatus;
