import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, Button, FlatList, TouchableOpacity } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SokitOnlineStatusIndicator from './SokitOnlineStatusIndicator';

const SokitOnlineStatusDemo = () => {
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [newUserId, setNewUserId] = useState('');
  const [monitoredUsers, setMonitoredUsers] = useState<string[]>([]);

  // Get current user ID from AsyncStorage
  useEffect(() => {
    const getCurrentUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          const userId = parsedSession?.userId;
          setCurrentUserId(userId);
          
          // Add current user to monitored users
          if (userId) {
            setMonitoredUsers(prev => [...prev, userId]);
          }
        }
      } catch (err) {
        console.error('Error getting user session:', err);
      }
    };

    getCurrentUserId();
  }, []);

  const addUser = () => {
    if (!newUserId || monitoredUsers.includes(newUserId)) {
      return;
    }
    
    setMonitoredUsers(prev => [...prev, newUserId]);
    setNewUserId('');
  };

  const removeUser = (userId: string) => {
    if (userId === currentUserId) {
      return; // Don't remove current user
    }
    
    setMonitoredUsers(prev => prev.filter(id => id !== userId));
  };

  const renderUserItem = ({ item }: { item: string }) => {
    const isCurrentUser = item === currentUserId;
    
    return (
      <View style={styles.userItem}>
        <View style={styles.userInfo}>
          <SokitOnlineStatusIndicator userId={item} size={12} showText />
          <Text style={styles.userId}>
            {item} {isCurrentUser ? '(You)' : ''}
          </Text>
        </View>
        {!isCurrentUser && (
          <TouchableOpacity style={styles.removeButton} onPress={() => removeUser(item)}>
            <Text style={styles.removeButtonText}>Remove</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Sokit Online Status Demo</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          This demo shows real-time online/offline status using WebSockets.
          The green dot indicates a user is online, and gray means offline.
        </Text>
      </View>
      
      <View style={styles.addUserContainer}>
        <TextInput
          style={styles.input}
          value={newUserId}
          onChangeText={setNewUserId}
          placeholder="Enter user ID to monitor"
          keyboardType="numeric"
        />
        <Button
          title="Add User"
          onPress={addUser}
          disabled={!newUserId}
        />
      </View>

      <Text style={styles.sectionTitle}>Monitored Users</Text>
      
      {monitoredUsers.length === 0 ? (
        <Text style={styles.emptyText}>No users being monitored</Text>
      ) : (
        <FlatList
          data={monitoredUsers}
          renderItem={renderUserItem}
          keyExtractor={(item) => item}
          style={styles.userList}
        />
      )}
      
      <View style={styles.testContainer}>
        <Text style={styles.testTitle}>Testing Instructions:</Text>
        <Text style={styles.testText}>
          1. Add your own user ID and other users to monitor{'\n'}
          2. Close the app or put it in the background to see your status change to offline{'\n'}
          3. Reopen the app to see your status change back to online{'\n'}
          4. Monitor other users to see their status change in real-time
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  infoContainer: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoText: {
    color: '#0D47A1',
    fontSize: 14,
  },
  addUserContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    backgroundColor: 'white',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyText: {
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 16,
  },
  userList: {
    flex: 1,
  },
  userItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userId: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  removeButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  removeButtonText: {
    color: 'white',
    fontSize: 12,
  },
  testContainer: {
    backgroundColor: '#FFF9C4',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  testTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  testText: {
    fontSize: 12,
    lineHeight: 18,
  },
});

export default SokitOnlineStatusDemo;
