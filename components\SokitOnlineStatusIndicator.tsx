import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SokitOnlineStatusIndicatorProps {
  userId: string;
  size?: number;
  showText?: boolean;
  textStyle?: object;
}

const SokitOnlineStatusIndicator: React.FC<SokitOnlineStatusIndicatorProps> = ({
  userId,
  size = 8,
  showText = false,
  textStyle = {},
}) => {
  const [isOnline, setIsOnline] = useState(false);
  const [lastSeen, setLastSeen] = useState<string | null>(null);
  const [connected, setConnected] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);
  const isCurrentUserRef = useRef<boolean>(false);
  const currentUserIdRef = useRef<string | null>(null);

  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const setupWebSocket = async () => {
      try {
        // Get current user ID from AsyncStorage
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          const currentUserId = parsedSession?.userId;
          currentUserIdRef.current = currentUserId;

          // Check if this is the current user
          isCurrentUserRef.current = userId === currentUserId;
        }

        // Initial fetch of user status and visibility
        await checkVisibilityAndStatus();

        // Only set up WebSocket if we have a current user
        if (currentUserIdRef.current) {
          // Connect to WebSocket server
          const ws = new WebSocket(`wss://status.api.nityasha.com/ws?userId=${currentUserIdRef.current}&type=status`);
          wsRef.current = ws;

          ws.onopen = () => {
            console.log('WebSocket connected for status updates');
            setConnected(true);

            // If this is the current user, set them as online
            if (isCurrentUserRef.current) {
              setUserOnline();
            }
          };

          ws.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);

              if (data.type === 'user_status_update' && data.userId === userId) {
                console.log(`Status update for user ${userId}:`, data.isOnline ? 'online' : 'offline');

                // First check if the user is visible to us
                checkVisibilityAndStatus();
              }
            } catch (err) {
              console.error('Error parsing WebSocket message:', err);
            }
          };

          ws.onerror = (event) => {
            console.error('WebSocket error:', event);
            setConnected(false);
          };

          ws.onclose = () => {
            console.log('WebSocket disconnected');
            setConnected(false);

            // If this is the current user, set them as offline
            if (isCurrentUserRef.current) {
              setUserOffline();
            }
          };

          // Set up app state change handler
          // Handle app state changes with debounce to prevent rapid status changes
          let appStateTimeout: NodeJS.Timeout | null = null;

          const handleAppStateChange = async (nextAppState: AppStateStatus) => {
            // Clear any pending timeout
            if (appStateTimeout) {
              clearTimeout(appStateTimeout);
            }

            // Set a timeout to update status after a short delay
            appStateTimeout = setTimeout(async () => {
              if (
                appStateRef.current.match(/inactive|background/) &&
                nextAppState === 'active' &&
                isCurrentUserRef.current
              ) {
                // App has come to the foreground
                await setUserOnline();
              } else if (
                appStateRef.current === 'active' &&
                nextAppState.match(/inactive|background/) &&
                isCurrentUserRef.current
              ) {
                // Add a delay before marking offline to prevent flicker
                setTimeout(async () => {
                  // Double-check the app is still in background
                  if (AppState.currentState !== 'active' && isCurrentUserRef.current) {
                    await setUserOffline();
                  }
                }, 5000); // 5 second delay before marking offline
              }

              appStateRef.current = nextAppState;
            }, 1000); // 1 second debounce
          };

          const subscription = AppState.addEventListener('change', handleAppStateChange);

          // Set up cleanup function
          cleanup = () => {
            console.log('Cleaning up WebSocket and app state listeners');
            subscription.remove();

            if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
              // If this is the current user, set them as offline before closing
              if (isCurrentUserRef.current) {
                setUserOffline().then(() => {
                  if (wsRef.current) {
                    wsRef.current.close();
                  }
                });
              } else {
                wsRef.current.close();
              }
            }
          };
        }
      } catch (err) {
        console.error('Error setting up WebSocket:', err);
      }
    };

    // Check if the user is visible to the current user and then check their status
    const checkVisibilityAndStatus = async () => {
      try {
        // Skip visibility check if this is the current user (always visible to self)
        if (isCurrentUserRef.current) {
          await fetchUserStatus();
          return;
        }

        // Get current user ID
        const currentUserId = currentUserIdRef.current;
        if (!currentUserId) {
          setIsOnline(false);
          return;
        }

        // Check if the user is visible to us
        const visibilityResponse = await fetch(`https://status.api.nityasha.com/user/${userId}/visibility/${currentUserId}`);

        // If API fails, try Firebase directly
        if (!visibilityResponse.ok) {
          // Check Firebase for visibility
          const db = await import('@/lib/Firebase').then(module => module.db);
          const { ref, get } = await import('firebase/database');

          const visibilityRef = ref(db, `user_visibility/${userId}/visible_to/${currentUserId}`);
          const visibilitySnapshot = await get(visibilityRef);
          const visibilityData = visibilitySnapshot.val();

          // If not visible to us, show as offline
          if (!visibilityData || visibilityData.visible !== true) {
            setIsOnline(false);
            return;
          }
        } else {
          const visibilityData = await visibilityResponse.json();
          if (!visibilityData.visible) {
            setIsOnline(false);
            return;
          }
        }

        // If we get here, the user is visible to us, so check their actual status
        await fetchUserStatus();
      } catch (err) {
        console.error('Error checking visibility:', err);
        setIsOnline(false);
      }
    };

    const fetchUserStatus = async () => {
      try {
        const response = await fetch(`https://status.api.nityasha.com/user/${userId}/online-status`);

        if (!response.ok) {
          throw new Error('Failed to fetch user status');
        }

        const data = await response.json();

        // Check if the user is truly online - must be explicitly true and last seen must be recent
        const isUserOnline = data.isOnline === true;
        const lastSeenTime = data.lastSeen ? new Date(data.lastSeen).getTime() : 0;
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000; // 5 minutes in milliseconds

        // Only consider online if status is true AND last activity was recent
        setIsOnline(isUserOnline && lastSeenTime > fiveMinutesAgo);
        setLastSeen(data.lastSeen);

        return data;
      } catch (err) {
        console.error('Error fetching user status:', err);
        // Set to offline on error
        setIsOnline(false);
        return null;
      }
    };

    const setUserOnline = async () => {
      if (!isCurrentUserRef.current) return;

      try {
        const response = await fetch(`https://status.api.nityasha.com/user/${userId}/online`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to set user as online');
        }

        const data = await response.json();
        setIsOnline(data.isOnline);
        setLastSeen(data.lastSeen);

        console.log('User set as online:', userId);
      } catch (err) {
        console.error('Error setting user as online:', err);
      }
    };

    const setUserOffline = async () => {
      if (!isCurrentUserRef.current) return;

      try {
        const response = await fetch(`https://status.api.nityasha.com/user/${userId}/offline`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to set user as offline');
        }

        const data = await response.json();
        setIsOnline(false);
        setLastSeen(data.lastSeen);

        console.log('User set as offline:', userId);
      } catch (err) {
        console.error('Error setting user as offline:', err);
      }
    };

    // Set up WebSocket and fetch initial status
    setupWebSocket();

    // Set up interval to refresh status if WebSocket is not connected
    const intervalId = setInterval(() => {
      if (!connected) {
        checkVisibilityAndStatus();
      }
    }, 30000); // Every 30 seconds

    // Clean up WebSocket connection and interval when component unmounts
    return () => {
      if (cleanup) {
        cleanup();
      }
      clearInterval(intervalId);
    };
  }, [userId]);

  // Format last seen time
  const formatLastSeen = (timestamp: string | null) => {
    if (!timestamp) return 'recently';

    try {
      const lastSeenDate = new Date(timestamp);

      // Check if date is valid
      if (isNaN(lastSeenDate.getTime())) {
        return 'recently';
      }

      const now = new Date();
      const diffMs = now.getTime() - lastSeenDate.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);

      if (diffMins < 1) return 'just now';
      if (diffMins < 60) return `${diffMins} min ago`;
      if (diffHours < 24) return `${diffHours} hr ago`;
      if (diffDays === 1) return 'yesterday';
      return `${diffDays} days ago`;
    } catch (error) {
      console.error('Error formatting last seen time:', error);
      return 'recently';
    }
  };

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.indicator,
          { backgroundColor: isOnline ? '#4CAF50' : '#9E9E9E' },
          { width: size, height: size, borderRadius: size / 2 }
        ]}
      />
      {showText && (
        <Text style={[styles.text, textStyle]}>
          {isOnline ? 'Online' : lastSeen ? `Last seen ${formatLastSeen(lastSeen)}` : 'Offline'}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  text: {
    fontSize: 12,
    color: '#757575',
  },
});

export default SokitOnlineStatusIndicator;
