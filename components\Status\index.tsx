import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
// WebSocket hook removed

interface Status {
  id: number;
  user_id: string;
  media_url: string;
  created_at: string;
}

interface StoryCardProps {
  story: {
    id: number;
    username: string;
    profilePic: string;
    hasUnseenStory?: boolean;
    statuses?: Status[];
    isCurrentUserStatus?: boolean;
  };
  onPress: (id: number, statuses: Status[], isCurrentUserStatus?: boolean) => void;
}

// Constants for sizing
const STORY_SIZE = 70;
const BORDER_WIDTH = 3;

// No example stories - we'll only use real data

function StoryCard({ story, onPress }: StoryCardProps) {
  // Check if the story has statuses
  const hasStatuses = story.statuses && story.statuses.length > 0;

  // Check if this is the "My Status" card with no statuses
  const isEmptyMyStatus = story.isCurrentUserStatus && !hasStatuses;

  return (
    <TouchableOpacity
      onPress={() => onPress(story.id, story.statuses || [], story.isCurrentUserStatus)}
      style={tw`items-center mx-2 h-24`}
      disabled={false} // Enable all cards, even empty "My Status"
    >
      {/* Use gradient for status ring with different colors based on status */}
      <LinearGradient
        colors={hasStatuses ? ['#075E54', '#128C7E'] : (isEmptyMyStatus ? ['#128C7E', '#075E54'] : ['#CCCCCC', '#AAAAAA'])}
        style={[
          tw`justify-center items-center`,
          {
            width: STORY_SIZE,
            height: STORY_SIZE,
            borderRadius: STORY_SIZE / 2,
            padding: BORDER_WIDTH,
          },
        ]}
      >
        <View style={[
          tw`bg-white overflow-hidden`,
          {
            width: STORY_SIZE - BORDER_WIDTH * 2,
            height: STORY_SIZE - BORDER_WIDTH * 2,
            borderRadius: (STORY_SIZE - BORDER_WIDTH * 2) / 2,
          }
        ]}>
          {isEmptyMyStatus ? (
            <View style={tw`w-full h-full rounded-full bg-gray-100 justify-center items-center`}>
              <Text style={tw`text-2xl text-gray-400`}>+</Text>
            </View>
          ) : (
            <Image
              source={{ uri: story.profilePic }}
              style={tw`w-full h-full rounded-full`}
              resizeMode="cover"
            />
          )}
        </View>
      </LinearGradient>

      <Text
        numberOfLines={1}
        style={[
          tw`text-xs mt-1 text-center font-medium`,
          isEmptyMyStatus ? tw`text-gray-700` : {},
          { maxWidth: STORY_SIZE + 10 }
        ]}
      >
        {story.username}
      </Text>
    </TouchableOpacity>
  );
}

interface MappedUser {
  id: number;
  name: string;
  initial: string;
  contactName?: string;
  phoneNumber?: string;
  originalEmail?: string;
  lastMessage: string;
  timestamp: string;
  isOnline: boolean;
  statuses?: Status[];
  hasUnseenStory: boolean;
  hasStatus?: boolean;
}

interface StoriesContainerProps {
  userStatuses?: Status[];
  users?: MappedUser[];
  onStatusUpdate?: () => void; // Add this prop
}

// Add this interface for the API response
interface ApiStatus {
  id: number;
  user_id: string;
  media_url: string;
  created_at: string;
  body?: string;
}

export default function StoriesContainer({ userStatuses = [], users = [], onStatusUpdate }: StoriesContainerProps) {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [apiStatuses, setApiStatuses] = useState<ApiStatus[]>([]);

  // Fetch status updates from the API
  const fetchStatusUpdates = async (userId: string) => {
    try {
      // Fetch the current user's statuses
      const response = await fetch(`https://status.api.nityasha.com/statuses?userId=${userId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      if (Array.isArray(data)) {
        console.log('Fetched user statuses:', data);
        setApiStatuses(data);
        if (onStatusUpdate) {
          onStatusUpdate();
        }
      }
    } catch (error) {
      console.error('Error fetching status updates:', error);
    }
  };

  // Fetch a specific status by ID
  const fetchStatusById = async (statusId: string) => {
    try {
      const response = await fetch(`https://status.api.nityasha.com/status/${statusId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Fetched status by ID:', data);

      // If we got a single status object, convert it to an array
      if (!Array.isArray(data)) {
        setApiStatuses([data]);
      } else {
        setApiStatuses(data);
      }

      if (onStatusUpdate) {
        onStatusUpdate();
      }
    } catch (error) {
      console.error('Error fetching status by ID:', error);
    }
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const initializeStatusPolling = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          const userId = parsedSession.userId?.toString();
          setCurrentUserId(userId);

          if (userId) {
            console.log('Initializing status polling for user ID:', userId);
            // Initial fetch
            fetchStatusUpdates(userId);

            // Set up polling every 3 seconds
            intervalId = setInterval(() => {
              fetchStatusUpdates(userId);
            }, 3000);
          }
        }
      } catch (error) {
        console.error('Error initializing status polling:', error);
      }
    };

    initializeStatusPolling();

    // Cleanup interval on component unmount
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, []);

  // Combine API statuses with existing userStatuses
  const allStatuses = [...userStatuses, ...apiStatuses.map(apiStatus => ({
    id: apiStatus.id,
    user_id: apiStatus.user_id,
    media_url: apiStatus.media_url,
    created_at: apiStatus.created_at,
    body: apiStatus.body
  }))];

  const handleStoryPress = (id: number, statuses: Status[], isCurrentUserStatus: boolean = false) => {
    console.log(`Story ${id} pressed with statuses:`, statuses);
    if (statuses.length > 0) {
      // Find the matching user to get the name
      const matchingUser = users.find(user => user.id === id || user.id.toString() === statuses[0].user_id);

      // Check if any of the statuses belong to the current user
      const anyStatusBelongsToCurrentUser = statuses.some(status => status.user_id === currentUserId);
      const displayName = isCurrentUserStatus || anyStatusBelongsToCurrentUser ? 'My Status' : (matchingUser?.name || 'User');

      // Log the statuses being passed to StatusView
      console.log('Navigating to StatusView with statuses:', statuses);
      console.log('Current user ID:', currentUserId);
      console.log('Is current user status:', isCurrentUserStatus || anyStatusBelongsToCurrentUser);

      // Pass the user data to the StatusView component
      const userData = matchingUser ? {
        id: matchingUser.id,
        name: matchingUser.name,
        phoneNumber: matchingUser.phoneNumber,
        contactName: matchingUser.contactName,
        originalEmail: matchingUser.originalEmail
      } : null;

      // Always pass the specific user's statuses to ensure we only see that user's content
      // Filter statuses to only include those from the clicked user
      // If it's the current user's status, filter by currentUserId
      // Otherwise, filter by the first status's user_id
      const filterUserId = isCurrentUserStatus || anyStatusBelongsToCurrentUser ? currentUserId : statuses[0].user_id;
      const userStatuses = statuses.filter(status => status.user_id === filterUserId);

      // Navigate with the filtered statuses
      navigation.navigate('StatusView', {
        statuses: userStatuses,
        userName: displayName,
        onStatusUpdate: onStatusUpdate,
        userData: userData, // Pass the user data
        showRecent: false // Disable recent view to only show this user's statuses
      });
    }
  };

  // Group statuses by user_id and create a map of user_id to statuses
  const statusesByUserId: { [key: string]: Status[] } = {};

  // Group all statuses by user_id
  allStatuses.forEach(status => {
    if (!statusesByUserId[status.user_id]) {
      statusesByUserId[status.user_id] = [];
    }
    statusesByUserId[status.user_id].push(status);
  });

  console.log('Statuses by user ID:', statusesByUserId);
  console.log('Current user ID for status matching:', currentUserId);

  // Helper function to normalize user IDs for comparison
  const normalizeId = (id: string | number | null | undefined): string => {
    if (id === null || id === undefined) return '';
    return id.toString().trim();
  };

  // Create stories from the status data
  const statusStories = Object.keys(statusesByUserId).map(userId => {
    // Check if this is the current user's status using normalized IDs
    const isCurrentUserStatus = normalizeId(userId) === normalizeId(currentUserId);

    // Try to find a matching user to get their name
    const matchingUser = users.find(user => {
      // Check if this is the current user by ID using normalized IDs
      if (currentUserId &&
          (normalizeId(user.id) === normalizeId(currentUserId)) &&
          normalizeId(userId) === normalizeId(currentUserId)) {
        return true;
      }

      // Clean the user's phone number to match status user_id format
      const cleanPhoneNumber = user.phoneNumber
        ?.replace(/[\s-()]/g, '')
        ?.replace(/^\+?91/, '')
        ?.replace(/^0+/, '');

      return cleanPhoneNumber === userId || normalizeId(user.id) === normalizeId(userId);
    });

    // Check if the statuses belong to the current user using normalized IDs
    const statusBelongsToCurrentUser = currentUserId ? statusesByUserId[userId].some(status => {
      const isMatch = normalizeId(status.user_id) === normalizeId(currentUserId);
      if (isMatch) {
        console.log('Found status belonging to current user:', status);
      }
      return isMatch;
    }) : false;

    const isMyStatus = isCurrentUserStatus || statusBelongsToCurrentUser;

    if (isMyStatus) {
      console.log('Creating My Status story with statuses:', statusesByUserId[userId]);
    }

    // Create a story for each user with statuses
    return {
      id: parseInt(userId) || Math.floor(Math.random() * 1000), // Fallback to random ID if parsing fails
      username: isMyStatus ? 'My Status' : (matchingUser?.name || `User ${userId}`),
      profilePic: `https://ui-avatars.com/api/?name=${encodeURIComponent(isMyStatus ? 'Me' : (matchingUser?.name || `User ${userId}`))}`,
      hasUnseenStory: true,
      statuses: statusesByUserId[userId],
      isCurrentUserStatus: isMyStatus
    };
  });

  // Sort stories to show current user's story first
  const sortedStories = [...statusStories].sort((a, b) => {
    // If story a belongs to current user, it should come first
    if (a.isCurrentUserStatus) return -1;
    // If story b belongs to current user, it should come first
    if (b.isCurrentUserStatus) return 1;
    // Otherwise maintain original order
    return 0;
  });

  // Create a "My Status" card if the user is logged in, even if they don't have statuses
  const hasMyStatusCard = sortedStories.some(story => story.isCurrentUserStatus);

  // If there are no statuses and no logged-in user, don't render the component at all
  if (sortedStories.length === 0 && !currentUserId) {
    return null;
  }

  // Create a "My Status" object if the user doesn't already have a status
  const myStatusStory = {
    id: -1, // Use a negative ID to avoid conflicts
    username: 'My Status',
    profilePic: `https://ui-avatars.com/api/?name=Me`,
    hasUnseenStory: false,
    statuses: [], // Empty statuses array
    isCurrentUserStatus: true
  };

  // Add the "My Status" card only if the user is logged in and doesn't already have a status
  const finalStories = hasMyStatusCard || !currentUserId ? sortedStories : [myStatusStory, ...sortedStories];

  return (
    <View style={tw`mb-2`}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={tw`py-2`}>
        {finalStories.map((story) => (
          <StoryCard
            key={story.id}
            story={story}
            onPress={(id, statuses, isCurrentUserStatus) => {
              // If this is the "My Status" card with no statuses, navigate to status creation
              if (id === -1 && statuses.length === 0) {
                navigation.navigate('CreateStatus', {
                  onStatusUpdate: onStatusUpdate // Pass the callback here
                });
              } else {
                // Otherwise, handle normal status viewing
                handleStoryPress(id, statuses, isCurrentUserStatus);
              }
            }}
          />
        ))}
      </ScrollView>
    </View>
  );
}
