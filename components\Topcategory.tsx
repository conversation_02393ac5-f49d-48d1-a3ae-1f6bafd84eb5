import React, { useEffect, useState, useRef } from 'react';
import { View, ActivityIndicator, Text, FlatList, TouchableOpacity, Image } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

// Add proper type definitions at the top
type Consultant = {
    id: number;
    name: string;
    icon: string;
};
type NavigationProps = {
    navigate: (screen: string, params?: { id: number }) => void;
};

const TopConsultants = () => {
    const [consultants, setConsultants] = useState<Consultant[]>([]);
    const [loading, setLoading] = useState(false);
    const [isFirstLoad, setIsFirstLoad] = useState(true); // Track if it's the first load
    const [error, setError] = useState<string | null>(null);
    const navigation = useNavigation<NavigationProps>();
    const flatListRef = useRef<FlatList<Consultant>>(null); // Reference to FlatList
    const reloadIntervalMs = 1000000; // Auto reload every 60 seconds

    // Fetch consultants from API
    const fetchConsultants = async () => {
        setLoading(true); // Show loader during fetch
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/categories/get');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An unknown error occurred');
        } finally {
            setLoading(false); // Hide loader after data is fetched
            setIsFirstLoad(false); // Set first load to false after the initial fetch
        }
    };

    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (!userSession) {
                    navigation.navigate('Welcome');
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        checkLoginStatus(); // Call the function to check login status
        fetchConsultants(); // Initial fetch of consultants

        // Set up auto-reload for consultants
        const interval = setInterval(() => {
            fetchConsultants();
        }, reloadIntervalMs);

        return () => {
            clearInterval(interval); // Cleanup interval on component unmount
        };
    }, [navigation]);

    // Add proper type to handleChatNow
    const handleChatNow = (id: number) => {
        navigation.navigate('Category', { id });
    };

    // Error handling UI
    if (error) {
        return (
            <View style={tw`flex-1 justify-center items-center`}>
                <Text style={tw`text-red-500 text-center`}>{error}</Text>
                <TouchableOpacity onPress={fetchConsultants} style={tw`mt-4 p-2 bg-blue-500 rounded`}>
                    <Text style={tw`text-white`}>Retry</Text>
                </TouchableOpacity>
            </View>
        );
    }

    // Loading UI - show loader only during the first load
    if (isFirstLoad && loading) {
        return (
            <View style={tw`flex-1 justify-center items-center`}>
                <View style={tw`flex flex-row`}>
                    <View
                        style={tw`flex items-center py-3 px-1 mr-[0px] overflow-hidden`}
                    >
                        <View style={tw`w-[68px] h-[68px] rounded-full bg-[#d4d4d8] flex-row items-center justify-center`}>
                        </View>
                        <Text style={tw`mt-2 text-[14px] text-[#d4d4d8] h-5 bg-[#d4d4d8]`}>
                            Unknown Consultan
                        </Text>
                    </View>
                    <View
                        style={tw`flex items-center py-3 px-1 mr-[0px] overflow-hidden`}
                    >
                        <View style={tw`w-[68px] h-[68px] rounded-full bg-[#d4d4d8] flex-row items-center justify-center`}>
                        </View>
                        <Text style={tw`mt-2 text-[14px] text-[#d4d4d8] h-5 bg-[#d4d4d8]`}>
                            Unknown Consultan
                        </Text>
                    </View>
                    <View
                        style={tw`flex items-center py-3 px-1 mr-[0px] overflow-hidden`}
                    >
                        <View style={tw`w-[68px] h-[68px] rounded-full bg-[#d4d4d8] flex-row items-center justify-center`}>
                        </View>
                        <Text style={tw`mt-2 text-[14px] text-[#d4d4d8] h-5 bg-[#d4d4d8]`}>
                            Unknown Consultan
                        </Text>
                    </View>
                </View>
            </View>
        );
    }

    // Add proper types to renderItem
    const renderItem = ({ item }: { item: Consultant }) => {
        return (
            <TouchableOpacity
                key={item.id}
                style={tw`flex items-center py-3 px-1 mr-[5px] overflow-hidden`}
                onPress={() => handleChatNow(item.id)}
            >
                <View style={tw`w-[68px] h-[68px] rounded-[20px] bg-[#fff] flex-row items-center justify-center`}>
                    <Ionicons name={item.iconname} size={33} color="black" />
                </View>
                <Text style={[tw`mt-2 text-[14px] text-[#fff] h-5`, { fontFamily: 'Helvetica_bold' }]}>
                    {item.name ? item.name : 'Unknown Consultant'}
                </Text>
            </TouchableOpacity>
        );
    };

    return (
        <View>
            <FlatList
                ref={flatListRef}
                horizontal
                data={consultants}
                keyExtractor={(item) => item.id.toString()}
                renderItem={renderItem}
                showsHorizontalScrollIndicator={false}
            />
        </View>
    );
};

export default TopConsultants;