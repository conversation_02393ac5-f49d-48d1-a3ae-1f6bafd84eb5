import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import tw from 'twrnc';

interface User {
  id: number;
  name: string;
  pfp: string;
  category_name?: string;
  exp?: number;
  per_minute_rate?: number;
}

interface UserMentionCardProps {
  user: User;
  onPress: (userId: number) => void;
}

const UserMentionCard: React.FC<UserMentionCardProps> = ({ user, onPress }) => {
  // Ensure we have valid data with fallbacks
  const {
    id = 0,
    name = 'Unknown User',
    pfp = '',
    category_name,
    exp,
    per_minute_rate
  } = user || {};

  const defaultAvatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}`;

  return (
    <TouchableOpacity 
      onPress={() => onPress(id)}
      style={tw`bg-white rounded-xl p-4 mb-3 shadow-sm`}
    >
      <View style={tw`flex-row items-center`}>
        <Image 
          source={{ uri: pfp || defaultAvatar }}
          style={tw`w-16 h-16 rounded-full`}
          defaultSource={{ uri: defaultAvatar }}
        />
        <View style={tw`ml-4 flex-1`}>
          <Text style={tw`text-lg font-bold`}>
            @{name}
          </Text>
          {category_name && (
            <Text style={tw`text-gray-600`}>
              {category_name}
            </Text>
          )}
          {exp && (
            <Text style={tw`text-gray-500 text-sm`}>
              {exp} years experience
            </Text>
          )}
        </View>
        {per_minute_rate && (
          <View style={tw`items-end`}>
            <Text style={tw`text-sm text-gray-600`}>
              ₹{per_minute_rate}/min
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default UserMentionCard;
