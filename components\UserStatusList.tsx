import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { useMultipleUserStatus } from '@/hooks/useMultipleUserStatus';
import OnlineStatusIndicator from './OnlineStatusIndicator';

interface UserStatusListProps {
  userIds: string[];
  userNames?: Record<string, string>;
}

const UserStatusList: React.FC<UserStatusListProps> = ({ userIds, userNames = {} }) => {
  const { userStatuses, loading, error } = useMultipleUserStatus(userIds);

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading user statuses...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Error: {error}</Text>
      </View>
    );
  }

  const renderItem = ({ item: userId }: { item: string }) => {
    const status = userStatuses[userId];
    const userName = userNames[userId] || `User ${userId}`;
    
    return (
      <View style={styles.userRow}>
        <OnlineStatusIndicator userId={userId} size={10} />
        <Text style={styles.userName}>{userName}</Text>
        <Text style={styles.statusText}>
          {status?.isOnline ? 'Online' : 'Offline'}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>User Status</Text>
      <FlatList
        data={userIds}
        renderItem={renderItem}
        keyExtractor={(userId) => userId}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: 'red',
  },
  listContent: {
    paddingBottom: 16,
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
    flex: 1,
  },
  statusText: {
    fontSize: 14,
    color: '#666',
  },
});

export default UserStatusList;
