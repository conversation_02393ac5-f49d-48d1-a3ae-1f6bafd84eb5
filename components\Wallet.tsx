import React, { useState, useEffect, useRef } from "react";
import { Alert } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Button } from "react-native-paper";
import tw from "twrnc";
import { Text, View } from "moti";
import { Wallet2 } from "lucide-react-native";

export default function Wallet() {
  const [balance, setBalance] = useState(0);
  const [userId, setUserId] = useState<string | null>(null);
  const reconnectAttempts = useRef(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const getBalanceFromAsyncStorage = async () => {
    try {
      const storedBalance = await AsyncStorage.getItem("balance");
      if (storedBalance) {
        const parsedBalance = parseFloat(JSON.parse(storedBalance)) || 0;
        setBalance(parsedBalance);
        console.log("Retrieved balance from AsyncStorage:", parsedBalance);
        return true;
      }
    } catch (err) {
      console.error("Failed to get balance from AsyncStorage:", err);
    }
    return false;
  };

  const fetchBalanceHTTP = async (userId: string) => {
    const apis = [
      `https://api.nityasha.com/api/v1/users/${userId}`,
      `https://backup-api.nityasha.com/api/v1/users/${userId}`,
      `https://nityasha.vercel.app/api/v1/users/${userId}`
    ];

    for (const api of apis) {
      try {
        console.log(`Attempting HTTP fallback: ${api}`);
        const response = await fetch(api);
        if (!response.ok) continue;

        const data = await response.json();
        if (data.balance !== undefined) {
          const newBalance = parseFloat(data.balance) || 0;
          setBalance(newBalance);
          await AsyncStorage.setItem("balance", JSON.stringify(newBalance));
          return true;
        }
      } catch (err) {
        console.log(`Failed to fetch balance from ${api}`);
        continue;
      }
    }

    console.error("All HTTP endpoints failed");

    // Try to get balance from AsyncStorage if network requests fail
    const gotFromStorage = await getBalanceFromAsyncStorage();
    if (!gotFromStorage) {
      Alert.alert("Error", "Failed to fetch balance from all endpoints");
    }

    return gotFromStorage;
  };

  useEffect(() => {
    const fetchUserIdAndBalance = async () => {
      try {
        console.log("Fetching user session...");
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          setUserId(parsedSession.userId);
        } else {
          console.error("No user session found!");
        }

        // Try to load balance from AsyncStorage first
        await getBalanceFromAsyncStorage();
      } catch (error) {
        console.error("Fetching user session failed:", error);
        Alert.alert("Error", "Could not fetch user session.");
      }
    };

    fetchUserIdAndBalance();
  }, []);

  useEffect(() => {
    if (!userId) return;

    const connectWebSocket = () => {
      console.log("Connecting to WebSocket...");
      wsRef.current = new WebSocket("wss://balance-app-api.nityasha.com");

      let connectionTimeout = setTimeout(() => {
        if (wsRef.current?.readyState !== WebSocket.OPEN) {
          console.error("WebSocket connection timed out!");
          fetchBalanceHTTP(userId);
          wsRef.current?.close();
        }
      }, 5000);

      wsRef.current.onopen = () => {
        clearTimeout(connectionTimeout);
        reconnectAttempts.current = 0;

        intervalRef.current = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({ action: "GET_BALANCE", userId }));
          }
        }, 1000);
      };

      wsRef.current.onmessage = async (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.balance !== undefined) {
            const newBalance = parseFloat(data.balance) || 0;
            setBalance(newBalance);
            await AsyncStorage.setItem("balance", JSON.stringify(newBalance));
          }
        } catch (err) {
          console.error("WebSocket message parsing failed:", err);
        }
      };

      wsRef.current.onerror = async (event) => {
        console.error("🔥 WebSocket error:", event);
        await fetchBalanceHTTP(userId);
      };

      wsRef.current.onclose = async () => {
        console.warn("WebSocket closed. Attempting reconnect...");
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }

        if (reconnectAttempts.current < 5) {
          const httpSuccess = await fetchBalanceHTTP(userId);
          if (!httpSuccess) {
            setTimeout(connectWebSocket, Math.pow(2, reconnectAttempts.current) * 1000);
            reconnectAttempts.current++;
          }
        } else {
          console.error("Max reconnect attempts reached");
          // Continue with HTTP fallback even after max attempts
          const httpPollingInterval = setInterval(() => {
            fetchBalanceHTTP(userId);
          }, 5000); // Poll every 5 seconds

          // Store the interval for cleanup
          intervalRef.current = httpPollingInterval;
        }
      };
    };

    connectWebSocket();

    return () => {
      console.log("Cleaning up...");
      wsRef.current?.close();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [userId]);

  return (
    <View style={tw`items-start justify-center h-10 w-10  rounded-full`}>
      <Text style={tw`text-black text-base font-semibold`}>
        ₹ {balance >= 10000000
          ? `${Math.floor(balance / 10000000)}Cr`
          : balance >= 100000
            ? `${(balance / 100000).toFixed(balance % 100000 ? 1 : 0)}L`
            : balance >= 1000
              ? `${(balance / 1000).toFixed(balance % 1000 ? 1 : 0)}k`
              : Math.floor(balance)}
      </Text>
    </View>
  );
}

