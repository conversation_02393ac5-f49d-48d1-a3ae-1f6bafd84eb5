import React from 'react';
import { WebView } from 'react-native-webview';
import { SafeAreaView } from 'react-native';
import tw from 'twrnc';

interface WebViewScreenProps {
  route: {
    params: {
      url: string;
    };
  };
}

const WebViewScreen = ({ route }: WebViewScreenProps) => {
  const { url } = route.params;

  return (
    <SafeAreaView style={tw`flex-1`}>
      <WebView
        source={{ uri: url }}
        style={tw`flex-1`}
        javaScriptEnabled={true}
        domStorageEnabled={true}
      />
    </SafeAreaView>
  );
};

export default WebViewScreen;