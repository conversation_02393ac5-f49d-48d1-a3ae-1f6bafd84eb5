import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity, ActivityIndicator, Text } from 'react-native';
import tw from 'twrnc'; // Ensure you're using Tailwind CSS with React Native
import Headingsixth from '@/components/ui/Headingsixth'; // Import your Headingsixth component
import { ExternalLink, Eye } from 'lucide-react-native';

const WideVideoCard = ({ navigation }) => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const limit = 1; // Set the limit for the number of consultants displayed

    // Fetch consultants from API
    const fetchConsultants = async () => {
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/live/get'); // Replace with your API URL
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchConsultants(); // Initial fetch
        const intervalId = setInterval(fetchConsultants, 9000); // Set interval for 30 seconds

        return () => clearInterval(intervalId); // Cleanup interval on component unmount
    }, []);

    if (loading) {
        return (
            <View style={tw`flex items-center justify-center h-full`}>
                <ActivityIndicator size="large" color="#0000ff" />
            </View>
        );
    }

    if (error) {
        return (
            <View style={tw`flex items-center justify-center h-full`}>
                <Text style={tw`text-red-500`}>{error}</Text>
            </View>
        );
    }

    return (
        <View style={tw`flex items-center justify-between flex-row mt-2`}>

            {consultants.slice(0, limit).map((consultant, index) => {
                // Determine which avatar URL to use

                return (
                <TouchableOpacity  key={index} style={tw`flex items-center justify-center mt-5`}>
                    <View style={tw`flex items-center justify-center flex-row w-full bg-zinc-400 rounded-[10px]`}>
                      <Image source={{ uri: consultant.thumbnail }} style={tw`w-full h-[200px] rounded-[10px]`} />
                    </View>
                    <View style={tw`flex items-start justify-start w-full mt-2`}>
                      <Text style={[tw`text-red-500`, { fontFamily: 'Helvetica_bold' }]}>LIVE NOW</Text>
                      <Text style={[tw`text-black leading-[25px]`, { fontFamily: 'Helvetica_bold' }]}>{consultant.title > 40 ? `${consultant.title.substring(0, 40)}...` : consultant.title}</Text>
                      <View style={tw`flex items-center justify-between flex-row w-full`}>
                        <View style={tw`flex flex-row gap-1`}>
                          <Eye size={20} color={"#000"} />
                          <Text>3.4K watching’s</Text>
                        </View>
                        <ExternalLink size={20} color={"#000"} />
                      </View>
                    </View>
                  </TouchableOpacity>
                );
            })}
        </View>
    );
};

export default WideVideoCard;
