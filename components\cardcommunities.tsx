import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, ScrollView, Pressable, ActivityIndicator, Alert, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MotiView } from 'moti';
import { supabase } from '@/utils/supabase'; // Import your Supabase client
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from "twrnc";
import { Button, Searchbar } from 'react-native-paper';
import { debounce } from 'lodash';

interface Community {
  id: number;
  name: string;
  members: number;
  color: string;
  pfp?: string; // Profile picture URL
}

/**
 * Helper function to sort communities by member count in descending order
 */
const sortCommunitiesByMembers = (communities: Community[]) => {
  return [...communities].sort((a, b) => b.members - a.members);
};

/**
 * CommunityCard component displays individual community information and join/view actions
 */
const CommunityCard = ({ community, index }: { community: Community; index: number }) => {
  const navigation = useNavigation();
  const [isJoining, setIsJoining] = useState(false);
  const [isMember, setIsMember] = useState(false);

  useEffect(() => {
    checkMembershipStatus();
  }, []);

  /**
   * Checks if the current user is a member of this community
   */
  const checkMembershipStatus = async () => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      // Query Supabase to check membership status
      const { data, error } = await supabase
        .from('community_members')
        .select()
        .eq('community_id', community.id)
        .eq('user_id', userId)
        .single();
      setIsMember(!!data); // Convert to boolean
    } catch (error) {
      console.error('Error checking membership:', error);
      setIsMember(false);
    }
  };

  /**
   * Navigates to the community view screen
   */
  const handleViewCommunity = () => {
    navigation.navigate('CommunityView', { communityId: community.id });
  };

  /**
   * Handles the join community action
   */
  const handleJoin = async () => {
    setIsJoining(true);
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) {
        Alert.alert('Please login to join communities');
        return;
      }
      const parsedSession = JSON.parse(userSession);
      const userId = parsedSession?.userId || userSession;

      // Insert new member into community_members table
      const { error } = await supabase
        .from('community_members')
        .insert([
          {
            community_id: community.id,
            user_id: userId,
            role: 'member',
          },
        ]);
      if (error) {
        if (error.code === '23505') {
          // Unique violation error code
          Alert.alert('You are already a member of this community');
        } else {
          console.error('Error joining community:', error.message);
          Alert.alert('Failed to join community');
        }
      } else {
        setIsMember(true);
      }
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('An error occurred');
    } finally {
      setIsJoining(false);
    }
  };

  return (
    <MotiView
      from={{ opacity: 0, translateY: 20 }}
      animate={{ opacity: 1, translateY: 0 }}
      transition={{ delay: index * 100, type: "timing" }}
      style={{
        flexDirection: "row",
        alignItems: "center",
        padding: 16,
        width: "100%"
      }}
    >
      {/* Community Avatar */}
      <View
        style={{
          width: 48,
          height: 48,
          borderRadius: 15,
          marginRight: 16,
          backgroundColor: community.color,
          alignItems: "center",
          overflow: "hidden",
          justifyContent: "center"
        }}
      >
        <Image source={{ uri: `${community.pfp}` }} style={tw`w-full h-full`} />
      </View>

      {/* Community Info */}
      <View style={{ flex: 1 }}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: "Familiar_Pro",
            color: "#1f2937",
            marginBottom: 4
          }}
        >
          {community.name}
        </Text>
        <Text style={{ fontSize: 14, color: "#6b7280" }}>
          {community.members} members
        </Text>
      </View>

      {/* Join/View Button */}
      <Pressable
        onPress={isMember ? handleViewCommunity : handleJoin}
        disabled={isJoining}
      >
        {isJoining ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Button mode="contained">{isMember ? "View" : "Join"}</Button>
        )}
      </Pressable>
    </MotiView>
  );
};

/**
 * PopularCommunities component displays a list of communities with real-time updates
 */
const PopularCommunities = () => {
  const [communities, setCommunities] = useState<Community[]>([]);
  const [filteredCommunities, setFilteredCommunities] = useState<Community[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const ITEMS_PER_PAGE = 20;

  // Improved search with debouncing
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      if (!query.trim()) {
        setFilteredCommunities(communities.slice(0, page * ITEMS_PER_PAGE));
        return;
      }

      const searchTerms = query.toLowerCase().split(' ');
      const filtered = communities.filter(community => {
        const communityName = community.name.toLowerCase();
        return searchTerms.every(term => communityName.includes(term));
      });

      setFilteredCommunities(filtered);
    }, 300),
    [communities, page]
  );

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  const handleScroll = ({ nativeEvent }) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const isEndReached = (layoutMeasurement.height + contentOffset.y) >= (contentSize.height - 20);
    
    if (isEndReached && !loadingMore && !searchQuery) {
      loadMoreCommunities();
    }
  };

  const loadMoreCommunities = async () => {
    if (loadingMore || communities.length <= page * ITEMS_PER_PAGE) return;

    setLoadingMore(true);
    setPage(prevPage => prevPage + 1);
    
    const nextBatch = communities.slice(0, (page + 1) * ITEMS_PER_PAGE);
    setFilteredCommunities(nextBatch);
    setLoadingMore(false);
  };

  useEffect(() => {
    // Fetch initial communities data with caching
    const fetchCommunities = async () => {
      try {
        // Try to get cached data first
        const cachedData = await AsyncStorage.getItem('cachedCommunities');
        if (cachedData) {
          const { data: cached, timestamp } = JSON.parse(cachedData);
          const isExpired = Date.now() - timestamp > 5 * 60 * 1000; // 5 minutes cache
          
          if (!isExpired) {
            setCommunities(cached);
            setFilteredCommunities(cached);
            setLoading(false);
            return;
          }
        }

        // Fetch fresh data if cache is expired or doesn't exist
        const { data, error } = await supabase
          .from('communities')
          .select(`
            id,
            name,
            color,
            pfp,
            members:community_members(count)
          `);

        if (error) throw error;

        // Format and sort the data
        const formattedData = data.map((community) => ({
          id: community.id,
          name: community.name,
          color: community.color,
          pfp: community.pfp,
          members: community.members[0]?.count || 0,
        }));

        const sortedCommunities = sortCommunitiesByMembers(formattedData);
        
        // Update state and cache the new data
        setCommunities(sortedCommunities);
        setFilteredCommunities(sortedCommunities);
        await AsyncStorage.setItem('cachedCommunities', JSON.stringify({
          data: sortedCommunities,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.error('❌ Error fetching communities:', error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchCommunities();

    const communitySubscription = supabase
      .channel('public:communities')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'communities' },
        (payload) => {
          setCommunities((prev) => {
            let updatedCommunities = [...prev];

            if (payload.eventType === 'INSERT') {
              const newCommunity = { ...payload.new, members: 0 };
              updatedCommunities.push(newCommunity);
            }
            if (payload.eventType === 'UPDATE') {
              updatedCommunities = prev.map((comm) =>
                comm.id === payload.new.id ? { ...comm, ...payload.new } : comm
              );
            }
            if (payload.eventType === 'DELETE') {
              updatedCommunities = prev.filter((comm) => comm.id !== payload.old.id);
            }

            return sortCommunitiesByMembers(updatedCommunities);
          });
        }
      )
      .subscribe();

    const memberSubscription = supabase
      .channel('public:community_members')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'community_members' },
        async (payload) => {
          const { data, error } = await supabase
            .from('community_members')
            .select('count')
            .eq('community_id', payload.new?.community_id || payload.old?.community_id)
            .single();
          if (!error && data) {
            setCommunities((prev) => {
              const updatedCommunities = prev.map((comm) => {
                if (
                  comm.id ===
                  (payload.new?.community_id || payload.old?.community_id)
                ) {
                  return { ...comm, members: data.count };
                }
                return comm;
              });

              return sortCommunitiesByMembers(updatedCommunities);
            });
          }
        }
      )
      .subscribe();

    return () => {
      communitySubscription.unsubscribe();
      memberSubscription.unsubscribe();
    };
  }, []);

  return (
    <MotiView
      from={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'timing', duration: 300 }}
      style={{ borderRadius: 16, width: '100%', height: '100%' }}
    >
      <View>
        <Searchbar
          placeholder="Search communities..."
          value={searchQuery}
          onChangeText={handleSearch}
        />      
      </View>
      {loading ? (
        <ActivityIndicator size="large" color="#1f2937" />
      ) : (
        <ScrollView 
          style={{ width: '100%' }}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          {filteredCommunities.map((community, index) => (
            <CommunityCard key={community.id} community={community} index={index} />
          ))}
          {loadingMore && (
            <ActivityIndicator 
              size="small" 
              color="#1f2937" 
              style={{ marginVertical: 16 }}
            />
          )}
        </ScrollView>
      )}
    </MotiView>
  );
};

const CardCommunities = PopularCommunities;
export default CardCommunities;
