import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity, ActivityIndicator, Text, ScrollView, Modal, Button, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';

const TopConsultants = () => {
    const navigation = useNavigation();
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [userId, setUserId] = useState(null);
    const limit = 4;

    // Check login status and fetch user ID
    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (userSession) {
                    setIsLoggedIn(true);
                    const id = await AsyncStorage.getItem('userId');
                    setUserId(id); // Store userId in state
                    if (id) {
                        await updateUserStatus(id, 'online');
                    }
                    const ip = await fetchUserIP();
                    if (ip) {
                        await sendUserIP(ip, id);
                    }
                } else {
                    navigation.navigate('Welcome');
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        checkLoginStatus(); 
    }, [navigation]);

    // Fetch consultants from API
    const fetchConsultants = async () => {
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/users/get');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchConsultants(); 
        const intervalId = setInterval(fetchConsultants, 9000);
        return () => clearInterval(intervalId);
    }, []);

    // Function to check user balance and navigate to chat
    const checkBalanceAndChat = async (consultantId, consultantName) => {
        if (!userId) return;

        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
            if (!response.ok) {
                throw new Error('Failed to fetch user balance');
            }
            const userData = await response.json();
            const userBalance = userData.balance; // Assuming the API response has a balance field

            if (userBalance > 0) {
                navigation.navigate('Chat', { consultantId, consultantName });
            } else {
                setIsModalVisible(true); // Open payment modal
            }
        } catch (err) {
            Alert.alert('Error', err.message);
        }
    };

    // Function to handle payment
    const handlePayment = () => {
        // Implement payment logic here
        setIsModalVisible(false); // Close modal after payment logic
    };

    if (loading) {
        return (
            <View style={tw`flex items-center justify-center h-full`}>
                <ActivityIndicator size="large" color="#0000ff" />
            </View>
        );
    }

    if (error) {
        return (
            <View style={tw`flex items-center justify-center h-full`}>
                <Text style={tw`text-red-500`}>{error}</Text>
            </View>
        );
    }

    return (
        <View style={tw`flex items-center justify-center`}>
            <ScrollView horizontal style={tw`flex gap-3`} showsHorizontalScrollIndicator={false}>
                {consultants.slice(0, limit).map((consultant, index) => {
                    const avatarUrl = consultant.pfp || `https://ui-avatars.com/api/?name=${encodeURIComponent(consultant.username)}`;

                    return (
                        <View key={index} style={tw`flex mx-1 items-center justify-between py-5 mt-3 flex-row border-2 rounded-xl`}>
                            <View style={tw`flex items-center flex-row gap-2 px-3`}>
                                <View style={tw`flex items-center justify-center bg-black rounded-full w-[50px] h-[50px]`}>
                                    {consultant.pfp ? (
                                        <Image
                                            source={{ uri: avatarUrl }}
                                            style={tw`w-[68px] h-[68px] rounded-full border-2 border-[#FF0000]`}
                                        />
                                    ) : (
                                        <View style={tw`w-[68px] h-[68px] rounded-full bg-stone-300 items-center justify-center`}>
                                            <Text style={[tw`text-gray-500 font-bold text-2xl`, { fontFamily: 'Helvetica_bold' }]}>
                                                {encodeURIComponent(consultant.username.substring(0, 2).toUpperCase())}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                                <View style={tw`flex px-2`}>
                                    <Text style={tw`py-1 font-semibold text-[15px]`}>Coding XX</Text>
                                    <Text style={tw`flex font-semibold`}>A Gamer For Everyone</Text>
                                </View>
                            </View>
                            <TouchableOpacity
                                onPress={() => checkBalanceAndChat(consultant.id, consultant.username)} 
                                style={tw`flex items-center justify-center py-2 px-5 bg-black rounded-xl mx-3`}
                            >
                                <Text style={tw`text-white font-semibold`}>Chat</Text>
                            </TouchableOpacity>
                        </View>
                    );
                })}
            </ScrollView>

            {/* Payment Modal */}
            <Modal
                visible={isModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setIsModalVisible(false)}
            >
                <View style={tw`flex-1 justify-center items-center bg-black bg-opacity-50`}>
                    <View style={tw`bg-white rounded-lg p-5 w-80`}>
                        <Text style={tw`text-lg font-bold mb-4`}>Insufficient Balance</Text>
                        <Text style={tw`mb-4`}>You do not have enough balance to chat with this consultant.</Text>
                        <Button title="Add Funds" onPress={handlePayment} />
                        <Button title="Cancel" onPress={() => setIsModalVisible(false)} color="red" />
                    </View>
                </View>
            </Modal>
        </View>
    );
};

export default TopConsultants;
