import React, { useEffect, useState } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    Alert,
    TextInput,
    FlatList,
    KeyboardAvoidingView,
    Platform
} from "react-native";
import tw from "twrnc";
import { supabase } from "@/utils/supabase";
import { Send } from "lucide-react-native";
import { Image } from "moti";

const Posts = ({ userId }) => {
  const [posts, setPosts] = useState([]);
  const [newPostContent, setNewPostContent] = useState("");
  const [revealedSpoilers, setRevealedSpoilers] = useState({});
  const [userProfile, setUserProfile] = useState({
    pfp: "https://ui-avatars.com/api/?name=Anonymous",
    username: "Anonymous"
  });

  // Add this useEffect to fetch user profile
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!userId) return;
      try {
        const response = await fetch(
          `https://nityasha.vercel.app/api/v1/users/${userId}`
        );
        const userData = await response.json();
        setUserProfile({
          pfp: userData.pfp || "https://ui-avatars.com/api/?name=Anonymous",
          username: userData.username || "Anonymous"
        });
      } catch (error) {
        console.error("Error fetching user profile:", error);
      }
    };
    fetchUserProfile();
  }, [userId]);

  // Format text function to handle markdown-like syntax
  const formatText = (text, postId) => {
    const parts = text.split(/(\*\*.*?\*\*|\`.*?\`|\|\|.*?\|\|)/g);
    
    return parts.map((part, index) => {
      if (part.startsWith('**') && part.endsWith('**')) {
        // Bold text
        return (
          <Text key={index} style={tw`font-bold`}>
            {part.slice(2, -2)}
          </Text>
        );
      } else if (part.startsWith('`') && part.endsWith('`')) {
        // Code text
        return (
          <Text key={index} style={tw`bg-gray-200 px-1 rounded font-mono`}>
            {part.slice(1, -1)}
          </Text>
        );
      } else if (part.startsWith('||') && part.endsWith('||')) {
        // Spoiler text
        const spoilerContent = part.slice(2, -2);
        const isSpoilerRevealed = revealedSpoilers[`${postId}-${index}`];
        
        return (
          <TouchableOpacity
            key={index}
            onPress={() => {
              setRevealedSpoilers(prev => ({
                ...prev,
                [`${postId}-${index}`]: !prev[`${postId}-${index}`]
              }));
            }}
          >
            <Text style={[
              tw`bg-gray-800`,
              isSpoilerRevealed ? tw`bg-transparent` : tw`text-transparent`
            ]}>
              {spoilerContent}
            </Text>
          </TouchableOpacity>
        );
      }
      return <Text key={index}>{part}</Text>;
    });
  };

  // Fetch posts created by the logged-in user
  useEffect(() => {
    const fetchUserPosts = async () => {
      if (!userId) return;
      try {
        const { data, error } = await supabase
          .from("posts")
          .select("*")
          .eq("user_id", userId)
          .order("created_at", { ascending: false });
        if (error) throw error;

        // Fetch user details for each post
        const postsWithUserDetails = await Promise.all(
          data.map(async (post) => {
            try {
              const response = await fetch(
                `https://nityasha.vercel.app/api/v1/users/${post.user_id}`
              );
              const userData = await response.json();
              return {
                ...post,
                author_name: userData.username || "Anonymous",
                pfp: userData.pfp || "https://ui-avatars.com/api/?name=Anonymous"
              };
            } catch (error) {
              console.error("Error fetching user data:", error);
              return { ...post, author_name: "Anonymous" };
            }
          })
        );
        setPosts(postsWithUserDetails || []);
      } catch (error) {
        console.error("Error fetching user posts:", error);
        Alert.alert("Error", "Failed to load your posts.");
      }
    };
    fetchUserPosts();
  }, [userId]);

  // Handle creating a new post
  const handleCreatePost = async () => {
    if (!newPostContent.trim() || !userId) {
      Alert.alert("Error", "Please enter some content before posting.");
      return;
    }
    try {
      // Create the post
      const { data: newPost, error } = await supabase
        .from("posts")
        .insert([{
          user_id: userId,
          content: newPostContent.trim()
        }])
        .select()
        .single();

      if (error) throw error;

      // Add user details to the new post
      const newPostWithDetails = {
        ...newPost,
        author_name: userProfile.username,
        pfp: userProfile.pfp
      };

      // Update posts state by adding the new post at the beginning
      setPosts(currentPosts => [newPostWithDetails, ...currentPosts]);

      // Clear the input field
      setNewPostContent("");
      
    } catch (error) {
      console.error("Error creating post:", error);
      Alert.alert("Error", `Failed to create post: ${error.message}`);
    }
  };

  // Render individual post
  const renderPost = ({ item }) => (
    <View style={tw`bg-[#F5FCF5] border-2 border-[#EEEEEE] p-3 mb-2 rounded-lg`}>
      <View style={tw`w-full flex-row items-center justify-between`}>
        <View style={tw`flex-row gap-2`}>
          <View style={tw`bg-black w-10 h-10 rounded-[10px] overflow-hidden`}>
            <Image source={{ uri: item.pfp }} style={tw`w-full h-full`} />
          </View>
          <View>
            <Text style={[tw`text-black`,{fontFamily:'Helvetica_bold'}]}>{item.author_name || "Anonymous"}</Text>
            <Text style={[tw`text-[#828282]`,{fontFamily:'Helvetica_bold'}]}>{new Date(item.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</Text>
          </View>
        </View>
      </View>
      <Text style={[tw`text-black mt-1`,{fontFamily:'GoogleSans-Medium'}]}>
        {formatText(item.content, item.id)}
      </Text>
    </View>
  );

  return (
    <View style={tw`flex-1`}>
      {/* Posts List */}
      <FlatList
        data={posts}
        keyExtractor={(post) => post.id.toString()}
        renderItem={renderPost}
        ListEmptyComponent={
          <Text style={tw`text-black text-center`}>No posts yet.</Text>
        }
        contentContainerStyle={tw`p-2 pb-20`} // Added bottom padding for input
      />

      {/* Fixed Input Section at Bottom */}
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
        style={tw`absolute bottom-0 left-0 right-0 bg-white`}
      >
        <View style={tw`border-t border-gray-200 p-2`}>
          <View style={tw`flex-row items-center gap-2 bg-[#F5FCF5] border-2 border-[#EEEEEE] rounded-xl p-2`}>
            <View style={tw`bg-black w-8 h-8 rounded-full overflow-hidden`}>
              <Image 
                source={{ uri: userProfile.pfp }} 
                style={tw`w-full h-full`} 
              />
            </View>
            <TextInput
              placeholder="Write your post here"
              multiline
              maxLength={200}
              value={newPostContent}
              onChangeText={setNewPostContent}
              style={[
                tw`flex-1 max-h-20 text-black`,
                { fontFamily: "Helvetica_bold" }
              ]}
              placeholderTextColor="#666"
            />
            <TouchableOpacity
              style={tw`bg-black rounded-full p-2`}
              onPress={handleCreatePost}
            >
              <Send size={20} color="white" />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

export default Posts;













