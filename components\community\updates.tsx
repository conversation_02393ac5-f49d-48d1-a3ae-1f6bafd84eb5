import { View } from "react-native";
import tw from "twrnc";
import { Skeleton } from "moti/skeleton";

const UpdateSkeleton = () => {
  return (
    <View style={tw`p-4 bg-white rounded-lg mb-4`}>
      <View style={tw`flex-row items-center mb-3`}>
        <Skeleton circle width={40} height={40} />
        <View style={tw`ml-3`}>
          <Skeleton width={120} height={15} />
          <Skeleton width={80} height={12} style={tw`mt-1`} />
        </View>
      </View>
      <Skeleton width="100%" height={60} />
    </View>
  );
};

export default UpdateSkeleton;