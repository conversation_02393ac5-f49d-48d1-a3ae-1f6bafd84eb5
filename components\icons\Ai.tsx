import { View, Text } from 'react-native'
import React from 'react'
import Svg, { Path } from 'react-native-svg'
import tw from 'twrnc';

export default function Ai({ color = "#000" , size = 20 }) {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 19" fill="none">
<Path fill-rule="evenodd" clip-rule="evenodd" d="M5.47975 9.47972L7.02569 4.84189H7.97437L9.52032 9.47972L14.1581 11.0257V11.9743L9.52032 13.5203L7.97437 18.1581H7.02569L5.47975 13.5203L0.841919 11.9743V11.0257L5.47975 9.47972Z" fill="black"/>
<Path fill-rule="evenodd" clip-rule="evenodd" d="M14.2297 3.22972L15.0257 0.841888H15.9744L16.7703 3.22972L19.1581 4.02566V4.97434L16.7703 5.77029L15.9744 8.15812H15.0257L14.2297 5.77029L11.8419 4.97434V4.02566L14.2297 3.22972Z" fill="black"/>
</Svg>
  )
}
