import React from 'react';
import Svg, { Path } from 'react-native-svg';

const Bell = ({ color = "#39434F", size = 12 }) => {
  const height = (size / 12) * 16; // Maintaining the aspect ratio (12 width : 16 height)

  return (
    <Svg width={20} height={20} viewBox="0 0 12 16" fill="none">
      <Path
        d="M10 6.16634C10 5.10548 9.57858 4.08806 8.82844 3.33791C8.07829 2.58777 7.06088 2.16634 6.00001 2.16634C4.93914 2.16634 3.92173 2.58777 3.17158 3.33791C2.42144 4.08806 2.00001 5.10548 2.00001 6.16634V11.4997H10V6.16634ZM11.3333 11.9443L11.6 12.2997C11.6372 12.3492 11.6598 12.4081 11.6653 12.4697C11.6709 12.5314 11.6592 12.5934 11.6315 12.6487C11.6038 12.7041 11.5612 12.7507 11.5086 12.7832C11.4559 12.8158 11.3952 12.833 11.3333 12.833H0.666677C0.604773 12.833 0.544092 12.8158 0.491433 12.7832C0.438775 12.7507 0.396219 12.7041 0.368535 12.6487C0.34085 12.5934 0.329131 12.5314 0.334691 12.4697C0.34025 12.4081 0.362868 12.3492 0.40001 12.2997L0.666677 11.9443V6.16634C0.666677 4.75185 1.22858 3.3953 2.22877 2.39511C3.22897 1.39491 4.58552 0.833008 6.00001 0.833008C7.4145 0.833008 8.77105 1.39491 9.77125 2.39511C10.7714 3.3953 11.3333 4.75185 11.3333 6.16634V11.9443ZM4.33334 13.4997H7.66668C7.66668 13.9417 7.49108 14.3656 7.17852 14.6782C6.86596 14.9907 6.44204 15.1663 6.00001 15.1663C5.55798 15.1663 5.13406 14.9907 4.8215 14.6782C4.50894 14.3656 4.33334 13.9417 4.33334 13.4997V13.4997Z"
        fill={color}  // Use color prop here
      />
    </Svg>
  );
};

export default Bell;
