import { View, Text } from 'react-native'
import React from 'react'
import { Svg, <PERSON>, G, Defs, ClipPath, Rect } from 'react-native-svg'
export default function Discovericon({ color = "#000" , size = 20 }) {
    return (
        <Svg width={size} height={size} viewBox="0 0 16 16" fill="none" color={color}>
            <G clip-path="url(#clip0_3303_6)">
                <Path d="M8 1.14286C9.35622 1.14286 10.682 1.54502 11.8096 2.29849C12.9373 3.05197 13.8162 4.12291 14.3352 5.37588C14.8542 6.62886 14.99 8.00761 14.7254 9.33776C14.4608 10.6679 13.8077 11.8897 12.8487 12.8487C11.8897 13.8077 10.6679 14.4608 9.33776 14.7254C8.00761 14.99 6.62887 14.8542 5.37589 14.3352C4.12291 13.8162 3.05197 12.9373 2.2985 11.8096C1.54502 10.682 1.14286 9.35621 1.14286 8C1.14286 6.18137 1.86531 4.43723 3.15127 3.15127C4.43723 1.8653 6.18138 1.14286 8 1.14286ZM8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842854 10.1217 0 8 0Z" fill={color} />
                <Path d="M11.1475 5.5575C11.3422 5.36282 11.3422 5.04718 11.1475 4.8525C10.9528 4.65782 10.6372 4.65782 10.4425 4.8525L9.145 6.15C8.84534 6.44966 8.38343 6.4931 7.95979 6.50405C7.88805 6.50591 7.81624 6.51292 7.7449 6.52516C7.43918 6.57761 7.15723 6.72355 6.93789 6.94289C6.71855 7.16223 6.57261 7.44418 6.52016 7.7499C6.50792 7.82124 6.50091 7.89305 6.49905 7.96478C6.4881 8.38843 6.44466 8.85034 6.145 9.15L4.8525 10.4425C4.65782 10.6372 4.65782 10.9528 4.8525 11.1475C5.04718 11.3422 5.36282 11.3422 5.5575 11.1475L6.855 9.85C7.15466 9.55034 7.61657 9.5069 8.04022 9.49595C8.11195 9.49409 8.18376 9.48708 8.2551 9.47484C8.56082 9.42239 8.84277 9.27645 9.06211 9.05711C9.28145 8.83777 9.42739 8.55582 9.47984 8.2501C9.49208 8.17876 9.49909 8.10695 9.50095 8.03521C9.5119 7.61157 9.55534 7.14966 9.855 6.85L11.1475 5.5575ZM8.5 8C8.5 8.09889 8.47068 8.19556 8.41574 8.27779C8.36079 8.36001 8.2827 8.4241 8.19134 8.46194C8.09998 8.49978 7.99945 8.50969 7.90245 8.49039C7.80546 8.4711 7.71637 8.42348 7.64645 8.35355C7.57652 8.28363 7.5289 8.19454 7.50961 8.09755C7.49031 8.00056 7.50022 7.90002 7.53806 7.80866C7.5759 7.7173 7.63999 7.63921 7.72221 7.58427C7.80444 7.52932 7.90111 7.5 8 7.5C8.13261 7.5 8.25979 7.55268 8.35355 7.64645C8.44732 7.74021 8.5 7.86739 8.5 8Z" fill={color} />
                <Path d="M7 8C7 8.26522 7.10536 8.51957 7.29289 8.70711C7.48043 8.89464 7.73478 9 8 9C8.26522 9 8.51957 8.89464 8.70711 8.70711C8.89464 8.51957 9 8.26522 9 8C9 7.73478 8.89464 7.48043 8.70711 7.29289C8.51957 7.10536 8.26522 7 8 7C7.73478 7 7.48043 7.10536 7.29289 7.29289C7.10536 7.48043 7 7.73478 7 8Z" fill={color} />
            </G>
            <Defs>
                <ClipPath id="clip0_3303_6">
                    <Rect width="16" height="16" fill="white" />
                </ClipPath>
            </Defs>
        </Svg>
    )
}
