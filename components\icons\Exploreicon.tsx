import React from 'react';
import Svg, { Path, ClipPath, Rect } from 'react-native-svg';

const CustomExploreIcon = ({ color = 'black', size = 24 }) => (
    <Svg
        width={size}
        height={size}
        viewBox="0 0 25 25"
        fill="none"

    >
        <ClipPath id="clip0_3_27">
            <Rect
                width="24"
                height="24"
                fill="white"
                transform="translate(0.75 0.5)"
            />
        </ClipPath>
        <Path
            d="M12.75 24.5C9.5674 24.5 6.51516 23.2357 4.26472 20.9853C2.01428 18.7348 0.75 15.6826 0.75 12.5C0.75 9.3174 2.01428 6.26516 4.26472 4.01472C6.51516 1.76428 9.5674 0.5 12.75 0.5C15.9326 0.5 18.9848 1.76428 21.2353 4.01472C23.4857 6.26516 24.75 9.3174 24.75 12.5C24.75 15.6826 23.4857 18.7348 21.2353 20.9853C18.9848 23.2357 15.9326 24.5 12.75 24.5ZM10.206 9.956L5.958 19.292L15.294 15.044L19.542 5.708L10.206 9.956ZM12.75 13.7C12.4317 13.7 12.1265 13.5736 11.9015 13.3485C11.6764 13.1235 11.55 12.8183 11.55 12.5C11.55 12.1817 11.6764 11.8765 11.9015 11.6515C12.1265 11.4264 12.4317 11.3 12.75 11.3C13.0683 11.3 13.3735 11.4264 13.5985 11.6515C13.8236 11.8765 13.95 12.1817 13.95 12.5C13.95 12.8183 13.8236 13.1235 13.5985 13.3485C13.3735 13.5736 13.0683 13.7 12.75 13.7Z"
            fill={color}
        />
    </Svg>
);

export default CustomExploreIcon;
