import React from 'react';
import Svg, { Path } from 'react-native-svg';

const ProfileIcon = ({ color = "#7B828A", size = 17 }) => {

  return (
    <Svg width={20} height={20} viewBox="0 0 17 21" fill="none">
      <Path
        d="M0.199997 21C0.199997 18.8783 1.04285 16.8434 2.54314 15.3431C4.04343 13.8429 6.07827 13 8.2 13C10.3217 13 12.3566 13.8429 13.8569 15.3431C15.3571 16.8434 16.2 18.8783 16.2 21H14.2C14.2 19.4087 13.5679 17.8826 12.4426 16.7574C11.3174 15.6321 9.7913 15 8.2 15C6.6087 15 5.08257 15.6321 3.95736 16.7574C2.83214 17.8826 2.2 19.4087 2.2 21H0.199997ZM8.2 12C4.885 12 2.2 9.315 2.2 6C2.2 2.685 4.885 0 8.2 0C11.515 0 14.2 2.685 14.2 6C14.2 9.315 11.515 12 8.2 12ZM8.2 10C10.41 10 12.2 8.21 12.2 6C12.2 3.79 10.41 2 8.2 2C5.99 2 4.2 3.79 4.2 6C4.2 8.21 5.99 10 8.2 10Z"
        fill={color}  // Use color prop here
      />
    </Svg>
  );
};

export default ProfileIcon;
