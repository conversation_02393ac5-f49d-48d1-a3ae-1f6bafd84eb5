import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  Alert,
  AppState,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import tw from 'twrnc';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase';
import { ref, onValue, set, off, update } from 'firebase/database';
import SokitOnlineStatusIndicator from '@/components/SokitOnlineStatusIndicator';
import { MessageCircleOff } from 'lucide-react-native';
import { MotiView } from 'moti';
import * as Notifications from 'expo-notifications';

// Firebase chat collection prefixes
const FIREBASE_CHAT_PREFIX = 'user_chats';
const FIREBASE_MESSAGES_PREFIX = 'user_messages';

// Move generateUUID outside the component
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Define message type
interface ChatMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  isUser?: boolean; // For UI rendering
  status?: 'sent' | 'delivered' | 'read' | 'error';
  isRead?: boolean;
  readAt?: number;
}

interface FirebaseMessage {
  id: string;
  text: string;
  timestamp: number;
  senderId: string;
  receiverId: string;
  status?: 'sent' | 'delivered' | 'read' | 'error';
  isRead?: boolean;
  readAt?: number;
  deliveredAt?: number | null;
}

interface UserStatus {
  state: 'online' | 'offline';
  lastSeen: number;
  isTyping?: boolean;
  typingTimestamp?: number;
  typingInChatWith?: string; // ID of the user they're typing to
}

// Define route params interface
interface RouteParams {
  accountData?: {
    id: string;
    name: string;
    logo: string;
    online?: boolean;
    verified?: boolean;
  };
  // Direct parameters from UserInbox
  otherUserId?: string;
  name?: string;
  online?: boolean;
}

interface NavigationProps {
  navigation: any;
  route: { params?: RouteParams };
}

// Add this function at the top level
const sendPushNotification = async (pushToken: string, messageText: string, senderName: string) => {
  try {
    const notificationMessage = {
      to: pushToken,
      sound: 'default',
      title: senderName,
      body: messageText,
      data: { type: 'chat_message' },
    };

    await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notificationMessage),
    });
  } catch (error) {
    console.error('Error sending push notification:', error);
  }
};

// Add this function to fetch user's push token
const fetchUserPushToken = async (userId: string) => {
  try {
    const response = await fetch(`https://api.nityasha.com/api/v1/users/${userId}`);
    const data = await response.json();
    return data.pushtoken;
  } catch (error) {
    console.error('Error fetching user push token:', error);
    return null;
  }
};

export default function App({ navigation, route }: NavigationProps) {
  const [userId, setUserId] = useState<string | null>(null);
  const { accountData, otherUserId, name, online } = route.params || {};
  console.log('UserChatInbox route params:', { accountData, otherUserId, name, online, fullRoute: route })
  // Create a compatible accountData object if direct parameters are provided
  const effectiveAccountData = accountData || (otherUserId ? {
    id: otherUserId,
    name: name || 'User',
    logo: `https://api.dicebear.com/7.x/initials/png?seed=${name || 'Unknown'}`,
    online: online
  } : undefined);

  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const flatListRef = useRef<FlatList | null>(null);
  const viewableItemsRef = useRef<string[]>([]);
  const [isReceiverOnline, setIsReceiverOnline] = useState<boolean>(online === true);
  const [lastSeen, setLastSeen] = useState<number | null>(null);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [showMenu, setShowMenu] = useState<boolean>(false);
  const [isReceiverTyping, setIsReceiverTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Queue to store messages that should be marked as read when both users are online
  const [messageReadQueue, setMessageReadQueue] = useState<{chatRoomId: string, messageId: string}[]>([]);

  // Add notification sent state to prevent duplicate notifications
  const [notificationSent, setNotificationSent] = useState(false);

  // Get userId from AsyncStorage and set up online status
  useEffect(() => {
    const getUserId = async () => {
      try {
        const userDetails = await AsyncStorage.getItem('userSession');
        if (userDetails) {
          const parsedDetails = JSON.parse(userDetails);
          // Extract just the username as the ID since that's what we need
          const id = parsedDetails?.userId || userDetails;
          setUserId(id);

          // Set up online status for current user
          updateUserOnlineStatus(id, true);
        } else {
          console.error('No user details found');
          Alert.alert('Error', 'Please login again');
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error getting user details:', error);
        Alert.alert('Error', 'Failed to get user information');
        navigation.goBack();
      }
    };

    getUserId();

    // Set up app state listener for online/offline status
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Clean up when component unmounts
    return () => {
      subscription.remove();

      // Clear any pending app state timeout
      if (appStateTimeoutRef.current) {
        clearTimeout(appStateTimeoutRef.current);
      }

      // Clear any typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }

      if (userId) {
        // Reset typing status and set to offline if the app is actually closing
        updateTypingStatus(false);
        updateUserOnlineStatus(userId, false);
      }
    };
  }, [userId, navigation]);

  // Handle app state changes for online status with debounce
  const appStateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentAppStateRef = useRef<string>(AppState.currentState);

  const handleAppStateChange = (nextAppState: string) => {
    if (!userId) return;

    // Clear any pending timeout
    if (appStateTimeoutRef.current) {
      clearTimeout(appStateTimeoutRef.current);
    }

    // Set a timeout to update status after a short delay
    appStateTimeoutRef.current = setTimeout(() => {
      if (nextAppState === 'active') {
        updateUserOnlineStatus(userId, true);
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Add a delay before marking offline to prevent flicker
        setTimeout(() => {
          // Double-check the app is still in background
          if (AppState.currentState !== 'active') {
            updateUserOnlineStatus(userId, false);
          }
        }, 5000); // 5 second delay before marking offline
      }

      currentAppStateRef.current = nextAppState;
    }, 1000); // 1 second debounce
  };

  // Format last seen time
  const formatLastSeen = (timestamp: number): string => {
    const now = new Date();
    const lastSeenDate = new Date(timestamp);
    const diffMs = now.getTime() - lastSeenDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hr ago`;
    if (diffDays === 1) return 'yesterday';
    return `${diffDays} days ago`;
  };

  // Update user online status in Firebase
  const updateUserOnlineStatus = (uid: string, isOnline: boolean) => {
    if (!uid) return;

    // Get the other user's ID from the route params
    const otherUserId = effectiveAccountData?.id;
    if (!otherUserId) return;

    // 1. Update general status
    const userStatusRef = ref(db, `users/${uid}/status`);
    const status = {
      state: isOnline ? 'online' : 'offline',
      lastSeen: Date.now(),
      isTyping: false, // Reset typing status when changing online status
      typingInChatWith: null // Reset typing chat when changing online status
    };

    set(userStatusRef, status).catch(error => {
      console.error('Error updating online status:', error);
    });

    // 2. Update visibility to specific user
    // When entering chat, make current user visible to the other user
    if (isOnline) {
      // Set visibility to the specific user we're chatting with
      const visibilityRef = ref(db, `user_visibility/${uid}/visible_to/${otherUserId}`);
      set(visibilityRef, {
        visible: true,
        timestamp: Date.now()
      }).catch(error => {
        console.error('Error updating visibility status:', error);
      });
    } else {
      // When leaving chat, remove visibility
      const visibilityRef = ref(db, `user_visibility/${uid}/visible_to/${otherUserId}`);
      set(visibilityRef, null).catch(error => {
        console.error('Error removing visibility status:', error);
      });
    }
  };

  // Update user typing status in Firebase
  const updateTypingStatus = (isTyping: boolean) => {
    if (!userId || !effectiveAccountData?.id) return;

    const userStatusRef = ref(db, `users/${userId}/status`);

    // Only update the typing-related fields, don't touch other status fields
    const updates = {
      isTyping,
      typingTimestamp: Date.now(),
      typingInChatWith: isTyping ? effectiveAccountData.id : null // Set the recipient ID when typing, null when not typing
    };

    // Update the typing status in Firebase
    update(userStatusRef, updates).catch(error => {
      console.error('Error updating typing status:', error);
    });
  };

  // Listen for messages from Firebase
  useEffect(() => {
    if (!userId || !effectiveAccountData?.id) return;

    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    const chatRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);

    setIsLoadingHistory(true);

    // Mark messages as read when the chat is opened or queue them if both users aren't online
    // This will either mark them as read immediately if both users are online
    // or queue them to be marked as read when both users are online
    markMessagesAsRead(chatRoomId);

    // Listen for changes to the chat messages
    // We still listen for messages even if the user is offline
    // so we can update the UI when new messages arrive
    onValue(chatRef, (snapshot) => {
      try {
        const data = snapshot.val();
        if (data) {
          // Convert object to array and sort by timestamp
          const messageArray = Object.values(data)
            .filter((msg: any) => msg && msg.text) // Filter out invalid messages
            .map((msg: any) => ({
              ...msg,
              isUser: msg.senderId === userId
            }))
            .sort((a: ChatMessage, b: ChatMessage) => a.timestamp - b.timestamp);

          // Count unread messages sent by the other user
          const unreadMessages = messageArray.filter(
            (msg: ChatMessage) => !msg.isUser && !msg.isRead
          );
          setUnreadCount(unreadMessages.length);

          setMessages(messageArray as ChatMessage[]);
        } else {
          setMessages([]);
        }
        setLoadError(null);
      } catch (error) {
        console.error('Error processing chat data:', error);
        setLoadError('Failed to load chat history');
      } finally {
        setIsLoadingHistory(false);
      }
    }, (error) => {
      console.error('Firebase error:', error);
      setLoadError('Failed to connect to chat service');
      setIsLoadingHistory(false);
    });

    // Clean up listener on unmount
    return () => {
      off(chatRef);
    };
  }, [userId, effectiveAccountData?.id]);

  // Listen for receiver's online status
  useEffect(() => {
    if (!effectiveAccountData?.id || !userId) return;

    // Initialize with the value from navigation params if available
    if (online !== undefined) {
      console.log('Setting initial online status from params:', online);
      setIsReceiverOnline(online);
    }

    // 1. Check if the user is visible to us specifically
    const visibilityRef = ref(db, `user_visibility/${effectiveAccountData.id}/visible_to/${userId}`);

    // 2. Also check the general status
    const userStatusRef = ref(db, `users/${effectiveAccountData.id}/status`);

    let statusListener: () => void; // Variable to hold the status listener unsubscribe function

    // Listen for visibility changes
    const visibilityListener = onValue(visibilityRef, (visibilitySnapshot) => {
      const visibilityData = visibilitySnapshot.val();
      const isVisibleToUs = visibilityData && visibilityData.visible === true;

      console.log(`Visibility listener fired. isVisibleToUs: ${isVisibleToUs}, visibilityData:`, visibilityData);

      // If user is visible to us, set up a continuous listener for their status
      if (isVisibleToUs) {
        console.log('Receiver is visible, setting up continuous status listener');
        // If a previous status listener exists, unsubscribe it first
        if (statusListener) {
          console.log('Unsubscribing from previous status listener.');
          statusListener();
        }
        // Get the current status
        statusListener = onValue(userStatusRef, (statusSnapshot) => {
          const status = statusSnapshot.val() as UserStatus | null;
          console.log('Status listener fired. Received status:', status);
          if (status) {
            console.log('Received Firebase status state:', status.state);
            const wasOnline = isReceiverOnline;
            const isNowOnline = status.state === 'online';

            console.log(`Attempting to set isReceiverOnline to: ${isNowOnline} (was: ${wasOnline})`);
            setIsReceiverOnline(isNowOnline);
            setLastSeen(status.lastSeen);

            // If user just came online, update message delivery status
            if (!wasOnline && isNowOnline) {
              console.log('Receiver came online, updating message delivery status');

              // Get all messages in the chat
              const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
              const chatRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);

              onValue(chatRef, (snapshot) => {
                const data = snapshot.val();
                if (data) {
                  // Update delivery status for all sent messages
                  Object.entries(data).forEach(([messageId, messageData]: [string, any]) => {
                    if (messageData.senderId === userId && messageData.status === 'sent') {
                      // Update message status to delivered
                      const messageRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`);
                      set(messageRef, {
                        ...messageData,
                        status: 'delivered',
                        deliveredAt: Date.now()
                      });

                      // Also update in user_messages collection
                      const userMessageRef = ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`);
                      set(userMessageRef, {
                        ...messageData,
                        status: 'delivered',
                        deliveredAt: Date.now()
                      });
                    }
                  });
                }
              });

              // Process any queued read receipts
              if (messageReadQueue.length > 0) {
                console.log('Processing queued read receipts:', messageReadQueue.length);
                messageReadQueue.forEach(({chatRoomId, messageId}) => {
                  updateMessageReadStatus(chatRoomId, messageId);
                });
                setMessageReadQueue([]);
              }
            }

            // Check if the user is typing and if they're typing to the current user
            if (status.isTyping === true && status.typingInChatWith === userId) {
              // Check if typing timestamp is recent (within last 10 seconds)
              const isRecentTyping = status.typingTimestamp &&
                (Date.now() - status.typingTimestamp < 10000);

              setIsReceiverTyping(isRecentTyping || false);
            } else {
              setIsReceiverTyping(false);
            }
          } else { // Status data is null
            console.log('Receiver status data is null, setting to offline');
            console.log(`Attempting to set isReceiverOnline to: false (was: ${isReceiverOnline})`);
            setIsReceiverOnline(false);
            setIsReceiverTyping(false);
          }
        });
      } else {
        // If not visible to us, remove the status listener and show as offline
        console.log('Receiver is not visible, setting to offline and removing status listener');
        if (statusListener) {
          console.log('Unsubscribing from status listener because receiver is not visible.');
          statusListener(); // Unsubscribe from status updates
        }
        console.log(`Attempting to set isReceiverOnline to: false (was: ${isReceiverOnline})`);
        setIsReceiverOnline(false);
        setIsReceiverTyping(false);
      }
    });

    return () => {
      // Clean up listeners
      console.log('Cleaning up status/visibility listeners on unmount.');
      off(visibilityRef);
      if (statusListener) {
        statusListener(); // Ensure status listener is also cleaned up
      }
      // No need to explicitly detach userStatusRef using off(userStatusRef)
      // because the listener for userStatusRef is managed by the statusListener variable
    };
  }, [effectiveAccountData?.id, userId, online, messageReadQueue.length]);

  const fetchMessages = useCallback(() => {
    // No need to manually fetch with Firebase, it's real-time
    // Just reset the loading state to show the refresh indicator
    setIsLoadingHistory(true);
    setTimeout(() => setIsLoadingHistory(false), 500);
  }, []);

  // Function to clear chat history
  const clearChatHistory = useCallback(() => {
    if (!userId || !effectiveAccountData?.id) {
      Alert.alert('Error', 'Cannot clear chat history at this time');
      return;
    }

    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');

    // Set loading state
    setIsLoadingHistory(true);

    try {
      // Clear messages in user_chats collection
      const chatRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);
      set(chatRef, null);

      // Clear messages in user_messages collection
      const messagesRef = ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}`);
      set(messagesRef, null);

      // Clear local messages state
      setMessages([]);

      // Reset unread count
      setUnreadCount(0);

      // Update unread count in user_chats for both users
      const userChatRef = ref(db, `user_chats/${userId}/${effectiveAccountData.id}`);
      set(userChatRef, { unread: 0 });

      const receiverChatRef = ref(db, `user_chats/${effectiveAccountData.id}/${userId}`);
      set(receiverChatRef, { unread: 0 });

      Alert.alert('Success', 'Chat history has been cleared');
    } catch (error) {
      console.error('Error clearing chat history:', error);
      Alert.alert('Error', 'Failed to clear chat history. Please try again.');
    } finally {
      setIsLoadingHistory(false);
    }
  }, [userId, effectiveAccountData?.id]);

  const renderContent = () => {
    if (!userId || !effectiveAccountData?.id) {
      return (
        <View style={tw`flex-1 items-center justify-center`}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={tw`text-gray-500 text-center mt-4`}>Loading chat...</Text>
        </View>
      );
    }

    // Check if the user is offline - still show chat history but don't mark as read
    console.log('Current receiver online status:', isReceiverOnline);
    if (!isReceiverOnline) {
      return (
        <View style={tw`flex-1 items-center justify-center p-6`}>
          <MessageCircleOff size={48} color="#9E9E9E" style={tw`mb-4`} />
          <Text style={tw`text-gray-600 text-center text-lg font-semibold mb-2`}>
            {effectiveAccountData.name} is offline
          </Text>
          <Text style={tw`text-gray-500 text-center mb-4`}>
            {lastSeen ? `Last seen ${formatLastSeen(lastSeen)}` : 'Chat history is not available when user is offline'}
          </Text>
          <Text style={tw`text-gray-500 text-center`}>
            You can still send messages. They will be delivered when {effectiveAccountData.name} comes online.
          </Text>
        </View>
      );
    }

    return (
      <>
        {isLoadingHistory ? (
          <View style={tw`flex-1 items-center justify-center`}>
            <ActivityIndicator size="large" color="#0000ff" />
            <Text style={tw`text-gray-500 text-center mt-4`}>Loading messages...</Text>
          </View>
        ) : loadError ? (
          <View style={tw`flex-1 items-center justify-center p-4`}>
            <Text style={tw`text-red-500 text-center`}>{loadError}</Text>
            <TouchableOpacity
              style={tw`mt-4 bg-blue-500 px-4 py-2 rounded-full`}
              onPress={fetchMessages}
            >
              <Text style={tw`text-white`}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={[...messages].reverse()}
            renderItem={renderMessage}
            keyExtractor={item => String(item.id)} // Ensure key is always a string
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'flex-end',
              padding: 15
            }}
            inverted
            showsVerticalScrollIndicator={false}
            onRefresh={fetchMessages}
            refreshing={isLoadingHistory}
            removeClippedSubviews={false} // Add this to prevent rendering issues
            onViewableItemsChanged={areBothUsersOnline() ? onViewableItemsChanged : undefined}
            viewabilityConfig={{
              itemVisiblePercentThreshold: 50
            }}
          />
        )}
      </>
    );
  };

  const scrollToBottom = () => {
    if (flatListRef.current && messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    }
  };

  // Handle viewable items changed
  const onViewableItemsChanged = useCallback(({ viewableItems }: { viewableItems: any[] }) => {
    if (!userId || !effectiveAccountData?.id) return;

    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    const viewableIds = viewableItems.map(item => item.item.id);

    // Update ref with current viewable items
    viewableItemsRef.current = viewableIds;

    // Only mark messages as read if both users are online
    if (areBothUsersOnline()) {
      viewableItems.forEach(viewableItem => {
        const message = viewableItem.item;
        if (!message.isUser && !message.isRead) {
          markMessageAsRead(chatRoomId, message.id);
        }
      });
    } else {
      console.log('Not marking messages as read because both users are not online');
    }
  }, [userId, effectiveAccountData?.id, isReceiverOnline]);

  // Public method to mark messages as read by user ID
  // This is called from UserInbox component
  const markMessagesAsReadByUserId = (otherUserId: string) => {
    if (!userId) return;

    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, otherUserId].sort().join('_');
    markMessagesAsRead(chatRoomId);
  };

  // Make the function available via a ref that can be accessed by navigation params
  useEffect(() => {
    // Expose the markMessagesAsReadByUserId function to the navigation params
    navigation.setParams({
      markMessagesAsReadByUserId
    });
  }, [navigation, userId]);


  // Check if both users are online
  const areBothUsersOnline = (): boolean => {
    // Current user is always considered online when using the app
    // Check if the other user (message receiver) is online and visible to us
    const currentUserOnline = true; // Current user is always online when using the app
    console.log(`Both users online check: currentUser=${currentUserOnline}, receiver=${isReceiverOnline}`);
    return currentUserOnline && isReceiverOnline;
  };

  // Process message read queue when online status changes
  useEffect(() => {
    // If both users are online, process any queued messages
    if (areBothUsersOnline() && messageReadQueue.length > 0) {
      console.log('Both users online, processing queued messages:', messageReadQueue.length);

      // Process all queued messages
      messageReadQueue.forEach(({chatRoomId, messageId}) => {
        updateMessageReadStatus(chatRoomId, messageId);
      });

      // Clear the queue
      setMessageReadQueue([]);
    }
  }, [isReceiverOnline, messageReadQueue]);

  // Function to update message read status in Firebase
  const updateMessageReadStatus = (chatRoomId: string, messageId: string) => {
    // Update in both collections to ensure consistency
    const chatMessageRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`);
    const userMessageRef = ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`);

    // Get the current message data first from chat collection
    onValue(chatMessageRef, (snapshot) => {
      const messageData = snapshot.val();
      if (messageData && !messageData.isRead) {
        // Update with read status
        set(chatMessageRef, {
          ...messageData,
          isRead: true,
          readAt: Date.now(),
          status: 'read'
        });
      }
    }, { onlyOnce: true });

    // Also update in user_messages collection
    onValue(userMessageRef, (snapshot) => {
      const messageData = snapshot.val();
      if (messageData && (messageData.status !== 'read' || !messageData.isRead)) {
        // Update with read status
        set(userMessageRef, {
          ...messageData,
          isRead: true,
          readAt: Date.now(),
          status: 'read'
        });
      }
    }, { onlyOnce: true });
  };

  // Mark a single message as read
  const markMessageAsRead = (chatRoomId: string, messageId: string) => {
    // Check if both users are online
    if (areBothUsersOnline()) {
      // If both are online, update the message status immediately
      console.log('Both users online, marking message as read immediately:', messageId);
      updateMessageReadStatus(chatRoomId, messageId);
    } else {
      // If not both online, add to queue to process later
      console.log('Not both users online, queueing message for read status update:', messageId);
      setMessageReadQueue(prev => {
        // Check if this message is already in the queue
        const isAlreadyQueued = prev.some(item =>
          item.chatRoomId === chatRoomId && item.messageId === messageId
        );

        if (isAlreadyQueued) {
          return prev; // Don't add duplicates
        }

        return [...prev, {chatRoomId, messageId}];
      });
    }
  };

  // Mark all messages as read by chat room ID
  const markMessagesAsRead = async (chatRoomId: string) => {
    if (!userId || !effectiveAccountData?.id) return;

    try {
      // Always update unread count in user_chats for the current user
      // This is just UI state and doesn't affect the actual read status
      if (userId) {
        const userChatRef = ref(db, `user_chats/${userId}/${effectiveAccountData.id}`);
        set(userChatRef, { unread: 0 });
      }

      // Check if both users are online
      const bothOnline = areBothUsersOnline();

      // Only proceed with marking messages as read if both users are online
      if (!bothOnline) {
        console.log('Not marking messages as read because both users are not online');
        return;
      }

      console.log('Both users are online, marking messages as read');

      // Get all messages in the chat room from user_chats
      const chatRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}`);
      onValue(chatRef, (snapshot) => {
        const data = snapshot.val();
        if (!data) return;

        // Find unread messages sent by the other user
        Object.entries(data).forEach(([key, value]: [string, any]) => {
          if (value.senderId === effectiveAccountData?.id && (!value.isRead || value.status !== 'read')) {
            // Both users are online, mark as read immediately
            const messageRef = ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${key}`);
            set(messageRef, {
              ...value,
              isRead: true,
              readAt: Date.now(),
              status: 'read'
            });

            // Also update in user_messages collection
            const userMessageRef = ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${key}`);
            onValue(userMessageRef, (msgSnapshot) => {
              const msgData = msgSnapshot.val();
              if (msgData) {
                set(userMessageRef, {
                  ...msgData,
                  isRead: true,
                  readAt: Date.now(),
                  status: 'read'
                });
              }
            });
          }
        });
      });
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  const sendMessage = async () => {
    if (message.trim() === '') return;
    if (!userId || !effectiveAccountData?.id) {
      Alert.alert('Error', 'Cannot send message at this time');
      return;
    }

    // Create a chat room ID by sorting and joining the user IDs
    const chatRoomId = [userId, effectiveAccountData.id].sort().join('_');
    const messageId = generateUUID();

    const newMsg: FirebaseMessage = {
      id: messageId,
      text: message.trim(),
      senderId: userId,
      receiverId: effectiveAccountData.id,
      timestamp: Date.now(),
      status: 'sent',
      isRead: false,
      deliveredAt: null
    };

    try {
      // Add message to local state immediately for UI responsiveness
      setMessages(prev => [...prev, {...newMsg, isUser: true}]);
      setMessage('');
      scrollToBottom();

      // Reset typing status when sending a message
      updateTypingStatus(false);

      // Clear any typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }

      // Save message to Firebase in both collections
      await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), newMsg);
      await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), newMsg);

      // Update unread count for the receiver
      const receiverChatRef = ref(db, `user_chats/${effectiveAccountData.id}/${userId}`);
      onValue(receiverChatRef, (snapshot) => {
        const chatData = snapshot.val() || {};
        const currentUnread = chatData.unread || 0;
        set(receiverChatRef, { unread: currentUnread + 1 });
      });

      // If receiver is online, update message status to delivered
      if (isReceiverOnline) {
        const deliveredMsg = {
          ...newMsg,
          status: 'delivered',
          deliveredAt: Date.now()
        };

        // Update in both collections
        await set(ref(db, `${FIREBASE_CHAT_PREFIX}/${chatRoomId}/${messageId}`), deliveredMsg);
        await set(ref(db, `${FIREBASE_MESSAGES_PREFIX}/${chatRoomId}/${messageId}`), deliveredMsg);

        // Update local state to show delivered status
        setMessages(prev => prev.map(msg => 
          msg.id === messageId ? {...msg, status: 'delivered', deliveredAt: Date.now()} : msg
        ));
      }

      // Send push notification to receiver
      const receiverPushToken = await fetchUserPushToken(effectiveAccountData.id);
      if (receiverPushToken) {
        // Get current user's name for notification
        const userDetails = await AsyncStorage.getItem('userDetails');
        const parsedDetails = userDetails ? JSON.parse(userDetails) : null;
        const senderName = parsedDetails?.username || 'Someone';

        await sendPushNotification(
          receiverPushToken,
          message.trim(),
          senderName
        );
      }

      // No need to wait for response with Firebase - it's real-time
    } catch (err) {
      console.error('Sending message failed:', err);
      // Remove the message from local state if it failed to send
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      setMessage(message);
      Alert.alert(
        'Error',
        'Failed to send message. Please try again.'
      );
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <>
      <View
        style={[
          styles.messageContainer,
          item.isUser ? styles.userMessage : styles.otherMessage
        ]}
      >
        <View style={styles.messageContentContainer}>
          <Text style={styles.messageText}>{item.text}</Text>
          {item.isUser && (
            <View style={styles.messageStatusContainer}>
              {item.isRead ? (
                <Feather name="check-circle" size={14} color="#4CAF50" style={styles.readIcon} />
              ) : item.status === 'sent' || item.status === 'delivered' ? (
                <Feather name="check" size={14} color="#9E9E9E" style={styles.readIcon} />
              ) : null}
            </View>
          )}
        </View>
      </View>
    </>
  );

  // No WebSocket ping/pong needed with HTTP-only approach

  // Close menu when clicking outside
  const handleOutsidePress = () => {
    if (showMenu) {
      setShowMenu(false);
    }
  };

  // Typing indicator component with animation
  const TypingIndicator = () => {
    if (!isReceiverTyping || !isReceiverOnline) return null;

    return (
      <View style={tw`w-12  flex-row items-center justify-center  ml-5`}>
        <View style={styles.typingBubble}>
          <MotiView
            style={styles.typingDot}
            from={{ opacity: 0.4, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'timing', duration: 600, loop: true }}
          />
          <MotiView
            style={styles.typingDot}
            from={{ opacity: 0.4, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'timing', duration: 600, loop: true, delay: 200 }}
          />
          <MotiView
            style={styles.typingDot}
            from={{ opacity: 0.4, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'timing', duration: 600, loop: true, delay: 400 }}
          />
        </View>
      </View>
    );
  };

  // Add useEffect for sending notification when chat is opened
  useEffect(() => {
    const sendChatOpenNotification = async () => {
      if (!userId || !effectiveAccountData?.id || notificationSent) return;

      try {
        // Get receiver's push token
        const receiverPushToken = await fetchUserPushToken(effectiveAccountData.id);
        if (receiverPushToken) {
          // Get current user's name for notification
          const userDetails = await AsyncStorage.getItem('userDetails');
          const parsedDetails = userDetails ? JSON.parse(userDetails) : null;
          const senderName = parsedDetails?.username || 'Someone';

          // Send notification
          await sendPushNotification(
            receiverPushToken,
            ``,
            `Hy See ${senderName} is Active wanna chat Now? 👀`
          );

          // Mark notification as sent
          setNotificationSent(true);
        }
      } catch (error) {
        console.error('Error sending chat open notification:', error);
      }
    };

    // Send notification when chat is opened
    sendChatOpenNotification();

    // Reset notification sent state when component unmounts
    return () => {
      setNotificationSent(false);
    };
  }, [userId, effectiveAccountData?.id]);

  console.log(`Rendering UserChatInbox. isReceiverOnline state currently is: ${isReceiverOnline}`);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Invisible overlay to handle outside press */}
      {showMenu && (
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={0}
          onPress={handleOutsidePress}
        />
      )}

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.logoContainer}>
            <Image source={{ uri: effectiveAccountData?.logo || 'https://via.placeholder.com/150' }} style={tw`w-full h-full`} />
          </View>
          <View>
            <Text style={styles.headerTitle}>{effectiveAccountData?.name || 'User'}</Text>
            <View style={styles.headerSubtitleContainer}>
              <SokitOnlineStatusIndicator
                userId={effectiveAccountData?.id || ''}
                size={8}
                showText={false}
              />
              <Text style={styles.headerSubtitle}>
                {isReceiverTyping && isReceiverOnline ? 'typing...' :
                 isReceiverOnline ? 'Online' :
                 lastSeen ? `Last seen ${formatLastSeen(lastSeen)}` : 'Offline'}
                {effectiveAccountData?.verified && ' ✓'}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.headerRight}>
          {isReceiverOnline && (
            <TouchableOpacity onPress={() => setShowMenu(!showMenu)}>
              <Feather name="more-horizontal" size={24} color="black" />
            </TouchableOpacity>
          )}
          {showMenu && isReceiverOnline && (
            <View style={styles.menuContainer}>
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setShowMenu(false);
                    Alert.alert(
                      'Clear Chat History',
                      'Are you sure you want to clear all chat history? This action cannot be undone.',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Clear', style: 'destructive', onPress: clearChatHistory }
                      ]
                    );
                  }}
                >
                  <Feather name="trash-2" size={18} color="#FF5252" style={styles.menuIcon} />
                  <Text style={styles.menuItemText}>Clear Chat History</Text>
                </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      {/* Chat Messages */}
      {renderContent()}

      {/* Typing Indicator */}
      <TypingIndicator />

      {/* Message Input */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={styles.inputWrapper}>
          <TouchableOpacity style={styles.plusButton}>
            <Feather name="plus" size={24} color="#4CAF50" />
          </TouchableOpacity>
          <TextInput
            style={styles.input}
            placeholder="Type a message..."
            placeholderTextColor="#999"
            value={message}
            onChangeText={(text) => {
              setMessage(text);

              // Handle typing indicator
              if (text.length > 0) {
                // User is typing
                updateTypingStatus(true);

                // Clear any existing timeout
                if (typingTimeoutRef.current) {
                  clearTimeout(typingTimeoutRef.current);
                }

                // Set a new timeout to stop typing indicator after 2 seconds of inactivity
                typingTimeoutRef.current = setTimeout(() => {
                  updateTypingStatus(false);
                }, 2000);
              } else {
                // Message is empty, user stopped typing
                updateTypingStatus(false);

                // Clear any existing timeout
                if (typingTimeoutRef.current) {
                  clearTimeout(typingTimeoutRef.current);
                  typingTimeoutRef.current = null;
                }
              }
            }}
            onFocus={scrollToBottom}
          />
        </View>
        <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
          <Feather name="send" size={24} color="white" />
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c5e8c8',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBlockColor: '#B0DEBA',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 45,
    height: 45,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    overflow: 'hidden',
  },
  headerTitle: {
    fontSize: 15,
    fontFamily: 'Helvetica_bold',
    color: '#2c5e2e',
  },
  headerSubtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  headerSubtitle: {
    fontSize: 13,
    color: '#2c5e2e',
    fontFamily: 'Helvetica_bold',
  },
  messageContainer: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 20,
    marginVertical: 4,
  },
  userMessage: {
    backgroundColor: '#DCF8C6',
    alignSelf: 'flex-end',
    marginLeft: '20%',
  },
  otherMessage: {
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    marginRight: '20%',
  },
  messageText: {
    fontSize: 16,
  },
  messageContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  messageStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginLeft: 8,
  },
  readIcon: {
    marginLeft: 4,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  badgeContainer: {
    backgroundColor: '#FF5252',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    paddingHorizontal: 6,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  menuContainer: {
    position: 'absolute',
    top: 30,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
    minWidth: 180,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  menuIcon: {
    marginRight: 10,
  },
  menuItemText: {
    fontSize: 14,
    color: '#333',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 10,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    marginRight: 10,
  },
  plusButton: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    paddingVertical: 10,
    paddingRight: 15,
    color: '#333',
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageList: {
    flexGrow: 1,
    justifyContent: 'flex-end',
    padding: 15,
  },
  typingIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
    marginBottom: 10,
  },
  typingBubble: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginHorizontal: 3,
  },
  typingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
});
badgeContainer: {
    backgroundColor: '#FF5252',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    paddingHorizontal: 6,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  menuContainer: {
    position: 'absolute',
    top: 30,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
    minWidth: 180,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  menuIcon: {
    marginRight: 10,
  },
  menuItemText: {
    fontSize: 14,
    color: '#333',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 10,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    marginRight: 10,
  },
  plusButton: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    paddingVertical: 10,
    paddingRight: 15,
    color: '#333',
  },
  sendButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageList: {
    flexGrow: 1,
    justifyContent: 'flex-end',
    padding: 15,
  },
  typingIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
    marginBottom: 10,
  },
  typingBubble: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginHorizontal: 3,
  },
  typingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
});
