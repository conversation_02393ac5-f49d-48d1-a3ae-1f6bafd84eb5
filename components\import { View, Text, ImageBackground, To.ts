import { View, Text, ImageBackground, TouchableOpacity, Dimensions, ActivityIndicator, TextInput, Modal, TouchableWithoutFeedback, Platform, Alert, ScrollView, Animated, PanResponder, Keyboard } from 'react-native'
import React, { useState, useCallback, useRef, useEffect } from 'react'
import tw from 'twrnc'
import { ArrowLeft, Heart, MessageCircle, EllipsisVertical, Send, Plus, Image, Camera, Video, FileText, Zap, X } from 'lucide-react-native'
import { Image as MotiImage, MotiView } from 'moti'
import { FlatList } from 'react-native-gesture-handler'
import * as ImagePicker from 'expo-image-picker'
import Ra<PERSON>payCheckout from 'react-native-razorpay'
import PaymentPopup from '@/components/payment/paymentpopup'
import AsyncStorage from '@react-native-async-storage/async-storage'

interface PollOption {
  text: string;
  votes: number;
}

interface Message {
  id: string;
  type: 'text' | 'poll' | 'image';
  content: string;
  timestamp: string;
  likes: number;
  comments: number;
  pollOptions?: PollOption[];
  imageUrl?: string;
}

// Add these new interfaces
interface Poll {
  _id: string;
  channelId: string;
  question: string;
  options: {
    text: string;
    votes: string[];
  }[];
  createdBy: string;
  isActive: boolean;
  createdAt: string;
}

interface PollResults {
  pollId: string;
  question: string;
  totalVotes: number;
  isActive: boolean;
  createdAt: string;
  options: {
    text: string;
    votes: number;
    percentage: number;
  }[];
}

interface ChatMessage {
  _id: string;
  channelId: string;
  content: string;
  senderId: string;
  type: 'text' | 'image' | 'poll';
  timestamp: string;
}

interface Follow {
  followerId: string;
  followingId: string;
  createdAt: string;
}

interface Channel {
  _id: string;
  name: string;
  description: string;
  isPaid: boolean;
  price: number;
  ownerId: string;
  createdAt: string;
}

export default function ConsultantCommunity({ route, navigation }: any) {
  const { consultantId, username, pfp, active } = route.params;
  const windowWidth = Dimensions.get('window').width;
  const [selectedPollOptions, setSelectedPollOptions] = useState<Record<string, number>>({});
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});
  const [messageText, setMessageText] = useState('');
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [userId, setuserId] = useState('132');
  const flatListRef = useRef(null);

  // Add these new states
  const [showPaymentPopup, setShowPaymentPopup] = useState(false);
  const [selectedRate, setSelectedRate] = useState(0);
  const [polls, setPolls] = useState<Poll[]>([]);
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followersCount, setFollowersCount] = useState(0);
  const [isPaidChannel, setIsPaidChannel] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);
  const [channelPrice, setChannelPrice] = useState(0);

  // Add these state variables to your component
  const [showCreatePollModal, setShowCreatePollModal] = useState(false);
  const [pollQuestion, setPollQuestion] = useState('');
  const [pollOptions, setPollOptions] = useState(['', '']);
  const [activePoll, setActivePoll] = useState<Poll | null>(null);
  const [pollResults, setPollResults] = useState<PollResults | null>(null);

  // YouTube-style popup states
  const [showYouTubeStylePoll, setShowYouTubeStylePoll] = useState(false);
  const slideAnimation = useRef(new Animated.Value(0)).current;

  // Add this state for super chat
  const [showSuperChat, setShowSuperChat] = useState(false);
  const [superChatAmount, setSuperChatAmount] = useState(0);

  // Add this to your imports
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Add this useEffect to detect keyboard visibility
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
        // Scroll to bottom when keyboard appears
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    );
    
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Remove these demo data arrays
  // const demoMessages: ChatMessage[] = [...]
  // const demoPolls: Poll[] = [...]

  // Fetch polls for the channel
  const fetchPolls = useCallback(async () => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/polls`);
      if (!response.ok) throw new Error('Failed to fetch polls');
      const data = await response.json();
      setPolls(data);
    } catch (error) {
    }
  }, [consultantId]);

  // Create a new poll
  const createPoll = async (question: string, options: string[]) => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/polls`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          question,
          options,
          userId: consultantId // assuming this is the owner
        })
      });

      if (!response.ok) throw new Error('Failed to create poll');
      const newPoll = await response.json();
      setPolls(prev => [...prev, newPoll]);
    } catch (error) {
    }
  };

  // Vote on a poll
  const votePoll = async (pollId: string, optionIndex: number) => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: consultantId, // or the current user's ID
          optionIndex
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      // Update local state
      const updatedPoll = await response.json();
      setPolls(prev => prev.map(poll =>
        poll._id === pollId ? updatedPoll : poll
      ));
    } catch (error) {
    }
  };

  // Get poll results
  const getPollResults = async (pollId: string): Promise<PollResults> => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/results`);
      if (!response.ok) throw new Error('Failed to get poll results');
      return await response.json();
    } catch (error) {
      throw error;
    }
  };

  const handleAttachmentPress = async (type: string) => {
    setShowAttachmentMenu(false);

    switch (type) {
      case 'image':
        const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (permissionResult.granted === false) {
          alert("You need to enable permission to access the gallery");
          return;
        }

        const pickerResult = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          quality: 1,
        });

        if (!pickerResult.canceled) {
          // Handle the selected image
          console.log('Selected image:', pickerResult.assets[0].uri);

          // Set loading state for this image
          const tempId = `temp-${Date.now()}`;
          setLoadingImages(prev => ({ ...prev, [tempId]: true }));

          try {
            // First, you would typically upload the image to your server or a cloud storage
            // For this example, we'll assume the image is already accessible via URL
            const imageUrl = pickerResult.assets[0].uri;

            // Send the image message
            const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/messages`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                content: imageUrl,
                userId: userId,
                type: 'image'
              })
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || 'Failed to send image');
            }

            // Refresh chat history to show the new image
            fetchChatHistory();
          } catch (error) {
            console.error('Error sending image:', error);
            Alert.alert('Error', 'Failed to send image. Please try again.');
          } finally {
            setLoadingImages(prev => ({ ...prev, [tempId]: false }));
          }
        }
        break;

      case 'camera':
        const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

        if (cameraPermission.granted === false) {
          alert("You need to enable camera permission");
          return;
        }

        const cameraResult = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          quality: 1,
        });

        if (!cameraResult.canceled) {
          // Handle the captured image
          console.log(cameraResult.assets[0].uri);
          // Add your camera handling logic here
        }
        break;

      case 'video':
        // Add video picker logic
        break;

      case 'document':
        // Add document picker logic
        break;

      case 'poll':
        setShowCreatePollModal(true);
        break;
    }
  };

  const fetchChatHistory = async () => {
    try {
      setIsLoadingHistory(true);
      console.log('Fetching chat history for consultant ID:', consultantId);

      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/messages`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(`Failed to fetch chat history: ${errorData.error || response.statusText}`);
      }

      const messages = await response.json();
      console.log(`Received ${messages.length} messages from API`);
      setChatHistory(messages);
    } catch (error) {
      console.error('Error fetching chat history:', error);
      // Fallback to empty array if API fails
      console.log('Using empty array as fallback');
      setChatHistory([]);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Add this function to view a poll
  const handleViewPoll = useCallback(async (pollId: string) => {
    try {
      // First get the poll details
      const pollResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}`);
      if (!pollResponse.ok) {
        throw new Error('Failed to fetch poll details');
      }

      const poll = await pollResponse.json();
      setActivePoll(poll);

      // Then get the poll results
      const resultsResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/results`);
      if (!resultsResponse.ok) {
        throw new Error('Failed to fetch poll results');
      }

      const results = await resultsResponse.json();
      setPollResults(results);

      // पॉपअप के बजाय, हम बस स्टेट अपडेट करेंगे
      // setShowYouTubeStylePoll(true) और एनिमेशन हटा दिया
    } catch (error) {
      console.error('Error viewing poll:', error);
      Alert.alert('Error', 'Failed to load poll. Please try again.');
    }
  }, []);

  const handlePollVote = useCallback(async (messageId: string, optionIndex: number) => {
    try {
      await votePoll(messageId, optionIndex);
      setSelectedPollOptions(prev => ({
        ...prev,
        [messageId]: optionIndex
      }));
    } catch (error) {
      Alert.alert('Error', 'Failed to submit vote');
    }
  }, []);

  const handleImageLoad = useCallback((messageId: string) => {
    setLoadingImages(prev => ({
      ...prev,
      [messageId]: false
    }))
  }, [])

  // पोल आईडी को ट्रैक करने के लिए एक स्टेट जोड़ें
  const [loadedPollIds, setLoadedPollIds] = useState<Set<string>>(new Set());

  // useEffect में पोल्स को लोड करें
  useEffect(() => {
    // फिल्टर करें और केवल उन पोल्स को लोड करें जो अभी तक लोड नहीं हुए हैं
    const pollMessages = chatHistory
      .filter(msg => msg.type === 'poll')
      .map(msg => msg.content);
    
    const newPollIds = pollMessages.filter(id => !loadedPollIds.has(id));
    
    if (newPollIds.length > 0) {
      // नए पोल्स को लोड करें
      newPollIds.forEach(pollId => {
        handleViewPoll(pollId);
      });
      
      // लोड किए गए पोल आईडी को ट्रैक करें
      setLoadedPollIds(prev => {
        const updated = new Set(prev);
        newPollIds.forEach(id => updated.add(id));
        return updated;
      });
    }
  }, [chatHistory, loadedPollIds, handleViewPoll]);

  const renderMessage = useCallback(({ item }: { item: ChatMessage }) => {
    const isOwnMessage = item.senderId === userId;

    return (
      <>
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 500 }}
          style={[
            tw`mx-4 my-2 p-2 rounded-xl`,
            !['image', 'poll'].includes(item.type) &&
            (isOwnMessage ? tw`bg-blue-500 ml-auto` : tw`bg-white mr-auto border-2`),
            ['image', 'poll'].includes(item.type) &&
            (isOwnMessage ? tw`ml-auto` : tw`mr-auto`),
            { maxWidth: '80%' }
          ]}
        >
          {/* Poll Message - Always show full poll */}
          {item.type === 'poll' && (
            <View style={tw`bg-gray-100 p-3 rounded-lg w-[300px]`}>
              <View style={tw`flex-row items-center mb-2`}>
                <Text style={tw`text-2xl mr-2`}>📊</Text>
                <Text style={[tw`font-bold`, { fontFamily: 'Helvetica_bold' }]}>
                  Poll
                </Text>
              </View>
              
              {pollResults && pollResults.pollId === item.content ? (
                // पोल परिणाम और वोटिंग विकल्प सीधे दिखाएं
                <View style={tw`mt-2`}>
                  <Text style={[tw`font-bold mb-2`, { fontFamily: 'Helvetica_bold' }]}>
                    {activePoll?.question || "Loading poll..."}
                  </Text>
                  
                  {pollResults.options.map((option, index) => {
                    const isSelected = selectedPollOptions[pollResults.pollId] === index;
                    
                    return (
                      <TouchableOpacity
                        key={index}
                        onPress={() => handleVoteOnPoll(pollResults.pollId, index)}
                        disabled={!pollResults.isActive}
                        style={[
                          tw`border rounded-full p-3 mb-2 relative overflow-hidden`,
                          isSelected && tw`border-blue-500`
                        ]}
                      >
                        <View
                          style={[
                            tw`absolute top-0 left-0 bottom-0 bg-blue-100 opacity-50`,
                            { width: `${option.percentage}%` }
                          ]}
                        />
                        <View style={tw`flex-row justify-between z-10`}>
                          <Text style={{ fontFamily: 'Helvetica' }}>{option.text}</Text>
                          <Text style={tw`text-gray-500`}>{option.percentage}% ({option.votes})</Text>
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                  
                  {!pollResults.isActive && (
                    <Text style={[tw`text-red-500 mt-2`, { fontFamily: 'Helvetica' }]}>
                      This poll is closed. Voting is no longer allowed.
                    </Text>
                  )}
                </View>
              ) : (
                // लोडिंग इंडिकेटर दिखाएं
                <View style={tw`items-center justify-center py-4`}>
                  <ActivityIndicator size="small" color="#0000ff" />
                  <Text style={tw`mt-2 text-gray-500`}>Loading poll...</Text>
                </View>
              )}
            </View>
          )}

          {/* अन्य मैसेज टाइप्स... */}
          {item.type === 'text' && (
            <Text style={[
              tw``,
              {
                fontFamily: 'Helvetica',
                color: isOwnMessage ? '#fff' : '#000'
              }
            ]}>
              {item.content}
            </Text>
          )}

          {item.type === 'image' && (
            <View style={tw`relative items-center px-3`}>
              {/* Loading Spinner */}
              {loadingImages[item._id] && (
                <View style={tw`absolute inset-0 z-10 items-center justify-center`}>
                  <ActivityIndicator size="large" color="#0000ff" />
                </View>
              )}

              <MotiImage
                source={{ uri: item.content }}
                style={[
                  tw`rounded-xl`,
                  { width: windowWidth * 0.8, height: 200 },
                ]}
                onLoadStart={() =>
                  setLoadingImages((prev) => ({ ...prev, [item._id]: true }))
                }
                onLoad={() => handleImageLoad(item._id)}
                resizeMode="cover"
              />
            </View>
          )}

          {item.type === 'superChat' && (
            <View style={tw`bg-gradient-to-r from-yellow-400 to-yellow-600 p-3 rounded-lg border-2 border-yellow-300`}>
              <View style={tw`flex-row items-center mb-2`}>
                <Text style={tw`text-xl mr-2`}>💰</Text>
                <Text style={[tw`font-bold text-white`, { fontFamily: 'Helvetica_bold' }]}>
                  Super Chat · ₹{item.amount}
                </Text>
              </View>
              <Text style={[tw`text-white`, { fontFamily: 'Helvetica' }]}>
                {item.content}
              </Text>
            </View>
          )}
        </MotiView>
      </>
    );
  }, [userId, windowWidth, loadingImages, pollResults, activePoll, selectedPollOptions]);

  const handlePayment = async (amount: number) => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          amount: channelPrice
        })
      });

      if (!response.ok) throw new Error('Payment failed');

      setHasAccess(true);
      setShowPaymentPopup(false);
    } catch (error) {
      Alert.alert('Error', 'Payment failed');
    }
  };

  const checkBalanceAndSendMessage = async () => {
    if (!messageText.trim()) return;
    
    // Show super chat options directly instead of payment popup
    setShowSuperChat(true);
  };

  const sendMessage = async () => {
    if (!messageText.trim()) return;

    try {
      console.log('Sending message to consultant ID:', consultantId);
      console.log('Message content:', messageText.trim());
      console.log('User ID:', userId);

      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: messageText.trim(),
          userId: userId,
          type: 'text'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(`Failed to send message: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      console.log('Message sent successfully:', result);

      setMessageText('');

      // Add the new message to the chat history immediately for better UX
      const newMessage: ChatMessage = {
        _id: result._id || `temp-${Date.now()}`,
        channelId: consultantId,
        content: messageText.trim(),
        senderId: userId,
        type: 'text',
        timestamp: new Date().toISOString()
      };

      setChatHistory(prev => [newMessage, ...prev]);

      // Then fetch the updated chat history from the server
      fetchChatHistory();

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  // Add follow/unfollow functionality
  const toggleFollow = async () => {
    try {
      const method = isFollowing ? 'DELETE' : 'POST';
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/follow/${consultantId}`, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          followerId: userId
        })
      });

      if (!response.ok) throw new Error('Failed to update follow status');

      setIsFollowing(!isFollowing);
      setFollowersCount(prev => isFollowing ? prev - 1 : prev + 1);
    } catch (error) {
      Alert.alert('Error', 'Failed to update follow status');
    }
  };

  // Check channel access
  const checkChannelAccess = async () => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}`);
      const channel = await response.json();

      setIsPaidChannel(channel.isPaid);
      setChannelPrice(channel.price);

      if (!channel.isPaid) {
        setHasAccess(true);
        return;
      }

      // Check if user has paid for access
      const accessResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/access/${userId}`);
      const accessData = await accessResponse.json();
      setHasAccess(accessData.hasAccess);
    } catch (error) {
      console.error('Error checking channel access:', error);
    }
  };

  // Add useEffects
  useEffect(() => {
    const checkFollowStatus = async () => {
      try {
        const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/follow/${consultantId}/${userId}`);
        const data = await response.json();
        setIsFollowing(data.isFollowing);
      } catch (error) {
        console.error('Error checking follow status:', error);
      }
    };

    const getFollowersCount = async () => {
      try {
        const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/followers/${consultantId}`);
        const followers = await response.json();
        setFollowersCount(followers.length);
      } catch (error) {
        console.error('Error getting followers count:', error);
      }
    };

    checkFollowStatus();
    getFollowersCount();
    checkChannelAccess();
  }, [consultantId, userId]);

  useEffect(() => {
    fetchPolls();
  }, [fetchPolls]);

  useEffect(() => {
    fetchChatHistory();
  }, [consultantId]);

  // Update the useEffect to load demo data
  useEffect(() => {
    // Get user ID from AsyncStorage
    const getUserId = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          setuserId(parsedSession?.userId || '132');
        }
      } catch (error) {
        console.error('Error getting user session:', error);
      }
    };

    getUserId();
  }, [consultantId]);

  // Add this function to create a new poll
  const handleCreatePoll = async () => {
    if (!pollQuestion.trim()) {
      Alert.alert('Error', 'Please enter a poll question');
      return;
    }

    // Filter out empty options
    const validOptions = pollOptions.filter(option => option.trim() !== '');

    if (validOptions.length < 2) {
      Alert.alert('Error', 'Please enter at least 2 options');
      return;
    }

    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/polls`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          question: pollQuestion,
          options: validOptions,
          userId: userId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create poll');
      }

      const newPoll = await response.json();
      setPolls(prev => [newPoll, ...prev]);

      // Reset form and close modal
      setPollQuestion('');
      setPollOptions(['', '']);
      setShowCreatePollModal(false);

      // Refresh chat history to show the new poll
      fetchChatHistory();

      Alert.alert('Success', 'Poll created successfully');
    } catch (error) {
      console.error('Error creating poll:', error);
      Alert.alert('Error', 'Failed to create poll. Please try again.');
    }
  };

  // Add this function to add a poll option
  const addPollOption = () => {
    setPollOptions([...pollOptions, '']);
  };

  // Add this function to remove a poll option
  const removePollOption = (index: number) => {
    if (pollOptions.length <= 2) {
      Alert.alert('Error', 'A poll must have at least 2 options');
      return;
    }

    const newOptions = [...pollOptions];
    newOptions.splice(index, 1);
    setPollOptions(newOptions);
  };

  // Add this function to handle poll option changes
  const handlePollOptionChange = (text: string, index: number) => {
    const newOptions = [...pollOptions];
    newOptions[index] = text;
    setPollOptions(newOptions);
  };



  // Function to close the YouTube-style popup
  const closeYouTubeStylePoll = () => {
    // Animate the popup sliding down
    Animated.timing(slideAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      // After animation completes, hide the popup
      setShowYouTubeStylePoll(false);
    });
  };

  // Add this function to vote on a poll
  const handleVoteOnPoll = async (pollId: string, optionIndex: number) => {
    try {
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: userId,
          optionIndex: optionIndex
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to vote on poll');
      }

      // Update the poll results
      const resultsResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/polls/${pollId}/results`);
      if (!resultsResponse.ok) {
        throw new Error('Failed to fetch updated poll results');
      }

      const results = await resultsResponse.json();
      setPollResults(results);

      // Update selected poll options
      setSelectedPollOptions(prev => ({
        ...prev,
        [pollId]: optionIndex
      }));

      // Don't show alert in YouTube-style popup, just update the UI
    } catch (error) {
      console.error('Error voting on poll:', error);
      Alert.alert('Error', 'Failed to vote on poll. Please try again.');
    }
  };

  // Add this function to handle super chat selection
  const handleSuperChatSelect = async (amount: number) => {
    try {
      setSuperChatAmount(amount);
      setShowSuperChat(false);
      
      // Check if user has access, if not, handle payment first
      if (!hasAccess && isPaidChannel) {
        // Handle channel access payment
        const accessResponse = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/purchase`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            userId,
            amount: channelPrice
          })
        });
        
        if (!accessResponse.ok) throw new Error('Channel access payment failed');
        setHasAccess(true);
      }
      
      // Send super chat message
      const response = await fetch(`https://api-asia-communtiy.nityasha.com/api/channels/${consultantId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: messageText.trim(),
          userId: userId,
          type: 'superChat',
          amount: amount
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send super chat');
      }

      setMessageText('');
      fetchChatHistory();
    } catch (error) {
      console.error('Error sending super chat:', error);
      Alert.alert('Error', 'Failed to send super chat. Please try again.');
    }
  };

  return (
    <>
      <PaymentPopup
        visible={showPaymentPopup}
        onClose={() => setShowPaymentPopup(false)}
        rate={selectedRate}
        username={username}
      />

      {/* YouTube-style Poll Popup - हटा दें या कमेंट करें
        {showYouTubeStylePoll && pollResults && (
          <Animated.View>
            ...
          </Animated.View>
        )}
      */}

      {/* Create Poll Modal */}
      <Modal
        visible={showCreatePollModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCreatePollModal(false)}
      >
        <TouchableWithoutFeedback onPress={() => setShowCreatePollModal(false)}>
          <View style={tw`flex-1 justify-center items-center bg-black bg-opacity-50`}>
            <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
              <View style={tw`bg-white w-[90%] rounded-xl p-4`}>
                <Text style={[tw`text-xl font-bold mb-4`, { fontFamily: 'Helvetica_bold' }]}>
                  Create a Poll
                </Text>

                <TextInput
                  placeholder="Enter your question"
                  style={tw`border border-gray-300 rounded-lg p-3 mb-4`}
                  value={pollQuestion}
                  onChangeText={setPollQuestion}
                />

                <Text style={[tw`font-bold mb-2`, { fontFamily: 'Helvetica_bold' }]}>
                  Options:
                </Text>

                <ScrollView style={tw`max-h-[200px]`}>
                  {pollOptions.map((option, index) => (
                    <View key={index} style={tw`flex-row items-center mb-2`}>
                      <TextInput
                        placeholder={`Option ${index + 1}`}
                        style={tw`border border-gray-300 rounded-lg p-3 flex-1 mr-2`}
                        value={option}
                        onChangeText={(text) => handlePollOptionChange(text, index)}
                      />
                      <TouchableOpacity
                        onPress={() => removePollOption(index)}
                        style={tw`bg-red-500 w-8 h-8 rounded-full items-center justify-center`}
                      >
                        <Text style={tw`text-white font-bold`}>-</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>

                <TouchableOpacity
                  onPress={addPollOption}
                  style={tw`bg-blue-500 p-2 rounded-lg items-center mb-4 mt-2`}
                >
                  <Text style={tw`text-white font-bold`}>Add Option</Text>
                </TouchableOpacity>

                <View style={tw`flex-row justify-end`}>
                  <TouchableOpacity
                    onPress={() => setShowCreatePollModal(false)}
                    style={tw`bg-gray-300 p-2 rounded-lg mr-2`}
                  >
                    <Text style={tw`font-bold`}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={handleCreatePoll}
                    style={tw`bg-blue-500 p-2 rounded-lg`}
                  >
                    <Text style={tw`text-white font-bold`}>Create Poll</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Super Chat Panel */}
      {showSuperChat && (
        <MotiView
          from={{ translateY: 300 }}
          animate={{ translateY: 0 }}
          transition={{ type: 'spring', damping: 15 }}
          style={tw`absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-lg p-4 z-50`}
        >
          <View style={tw`flex-row justify-between items-center mb-4`}>
            <Text style={[tw`text-xl font-bold`, { fontFamily: 'Helvetica_bold' }]}>Super Chat</Text>
            <TouchableOpacity onPress={() => setShowSuperChat(false)}>
              <X size={24} color="#000" />
            </TouchableOpacity>
          </View>
          
          <Text style={[tw`mb-4`, { fontFamily: 'Helvetica' }]}>
            Highlight your message with a Super Chat:
          </Text>
          
          <View style={tw`flex-row flex-wrap justify-between mb-4`}>
            {[10, 20, 30, 40, 50, 500].map((amount) => (
              <TouchableOpacity
                key={amount}
                onPress={() => handleSuperChatSelect(amount)}
                style={[
                  tw`w-[30%] h-20 mb-4 rounded-xl items-center justify-center shadow-sm`,
                  amount === 10 ? tw`bg-blue-100` : 
                  amount === 20 ? tw`bg-green-100` : 
                  amount === 30 ? tw`bg-yellow-100` : 
                  amount === 40 ? tw`bg-orange-100` : 
                  amount === 50 ? tw`bg-red-100` : 
                  tw`bg-purple-100`
                ]}
              >
                <Text style={[tw`text-lg font-bold`, { fontFamily: 'Helvetica_bold' }]}>₹{amount}</Text>
                {amount === 10 && <Text style={tw`text-xs text-gray-500`}>Basic</Text>}
                {amount === 20 && <Text style={tw`text-xs text-gray-500`}>Standard</Text>}
                {amount === 30 && <Text style={tw`text-xs text-gray-500`}>Premium</Text>}
                {amount === 40 && <Text style={tw`text-xs text-gray-500`}>Gold</Text>}
                {amount === 50 && <Text style={tw`text-xs text-gray-500`}>Platinum</Text>}
                {amount === 500 && <Text style={tw`text-xs text-gray-500`}>VIP</Text>}
              </TouchableOpacity>
            ))}
          </View>
          
          <Text style={[tw`text-xs text-gray-500 text-center mb-2`, { fontFamily: 'Helvetica' }]}>
            Your message will be highlighted and more visible to everyone
          </Text>
        </MotiView>
      )}

      <ImageBackground
        source={require('@/assets/screens/screen7th.png')}
        style={tw`flex-1`}
      >
        {/* Header */}
        <View style={tw`h-[54px] border-b border-[#E4E4E4] flex-row justify-between items-center px-3`}>
          <View style={tw`flex-row gap-3 items-center`}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <ArrowLeft size={24} color="#000" />
            </TouchableOpacity>
            <View style={tw`w-[40px] h-[40px] rounded-[14.5px] overflow-hidden`}>
              <MotiImage style={tw`w-full h-full`} source={{ uri: pfp }} />
            </View>
            <View>
              <Text style={[tw``, { fontFamily: 'Helvetica_bold' }]}>{username}</Text>
              <Text style={[tw`text-xs`, { fontFamily: 'Helvetica_bold' }]}>{active === 1 ? 'Online' : 'Offline'}</Text>
            </View>
          </View>
          <View style={tw`flex-row items-center gap-2`}>
            <TouchableOpacity style={tw`border-2 px-4 py-1 rounded-full`}>
              <Text style={[tw`font-bold`, { fontFamily: 'Helvetica_bold' }]}>Join</Text>
            </TouchableOpacity>
            <TouchableOpacity>
              <EllipsisVertical size={24} color="#000" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Message List */}
        <FlatList
          ref={flatListRef}
          data={chatHistory}
          renderItem={renderMessage}
          keyExtractor={item => item._id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={tw`pb-20 pt-3`}
          ListEmptyComponent={() => (
            <View style={tw`flex-1 items-center justify-center p-4`}>
              {isLoadingHistory ? (
                <ActivityIndicator size="large" color="#0000ff" />
              ) : (
                <Text style={[tw`text-gray-500`, { fontFamily: 'Helvetica' }]}>
                  No messages yet. Start the conversation!
                </Text>
              )}
            </View>
          )}
          onRefresh={fetchChatHistory}
          refreshing={isLoadingHistory}
        />


        {/* Input field - only shown when user is online */}
        {active === 1 && (
          <View style={[
            tw`absolute bottom-0 left-0 right-0 px-2 py-2`,
          ]}>


            {/* Message Input */}
            <View style={tw`flex-row items-center justify-between`}>
              <View style={tw`bg-gray-100 w-[88%] rounded-full flex-row items-center py-1.5 px-3`}>
                <TextInput
                  placeholder="Type your message..."
                  style={[
                    tw`flex-1 rounded-full px-4 py-2`,
                    { fontFamily: 'Helvetica' }
                  ]}
                  multiline
                  value={messageText}
                  onChangeText={setMessageText}
                />
              </View>
              <TouchableOpacity
                style={tw`w-12 h-12 bg-blue-500 rounded-full items-center justify-center`}
                onPress={checkBalanceAndSendMessage}
              >
                <Send size={24} color="#FFF" />
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ImageBackground>

    </>
  );
}
