import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import tw from 'twrnc';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LogOut } from 'lucide-react-native';
import { CommonActions } from '@react-navigation/native';

export default function Logout({ navigation }) {
  // Function to handle Logout
  const handleLogout = async () => {
    try {
      // Clear the user session from AsyncStorage
      await AsyncStorage.removeItem('userSession');

      // Show an alert to inform the user they have logged out
      Alert.alert('Logged Out', 'You have successfully logged out.', [
        {
          text: 'OK',
          onPress: () => {
            // Reset the navigation stack and navigate to the Welcome screen
            navigation.dispatch(
              CommonActions.reset({
                index: 1,
                routes: [{ name: 'Welcome' }],
              })
            );
          },
        },
      ]);
    } catch (error) {
      console.error('Error logging out:', error);
      Alert.alert('Error', 'Something went wrong. Please try again later.');
    }
  };

  return (
    <TouchableOpacity onPress={handleLogout} style={tw`flex-row items-center`}>
      <LogOut size={24} color={"red"} /> 
    </TouchableOpacity>
  );
}
