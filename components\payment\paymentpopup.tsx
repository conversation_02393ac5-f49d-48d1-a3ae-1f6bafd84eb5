import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useState, useEffect, useRef } from 'react';
import {
  View, Text, TouchableOpacity, Modal, Alert,
  ActivityIndicator, BackHandler, Animated, Dimensions,
  StyleSheet, ScrollView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import RazorpayCheckout from 'react-native-razorpay';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import * as Notifications from 'expo-notifications';
import ConfettiCannon from 'react-native-confetti-cannon';
import {
  Surface, Text as PaperText, Button,
  Divider, Avatar, Chip, ProgressBar,
  useTheme
} from 'react-native-paper';
import {
  CreditCard, Check, X, Shield, Gift,
  TrendingUp, Clock, Star, Zap
} from 'lucide-react-native';
import { MotiView } from 'moti';

interface PaymentOption {
  id: number;
  amount: number;
  bonus: number;
  label: string;
  minutes: number;
  popular?: boolean;
}

interface PaymentPopupProps {
  visible: boolean;
  onClose: () => void;
  rate: number;
  username: string;
}

const { width } = Dimensions.get('window');

const generateRechargeOptions = (rate: number): PaymentOption[] => {
  const multipliers = [3, 5, 10, 20, 30, 50, 100, 200];
  return multipliers.map((multiplier, index) => ({
    id: index + 1,
    amount: rate * multiplier,
    bonus: index === 1 ? Math.floor((rate * multiplier) * 0.5) :
           index === 2 ? Math.floor((rate * multiplier) * 0.2) :
           index === 3 ? Math.floor((rate * multiplier) * 0.3) : 0,
    label: `₹${rate * multiplier}`,
    minutes: multiplier,
    popular: index === 2 // Mark the 10-minute option as popular
  }));
};

const PaymentPopup: React.FC<PaymentPopupProps> = ({ visible, onClose, rate, username }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [paymentState, setPaymentState] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const rechargeOptions = generateRechargeOptions(rate);
  const confettiRef = useRef<ConfettiCannon>(null);
  // We don't need these states anymore as we're using the navigateToBottomTabs function

  // Function to handle navigation to bottom tabs
  const navigateToBottomTabs = () => {
    // First navigate to bottom tabs, then close the popup
    // @ts-ignore - Ignore the type error for navigation
    navigation.navigate('BottomTabs');
    // Call onClose after navigation to ensure the navigation happens
    setTimeout(() => onClose(), 100);
  };

  // We don't need to fetch tokens anymore as we're using the navigateToBottomTabs function

  // Animation when modal opens
  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, slideAnim, scaleAnim, opacityAnim]);

  // Handle back button
  useEffect(() => {
    if (!visible) return;
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Close the popup and navigate to bottom tabs when back button is pressed
      navigateToBottomTabs();
      return true;
    });
    return () => backHandler.remove();
  }, [visible, navigateToBottomTabs]);

  // Set reminder notification if payment fails
  useEffect(() => {
    if (paymentState === 'failed') {
      const timeoutId = setTimeout(() => {
        try {
          Notifications.scheduleNotificationAsync({
            content: {
              title: "Complete Your Recharge",
              body: `Don't miss out on chatting with ${username}. Complete your recharge now!`,
            },
            trigger: null, // Schedule for immediate delivery
          });
        } catch (error) {
          console.error('Failed to schedule notification:', error);
        }
      }, 60 * 1000); // 1 minute after failure

      return () => clearTimeout(timeoutId);
    }
  }, [paymentState, username]);

  // Trigger confetti on success
  useEffect(() => {
    if (paymentState === 'success' && confettiRef.current) {
      setTimeout(() => {
        confettiRef.current?.start();
      }, 300);
    }
  }, [paymentState]);

  // Handle back button for all payment states
  useEffect(() => {
    if (paymentState === 'processing' || paymentState === 'success' || paymentState === 'failed') {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        // Reset payment state and close popup when back button is pressed and navigate to bottom tabs
        setPaymentState('idle');
        navigateToBottomTabs();
        return true;
      });
      return () => backHandler.remove();
    }
  }, [paymentState, onClose]);

  const handlePayment = async () => {
    if (!selectedOption) return;

    setPaymentState('processing');
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      const user = userSession ? JSON.parse(userSession) : null;

      if (!user) {
        Alert.alert('Account Required', 'Please login to continue with payment');
        setPaymentState('idle');
        return;
      }

      const selectedRecharge = rechargeOptions.find(opt => opt.amount === selectedOption);
      const totalAmount = selectedOption + (selectedRecharge?.bonus || 0);

      const orderResponse = await fetch('https://api.nityasha.com/api/v1/paymemt/2');
      const orderData = await orderResponse.json();

      if (!orderData || !orderData.api) {
        Alert.alert('Service Unavailable', 'Payment service is temporarily unavailable. Please try again later.');
        setPaymentState('failed');
        return;
      }

      const options = {
        description: `Chat Balance: ${selectedRecharge?.minutes} minutes`,
        image: 'https://nityasha.com/logo-dark.svg',
        currency: 'INR',
        key: orderData.api,
        amount: selectedOption * 100,
        name: 'Nityasha Services',
        theme: { color: theme.colors.primary },
        order_id: orderData.order_id,
        notes: {
          userId: user.userId.toString(),
          rechargeAmount: totalAmount.toString(),
          minutes: selectedRecharge?.minutes ? selectedRecharge.minutes.toString() : '0'
        },
        prefill: {
          email: user.email || '',
          contact: user.phone || '',
          name: user.name || username
        }
      };

      RazorpayCheckout.open(options)
        .then(async (data) => {
          if (data.razorpay_payment_id) {
            try {
              // Get current balance and user data
              const userResponse = await fetch(`https://api.nityasha.com/api/balancepayment/${user.userId}`);
              console.log('User ID:', user.userId); // Debug user ID

              if (!userResponse.ok) {
                throw new Error(`Failed to fetch balance: ${userResponse.status}`);
              }
              const userData = await userResponse.json();

              // Get auth token with error handling
              const userSession = await AsyncStorage.getItem('userSession');
              if (!userSession) {
                throw new Error('No user session found');
              }
              // We already have user data from earlier, no need to parse again


              // Calculate new balance
              const currentBalance = Number(userData.balance);
              const selectedRechargeOption = rechargeOptions.find(opt => opt.amount === selectedOption);
              const bonusAmount = selectedRechargeOption?.bonus || 0;
              const newBalance = currentBalance + selectedOption + bonusAmount;

              // Validate request data
              if (!user.userId) {
                throw new Error('User ID is missing');
              }

              // Make balance update request
              const balanceUpdateResponse = await fetch(
                `https://api.nityasha.com/api/balancepayment/${user.userId}`,
                {
                  method: 'PUT',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    balance: newBalance,
                    userId: user.userId  // Add userId to request body
                  })
                }
              );

              if (!balanceUpdateResponse.ok) {
                throw new Error(`Balance update failed: ${balanceUpdateResponse.status}`);
              }

              const updatedData = await balanceUpdateResponse.json();

              // Verify payment success message
              if (updatedData.message !== "Balance updated successfully") {
                throw new Error('Payment update failed');
              }

              // Fetch latest balance
              const balanceResponse = await fetch(`https://api.nityasha.com/api/balancepayment/${user.userId}`, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                }
              });

              if (!balanceResponse.ok) {
                throw new Error('Failed to fetch updated balance');
              }

              const balanceData = await balanceResponse.json();

              if (!balanceData.balance) {
                throw new Error('Invalid balance data received');
              }

              // Update local storage with new balance
              const updatedUserSession = {
                ...user,
                balance: balanceData.balance
              };
              await AsyncStorage.setItem('userSession', JSON.stringify(updatedUserSession));

              setPaymentState('success');

              return; // Success - exit the retry loop
            } catch (error) {
              console.error('Balance update error:', error);

              // Store failed transaction details for retry
              try {
                const failedTransaction = {
                  payment_id: data.razorpay_payment_id,
                  amount: selectedOption,
                  bonus: selectedRecharge?.bonus || 0,
                  timestamp: new Date().toISOString(),
                  user_id: user.userId
                };
                await AsyncStorage.setItem('failed_transaction', JSON.stringify(failedTransaction));
              } catch (storageError) {
                console.error('Failed to store transaction:', storageError);
              }

              Alert.alert(
                'Payment Successful',
                'Your payment was processed but we had trouble updating your balance. Please contact support with payment ID: ' + data.razorpay_payment_id,
                [{
                  text: 'OK',
                  onPress: () => {
                    setPaymentState('idle');
                    navigateToBottomTabs();
                  }
                }]
              );
            }
          } else {
            throw new Error('Payment verification failed');
          }
        })
        .catch((error) => {
          console.log('Payment failed', error);
          setPaymentState('failed');
          setTimeout(() => setPaymentState('idle'), 4000);
        });
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert('Payment Error', 'An unexpected error occurred. Please try again later.');
      setPaymentState('failed');
      setTimeout(() => setPaymentState('idle'), 4000);
    }
  };

  const selectedRechargeOption = selectedOption
    ? rechargeOptions.find(opt => opt.amount === selectedOption)
    : null;

  // Processing Payment Screen
  if (paymentState === 'processing') {
    return (
      <Modal
        visible={true}
        transparent={false}
        animationType="fade"
        onRequestClose={() => {
          // Allow closing the processing screen with back button and navigate to bottom tabs
          setPaymentState('idle');
          navigateToBottomTabs();
        }}
      >
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <StatusBar backgroundColor={theme.colors.background} />

          <Surface style={styles.processingCard} elevation={0}>
            <ActivityIndicator size={60} color={theme.colors.primary} style={styles.loader} />

            <PaperText variant="headlineMedium" style={styles.processingTitle}>
              Processing Payment
            </PaperText>

            <PaperText variant="bodyLarge" style={styles.processingSubtitle}>
              Please wait while we verify your payment
            </PaperText>

            <ProgressBar
              indeterminate
              color={theme.colors.primary}
              style={styles.progressBar}
            />

            <View style={styles.securityInfoContainer}>
              <View style={styles.securityItem}>
                <Shield size={18} color={theme.colors.primary} />
                <PaperText variant="bodyMedium" style={styles.securityText}>
                  Secure Payment Gateway
                </PaperText>
              </View>

              <View style={styles.securityItem}>
                <Shield size={18} color={theme.colors.primary} />
                <PaperText variant="bodyMedium" style={styles.securityText}>
                  256-bit SSL Encryption
                </PaperText>
              </View>
            </View>

            <PaperText variant="bodySmall" style={styles.processingNote}>
              Please do not close the app or lock your device
            </PaperText>

            <Button
              mode="outlined"
              onPress={() => {
                setPaymentState('idle');
                navigateToBottomTabs();
              }}
              style={styles.cancelButton}
              labelStyle={styles.cancelButtonLabel}
            >
              Cancel
            </Button>
          </Surface>
        </View>
      </Modal>
    );
  }

  // Success Screen
  if (paymentState === 'success') {
    return (
      <Modal
        visible={true}
        transparent={false}
        animationType="fade"
        onRequestClose={() => {
          // Allow closing the success screen with back button and navigate to bottom tabs
          setPaymentState('idle');
          navigateToBottomTabs();
        }}
      >
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <StatusBar backgroundColor={theme.colors.background} />

          <ConfettiCannon
            ref={confettiRef}
            count={100}
            origin={{ x: width / 2, y: 0 }}
            autoStart={false}
            fadeOut={true}
            explosionSpeed={350}
            fallSpeed={3000}
            colors={['#FFD700', '#FFA500', '#FF4500', '#32CD32', '#1E90FF']}
          />

          <Surface style={styles.successCard} elevation={0}>
            <View style={[styles.successIconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
              <Check size={48} color={theme.colors.primary} />
            </View>

            {selectedRechargeOption && (
              <View style={styles.amountContainer}>
                <PaperText variant="headlineLarge" style={[styles.amountText, { color: theme.colors.primary }]}>
                  ₹{selectedRechargeOption.amount}
                </PaperText>

                {selectedRechargeOption.bonus > 0 && (
                  <Chip
                    icon={() => <Gift size={16} color={theme.colors.primary} />}
                    style={[styles.bonusChip, { backgroundColor: theme.colors.primaryContainer }]}
                  >
                    +₹{selectedRechargeOption.bonus} Bonus
                  </Chip>
                )}
              </View>
            )}

            <PaperText variant="titleLarge" style={styles.successTitle}>
              Recharge Successful!
            </PaperText>

            <PaperText variant="bodyLarge" style={styles.successSubtitle}>
              You can now chat with {username} for {selectedRechargeOption?.minutes} minutes
            </PaperText>

            <Button
              mode="contained"
              onPress={() => {
                setPaymentState('idle');
                navigateToBottomTabs();
              }}
              style={[styles.doneButton, { backgroundColor: theme.colors.primary }]}
              labelStyle={styles.doneButtonLabel}
            >
              Start Chatting
            </Button>
          </Surface>
        </View>
      </Modal>
    );
  }

  // Failed Screen
  if (paymentState === 'failed') {
    return (
      <Modal
        visible={true}
        transparent={false}
        animationType="fade"
        onRequestClose={() => {
          // Allow closing the failed screen with back button and navigate to bottom tabs
          setPaymentState('idle');
          navigateToBottomTabs();
        }}
      >
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          <StatusBar backgroundColor={theme.colors.background} />

          <Surface style={styles.failedCard} elevation={0}>
            <View style={styles.failedIconContainer}>
              <X size={48} color="#FFFFFF" />
            </View>

            <PaperText variant="headlineMedium" style={styles.failedTitle}>
              Payment Failed
            </PaperText>

            <PaperText variant="bodyLarge" style={styles.failedSubtitle}>
              We couldn't process your payment. Please try again.
            </PaperText>

            <Button
              mode="contained"
              onPress={() => setPaymentState('idle')}
              style={styles.tryAgainButton}
              labelStyle={styles.tryAgainButtonLabel}
            >
              Try Again
            </Button>
          </Surface>
        </View>
      </Modal>
    );
  }

  // Main Payment Screen
  return (
   <>
   {visible && (
    <MotiView
    from={{
      opacity: 0,
      transform: [{ translateY: 300 }]
    }}
    animate={{
      opacity: 1,
      transform: [{ translateY: 0 }]
    }}
    exit={{
      opacity: 0,
      transform: [{ translateY: 300 }]
    }}
    transition={{
      type: 'spring',
      damping: 20,
      mass: 1,
      stiffness: 250,
    }}
    style={[
      styles.modalOverlay,
      { flex: 1, backgroundColor: 'rgba(0,0,0,0.5)' }
    ]}
  >
    {/* Keep your existing StatusBar and TouchableOpacity components */}
    <StatusBar backgroundColor="rgba(0,0,0,0.5)" />
    <TouchableOpacity
      style={styles.dismissArea}
      activeOpacity={1}
      onPress={navigateToBottomTabs}
    />

    {/* Wrap modal content in another MotiView for additional animation */}
    <MotiView
      from={{
        transform: [{ scale: 0.9 }]
      }}
      animate={{
        transform: [{ scale: 1 }]
      }}
      transition={{
        type: 'timing',
        duration: 250,
      }}
      style={[
        styles.modalContent,
        {
          backgroundColor: theme.colors.background,
          paddingBottom: insets.bottom + 16,
        }
      ]}
    >
      {/* Keep your existing modal content */}
      <View style={styles.header}>
           <View style={styles.headerLeft}>
             <PaperText variant="titleMedium" style={styles.headerTitle}>
               Recharge Your Account
             </PaperText>

             <View style={styles.minBalanceContainer}>
               <Clock size={14} color={theme.colors.primary} style={styles.minBalanceIcon} />
               <PaperText variant="bodySmall" style={[styles.minBalanceText, { color: theme.colors.primary }]}>
                 Minimum balance of 3 minutes (₹{rate * 3}) required
               </PaperText>
             </View>
           </View>

           <TouchableOpacity
             onPress={() => {
               // Force close the popup when X button is pressed and navigate to bottom tabs
               navigateToBottomTabs();
             }}
             style={styles.closeButton}
           >
             <X size={24} color={theme.colors.onBackground} />
           </TouchableOpacity>
         </View>

         <Divider style={styles.divider} />

         {/* User Info */}
         <View style={styles.userInfoContainer}>
           <Avatar.Text
             size={40}
             label={username.substring(0, 2).toUpperCase()}
             style={{ backgroundColor: theme.colors.primaryContainer }}
             labelStyle={{ color: theme.colors.primary }}
           />

           <View style={styles.userTextContainer}>
             <PaperText variant="titleMedium" style={styles.usernameText}>
               Chat with {username}
             </PaperText>

             <PaperText variant="bodySmall" style={styles.rateText}>
               ₹{rate}/minute
             </PaperText >
           </View>
         </View>

         {/* Popular Tag */}
         <View style={styles.popularTagContainer}>
           <TrendingUp size={16} color={theme.colors.primary} />
           <PaperText variant="bodyMedium" style={[styles.popularTagText, { color: theme.colors.primary }]}>
             90% users recharge for 10 mins or more
           </PaperText>
         </View>

         {/* Payment Options */}
         <ScrollView
           horizontal={false}
           showsVerticalScrollIndicator={false}
           contentContainerStyle={styles.optionsScrollContent}
         >
           <View style={styles.optionsContainer}>
             {rechargeOptions.map((option) => (
               <TouchableOpacity
                 key={option.id}
                 onPress={() => setSelectedOption(option.amount)}
                 style={[
                   styles.optionCard,
                   selectedOption === option.amount && [
                     styles.selectedOption,
                     { borderColor: theme.colors.primary, backgroundColor: theme.colors.primaryContainer }
                   ],
                 ]}
               >
                 {option.popular && (
                   <View style={[styles.popularBadge, { backgroundColor: theme.colors.primary }]}>
                     <Star size={12} color="#FFFFFF" />
                     <PaperText variant="labelSmall" style={styles.popularBadgeText}>
                       POPULAR
                     </PaperText>
                   </View>
                 )}

                 <View style={styles.optionContent}>
                   <View style={styles.optionTopRow}>
                     <CreditCard
                       size={16}
                       color={selectedOption === option.amount ? theme.colors.primary : theme.colors.onSurfaceVariant}
                     />
                     <PaperText
                       variant="titleMedium"
                       style={[
                         styles.optionAmount,
                         selectedOption === option.amount && { color: theme.colors.primary }
                       ]}
                     >
                       {option.label}
                     </PaperText>
                   </View>

                   <PaperText
                     variant="bodySmall"
                     style={[
                       styles.optionMinutes,
                       selectedOption === option.amount && { color: theme.colors.primary }
                     ]}
                   >
                     {option.minutes} minutes
                   </PaperText>

                   {option.bonus > 0 && (
                     <View style={styles.bonusRow}>
                       <Gift
                         size={14}
                         color={selectedOption === option.amount ? theme.colors.primary : "#FF6B6B"}
                       />
                       <PaperText
                         variant="labelSmall"
                         style={[
                           styles.bonusText,
                           { color: selectedOption === option.amount ? theme.colors.primary : "#FF6B6B" }
                         ]}
                       >
                         +₹{option.bonus} BONUS
                       </PaperText>
                     </View>
                   )}
                 </View>
               </TouchableOpacity>
             ))}
           </View>
         </ScrollView>

         {/* Payment Button */}
         <Button
           mode="contained"
           onPress={handlePayment}
           disabled={!selectedOption}
           style={[
             styles.payButton,
             { backgroundColor: selectedOption ? theme.colors.primary : theme.colors.surfaceVariant }
           ]}
           labelStyle={styles.payButtonLabel}
           icon={({ size, color }) =>
             <Zap size={size - 4} color={color} />
           }
         >
           {selectedOption ? `Pay ₹${selectedOption}` : 'Select an amount'}
         </Button>

         {/* Security Note */}
         <View style={styles.securityNoteContainer}>
           <Shield size={14} color={theme.colors.onSurfaceVariant} />
           <PaperText variant="bodySmall" style={styles.securityNoteText}>
             Secure payment powered by Razorpay
           </PaperText>
         </View>
    </MotiView>
  </MotiView>
   )}
   </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  dismissArea: {
    flex: 1,
  },
  modalContent: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 16,
    paddingHorizontal: 16,
    maxHeight: '80%',
    backgroundColor: 'white', // Fallback color
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontWeight: '700',
  },
  minBalanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  minBalanceIcon: {
    marginRight: 4,
  },
  minBalanceText: {
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  divider: {
    marginVertical: 12,
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  userTextContainer: {
    marginLeft: 12,
  },
  usernameText: {
    fontWeight: '600',
  },
  rateText: {
    marginTop: 2,
  },
  popularTagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  popularTagText: {
    marginLeft: 6,
    fontWeight: '500',
  },
  optionsScrollContent: {
    paddingBottom: 8,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  optionCard: {
    width: '48%',
    borderWidth: 1.5,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    position: 'relative',
    overflow: 'hidden',
  },
  selectedOption: {
    borderWidth: 1.5,
  },
  popularBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomLeftRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  popularBadgeText: {
    color: 'white',
    marginLeft: 2,
    fontSize: 8,
    fontWeight: 'bold',
  },
  optionContent: {
    justifyContent: 'center',
  },
  optionTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionAmount: {
    marginLeft: 6,
    fontWeight: '600',
  },
  optionMinutes: {
    marginTop: 4,
  },
  bonusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  bonusText: {
    marginLeft: 4,
    fontWeight: '600',
  },
  payButton: {
    borderRadius: 12,
    marginTop: 8,
    paddingVertical: 6,
  },
  payButtonLabel: {
    fontSize: 16,
    fontWeight: '700',
    paddingVertical: 2,
  },
  securityNoteContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  securityNoteText: {
    marginLeft: 6,
    opacity: 0.7,
  },
  // Processing Screen
  processingCard: {
    width: '90%',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  loader: {
    marginBottom: 24,
  },
  processingTitle: {
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  processingSubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  progressBar: {
    width: '100%',
    height: 6,
    borderRadius: 3,
    marginBottom: 24,
  },
  securityInfoContainer: {
    width: '100%',
    backgroundColor: '#F5F5F5',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  securityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  securityText: {
    marginLeft: 8,
  },
  processingNote: {
    textAlign: 'center',
    opacity: 0.6,
  },
  // Success Screen
  successCard: {
    width: '90%',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  successIconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  amountContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  amountText: {
    fontWeight: '700',
    marginBottom: 8,
  },
  bonusChip: {
    height: 28,
  },
  successTitle: {
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  successSubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  doneButton: {
    width: '100%',
    borderRadius: 12,
  },
  doneButtonLabel: {
    fontSize: 16,
    fontWeight: '700',
    paddingVertical: 4,
  },
  // Failed Screen
  failedCard: {
    width: '90%',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  failedIconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: '#FF6B6B',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  failedTitle: {
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
    color: '#FF6B6B',
  },
  failedSubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  tryAgainButton: {
    width: '100%',
    borderRadius: 12,
    backgroundColor: '#FF6B6B',
  },
  tryAgainButtonLabel: {
    fontSize: 16,
    fontWeight: '700',
    paddingVertical: 4,
  },
  // Cancel button styles
  cancelButton: {
    marginTop: 16,
    borderRadius: 12,
    borderColor: '#FF6B6B',
  },
  cancelButtonLabel: {
    color: '#FF6B6B',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default PaymentPopup;