import React, { useState, useEffect } from 'react';
import { View, Text, Switch, StyleSheet, TouchableOpacity, Modal, Alert } from 'react-native';
import { setProdiston, isProdistonEnabled, getProdistonState } from '@/utils/config';
import tw from 'twrnc';
import { X, Info } from 'lucide-react-native';

interface ProdistonToggleProps {
  onToggle?: (isEnabled: boolean) => void;
  showLabel?: boolean;
  style?: any;
}

const ProdistonToggle: React.FC<ProdistonToggleProps> = ({ 
  onToggle, 
  showLabel = true,
  style 
}) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [showInfoModal, setShowInfoModal] = useState(false);

  // Load the initial state
  useEffect(() => {
    const loadProdistonState = async () => {
      const enabled = await isProdistonEnabled();
      setIsEnabled(enabled);
    };
    
    loadProdistonState();
  }, []);

  const toggleSwitch = async () => {
    try {
      // If turning on production mode, show confirmation
      if (!isEnabled) {
        Alert.alert(
          "Enable Production Mode?",
          "This will switch the app to use production API endpoints. Are you sure?",
          [
            {
              text: "Cancel",
              style: "cancel"
            },
            { 
              text: "Enable", 
              onPress: async () => {
                const newState = !isEnabled;
                await setProdiston(newState);
                setIsEnabled(newState);
                if (onToggle) onToggle(newState);
              }
            }
          ]
        );
      } else {
        // If turning off, just do it
        const newState = !isEnabled;
        await setProdiston(newState);
        setIsEnabled(newState);
        if (onToggle) onToggle(newState);
      }
    } catch (error) {
      console.error('Error toggling prodiston:', error);
      Alert.alert('Error', 'Failed to toggle production mode');
    }
  };

  return (
    <View style={[tw`flex-row items-center`, style]}>
      {showLabel && (
        <TouchableOpacity onPress={() => setShowInfoModal(true)}>
          <View style={tw`flex-row items-center mr-2`}>
            <Text style={tw`text-sm mr-1`}>Prodiston</Text>
            <Info size={16} color="#666" />
          </View>
        </TouchableOpacity>
      )}
      
      <Switch
        trackColor={{ false: "#767577", true: "#81b0ff" }}
        thumbColor={isEnabled ? "#0066cc" : "#f4f3f4"}
        ios_backgroundColor="#3e3e3e"
        onValueChange={toggleSwitch}
        value={isEnabled}
      />
      
      <Modal
        animationType="slide"
        transparent={true}
        visible={showInfoModal}
        onRequestClose={() => setShowInfoModal(false)}
      >
        <View style={tw`flex-1 justify-center items-center bg-black/50`}>
          <View style={tw`bg-white p-5 rounded-xl w-[80%] max-w-[350px]`}>
            <View style={tw`flex-row justify-between items-center mb-4`}>
              <Text style={tw`text-lg font-bold`}>About Prodiston</Text>
              <TouchableOpacity onPress={() => setShowInfoModal(false)}>
                <X size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <Text style={tw`mb-3`}>
              Prodiston is a production mode toggle that switches the app to use production API endpoints.
            </Text>
            
            <Text style={tw`mb-3`}>
              • When <Text style={tw`font-bold`}>enabled</Text>, the app uses production endpoints.
            </Text>
            
            <Text style={tw`mb-3`}>
              • When <Text style={tw`font-bold`}>disabled</Text>, the app uses development endpoints.
            </Text>
            
            <Text style={tw`mb-5 text-red-500`}>
              Note: Switching modes may require restarting the app for all changes to take effect.
            </Text>
            
            <TouchableOpacity 
              style={tw`bg-blue-500 py-2 px-4 rounded-lg self-center`}
              onPress={() => setShowInfoModal(false)}
            >
              <Text style={tw`text-white font-bold`}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ProdistonToggle;
