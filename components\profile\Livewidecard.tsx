import React, { useEffect, useState } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    Text,
    ScrollView,
    Alert,
    Modal,
    TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import Headingsixth from '@/components/ui/Headingsixth';
import { useNavigation } from '@react-navigation/native';
import { BlurView } from "expo-blur";

const TopConsultants = () => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [balance, setBalance] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
    const [rechargeAmount, setRechargeAmount] = useState('');
    const [isNewUser, setIsNewUser] = useState(null);
    const navigation = useNavigation();
    const rechargeMinimumAmount = 1; // Set a minimum recharge amount

    const fetchConsultants = async () => {
        setLoading(true);
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/consultants');
            if (!response.ok) throw new Error('Failed to fetch consultants.');
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const fetchUserBalance = async (userId) => {
        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
            if (!response.ok) throw new Error('Failed to fetch balance');
            const userData = await response.json();
            setBalance(parseFloat(userData.balance) || 0);
        } catch (err) {
            setError(err.message);
        }
    };

    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (userSession) {
                    const userId = JSON.parse(userSession).userId;
                    await fetchUserBalance(userId);
                    const userStatus = await AsyncStorage.getItem('isNewUser');
                    setIsNewUser(userStatus === '0');
                } else {
                    navigation.navigate('Welcome');
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        checkLoginStatus();
        fetchConsultants();

        // Set up an interval to auto-reload consultants and user balance every 6 seconds
        const intervalId = setInterval(() => {
            fetchConsultants();
            const userSession = AsyncStorage.getItem('userSession').then((session) => {
                const userId = JSON.parse(session).userId;
                fetchUserBalance(userId);
            });
        }, 6000); // 6 seconds interval

        // Clean up the interval on component unmount
        return () => clearInterval(intervalId);
    }, [navigation]);

    const updateBalance = async (newBalance) => {
        const userSession = await AsyncStorage.getItem('userSession');
        const userId = JSON.parse(userSession).userId;

        try {
            const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ balance: newBalance }),
            });

            if (response.ok) {
                if (newBalance !== balance) { // Check if the balance is actually changing
                    Alert.alert(`Balance updated. Your new balance is ₹${newBalance}.`);
                    setBalance(newBalance);
                }
            } else {
                const errorData = await response.json();
                Alert.alert(`Error updating balance: ${errorData.message || 'Unknown error'}`);
            }
        } catch (error) {
            Alert.alert("Error updating balance.");
        }
    };

    const handleRecharge = () => {
        const rechargeValue = parseFloat(rechargeAmount);
        if (!isNaN(rechargeValue) && rechargeValue >= rechargeMinimumAmount) {
            const newBalance = balance + rechargeValue;
            updateBalance(newBalance);
            setModalVisible(false);
            setRechargeAmount('');
        } else {
            Alert.alert("Please enter a valid amount to recharge.");
        }
    };

    const handleChatNow = async (consultantId, perMinuteRate) => {
        const maxChatDuration = Math.floor(balance / perMinuteRate); // Calculate maximum chat duration
        const freeChatDuration = isNewUser ? 5 : 0; // 5 free minutes for new users


        // If the user is new and has free minutes left
        if (isNewUser && freeChatDuration > 0) {
            Alert.alert(`Enjoy your 5 minutes of free chat!`);
            await AsyncStorage.setItem('isNewUser', '1'); // Mark the user as old after the free chat
            navigation.navigate('Chat', { consultantId, balance, chatDuration: freeChatDuration });
        } else if (maxChatDuration <= 0) {
            navigation.navigate('Balance') // Show insufficient balance modal
        } else {
            // Calculate total cost for the maximum duration
            const totalCost = maxChatDuration * perMinuteRate;
            const chatDuration = maxChatDuration; // Set chat duration to the maximum allowed

            Alert.alert(
                "Confirm Chat",
                `You are about to start a chat for ₹${totalCost} for ${chatDuration} minute(s). Do you want to proceed?`,
                [
                    {
                        text: "Cancel",
                        style: "cancel"
                    },
                    {
                        text: "OK",
                        onPress: async () => {
                            const newBalance = balance - totalCost; // Deduct total cost from balance
                            if (newBalance < 0) {
                                Alert.alert("Insufficient balance for this chat duration.");
                            } else {
                                await updateBalance(newBalance); // Update the balance
                                navigation.navigate('Chat', { consultantId, balance: newBalance, chatDuration }); // Navigate to chat
                            }
                        }
                    }
                ]
            );
        }
    };

    if (error) {
        return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
    }

    return (
        <View style={tw`mt-2`}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={tw`flex-row flex-wrap`}>
                    {consultants.length === 0 ? (
                        <Text style={tw`text-center w-full`}>No consultants available.</Text>
                    ) : (
                        consultants.map((consultant) => (
                            <TouchableOpacity key={consultant.id}  style={tw`flex items-center rounded-[8px] overflow-hidden justify-center mr-3 relative h-30 w-23`}>
                                <View style={tw`w-23 h-30 rounded-[8px] relative`}>
                                    {/* <View style={tw`flex items-center justify-center absolute px-1 py-0.5 top-2 z-10 left-2 bg-white rounded-[6px]`}>
                                        <Text style={[tw`text-[10px] font-bold text-[#000]`, { fontFamily: 'Helvetica_bold' }]}>₹{consultant.per_minute_rate}/min</Text>
                                    </View> */}
                                    <Image source={{ uri: consultant.thumnel }} style={tw`w-full h-full rounded-[8px]`} />
                                </View>
                            </TouchableOpacity>
                        ))
                    )}
                </View>
            </ScrollView>
        </View>
    );
};

export default TopConsultants;
