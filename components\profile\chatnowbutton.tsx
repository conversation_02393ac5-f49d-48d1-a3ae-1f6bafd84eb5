import React, { useEffect, useState } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Text,
  ScrollView,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import tw from 'twrnc';
import Headingsixth from '@/components/ui/Headingsixth';
import { useNavigation } from '@react-navigation/native';
import { BlurView } from "expo-blur";
import Button from '../ui/Button';

const TopConsultants = ({ consultantsss ,consultantssss}) => {
  const [consultants, setConsultants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [balance, setBalance] = useState(0);  // default balance is 0
  const [modalVisible, setModalVisible] = useState(false);
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [isNewUser, setIsNewUser] = useState(null);
  const navigation = useNavigation();
  const rechargeMinimumAmount = 1; // Set a minimum recharge amount

  // Fetch consultants
  const fetchConsultants = async () => {
    setLoading(true);
    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/consultants/${consultantssss}`);
      if (!response.ok) throw new Error('Failed to fetch consultants.');
      const data = await response.json();
      setConsultants(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch user balance with validation to avoid NaN
  const fetchUserBalance = async () => {
    const userSession = await AsyncStorage.getItem('userSession');
    const userId = JSON.parse(userSession).userId;
    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
      if (!response.ok) throw new Error('Failed to fetch balance');
      const userData = await response.json();
      
      // Check if userData.balance is a valid number before setting it
      const parsedBalance = parseFloat(userData.balance);
      if (isNaN(parsedBalance)) {
        setBalance(0);  // or set to a default valid balance if needed
      } else {
        setBalance(parsedBalance);
      }
    } catch (err) {
      setError(err.message);
    }
  };

  // Check login status and fetch consultants
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const userSession = await AsyncStorage.getItem('userSession');
        if (userSession) {
          const userId = JSON.parse(userSession).userId;
          await fetchUserBalance(userId);
          const userStatus = await AsyncStorage.getItem('isNewUser');
          setIsNewUser(userStatus === '0');
        } else {
          navigation.navigate('Welcome');
        }
      } catch (error) {
        console.error("Error checking login status:", error);
      }
    };

    checkLoginStatus();
    fetchConsultants();

    const intervalId = setInterval(fetchConsultants, 6000);
    return () => clearInterval(intervalId);
  }, [navigation]);

  // Update user balance
  const updateBalance = async (newBalance) => {
    const userSession = await AsyncStorage.getItem('userSession');
    const userId = JSON.parse(userSession).userId;

    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ balance: newBalance }),
      });

      if (response.ok) {
        if (newBalance !== balance) {
          setBalance(newBalance);
        }
      } else {
        const errorData = await response.json();
        Alert.alert(`Error updating balance: ${errorData.message || 'Unknown error'}`);
      }
    } catch (error) {
      Alert.alert("Error updating balance.");
    }
  };

  // Handle recharge action
  const handleRecharge = () => {
    const rechargeValue = parseFloat(rechargeAmount);
    if (isNaN(rechargeValue) || rechargeValue < rechargeMinimumAmount) {
      Alert.alert("Please enter a valid amount to recharge.");
    } else {
      const newBalance = balance + rechargeValue;
      updateBalance(newBalance);
      setModalVisible(false);
      setRechargeAmount('');
    }
  };

  // Handle chat now action
  const handleChatNow = async (consultantId, perMinuteRate) => {
    const maxChatDuration = Math.floor(balance / perMinuteRate);
    const freeChatDuration = isNewUser ? 5 : 0;


    // If the user is new and has free minutes left
    if (isNewUser && freeChatDuration > 0) {
      Alert.alert(`Enjoy your 5 minutes of free chat!`);
      await AsyncStorage.setItem('isNewUser', '1');
      navigation.navigate('Chat', { consultantId, balance, chatDuration: freeChatDuration });
    } else if (maxChatDuration <= 0) {
      navigation.navigate('Balance') // Show insufficient balance modal
    } else {
      const totalCost = maxChatDuration * perMinuteRate;
      const chatDuration = maxChatDuration;

      Alert.alert(
        "Confirm Chat",
        `You are about to start a chat for ₹${totalCost} for ${chatDuration} minute(s). Do you want to proceed?`,
        [
          {
            text: "Cancel",
            style: "cancel"
          },
          {
            text: "OK",
            onPress: async () => {
              const newBalance = balance - totalCost;
              if (newBalance < 0) {
                Alert.alert("Insufficient balance for this chat duration.");
              } else {
                await updateBalance(newBalance);
                navigation.navigate('Chat', { consultantId, balance, chatDuration });
              }
            }
          }
        ]
      );
    }
  };

  if (error) {
    return <Text style={tw`text-red-500 text-center`}>{error}</Text>;
  }

  return (
    <View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={tw`flex-row flex-wrap`}>
              <View>
                <Button onPress={() => handleChatNow(consultantssss, consultantsss)}>Chat Now</Button>
              </View>
        </View>
      </ScrollView>

      <Modal visible={modalVisible} animationType="slide" transparent={true}>
          <View style={tw`flex-1 justify-center items-center absolute bottom-0 w-full bg-white border-2 rounded-t-3xl border-yellow-500`}>
            <View style={tw`p-6 rounded-lg w-full`}>
              <Text style={[tw`text-lg font-semibold mb-4 w-full text-center`, { fontFamily: 'Helvetica_bold' }]}>
                Your Balance is ₹{balance.toFixed(2)}
              </Text>
              <TextInput
                style={[tw`border-2 p-2 px-3 rounded-xl font-bold`, { fontFamily: 'Helvetica_bold' }]}
                placeholder="Enter recharge amount"
                keyboardType="numeric"
                value={rechargeAmount}
                onChangeText={setRechargeAmount}
              />
              <View style={tw`flex flex-row gap-2 mt-3 justify-center`}>
                <TouchableOpacity onPress={handleRecharge} style={tw`w-[50%] flex bg-black px-5 py-2 rounded-lg items-center justify-center`}>
                  <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold', textAlign: 'center' }]}>Recharge</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setModalVisible(false)} style={tw`w-[50%] flex bg-black px-5 py-2 rounded-lg items-center justify-center`}>
                  <Text style={[tw`text-white font-semibold`, { fontFamily: 'Helvetica_bold', textAlign: 'center' }]}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
      </Modal>
    </View>
  );
};

export default TopConsultants;
