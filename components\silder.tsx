import { View, Text, ScrollView, TouchableOpacity, Image, RefreshControl } from 'react-native';
import React, { useEffect, useState } from 'react';
import tw from 'twrnc';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';

export default function Silder() {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const navigation = useNavigation();
    const reloadIntervalMs = 60000; // Auto reload every 60 seconds

    // Fetch consultants from API
    const fetchConsultants = async () => {
        setLoading(true);
        try {
            const response = await fetch('https://nityasha.vercel.app/api/v1/silder/get');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            setConsultants(data);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const checkLoginStatus = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');
                if (!userSession) {
                    navigation.navigate('Welcome');
                }
            } catch (error) {
                console.error("Error checking login status:", error);
            }
        };

        checkLoginStatus(); // Call the function to check login status
        fetchConsultants(); // Initial fetch of consultants

        // Set up auto-reload for consultants
        const interval = setInterval(() => {
            fetchConsultants();
        }, reloadIntervalMs);

        return () => clearInterval(interval); // Cleanup interval on component unmount
    }, [navigation]);

    // Handle refresh control for pull-to-refresh functionality
    const onRefresh = () => {
        fetchConsultants();
    };

    return (
        <ScrollView 
            horizontal={true} 
            refreshControl={
                <RefreshControl refreshing={loading} onRefresh={onRefresh} />
            }
        >
            {loading && <Text>Loading...</Text>}
            {error && <Text>Error: {error}</Text>}
            {consultants.map((consultant) => (
                <View key={consultant.id} style={tw`w-[25.2rem] mr-2 h-auto flex items-center overflow-hidden justify-center mt-2 rounded-[10px] border-2 border-red-600`}>
                    <Image 
                        source={{ uri: consultant.url }} // Fixed the uri to use the correct format
                        style={tw`w-[25rem] h-[10rem]`} 
                        resizeMode="cover" // Optional: improve image appearance
                    />
                </View>
            ))}
        </ScrollView>
    );
}
