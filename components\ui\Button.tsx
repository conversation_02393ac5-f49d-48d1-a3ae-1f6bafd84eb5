import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import React from 'react';
import { useFonts } from 'expo-font';
import tw from 'twrnc';

export default function Button({ children, onPress, variant = 'primary' }) {
    const [loaded] = useFonts({
        'Satoshi-Variable': require('../../assets/fonts/Satoshi-Medium.otf'),
    });

    if (!loaded) {
        return (
            <View style={tw`px-5 w-full py-3 flex items-center justify-center`}>
                <ActivityIndicator color="#000" />
            </View>
        );
    }

    const buttonStyles = {
        primary: tw`bg-[#000] text-white w-full`,
        secondary: tw`bg-white shadow-none w-full border-[#D7D9DC] border-2 `,
        primaryfifthi: tw`bg-[#000] text-white w-[50%]`,
        fifthi: tw`bg-[#ffff] text-white w-[50%] border`,
        Text: tw`text-white w-[50%]`,
    };

    const textStyles = {
        primary: tw`text-white`,
        secondary: tw`text-[#39434F]`,
        primaryfifthi: tw`text-white`,
        fifthi: tw`text-black`,
        Text: tw`text-white`,
    };

    return (
        <TouchableOpacity 
            style={[
                tw`px-5 text-[14px] py-3 flex items-center justify-center text-center rounded-[8px]`, 
                buttonStyles[variant]
                ,{fontFamily: 'Geist-SemiBold'}
            ]} 
            onPress={onPress}
            accessibilityLabel={children}
            accessibilityRole="button"
        >
            <Text style={[textStyles[variant], { fontFamily: 'Satoshi-Variable', fontWeight: '500' }]}>
                {children}
            </Text>
        </TouchableOpacity>
    );
}
