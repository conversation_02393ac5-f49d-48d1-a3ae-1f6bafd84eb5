"use client";

import { useRef, useEffect } from "react";
import { View, Text, Animated } from "react-native";
import tw, { style } from "twrnc";
import { Users, Search, AlertCircle } from "lucide-react-native";

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: "users" | "search" | "alert";
}

export default function EmptyState({ title, description, icon = "users" }: EmptyStateProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  // ✅ Fixed renderIcon function
  const renderIcon = () => {
    let IconComponent: React.ElementType;
  
    switch (icon) {
      case "users":
        IconComponent = Users;
        break;
      case "search":
        IconComponent = Search;
        break;
      case "alert":
        IconComponent = AlertCircle;
        break;
      default:
        IconComponent = Users;
    }
  
    return <IconComponent size={50} stroke="#D97706" />;
  };
  
  return (
    <Animated.View
      style={[
        tw`flex-1 items-center justify-center px-6`,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <View style={tw`bg-amber-100/70 p-6 rounded-full mb-4`}>{renderIcon()}</View>
      <Text style={tw`text-xl font-bold text-amber-900 mb-2 text-center`}>{title}</Text>
      <Text style={tw`text-amber-700 text-center`}>{description}</Text>
    </Animated.View>
  );
}
