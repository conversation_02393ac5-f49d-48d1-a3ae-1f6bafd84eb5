import { View, Text } from 'react-native'
import React from 'react'
import tw from 'twrnc';
import { useFonts } from 'expo-font';

export default function HeadingCenter({ children }) {
  const [loaded] = useFonts({
    'Satoshi-Variable': require('../../assets/fonts/Satoshi-Medium.otf'),
  });

  if (!loaded) {
    return null;
  }
  return (
    <Text
      style={[tw`text-[28px] text-center w-full font-medium leading-[40px] text-[#fff]`, { fontFamily: 'Geist-SemiBold', }]}
    >{children}
    </Text>
  )
}