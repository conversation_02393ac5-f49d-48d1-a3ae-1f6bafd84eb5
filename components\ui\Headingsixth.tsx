import { View, Text } from 'react-native'
import React from 'react'
import tw from 'twrnc';
import { useFonts } from 'expo-font';

export default function Headingsixth({ children }) {
  const [loaded] = useFonts({
    'Satoshi-Variable': require('@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf'),
  });

  if (!loaded) {
    return null;
  }
  return (
    <Text
      style={[tw`text-[16px] w-full font-medium leading-[24px] text-[#39434F]`, { fontFamily: 'Satoshi-Variable', }]}
    >{children}
    </Text>
  )
}