import { View, Text } from 'react-native'
import React from 'react'
import tw from 'twrnc';
import { useFonts } from 'expo-font';

export default function Paragraph({ children }) {
    const [loaded] = useFonts({
        'Satoshi-Variable': require('../../assets/fonts/Satoshi-Medium.otf'),
    });

    if (!loaded) {
        return null;
    }

  return (
      <Text style={[tw`font-normal text-[#39434F] w-full text-[14px] leading-[21px]`, { fontFamily: 'Satoshi-Variable' }]}>{children}</Text>
  )
};