import React from 'react';
import { View, StyleSheet } from 'react-native';
import { FlatListSlider } from 'react-native-flatlist-slider';

const ImageSlider = () => {
  const images = [
    {
        image: require('../../assets/images/Rectangle 17.png'),
    },
  ];

  return (
    <View style={styles.container}>
      <FlatListSlider
        data={images}
        width={300}
        height={200}
        timer={3000} // interval time in ms
        imageKey={'image'} // key to access image in data array
        local={false} // if images are local, set this to true
        autoscroll={true} // enable auto-scrolling
        separatorWidth={10} // adds gap between images
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ImageSlider;
