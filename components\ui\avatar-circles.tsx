import React from "react";
import { View, Image, Text, TouchableOpacity } from "react-native";
import { useNavigation } from "@react-navigation/native";

interface Avatar {
  imageUrl: string;
  profileUrl: string;
}

interface AvatarCirclesProps {
  className?: string;
  numPeople?: number;
  avatarUrls: Avatar[];
}

export const AvatarCircles = ({
  numPeople,
  avatarUrls
}: AvatarCirclesProps) => {
  const navigation = useNavigation();

  return (
    <View style={{ flexDirection: "row", alignItems: "center" }}>
      {avatarUrls.map((url, index) => (
        <TouchableOpacity
          key={index}
          onPress={() =>
          }
          style={{ marginLeft: index === 0 ? 0 : -10 }}
        >
          <Image
            source={{ uri: url.imageUrl }}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              borderWidth: 2,
              borderColor: "white"
            }}
          />
        </TouchableOpacity>
      ))}
      {(numPeople ?? 0) > 0 && (
        <TouchableOpacity
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: "black",
            alignItems: "center",
            justifyContent: "center",
            borderWidth: 2,
            borderColor: "white"
          }}
        >
          <Text style={{ color: "white", fontSize: 12, fontWeight: "bold" }}>
            +{numPeople}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};
