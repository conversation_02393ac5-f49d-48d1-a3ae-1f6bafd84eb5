import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, Alert, Modal, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import tw from 'twrnc';
import { useFonts } from 'expo-font';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Profile({ navigation }) {
  const [modalVisible, setModalVisible] = useState(false);
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(false);

  const openLogoutModal = () => {
    navigation.navigate('Balance')
  };

  const closeLogoutModal = () => {
    setModalVisible(false);
  };

  const [loaded] = useFonts({
    'Helvetica_bold': require('@/assets/fonts/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.otf'),
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const userSession = await AsyncStorage.getItem('userSession');
  
        if (!userSession) {
          return;
        }
  
        // Ensure userSession is parsed correctly if it's stored as a stringified JSON object
        const parsedUserSession = JSON.parse(userSession); 
  
        // Fetch user data using the userId
        const fetchUrl = `https://nityasha.vercel.app/api/v1/users/${parsedUserSession.userId}`;
  
        const response = await fetch(fetchUrl);
  
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
  
        const data = await response.json();
        setUserData(data);
      } catch (error) {
        console.error('Error fetching user data:', error);
        // Handle error (e.g., show error message)
      } finally {
        setLoading(false);
      }
    };
  
    fetchUserData();
  }, []);  

  return (
        <Image 
          source={{ uri: userData?.pfp || `https://www.pngarts.com/files/10/Default-Profile-Picture-PNG-Download-Image.png` }} 
          style={tw`w-full h-full rounded-full`} 
        />
  );
}
