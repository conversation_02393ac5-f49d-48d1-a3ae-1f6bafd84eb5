import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the interface for consultant data
interface Consultant {
  id: string;
  name: string;
  category_name?: string;
  per_minute_rate: number | string;
  pfp?: string;
  active?: number;
  verified?: number;
  exp?: number;
  isChatOn?: number;
  isLive?: number;
}

// Define the context type
interface ConsultantPreloadContextType {
  isPreloaded: boolean;
  preloadedConsultants: Consultant[];
  preloadConsultants: () => Promise<void>;
  clearPreloadedData: () => void;
}

// Create the context with default values
const ConsultantPreloadContext = createContext<ConsultantPreloadContextType>({
  isPreloaded: false,
  preloadedConsultants: [],
  preloadConsultants: async () => {},
  clearPreloadedData: () => {},
});

// Custom hook to use the context
export const useConsultantPreload = () => useContext(ConsultantPreloadContext);

// Cache key for storing preloaded consultants
const CONSULTANTS_CACHE_KEY = 'preloaded_consultants_cache';

// Provider component
export const ConsultantPreloadProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isPreloaded, setIsPreloaded] = useState(false);
  const [preloadedConsultants, setPreloadedConsultants] = useState<Consultant[]>([]);
  const [lastPreloadTime, setLastPreloadTime] = useState<number>(0);
  const isLoadingRef = useRef(false);

  // API URL for consultants
  const API_URL = 'https://nityasha.vercel.app/api/v1/consultants';

  // Function to preload consultant data
  const preloadConsultants = async () => {
    try {
      // Prevent multiple simultaneous preloads
      if (isLoadingRef.current) {
        return;
      }

      // Check if we already have fresh preloaded data (less than 2 minutes old)
      const currentTime = Date.now();
      const CACHE_EXPIRY = 2 * 60 * 1000; // 2 minutes in milliseconds

      if (isPreloaded && preloadedConsultants.length > 0 && (currentTime - lastPreloadTime) < CACHE_EXPIRY) {
        // Use existing data without logging to reduce console noise
        return;
      }

      // Set loading flag
      isLoadingRef.current = true;

      // Try to load from cache first for immediate display
      try {
        const cachedData = await AsyncStorage.getItem(CONSULTANTS_CACHE_KEY);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          if (parsedData && Array.isArray(parsedData) && parsedData.length > 0) {
            // Set preloaded consultants from cache immediately
            setPreloadedConsultants(parsedData);

            // Mark as preloaded so the UI can use this data immediately
            setIsPreloaded(true);

            console.log(`Loaded ${parsedData.length} consultants from cache`);
          }
        }
      } catch (cacheError) {
        console.error('Error loading cached consultants:', cacheError);
      }

      // Fetch fresh data from API
      try {
        const response = await fetch(API_URL);
        if (!response.ok) {
          throw new Error(`Failed to fetch consultants: ${response.status}`);
        }

        const data = await response.json();

        // Only update if we have new data and it's different from what we already have
        if (data.length > 0 &&
            (preloadedConsultants.length === 0 ||
             JSON.stringify(data) !== JSON.stringify(preloadedConsultants))) {

          setPreloadedConsultants(data);
          setLastPreloadTime(currentTime);

          // Save to AsyncStorage for faster loading next time
          try {
            await AsyncStorage.setItem(CONSULTANTS_CACHE_KEY, JSON.stringify(data));
            console.log(`Preloaded ${data.length} consultants saved to AsyncStorage`);
          } catch (saveError) {
            console.error('Error saving preloaded consultants to AsyncStorage:', saveError);
          }
        }
      } catch (apiError) {
        console.error('Error fetching consultants from API:', apiError);
      }

      // Mark as preloaded if not already done
      setIsPreloaded(true);
    } catch (error) {
      console.error('Error preloading consultants:', error);
    } finally {
      // Reset loading flag
      isLoadingRef.current = false;
    }
  };

  // Function to clear preloaded data
  const clearPreloadedData = () => {
    setIsPreloaded(false);
    setPreloadedConsultants([]);
    setLastPreloadTime(0);
  };

  // Preload data when the app starts
  useEffect(() => {
    preloadConsultants();
  }, []);

  return (
    <ConsultantPreloadContext.Provider
      value={{
        isPreloaded,
        preloadedConsultants,
        preloadConsultants,
        clearPreloadedData,
      }}
    >
      {children}
    </ConsultantPreloadContext.Provider>
  );
};

export default ConsultantPreloadContext;
