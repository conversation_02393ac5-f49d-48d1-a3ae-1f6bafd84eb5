import React, { createContext, useContext, useState, useEffect } from 'react';
import { Keyboard, Platform } from 'react-native';

// Define the context type
type KeyboardContextType = {
  isKeyboardVisible: boolean;
};

// Create the context with a default value
const KeyboardContext = createContext<KeyboardContextType>({
  isKeyboardVisible: false,
});

// Custom hook to use the keyboard context
export const useKeyboard = () => useContext(KeyboardContext);

// Provider component
export const KeyboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  useEffect(() => {
    // Use the appropriate event names based on platform
    const showEvent = Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow';
    const hideEvent = Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide';

    // Add keyboard show listener
    const keyboardDidShowListener = Keyboard.addListener(showEvent, () => {
      setIsKeyboardVisible(true);
    });

    // Add keyboard hide listener
    const keyboardDidHideListener = Keyboard.addListener(hideEvent, () => {
      setIsKeyboardVisible(false);
    });

    // Clean up listeners when component unmounts
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <KeyboardContext.Provider value={{ isKeyboardVisible }}>
      {children}
    </KeyboardContext.Provider>
  );
};
