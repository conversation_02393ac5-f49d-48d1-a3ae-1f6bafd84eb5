import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ref, onValue } from 'firebase/database';
import { db } from '@/lib/Firebase';

// Define the context type
interface MessageTabPreloadContextType {
  isPreloaded: boolean;
  preloadedUsers: any[];
  preloadedUnreadCounts: { [key: string]: number };
  preloadedStatuses: any[];
  preloadMessageTab: () => Promise<void>;
  clearPreloadedData: () => void;
}

// Create the context with default values
const MessageTabPreloadContext = createContext<MessageTabPreloadContextType>({
  isPreloaded: false,
  preloadedUsers: [],
  preloadedUnreadCounts: {},
  preloadedStatuses: [],
  preloadMessageTab: async () => {},
  clearPreloadedData: () => {},
});

// Custom hook to use the context
export const useMessageTabPreload = () => useContext(MessageTabPreloadContext);

// Helper function to clean phone numbers (copied from UserInbox component)
const cleanPhoneNumber = (phoneNumber: string | null | undefined): string => {
  if (!phoneNumber) return '';

  // Step 1: Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // Step 2: Handle country codes
  // Remove leading country code if present (e.g., 91 for India)
  if (cleaned.startsWith('91') && cleaned.length > 10) {
    cleaned = cleaned.substring(2);
  }

  // Step 3: Remove leading zeros
  cleaned = cleaned.replace(/^0+/, '');

  return cleaned;
};

// Provider component
export const MessageTabPreloadProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isPreloaded, setIsPreloaded] = useState(false);
  const [preloadedUsers, setPreloadedUsers] = useState<any[]>([]);
  const [preloadedUnreadCounts, setPreloadedUnreadCounts] = useState<{ [key: string]: number }>({});
  const [preloadedStatuses, setPreloadedStatuses] = useState<any[]>([]);
  const [lastPreloadTime, setLastPreloadTime] = useState<number>(0);
  const isLoadingRef = useRef(false);

  // Function to preload message tab data
  const preloadMessageTab = async () => {
    try {
      // Prevent multiple simultaneous preloads
      if (isLoadingRef.current) {
        return;
      }

      // Check if we already have fresh preloaded data (less than 2 minutes old)
      const currentTime = Date.now();
      const CACHE_EXPIRY = 2 * 60 * 1000; // 2 minutes in milliseconds

      if (isPreloaded && preloadedUsers && preloadedUsers.length > 0 &&
          (currentTime - lastPreloadTime) < CACHE_EXPIRY) {
        return;
      }

      // Set loading flag
      isLoadingRef.current = true;

      // Check if permission has been granted before
      const PERMISSION_GRANTED_KEY = 'contacts_permission_granted';
      let permissionGranted = null;
      try {
        permissionGranted = await AsyncStorage.getItem(PERMISSION_GRANTED_KEY);
      } catch (permissionError) {
        console.error('Error checking permission status:', permissionError);
      }

      // Get cached contacts data
      const CONTACTS_CACHE_KEY = 'cached_contacts_data';
      let cachedData = null;
      try {
        cachedData = await AsyncStorage.getItem(CONTACTS_CACHE_KEY);
      } catch (cacheError) {
        console.error('Error retrieving cached contacts:', cacheError);
      }

      // Only use cached data if permission has been granted
      if (permissionGranted === 'true' && cachedData) {
        try {
          const parsedData = JSON.parse(cachedData);
          if (parsedData && Array.isArray(parsedData) && parsedData.length > 0) {
            // Set preloaded users from cache immediately
            setPreloadedUsers(parsedData);

            // Mark as preloaded so the UI can use this data immediately
            setIsPreloaded(true);

            // Continue with background loading of other data
          }
        } catch (parseError) {
          console.error('Error parsing cached contacts data:', parseError);
        }
      }

      // If no cached data or we need to refresh, fetch from API
      // We'll do this even if we loaded from cache to ensure data is fresh
      try {
        // Get user session
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) {
          return;
        }

        let parsedSession;
        try {
          parsedSession = JSON.parse(userSession);
          if (!parsedSession?.userId) {
            return;
          }
        } catch (sessionParseError) {
          console.error('Error parsing user session:', sessionParseError);
          return;
        }

        // Fetch users in the background
        try {
          const response = await fetch('https://nityasha.vercel.app/api/v1/users/get?limit=500');
          if (response.ok) {
            const apiUsers = await response.json();

            // Process users
            const processedUsers = apiUsers.map((user: any) => {
              const userNumber = cleanPhoneNumber(user.email);
              const hasStatuses = user.statuses && Array.isArray(user.statuses) && user.statuses.length > 0;

              return {
                id: user.id,
                name: user.username || 'Anonymous',
                initial: (user.username?.[0] || 'A').toUpperCase(),
                phoneNumber: userNumber,
                originalEmail: user.email,
                lastMessage: user.last_message || 'Hey there!',
                timestamp: user.last_message_time || '2:17:25 PM',
                is_online: false,
                ...(hasStatuses && {
                  statuses: user.statuses,
                  hasUnseenStory: true,
                  hasStatus: true
                })
              };
            });

            // Only update if we have new data and it's different from what we already have
            if (processedUsers.length > 0 &&
                (preloadedUsers.length === 0 ||
                 JSON.stringify(processedUsers) !== JSON.stringify(preloadedUsers))) {
              // Filter to only include relevant users
              const relevantUsers = processedUsers.filter((user: any) => {
                // Keep users with custom messages or statuses
                return user.lastMessage !== 'Hey there!' ||
                       (user.statuses && user.statuses.length > 0) ||
                       user.is_online === true;
              });

              // If we have too few relevant users, include some regular users
              if (relevantUsers.length < 10 && processedUsers.length > 0) {
                const additionalUsers = processedUsers
                  .filter((user: any) => !relevantUsers.some((ru: any) => ru.id === user.id))
                  .slice(0, Math.min(20, processedUsers.length));

                relevantUsers.push(...additionalUsers);
              }

              // Update state with relevant users
              setPreloadedUsers(relevantUsers);

              // Also save to AsyncStorage for faster loading next time
              try {
                await AsyncStorage.setItem(CONTACTS_CACHE_KEY, JSON.stringify(relevantUsers));
                console.log(`Preloaded ${relevantUsers.length} relevant contacts saved to AsyncStorage`);
              } catch (saveError) {
                console.error('Error saving preloaded contacts to AsyncStorage:', saveError);
              }
            }
          }
        } catch (apiError) {
          console.error('Error fetching users from API:', apiError);
        }

        // Preload unread counts from Firebase
        try {
          const userChatsRef = ref(db, `user_chats/${parsedSession.userId}`);
          onValue(userChatsRef, (snapshot) => {
            try {
              const data = snapshot.val();
              if (data) {
                const unreadData: {[key: string]: number} = {};

                // Process each chat entry
                Object.entries(data).forEach(([otherUserId, chatData]: [string, any]) => {
                  if (chatData.unread && chatData.unread > 0) {
                    unreadData[otherUserId] = chatData.unread;
                  }
                });

                setPreloadedUnreadCounts(unreadData);
              }
            } catch (snapshotError) {
              console.error('Error processing Firebase snapshot:', snapshotError);
            }
          });
        } catch (firebaseError) {
          console.error('Error setting up Firebase listener:', firebaseError);
        }

        // Preload statuses in parallel
        try {
          const response = await fetch('https://status.api.nityasha.com/statuses');
          if (response.ok) {
            const data = await response.json();
            if (data && data.statuses && data.statuses.length > 0) {
              setPreloadedStatuses(data.statuses);
            }
          }
        } catch (statusError) {
          console.error('Error fetching statuses:', statusError);
        }
      } catch (sessionError) {
        console.error('Error getting user session:', sessionError);
      }

      // Mark as preloaded if not already done
      setIsPreloaded(true);

      // Update last preload time
      setLastPreloadTime(Date.now());
    } catch (error) {
      console.error('Error preloading message tab:', error);
    } finally {
      // Reset loading flag
      isLoadingRef.current = false;
    }
  };

  // Function to clear preloaded data
  const clearPreloadedData = () => {
    setIsPreloaded(false);
    setPreloadedUsers([]);
    setPreloadedUnreadCounts({});
    setPreloadedStatuses([]);
  };

  // Preload data when the app starts
  useEffect(() => {
    preloadMessageTab();
  }, []);

  return (
    <MessageTabPreloadContext.Provider
      value={{
        isPreloaded,
        preloadedUsers,
        preloadedUnreadCounts,
        preloadedStatuses,
        preloadMessageTab,
        clearPreloadedData,
      }}
    >
      {children}
    </MessageTabPreloadContext.Provider>
  );
};
