import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the interface for official accounts
interface OfficialAccount {
  id: number;
  name: string;
  logo: string;
  description: string;
  verified: number;
  online: number;
  unread: string;
}

// Define the context type
interface OfficialSearchPreloadContextType {
  isPreloaded: boolean;
  preloadedAccounts: OfficialAccount[];
  preloadOfficialSearch: () => Promise<void>;
  clearPreloadedData: () => void;
}

// Create the context with default values
const OfficialSearchPreloadContext = createContext<OfficialSearchPreloadContextType>({
  isPreloaded: false,
  preloadedAccounts: [],
  preloadOfficialSearch: async () => {},
  clearPreloadedData: () => {},
});

// Custom hook to use the context
export const useOfficialSearchPreload = () => useContext(OfficialSearchPreloadContext);

// Cache key for storing preloaded accounts
const ACCOUNTS_CACHE_KEY = 'official_accounts_cache';

// Provider component
export const OfficialSearchPreloadProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isPreloaded, setIsPreloaded] = useState(false);
  const [preloadedAccounts, setPreloadedAccounts] = useState<OfficialAccount[]>([]);
  const [lastPreloadTime, setLastPreloadTime] = useState<number>(0);
  const isLoadingRef = useRef(false);

  // API URL for official accounts
  const API_URL = 'https://api.search.nityasha.com/official_accounts';

  // Function to preload official search data
  const preloadOfficialSearch = async () => {
    try {
      // Prevent multiple simultaneous preloads
      if (isLoadingRef.current) {
        return;
      }

      // Check if we already have fresh preloaded data (less than 3 minutes old)
      const currentTime = Date.now();
      const CACHE_EXPIRY = 3 * 60 * 1000; // 3 minutes in milliseconds

      if (isPreloaded && preloadedAccounts.length > 0 && (currentTime - lastPreloadTime) < CACHE_EXPIRY) {
        // Use existing data without logging to reduce console noise
        return;
      }

      // Set loading flag
      isLoadingRef.current = true;

      // Try to load from cache first for immediate display
      try {
        const cachedData = await AsyncStorage.getItem(ACCOUNTS_CACHE_KEY);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          if (parsedData && Array.isArray(parsedData) && parsedData.length > 0) {
            // Set preloaded accounts from cache immediately
            setPreloadedAccounts(parsedData);

            // Mark as preloaded so the UI can use this data immediately
            setIsPreloaded(true);

            console.log(`Loaded ${parsedData.length} official accounts from cache`);
          }
        }
      } catch (cacheError) {
        console.error('Error loading cached official accounts:', cacheError);
      }

      // Fetch fresh data in the background
      try {
        console.log('Fetching fresh official accounts data...');
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(API_URL, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Failed to fetch accounts: ${response.status}`);
        }

        const data = await response.json();

        // Only update if we have new data and it's different from what we already have
        if (data.length > 0 &&
            (preloadedAccounts.length === 0 ||
             JSON.stringify(data) !== JSON.stringify(preloadedAccounts))) {

          setPreloadedAccounts(data);
          setLastPreloadTime(currentTime);

          // Save to AsyncStorage for faster loading next time
          try {
            await AsyncStorage.setItem(ACCOUNTS_CACHE_KEY, JSON.stringify(data));
            console.log(`Preloaded ${data.length} official accounts saved to AsyncStorage`);
          } catch (saveError) {
            console.error('Error saving preloaded official accounts to AsyncStorage:', saveError);
          }
        }
      } catch (apiError) {
        console.error('Error fetching official accounts from API:', apiError);
      }

      // Mark as preloaded if not already done
      setIsPreloaded(true);
    } catch (error) {
      console.error('Error preloading official search:', error);
    }
  };

  // Function to clear preloaded data
  const clearPreloadedData = () => {
    setIsPreloaded(false);
    setPreloadedAccounts([]);
    setLastPreloadTime(0);
  };

  // Preload data when the app starts
  useEffect(() => {
    preloadOfficialSearch();
  }, []);

  return (
    <OfficialSearchPreloadContext.Provider
      value={{
        isPreloaded,
        preloadedAccounts,
        preloadOfficialSearch,
        clearPreloadedData,
      }}
    >
      {children}
    </OfficialSearchPreloadContext.Provider>
  );
};
