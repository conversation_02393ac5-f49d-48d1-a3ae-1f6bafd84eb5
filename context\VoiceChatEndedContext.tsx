import React, { createContext, useContext, useState, ReactNode } from 'react';

interface VoiceChatEndedContextType {
  showSnackbar: boolean;
  setShowSnackbar: (show: boolean) => void;
  handleVoiceChatEnded: (duration?: number) => void;
  handleSnackbarFeedback: (feedback: "up" | "down") => void;
  chatDuration: number;
}

const VoiceChatEndedContext = createContext<VoiceChatEndedContextType | undefined>(undefined);

export const useVoiceChatEnded = () => {
  const context = useContext(VoiceChatEndedContext);
  if (context === undefined) {
    throw new Error('useVoiceChatEnded must be used within a VoiceChatEndedProvider');
  }
  return context;
};

interface VoiceChatEndedProviderProps {
  children: ReactNode;
}

export const VoiceChatEndedProvider: React.FC<VoiceChatEndedProviderProps> = ({ children }) => {
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [chatDuration, setChatDuration] = useState(0);

  const handleVoiceChatEnded = (duration?: number) => {
    if (duration) {
      setChatDuration(duration);
    }
    setShowSnackbar(true);
    setTimeout(() => setShowSnackbar(false), 3000); // 3s ke baad hide
  };

  const handleSnackbarFeedback = (feedback: "up" | "down") => {
    console.log("User feedback:", feedback);
    // Here you can implement feedback logic
    setShowSnackbar(false);
  };

  const value = {
    showSnackbar,
    setShowSnackbar,
    handleVoiceChatEnded,
    handleSnackbarFeedback,
    chatDuration,
  };

  return (
    <VoiceChatEndedContext.Provider value={value}>
      {children}
    </VoiceChatEndedContext.Provider>
  );
}; 