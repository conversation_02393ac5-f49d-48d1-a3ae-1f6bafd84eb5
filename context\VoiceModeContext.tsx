import React, { createContext, useContext, useState } from 'react';

interface VoiceModeContextType {
  activeChatId: string | null;
  setActiveChatId: (chatId: string | null) => void;
}

const VoiceModeContext = createContext<VoiceModeContextType | undefined>(undefined);

export const VoiceModeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [activeChatId, setActiveChatId] = useState<string | null>(null);

  return (
    <VoiceModeContext.Provider value={{ activeChatId, setActiveChatId }}>
      {children}
    </VoiceModeContext.Provider>
  );
};

export const useVoiceMode = () => {
  const context = useContext(VoiceModeContext);
  if (context === undefined) {
    throw new Error('useVoiceMode must be used within a VoiceModeProvider');
  }
  return context;
}; 