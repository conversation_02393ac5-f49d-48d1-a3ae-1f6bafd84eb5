#!/bin/bash
# This script runs before the EAS Build process starts

# Ignore any additional parameters
# This allows the script to work even if EAS passes extra parameters like --platform

# Print environment information
echo "Node version: $(node -v)"
echo "NPM version: $(npm -v)"
echo "Java version: $(java -version)"

# Force install the correct version of @expo/config-plugins
echo "Installing correct version of @expo/config-plugins..."
npm install @expo/config-plugins@9.0.17 --no-save

# Any other pre-build steps
echo "Pre-install script completed successfully"
