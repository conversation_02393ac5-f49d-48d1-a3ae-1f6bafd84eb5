{"build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk"}, "node": "18.20.2", "prebuildCommand": "expo prebuild"}, "preview": {"android": {"buildType": "apk"}, "prebuildCommand": "expo prebuild"}, "preview2": {"android": {"gradleCommand": ":app:assembleRelease"}, "prebuildCommand": "expo prebuild"}, "preview3": {"developmentClient": true, "prebuildCommand": "expo prebuild"}, "preview4": {"distribution": "internal", "prebuildCommand": "expo prebuild"}, "production": {"prebuildCommand": "expo prebuild"}}}