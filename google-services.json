{"project_info": {"project_number": "262010279934", "firebase_url": "https://black-function-380411-default-rtdb.firebaseio.com", "project_id": "black-function-380411", "storage_bucket": "black-function-380411.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:262010279934:android:b8af0a518f618659df12af", "android_client_info": {"package_name": "com.Ni<PERSON><PERSON>a<PERSON>ner"}}, "oauth_client": [{"client_id": "262010279934-i5shekc0hm9j2obsrcob5tue3a4d4val.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC6mmUk8v9Pwp7upeb5Qscc1vOz_QAkMGI"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "262010279934-i5shekc0hm9j2obsrcob5tue3a4d4val.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:262010279934:android:b176a5a796529350df12af", "android_client_info": {"package_name": "com.amberasdasd.Nityasha"}}, "oauth_client": [{"client_id": "262010279934-i5shekc0hm9j2obsrcob5tue3a4d4val.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC6mmUk8v9Pwp7upeb5Qscc1vOz_QAkMGI"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "262010279934-i5shekc0hm9j2obsrcob5tue3a4d4val.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:262010279934:android:3b87f4cf0ce94703df12af", "android_client_info": {"package_name": "com.nityasha.com.nityasha"}}, "oauth_client": [{"client_id": "262010279934-i5shekc0hm9j2obsrcob5tue3a4d4val.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC6mmUk8v9Pwp7upeb5Qscc1vOz_QAkMGI"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "262010279934-i5shekc0hm9j2obsrcob5tue3a4d4val.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}