import { useEffect, useState, useRef, useCallback } from 'react';
import io, { Socket } from 'socket.io-client';
import { AppState, Platform, AppStateStatus } from 'react-native';

interface OnlineUser {
  userId: string;
  status: 'online' | 'offline';
  lastSeen: string | null;
}

interface OnlineStatusOptions {
  socketUrl: string;
  heartbeatInterval?: number;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  timeout?: number;
}

/**
 * Custom hook for managing online/offline status in React Native
 * 
 * @param userId - The current user's ID
 * @param options - Configuration options
 * @returns Status management object
 */
export const useOnlineStatus = (userId: string | null, options: OnlineStatusOptions) => {
  const {
    socketUrl,
    heartbeatInterval = 15000,
    reconnectionAttempts = 5,
    reconnectionDelay = 1000,
    timeout = 10000
  } = options;

  const [connected, setConnected] = useState<boolean>(false);
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const socketRef = useRef<Socket | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Initialize socket connection
  useEffect(() => {
    if (!userId) return;

    // Create socket connection with configuration
    socketRef.current = io(socketUrl, {
      reconnectionAttempts,
      reconnectionDelay,
      timeout
    });
    
    // Set up event listeners
    socketRef.current.on('connect', () => {
      console.log('Connected to Socket.IO server');
      setConnected(true);
      socketRef.current?.emit('join', userId);
    });
    
    socketRef.current.on('disconnect', () => {
      console.log('Disconnected from Socket.IO server');
      setConnected(false);
    });
    
    socketRef.current.on('online_users', (data: { users: OnlineUser[] }) => {
      console.log('Received online users:', data.users);
      setOnlineUsers(data.users);
    });
    
    socketRef.current.on('user_status_change', (data: OnlineUser) => {
      console.log('User status change:', data);
      setOnlineUsers(prevUsers => {
        // Find if user already exists in our list
        const userIndex = prevUsers.findIndex(u => u.userId === data.userId);
        
        if (userIndex >= 0) {
          // Update existing user
          const updatedUsers = [...prevUsers];
          updatedUsers[userIndex] = data;
          return updatedUsers;
        } else {
          // Add new user
          return [...prevUsers, data];
        }
      });
    });
    
    socketRef.current.on('connect_error', (error: any) => {
      console.error('Socket connection error:', error);
    });
    
    socketRef.current.on('reconnect', (attemptNumber: number) => {
      console.log(`Socket reconnected after ${attemptNumber} attempts`);
      // Refresh online users after reconnection
      if (socketRef.current?.connected) {
        socketRef.current.emit('get_online_users');
      }
    });
    
    // Set up AppState listener for background/foreground transitions
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appStateRef.current.match(/inactive|background/) && 
        nextAppState === 'active'
      ) {
        console.log('App has come to the foreground!');
        // Reconnect if needed and refresh online users
        if (socketRef.current && !socketRef.current.connected) {
          socketRef.current.connect();
        } else if (socketRef.current) {
          socketRef.current.emit('get_online_users');
        }
      } else if (
        appStateRef.current === 'active' &&
        nextAppState.match(/inactive|background/)
      ) {
        console.log('App has gone to the background!');
        // We'll keep the socket connection but could disconnect here if needed
      }
      
      appStateRef.current = nextAppState;
    };
    
    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);
    
    // Start heartbeat
    startHeartbeat();
    
    // Initial request for online users
    if (socketRef.current.connected) {
      socketRef.current.emit('get_online_users');
    }
    
    // Cleanup function
    return () => {
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }
      
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      
      appStateSubscription.remove();
    };
  }, [userId, socketUrl, reconnectionAttempts, reconnectionDelay, timeout]);
  
  // Start sending heartbeat messages
  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }
    
    heartbeatIntervalRef.current = setInterval(() => {
      if (socketRef.current && socketRef.current.connected) {
        socketRef.current.emit('heartbeat');
      }
    }, heartbeatInterval);
  }, [heartbeatInterval]);
  
  // Request the current list of online users
  const refreshOnlineUsers = useCallback(() => {
    if (socketRef.current && socketRef.current.connected) {
      socketRef.current.emit('get_online_users');
    }
  }, []);
  
  // Check if a specific user is online
  const isUserOnline = useCallback((checkUserId: string) => {
    return onlineUsers.some(user => 
      user.userId === checkUserId && user.status === 'online'
    );
  }, [onlineUsers]);
  
  // Get a specific user's status
  const getUserStatus = useCallback((checkUserId: string) => {
    const user = onlineUsers.find(user => user.userId === checkUserId);
    return user || { userId: checkUserId, status: 'offline' as const, lastSeen: null };
  }, [onlineUsers]);
  
  // Request a specific user's status from the server
  const requestUserStatus = useCallback((checkUserId: string) => {
    if (socketRef.current && socketRef.current.connected) {
      socketRef.current.emit('get_user_status', checkUserId);
    }
  }, []);
  
  // Manually reconnect the socket
  const reconnect = useCallback(() => {
    if (socketRef.current && !socketRef.current.connected) {
      socketRef.current.connect();
    }
  }, []);
  
  // Manually disconnect the socket
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
  }, []);
  
  return {
    connected,
    onlineUsers,
    refreshOnlineUsers,
    isUserOnline,
    getUserStatus,
    requestUserStatus,
    reconnect,
    disconnect,
    socket: socketRef.current
  };
};

/**
 * Format the last seen timestamp into a human-readable string
 * 
 * @param lastSeen - The last seen timestamp
 * @returns Formatted last seen string
 */
export const formatLastSeen = (lastSeen: string | Date | null): string => {
  if (!lastSeen) return 'Never';
  
  const lastSeenDate = new Date(lastSeen);
  const now = new Date();
  const diffMs = now.getTime() - lastSeenDate.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffSec < 30) return 'Just now';
  if (diffMin < 1) return 'Less than a minute ago';
  if (diffMin === 1) return '1 minute ago';
  if (diffMin < 60) return `${diffMin} minutes ago`;
  if (diffHour === 1) return '1 hour ago';
  if (diffHour < 24) return `${diffHour} hours ago`;
  if (diffDay === 1) return 'Yesterday';
  if (diffDay < 7) return `${diffDay} days ago`;
  
  // Format date for older timestamps
  return lastSeenDate.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Custom hook for managing user online status
 * 
 * @param userId - The user ID to check
 * @returns Online status and last seen time
 */
export const useUserOnlineStatus = (userId: string | null) => {
  const [isOnline, setIsOnline] = useState(false);
  const [lastSeen, setLastSeen] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);
  
  useEffect(() => {
    if (!userId) {
      setIsOnline(false);
      setLastSeen(null);
      return;
    }
    
    // Connect to Socket.IO server
    socketRef.current = io('https://chats.api.asia.nityasha.com', {
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 10000
    });
    
    // Request user status
    const requestUserStatus = () => {
      if (socketRef.current && socketRef.current.connected) {
        socketRef.current.emit('get_user_status', userId);
      }
    };
    
    // Handle connection events
    socketRef.current.on('connect', () => {
      console.log('Connected to Socket.IO server for user status');
      requestUserStatus();
    });
    
    // Handle user status updates
    socketRef.current.on('user_status_change', (data: OnlineUser) => {
      if (data.userId === userId) {
        setIsOnline(data.status === 'online');
        setLastSeen(data.lastSeen);
      }
    });
    
    // Handle online users list
    socketRef.current.on('online_users', (data: { users: OnlineUser[] }) => {
      const user = data.users.find(u => u.userId === userId);
      if (user) {
        setIsOnline(user.status === 'online');
        setLastSeen(user.lastSeen);
      }
    });
    
    // Set up periodic status check
    const intervalId = setInterval(requestUserStatus, 30000);
    
    // Initial request
    requestUserStatus();
    
    // Cleanup
    return () => {
      clearInterval(intervalId);
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [userId]);
  
  return { isOnline, lastSeen };
};
