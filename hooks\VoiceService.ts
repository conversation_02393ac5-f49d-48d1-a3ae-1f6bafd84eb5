"use client"

import { useEffect } from "react"
import * as Notifications from "expo-notifications"
import { AppState, Platform } from "react-native"

export default function useForegroundStickyNotification() {
  useEffect(() => {
    const sub = AppState.addEventListener("change", async (state) => {
      if (Platform.OS === "android") {
        if (state === "background") {
          // When app goes to background, show sticky notification
          await Notifications.setNotificationHandler({
            handleNotification: async () => ({
              shouldShowAlert: false,
              shouldPlaySound: false,
              shouldSetBadge: false,
            }),
          })

          await Notifications.scheduleNotificationAsync({
            content: {
              title: "<PERSON><PERSON><PERSON><PERSON> Voice Assistant",
              body: "Listening in background...",
              sticky: true,
              color: "#000000",
              priority: Notifications.AndroidNotificationPriority.MAX,
            },
            trigger: null,
          })
        }

        if (state === "active") {
          // When app comes to foreground, remove sticky notification
          const all = await Notifications.getAllScheduledNotificationsAsync()
          for (const notif of all) {
            await Notifications.cancelScheduledNotificationAsync(notif.identifier)
          }
        }
      }
    })

    return () => sub.remove()
  }, [])
}
