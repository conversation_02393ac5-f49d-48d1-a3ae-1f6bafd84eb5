import React, { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

function AuthProvider({ children }) {
    const navigation = useNavigation();

    useEffect(() => {
        const checkUserId = async () => {
            try {
                const id = await AsyncStorage.getItem("userSession");
                if (id) {
                    navigation.navigate('BottomTabs');
                } else {
                    navigation.reset({
                        index: 0,
                        routes: [{ name: 'Welcome' }],
                    });
                }
            } catch (error) {
                console.error('Error fetching userId:', error);
            }
        };

        checkUserId();
    }, [navigation]);

    return <>{children}</>;
}

export default AuthProvider;
