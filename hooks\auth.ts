import { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const useAuth = () => {
    const [userId, setUserId] = useState<string | null>(null);

    useEffect(() => {
        const fetchUserSession = async () => {
            try {
                const userSession = await AsyncStorage.getItem('userSession');

                if (userSession) {
                    const sessionData = JSON.parse(userSession);
                    setUserId(sessionData.userId);
                } else {
                    setUserId(null);
                }
            } catch (error) {
                console.error("Failed to fetch user session", error);
                setUserId(null);
            }
        };

        fetchUserSession();

    }, []);

    return userId;
}

export default useAuth;