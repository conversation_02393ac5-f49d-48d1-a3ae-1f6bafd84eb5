import { useEffect } from 'react';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';

const     useTokenPush = () => {
  useEffect(() => {
    const registerForPushNotifications = async () => {
      try {
        
        // Get the push token (don't check for existing token)
        const { status } = await Notifications.requestPermissionsAsync();
        if (status !== 'granted') {
          console.warn('Notification permission not granted');
          return;
        }

        const token = (await Notifications.getExpoPushTokenAsync()).data;

        if (!token) {
          throw new Error('Failed to get push token');
        }

        console.log('Push Token:', token);

        await AsyncStorage.setItem('pushToken', token);

        // Get user ID from AsyncStorage and parse it
        const userSession = await AsyncStorage.getItem('userSession');
        
        let userId = null;
        if (userSession) {
          const sessionData = JSON.parse(userSession);
          userId = sessionData.userId;
        }

        // Send token to server if userId exists
        if (userId) {
          await sendTokenToServer(userId, token);
        } else {
          console.warn('No userId found, cannot send token to server');
        }

      } catch (error) {
        console.error('Error in push notification registration:', error);
      }
    };

    const sendTokenToServer = async (userId: string, token: string) => {
      try {
        
        const response = await fetch(`https://api.nityasha.com/api/token/${userId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pushToken: token
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Server responded with status ${response.status}: ${errorText}`);
        }

        const data = await response.json();

      } catch (error) {
        console.error('Error sending token to server:', error);
      }
    };

    registerForPushNotifications();
  }, []);
};

export default useTokenPush; 