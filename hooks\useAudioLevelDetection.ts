import { useRef, useCallback } from 'react'
import { Audio } from 'expo-av'

export const useAudioLevelDetection = () => {
  const audioRecording = useRef<Audio.Recording | null>(null)
  const meteringInterval = useRef<NodeJS.Timeout | null>(null)

  const startAudioLevelDetection = useCallback(async (
    onLevelChange: (level: number) => void,
    intervalMs: number = 100
  ) => {
    try {
      // Request permissions
      const { status } = await Audio.requestPermissionsAsync()
      if (status !== 'granted') {
        console.warn('Audio permission not granted')
        return
      }

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      })

      // Create recording instance
      const recording = new Audio.Recording()
      await recording.prepareToRecordAsync({
        android: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_FORMAT_MPEG_4,
          audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_FORMAT_MPEG_4,
          audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
      })

      audioRecording.current = recording
      await recording.startAsync()

      // Start metering
      meteringInterval.current = setInterval(async () => {
        if (audioRecording.current) {
          const status = await audioRecording.current.getStatusAsync()
          if (status.isRecording && status.metering !== undefined) {
            // Convert dB to 0-1 range (typical range is -160 to 0 dB)
            const normalizedLevel = Math.max(0, Math.min(1, (status.metering + 60) / 60))
            onLevelChange(normalizedLevel)
          }
        }
      }, intervalMs)

    } catch (error) {
      console.error('Failed to start audio level detection:', error)
    }
  }, [])

  const stopAudioLevelDetection = useCallback(async () => {
    if (meteringInterval.current) {
      clearInterval(meteringInterval.current)
      meteringInterval.current = null
    }

    if (audioRecording.current) {
      try {
        await audioRecording.current.stopAndUnloadAsync()
        audioRecording.current = null
      } catch (error) {
        console.error('Failed to stop recording:', error)
      }
    }
  }, [])

  return {
    startAudioLevelDetection,
    stopAudioLevelDetection,
  }
}
