import { useEffect, useRef, useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";

const useBalance = () => {
  const [balance, setBalance] = useState(0);
  const [error, setError] = useState(null);
  const socketRef = useRef(null);
  const heartbeatInterval = useRef(null);

  const fetchBalanceHTTP = async (userId) => {
    try {
      const response = await fetch(`https://nityasha.vercel.app/api/v1/users/${userId}`);
      const data = await response.json();
      if (data.balance !== undefined) {
        setBalance(parseFloat(data.balance) || 0);
        return true;
      }
      return false;
    } catch (err) {
      setError("Failed to fetch balance via HTTP");
      return false;
    }
  };

  const refreshBalance = async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (userSession) {
        const userId = JSON.parse(userSession).userId;
        await fetchBalanceHTTP(userId);
      }
    } catch (error) {
      setError("Error refreshing balance");
    }
  };

  const startHTTPPolling = (userId) => {
    const interval = setInterval(() => fetchBalanceHTTP(userId), 5000); // Poll every 5 seconds
    return () => clearInterval(interval);
  };

  const initializeWebSocket = async (userId) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    if (socketRef.current) {
      socketRef.current.close();
      clearInterval(heartbeatInterval.current);
    }

    const socket = new WebSocket("wss://balance-app-api.nityasha.com");
    socketRef.current = socket;

    let reconnectAttempts = 0;
    const MAX_RECONNECT_ATTEMPTS = 300;

    socket.onopen = () => {
      console.log("WebSocket connected");
      reconnectAttempts = 0;
      socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
      heartbeatInterval.current = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
        }
      }, 1000);
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.balance !== undefined) {
          setBalance(parseFloat(data.balance) || 0);
        }
      } catch (err) {
        console.error("WebSocket message error:", err);
      }
    };

    socket.onerror = () => {
      console.log("WebSocket error, falling back to HTTP");
      fetchBalanceHTTP(userId);
    };

    socket.onclose = async () => {
      console.log("WebSocket disconnected");
      clearInterval(heartbeatInterval.current);

      if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        const httpSuccess = await fetchBalanceHTTP(userId);
        if (!httpSuccess) {
          reconnectAttempts++;
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 10000);
          console.log(`Reconnecting in ${delay}ms... Attempt ${reconnectAttempts}`);
          setTimeout(() => initializeWebSocket(userId), delay);
        }
      } else {
        console.log("Max reconnection attempts reached");
        setError("Connection failed after multiple attempts");
      }
    };
  };

  useEffect(() => {
    let stopPolling = null;

    const initializeComponent = async () => {
      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (userSession) {
          const userId = JSON.parse(userSession).userId;
          await initializeWebSocket(userId);
          stopPolling = startHTTPPolling(userId);
        }
      } catch (error) {
        console.error("Initialization error:", error);
      }
    };

    initializeComponent();

    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
      }
      if (stopPolling) {
        stopPolling();
      }
    };
  }, []);

  return { balance, error, refreshBalance };
};

export default useBalance;