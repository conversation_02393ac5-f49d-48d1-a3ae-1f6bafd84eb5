import { useEffect, useRef } from 'react';
import { ref, onValue, set, onDisconnect, serverTimestamp } from 'firebase/database';
import { db } from '@/lib/Firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';

export const useFirebaseStatus = () => {
  const userStatusRef = useRef<any>(null);

  const setupPresence = async () => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const { userId, name } = JSON.parse(userSession);
      if (!userId) return;

      // Create references to the user's status node
      userStatusRef.current = ref(db, `users/${userId}/status`);
      const userRef = ref(db, `users/${userId}`);

      // Create a reference to the '.info/connected' node
      const connectedRef = ref(db, '.info/connected');

      // When the client's connection state changes, update the user's status
      onValue(connectedRef, async (snap) => {
        if (snap.val() === false) {
          return;
        }

        // Set user as online
        await set(userRef, {
          status: 'online',
          name: name || 'Anonymous',
          lastSeen: serverTimestamp()
        });

        // When the client disconnects, set the user as offline
        onDisconnect(userRef).update({
          status: 'offline',
          lastSeen: serverTimestamp()
        });
      });

      // Handle app state changes
      const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
        if (!userStatusRef.current) return;

        if (nextAppState === 'active') {
          set(userStatusRef.current, {
            state: 'online',
            lastSeen: Date.now(), // Use client timestamp for more accurate timing
          });
        } else if (nextAppState === 'background' || nextAppState === 'inactive') {
          // Add a small delay before marking as offline to prevent flicker
          setTimeout(() => {
            // Double-check the app is still in background
            if (AppState.currentState !== 'active' && userStatusRef.current) {
              set(userStatusRef.current, {
                state: 'offline',
                lastSeen: Date.now(), // Use client timestamp for more accurate timing
              });
            }
          }, 5000); // 5 second delay
        }
      });

      return () => {
        subscription.remove();
        if (userStatusRef.current) {
          set(userStatusRef.current, {
            state: 'offline',
            lastSeen: serverTimestamp(),
          });
        }
      };
    } catch (error) {
      console.error('Error setting up presence:', error);
    }
  };

  useEffect(() => {
    setupPresence();
  }, []);

  return null;
};