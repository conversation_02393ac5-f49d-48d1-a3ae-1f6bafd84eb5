import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus, BackHandler, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useManageOnlineStatus = () => {
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);
  const userIdRef = useRef<string | null>(null);

  useEffect(() => {
    // Function to set user as online
    const setUserOnline = async (userId: string) => {
      try {
        await fetch(`https://status.api.nityasha.com/user/${userId}/online`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        console.log('User set as online:', userId);
      } catch (error) {
        console.error('Failed to set user as online:', error);
      }
    };

    // Function to set user as offline
    const setUserOffline = async (userId: string) => {
      try {
        await fetch(`https://status.api.nityasha.com/user/${userId}/offline`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        console.log('User set as offline:', userId);
      } catch (error) {
        console.error('Failed to set user as offline:', error);
      }
    };

    // Get the user ID from AsyncStorage
    const getUserId = async () => {
      try {
        console.log('Getting user session from AsyncStorage...');
        const userSession = await AsyncStorage.getItem('userSession');
        console.log('User session from AsyncStorage:', userSession);

        if (userSession) {
          const parsedSession = JSON.parse(userSession);
          console.log('Parsed session:', parsedSession);

          userIdRef.current = parsedSession?.userId || null;
          console.log('User ID from session:', userIdRef.current);

          // Set user as online initially if we have a user ID
          if (userIdRef.current) {
            console.log('Setting user as online initially...');
            setUserOnline(userIdRef.current);
          } else {
            console.log('No user ID found in session, not setting online status');
          }
        } else {
          console.log('No user session found in AsyncStorage');
        }
      } catch (error) {
        console.error('Error getting user session:', error);
      }
    };

    // Handle app state changes
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        userIdRef.current
      ) {
        // App has come to the foreground
        setUserOnline(userIdRef.current);
      } else if (
        appStateRef.current === 'active' &&
        nextAppState.match(/inactive|background/) &&
        userIdRef.current
      ) {
        // App has gone to the background
        setUserOffline(userIdRef.current);
      }

      appStateRef.current = nextAppState;
    };

    // Function to ensure user is set as offline
    const ensureUserOffline = async () => {
      if (userIdRef.current) {
        console.log('Ensuring user is set as offline:', userIdRef.current);
        await setUserOffline(userIdRef.current);
      }
    };

    // Initialize
    getUserId();

    // Subscribe to app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Set up a heartbeat to keep the user online while the app is active
    const heartbeatInterval = setInterval(() => {
      if (appStateRef.current === 'active' && userIdRef.current) {
        setUserOnline(userIdRef.current);
      }
    }, 60000); // Every minute

    // Handle Android back button to set user as offline when app is closed
    let backHandlerSubscription: any;
    if (Platform.OS === 'android') {
      backHandlerSubscription = BackHandler.addEventListener('hardwareBackPress', () => {
        // Only handle back press if we're at the root of the app
        // This is a simple heuristic - you might need to adjust based on your navigation
        if (appStateRef.current === 'active') {
          ensureUserOffline();
        }
        return false; // Don't prevent default behavior
      });
    }

    // Note: We removed web-specific code that was causing errors in React Native

    // Cleanup function
    return () => {
      subscription.remove();
      clearInterval(heartbeatInterval);

      if (Platform.OS === 'android' && backHandlerSubscription) {
        backHandlerSubscription.remove();
      }

      // Note: We removed web-specific code that was causing errors in React Native

      // Set user as offline when component unmounts
      ensureUserOffline();
    };
  }, []);
};
