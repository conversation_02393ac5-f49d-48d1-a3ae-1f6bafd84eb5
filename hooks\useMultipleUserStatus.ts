import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UserStatus {
  isOnline: boolean;
  lastSeen: string | null;
}

interface UserStatusMap {
  [userId: string]: UserStatus;
}

export const useMultipleUserStatus = (userIds: string[]) => {
  const [userStatuses, setUserStatuses] = useState<UserStatusMap>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserStatuses = async () => {
      if (!userIds || userIds.length === 0) {
        setUserStatuses({});
        setLoading(false);
        return;
      }

      try {
        // Filter out any empty or invalid user IDs
        const validUserIds = userIds.filter(id => id && id.trim() !== '');

        if (validUserIds.length === 0) {
          setUserStatuses({});
          setLoading(false);
          return;
        }

        const response = await fetch('https://status.api.nityasha.com/users/online-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userIds: validUserIds }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user statuses');
        }

        const data = await response.json();
        setUserStatuses(data.userStatuses || {});
        setError(null);
      } catch (err) {
        console.error('Error fetching user statuses:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');

        // Create default offline status for all users on error
        const defaultStatuses: UserStatusMap = {};
        userIds.forEach(id => {
          defaultStatuses[id] = { isOnline: false, lastSeen: null };
        });
        setUserStatuses(defaultStatuses);
      } finally {
        setLoading(false);
      }
    };

    fetchUserStatuses();

    // Set up polling to refresh statuses every 30 seconds
    const intervalId = setInterval(fetchUserStatuses, 30000);

    return () => clearInterval(intervalId);
  }, [JSON.stringify(userIds)]); // Re-run when the userIds array changes

  return { userStatuses, loading, error };
};
