import { useState, useEffect } from 'react';
import { ref, onValue, set, serverTimestamp } from 'firebase/database';
import { db } from '@/lib/Firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState } from 'react-native';

export const useOnlineStatus = () => {
  const [onlineUsers, setOnlineUsers] = useState({});

  useEffect(() => {
    let userId = null;
    let cleanup = null;

    const initialize = async () => {
      try {
        // Get user session
        const session = await AsyncStorage.getItem('userSession');
        if (!session) return;
        
        const { userId: id } = JSON.parse(session);
        userId = id;

        if (!userId) return;

        // Reference to current user's status
        const myStatusRef = ref(db, `/online/${userId}`);
        
        // Update user status to online
        const updateOnlineStatus = () => {
          set(myStatusRef, {
            online: true,
            lastSeen: serverTimestamp()
          });
        };

        // Update user status to offline
        const updateOfflineStatus = () => {
          set(myStatusRef, {
            online: false,
            lastSeen: serverTimestamp()
          });
        };

        // Listen to all online statuses
        const onlineRef = ref(db, '/online');
        const unsubscribe = onValue(onlineRef, (snapshot) => {
          const data = snapshot.val() || {};
          const online = {};
          
          Object.keys(data).forEach((uid) => {
            online[uid] = data[uid]?.online || false;
          });
          
          setOnlineUsers(online);
        });

        // Handle app state changes
        const subscription = AppState.addEventListener('change', (nextAppState) => {
          if (nextAppState === 'active') {
            updateOnlineStatus();
          } else {
            updateOfflineStatus();
          }
        });

        // Set initial online status
        updateOnlineStatus();

        // Cleanup function
        cleanup = () => {
          updateOfflineStatus();
          unsubscribe();
          subscription.remove();
        };

      } catch (error) {
        console.error('Error in useOnlineStatus:', error);
      }
    };

    initialize();

    return () => {
      if (cleanup) cleanup();
    };
  }, []);

  return onlineUsers;
};

