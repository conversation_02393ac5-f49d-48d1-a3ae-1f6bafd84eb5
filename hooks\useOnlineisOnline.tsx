import { useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';

interface UseOnlineStatusProps {
  updateInterval?: number;
}

const useOnlineStatus = ({ updateInterval = 30000 }: UseOnlineStatusProps = {}) => {
  const statusIntervalRef = useRef<NodeJS.Timeout>();

  const updateUserStatus = async (isOnline: boolean) => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const { userId } = JSON.parse(userSession);

      // Fixed the endpoint URL - removed the extra 's' from 'statuss'
      const response = await fetch(`https://status.api.nityasha.com/user/${userId}/${isOnline ? 'online' : 'offline'}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to update user status');
      }
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  useEffect(() => {
    // Set initial online status
    updateUserStatus(true);

    // Setup periodic status updates with a longer interval (2 minutes)
    // This reduces the frequency of status updates to prevent rapid online/offline changes
    statusIntervalRef.current = setInterval(() => {
      // Only update if the app is in the foreground
      if (AppState.currentState === 'active') {
        updateUserStatus(true);
      }
    }, 120000); // 2 minutes instead of 30 seconds

    // Handle app state changes with debounce to prevent rapid status changes
    let appStateTimeout: NodeJS.Timeout | null = null;

    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      // Clear any pending timeout
      if (appStateTimeout) {
        clearTimeout(appStateTimeout);
      }

      // Set a timeout to update status after a short delay
      appStateTimeout = setTimeout(() => {
        if (nextAppState === 'active') {
          updateUserStatus(true);
        } else if (nextAppState === 'background' || nextAppState === 'inactive') {
          // Add a small delay before marking offline to prevent flicker
          setTimeout(() => {
            // Double-check the app is still in background
            if (AppState.currentState !== 'active') {
              updateUserStatus(false);
            }
          }, 5000); // 5 second delay before marking offline
        }
      }, 1000); // 1 second debounce
    });

    // Cleanup function
    return () => {
      if (statusIntervalRef.current) {
        clearInterval(statusIntervalRef.current);
      }
      if (appStateTimeout) {
        clearTimeout(appStateTimeout);
      }
      // Only update to offline if the app is actually closing
      updateUserStatus(false);
      subscription.remove(); // Remove the subscription properly
    };
  }, [updateInterval]);

  return {
    updateUserStatus
  };
};

export default useOnlineStatus;
