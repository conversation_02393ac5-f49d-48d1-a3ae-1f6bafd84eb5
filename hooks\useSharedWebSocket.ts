import { useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

let globalWs: WebSocket | null = null;
let listeners: Set<(data: any) => void> = new Set();
let reconnectTimeout: NodeJS.Timeout | null = null;
const MAX_RECONNECT_ATTEMPTS = 5;
let reconnectAttempts = 0;

export const useSharedWebSocket = (onMessage: (data: any) => void) => {
  useEffect(() => {
    listeners.add(onMessage);

    const connect = async () => {
      if (globalWs?.readyState === WebSocket.OPEN) return;

      try {
        const userSession = await AsyncStorage.getItem("userSession");
        if (!userSession) return;

        const userId = JSON.parse(userSession).userId;

        // First HTTPS request
        const response1 = await fetch(`https://api.nityasha.com/api/v1/${userId}`, {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        });

        if (response1.ok) {
          const balanceData = await response1.json();
          listeners.forEach(listener => listener(balanceData));
          await AsyncStorage.setItem("balance", JSON.stringify(balanceData.balance));
        }

        // Second HTTPS request before WebSocket connection
        const response2 = await fetch(`https://api2th.nityasha.com/api/v1/users/${userId}`, {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        });

        if (response2.ok) {
          const userDetails = await response2.json();
          listeners.forEach(listener => listener(userDetails));
        }

        // Set up WebSocket
        globalWs = new WebSocket("wss://balance-app-api.nityasha.com");

        globalWs.onopen = () => {
          reconnectAttempts = 0;
          globalWs?.send(JSON.stringify({ action: "GET_BALANCE", userId }));
        };

        globalWs.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            listeners.forEach(listener => listener(data));
          } catch {
            console.log('Fetch problem in useSharedWebSocket');
          }
        };

        globalWs.onclose = () => {
          if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
            reconnectTimeout = setTimeout(connect, delay);
            reconnectAttempts++;
          }
        };

        globalWs.onerror = () => {}; // No error handling, as per request

      } catch {
        // No console.error() here either
      }
    };

    connect();

    return () => {
      listeners.delete(onMessage);
      if (listeners.size === 0) {
        globalWs?.close();
        globalWs = null;
        if (reconnectTimeout) {
          clearTimeout(reconnectTimeout);
        }
      }
    };
  }, [onMessage]);

  return {
    sendMessage: (message: any) => {
      if (globalWs?.readyState === WebSocket.OPEN) {
        globalWs.send(JSON.stringify(message));
      }
    }
  };
};

