import { useState, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UserStatus {
  isOnline: boolean;
  lastSeen: string | null;
}

interface UserStatusMap {
  [userId: string]: UserStatus;
}

export const useSocketOnlineStatus = () => {
  const [userStatuses, setUserStatuses] = useState<UserStatusMap>({});
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const initializeWebSocket = async () => {
      try {
        // Get current user ID from AsyncStorage
        const userSession = await AsyncStorage.getItem('userSession');
        if (!userSession) {
          console.log('No user session found');
          return;
        }

        const parsedSession = JSON.parse(userSession);
        const userId = parsedSession?.userId;
        setCurrentUserId(userId);

        if (!userId) {
          console.log('No user ID found in session');
          return;
        }

        console.log('Connecting to WebSocket with user ID:', userId);

        // Connect to WebSocket server
        const ws = new WebSocket(`wss://status.api.nityasha.com/ws?userId=${userId}&type=status`);
        wsRef.current = ws;

        ws.onopen = () => {
          console.log('WebSocket connected');
          setConnected(true);
          setError(null);
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            if (data.type === 'status_connection') {
              console.log('Status connection established:', data);
            } else if (data.type === 'user_status_update') {
              // Update user status
              setUserStatuses(prev => ({
                ...prev,
                [data.userId]: {
                  isOnline: data.isOnline,
                  lastSeen: data.timestamp
                }
              }));
            }
          } catch (err) {
            console.error('Error parsing WebSocket message:', err);
          }
        };

        ws.onerror = (event) => {
          console.error('WebSocket error:', event);
          setError('WebSocket connection error');
        };

        ws.onclose = () => {
          console.log('WebSocket disconnected');
          setConnected(false);
        };

        // Set up cleanup function
        cleanup = () => {
          console.log('Cleaning up WebSocket connection');
          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            wsRef.current.close();
          }
        };
      } catch (err) {
        console.error('Error initializing WebSocket:', err);
        setError('Failed to initialize WebSocket connection');
      }
    };

    initializeWebSocket();

    // Clean up WebSocket connection when component unmounts
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  // Function to manually check a user's status
  const checkUserStatus = async (userId: string) => {
    try {
      const response = await fetch(`https://status.api.nityasha.com/user/${userId}/online-status`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch user status');
      }
      
      const data = await response.json();
      
      // Update the status in our state
      setUserStatuses(prev => ({
        ...prev,
        [userId]: {
          isOnline: data.isOnline,
          lastSeen: data.lastSeen
        }
      }));
      
      return data;
    } catch (err) {
      console.error('Error checking user status:', err);
      return null;
    }
  };

  return {
    userStatuses,
    connected,
    error,
    currentUserId,
    checkUserStatus
  };
};
