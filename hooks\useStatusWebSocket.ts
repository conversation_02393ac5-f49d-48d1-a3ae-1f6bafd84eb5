import { useEffect, useRef, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

type StatusUpdateListener = (data: any) => void;

const listeners = new Set<StatusUpdateListener>();
let pollingInterval: NodeJS.Timeout;

const useStatusPolling = (onStatusUpdate: StatusUpdateListener) => {
  const [isConnected, setIsConnected] = useState(false);

  const fetchStatus = async () => {
    try {
      const userSession = await AsyncStorage.getItem('userSession');
      if (!userSession) return;

      const { userId } = JSON.parse(userSession);
      const response = await fetch(`https://status.api.nityasha.com/statuses?userId=${userId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      listeners.forEach(listener => listener(data));
      setIsConnected(true);
    } catch (error) {
      console.error('Error fetching status:', error);
      setIsConnected(false);
    }
  };

  useEffect(() => {
    listeners.add(onStatusUpdate);
    fetchStatus();
    pollingInterval = setInterval(fetchStatus, 3000); // every 30 sec like heartbeat

    return () => {
      listeners.delete(onStatusUpdate);
      if (listeners.size === 0) {
        clearInterval(pollingInterval);
      }
    };
  }, [onStatusUpdate]);

  return isConnected;
};

export default useStatusPolling;


