import { useState, useEffect, useRef, useCallback } from 'react';
import io, { Socket } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UnreadMessage {
  senderId: string;
  count: number;
}

interface UnreadMessagesData {
  total: number;
  bySender: UnreadMessage[];
}

interface UseUnreadMessagesOptions {
  socketUrl: string;
  refreshInterval?: number;
}

/**
 * Custom hook for managing unread messages
 * 
 * @param userId - The current user's ID
 * @param options - Configuration options
 * @returns Unread messages data and management functions
 */
export const useUnreadMessages = (userId: string | null, options: UseUnreadMessagesOptions) => {
  const { socketUrl, refreshInterval = 60000 } = options;
  
  const [unreadData, setUnreadData] = useState<UnreadMessagesData>({
    total: 0,
    bySender: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // Fetch unread messages from the server
  const fetchUnreadMessages = useCallback(async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Try to get cached data first
      const cachedData = await AsyncStorage.getItem(`unread_messages_${userId}`);
      if (cachedData) {
        setUnreadData(JSON.parse(cachedData));
      }
      
      // Fetch from server
      const response = await fetch(`${socketUrl}/messages/unread-count/${userId}`);
      
      if (!response.ok) {
        throw new Error(`Server responded with status ${response.status}`);
      }
      
      const data = await response.json();
      setUnreadData(data);
      
      // Cache the data
      await AsyncStorage.setItem(`unread_messages_${userId}`, JSON.stringify(data));
    } catch (err) {
      console.error('Error fetching unread messages:', err);
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [userId, socketUrl]);
  
  // Mark messages from a specific sender as read
  const markAsRead = useCallback(async (senderId: string) => {
    if (!userId) return;
    
    try {
      // Optimistically update local state
      setUnreadData(prev => {
        const senderIndex = prev.bySender.findIndex(item => item.senderId === senderId);
        if (senderIndex === -1) return prev;
        
        const senderCount = prev.bySender[senderIndex].count;
        const newTotal = Math.max(0, prev.total - senderCount);
        const newBySender = prev.bySender.filter(item => item.senderId !== senderId);
        
        return {
          total: newTotal,
          bySender: newBySender
        };
      });
      
      // Call API to mark messages as read
      const response = await fetch(`${socketUrl}/messages/mark-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          senderId,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Server responded with status ${response.status}`);
      }
      
      // Emit via socket for real-time updates
      if (socketRef.current) {
        socketRef.current.emit('mark_messages_read', {
          userId,
          senderId,
        });
      }
      
      // Update cache
      const cachedData = await AsyncStorage.getItem(`unread_messages_${userId}`);
      if (cachedData) {
        const data = JSON.parse(cachedData);
        const senderIndex = data.bySender.findIndex((item: UnreadMessage) => item.senderId === senderId);
        if (senderIndex !== -1) {
          const senderCount = data.bySender[senderIndex].count;
          data.total = Math.max(0, data.total - senderCount);
          data.bySender = data.bySender.filter((item: UnreadMessage) => item.senderId !== senderId);
          await AsyncStorage.setItem(`unread_messages_${userId}`, JSON.stringify(data));
        }
      }
    } catch (err) {
      console.error('Error marking messages as read:', err);
      // Revert optimistic update on error
      fetchUnreadMessages();
    }
  }, [userId, socketUrl, fetchUnreadMessages]);
  
  // Get unread count for a specific sender
  const getUnreadCountBySender = useCallback((senderId: string) => {
    const sender = unreadData.bySender.find(item => item.senderId === senderId);
    return sender ? sender.count : 0;
  }, [unreadData.bySender]);
  
  // Initialize socket connection and set up listeners
  useEffect(() => {
    if (!userId) return;
    
    // Connect to Socket.IO server
    socketRef.current = io(socketUrl);
    
    // Join user's room
    socketRef.current.on('connect', () => {
      socketRef.current?.emit('join', userId);
    });
    
    // Listen for new messages
    socketRef.current.on('receive_message', (message: any) => {
      if (message.receiverId === userId) {
        // Update unread count when receiving a new message
        fetchUnreadMessages();
      }
    });
    
    // Listen for unread count updates
    socketRef.current.on('unread_count', (data: any) => {
      console.log('Unread count update:', data);
      fetchUnreadMessages();
    });
    
    // Listen for messages read
    socketRef.current.on('messages_read', (data: any) => {
      console.log('Messages read:', data);
      fetchUnreadMessages();
    });
    
    // Set up periodic refresh
    intervalRef.current = setInterval(fetchUnreadMessages, refreshInterval);
    
    // Initial fetch
    fetchUnreadMessages();
    
    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [userId, socketUrl, refreshInterval, fetchUnreadMessages]);
  
  return {
    unreadData,
    loading,
    error,
    refreshUnreadMessages: fetchUnreadMessages,
    markAsRead,
    getUnreadCountBySender,
    hasUnreadMessages: unreadData.total > 0
  };
};
