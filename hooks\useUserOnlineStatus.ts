import { useState, useEffect } from 'react';
import { ref, onValue } from 'firebase/database';
import { db } from '@/lib/Firebase';

interface UserOnlineStatus {
  isOnline: boolean;
  lastSeen: string | null;
}

export const useUserOnlineStatus = (userId: string | null) => {
  const [onlineStatus, setOnlineStatus] = useState<UserOnlineStatus>({
    isOnline: false,
    lastSeen: null
  });

  useEffect(() => {
    if (!userId) return;

    const userStatusRef = ref(db, `users/${userId}/status`);
    
    const unsubscribe = onValue(userStatusRef, (snapshot) => {
      const status = snapshot.val();
      if (status) {
        setOnlineStatus({
          isOnline: status.state === 'online',
          lastSeen: status.lastSeen || null
        });
      }
    });

    return () => unsubscribe();
  }, [userId]);

  return onlineStatus;
};
