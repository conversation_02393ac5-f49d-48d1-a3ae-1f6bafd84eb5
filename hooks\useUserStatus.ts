import { useState, useEffect } from 'react';

export const useUserStatus = (userId: string) => {
  const [isOnline, setIsOnline] = useState(false);
  const [lastSeen, setLastSeen] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      return;
    }

    const fetchUserStatus = async () => {
      try {
        const response = await fetch(`https://status.api.nityasha.com/user/${userId}/online-status`);

        if (!response.ok) {
          throw new Error('Failed to fetch user status');
        }

        const data = await response.json();
        setIsOnline(data.isOnline);
        setLastSeen(data.lastSeen);
        setError(null);
      } catch (err) {
        console.error('Error fetching user status:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchUserStatus();

    // Set up polling to refresh status every 15 seconds
    const intervalId = setInterval(fetchUserStatus, 15000);

    return () => clearInterval(intervalId);
  }, [userId]);

  return { isOnline, lastSeen, loading, error };
};