import { useEffect, useRef, useState } from "react"
import Voice from "@react-native-voice/voice"

export default function useVoiceRecognizer({ onResult, lang = "hi-IN" }) {
  const [isListening, setIsListening] = useState(false)
  const retryTimeout = useRef(null)

  useEffect(() => {
    Voice.onSpeechStart = () => setIsListening(true)
    Voice.onSpeechEnd = () => {
      setIsListening(false)
      startRecognition() // Auto-restart
    }

    Voice.onSpeechResults = (e) => {
      const text = e.value?.[0]
      if (text) onResult(text)
    }

    Voice.onSpeechError = (e) => {
      console.warn("Voice error:", e)
      retryTimeout.current = setTimeout(() => startRecognition(), 1000)
    }

    return () => {
      Voice.destroy().then(() => Voice.removeAllListeners())
    }
  }, [])

  const startRecognition = async () => {
    try {
      await Voice.start(lang)
    } catch (e) {
      console.error("Start error:", e)
    }
  }

  const stopRecognition = async () => {
    try {
      await Voice.stop()
    } catch (e) {
      console.error("Stop error:", e)
    }
  }

  return { isListening, startRecognition, stopRecognition }
}
