import { useState, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useWebSocket = () => {
  const [balance, setBalance] = useState(0);
  const [error, setError] = useState(null);
  const socketRef = useRef(null);
  const intervalRef = useRef(null);
  const reconnectAttemptsRef = useRef(0);
  const MAX_RECONNECT_ATTEMPTS = 5;

  const connectWebSocket = async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (!userSession) return;

      const userId = JSON.parse(userSession).userId;

      // Don't create a new connection if one already exists and is open
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        return;
      }

      // Close existing connection if any
      if (socketRef.current) {
        socketRef.current.close();
        clearInterval(intervalRef.current);
      }

      const socket = new WebSocket("wss://balance-app-api.nityasha.com");
      socketRef.current = socket;

      socket.onopen = () => {
        console.log("WebSocket connected");
        reconnectAttemptsRef.current = 0;
        socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
        
        intervalRef.current = setInterval(() => {
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ action: "GET_BALANCE", userId }));
          }
        }, 10000); // Poll every 10 seconds
      };

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.error) {
            setError(data.error);
          } else if (data.balance !== undefined) {
            setBalance(parseFloat(data.balance) || 0);
          }
        } catch (err) {
          setError("Invalid data received");
        }
      };

      socket.onerror = (err) => {
        console.error("WebSocket Error:", err);
        setError("WebSocket connection failed");
      };

      socket.onclose = () => {
        console.log("WebSocket disconnected");
        clearInterval(intervalRef.current);
        
        // Implement exponential backoff for reconnection
        if (reconnectAttemptsRef.current < MAX_RECONNECT_ATTEMPTS) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
          setTimeout(connectWebSocket, delay);
          reconnectAttemptsRef.current++;
        }
      };
    } catch (error) {
      console.error("WebSocket connection error:", error);
      setError("Failed to establish WebSocket connection");
    }
  };

  useEffect(() => {
    connectWebSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
      clearInterval(intervalRef.current);
    };
  }, []);

  return { balance, error };
};
