import { useState, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface WebSocketMessage {
  action: string;
  userId: string;
}

const WS_URL = "wss://balance-app-api.nityasha.com";
const RECONNECT_ATTEMPTS = 3;
const HEARTBEAT_INTERVAL = 1000;

let globalWs: WebSocket | null = null;
let globalListeners: Set<(balance: number) => void> = new Set();

export const useWebSocketBalance = () => {
  const [balance, setBalance] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const intervalRef = useRef<NodeJS.Timeout>();

  const connectWebSocket = async () => {
    try {
      const userSession = await AsyncStorage.getItem("userSession");
      if (!userSession) return;

      const userId = JSON.parse(userSession).userId;

      if (!globalWs || globalWs.readyState === WebSocket.CLOSED) {
        globalWs = new WebSocket(WS_URL);

        globalWs.onopen = () => {
          console.log("WebSocket connected");
          reconnectAttemptsRef.current = 0;
          globalWs?.send(JSON.stringify({ action: "GET_BALANCE", userId }));

          intervalRef.current = setInterval(() => {
            if (globalWs?.readyState === WebSocket.OPEN) {
              globalWs.send(JSON.stringify({ action: "GET_BALANCE", userId }));
            }
          }, HEARTBEAT_INTERVAL);
        };

        globalWs.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            if (data.error) {
              setError(data.error);
            } else if (data.balance !== undefined) {
              const newBalance = parseFloat(data.balance) || 0;
              globalListeners.forEach(listener => listener(newBalance));
            }
          } catch (err) {
            setError("Invalid data received");
          }
        };

        globalWs.onclose = () => {
          console.log("WebSocket disconnected");
          clearInterval(intervalRef.current);

          if (reconnectAttemptsRef.current < RECONNECT_ATTEMPTS) {
            const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
            setTimeout(connectWebSocket, delay);
            reconnectAttemptsRef.current++;
          }
        };
      }
    } catch (error) {
      console.error("WebSocket connection error:", error);
      setError("Failed to establish WebSocket connection");
    }
  };

  useEffect(() => {
    const balanceListener = (newBalance: number) => {
      setBalance(newBalance);
    };

    globalListeners.add(balanceListener);
    
    if (!globalWs || globalWs.readyState === WebSocket.CLOSED) {
      connectWebSocket();
    }

    return () => {
      globalListeners.delete(balanceListener);
      if (globalListeners.size === 0) {
        globalWs?.close();
        globalWs = null;
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return { balance, error };
};