import { StatusBar, TouchableOpacity, Text, ScrollView, TextInput, ActivityIndicator, Alert, Clipboard, ImageBackground, Keyboard, KeyboardEvent, Platform, ToastAndroid } from 'react-native'
import React, { useState, useRef, useEffect } from 'react'
import { SafeAreaView, View, MotiView, Image } from 'moti';
import tw from 'twrnc';
import { useNavigation } from '@react-navigation/native';
import { MoveLeft, EllipsisVertical, Send, Copy, Battery as BatteryIcon, Sun, Compass } from 'lucide-react-native';
import { checkForIllegalWords } from '@/app/utils/wordFilter';
import { KeyboardAvoidingView } from "react-native-keyboard-controller";
import * as ExpoBattery from 'expo-battery';
import * as Brightness from 'expo-brightness';
import * as Location from 'expo-location';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';

// Battery and Brightness Info Component
const SystemInfo = () => {
  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
  const [brightness, setBrightness] = useState<number | null>(null);

  useEffect(() => {
    const getBatteryLevel = async () => {
      const level = await ExpoBattery.getBatteryLevelAsync();
      setBatteryLevel(level);
    };

    const getBrightness = async () => {
      const level = await Brightness.getBrightnessAsync();
      setBrightness(level);
    };

    getBatteryLevel();
    getBrightness();

    // Update battery level every minute
    const batteryInterval = setInterval(getBatteryLevel, 60000);
    // Update brightness every 5 seconds
    const brightnessInterval = setInterval(getBrightness, 5000);

    return () => {
      clearInterval(batteryInterval);
      clearInterval(brightnessInterval);
    };
  }, []);

  return (
    <View style={tw`flex-row justify-end items-center gap-2 px-4 py-2`}>
      <View style={tw`flex-row items-center gap-1 bg-black/10 px-2 py-1 rounded-full`}>
        <BatteryIcon size={16} color="#000" />
        <Text style={tw`text-black text-sm`}>
          {batteryLevel !== null ? `${Math.round(batteryLevel * 100)}%` : '...'}
        </Text>
      </View>
      <View style={tw`flex-row items-center gap-1 bg-black/10 px-2 py-1 rounded-full`}>
        <Sun size={16} color="#000" />
        <Text style={tw`text-black text-sm`}>
          {brightness !== null ? `${Math.round(brightness * 100)}%` : '...'}
        </Text>
      </View>
    </View>
  );
};

// Message component to display chat messages
const MessageBubble = ({ message, isUser }: any) => {
  const displayText = isUser ? (message.text || '') : (message.text ? String(message.text) : '');

  // Function to handle copying message text
  const copyMessageToClipboard = () => {
    Clipboard.setString(message.text);
    // Show a platform-specific notification
    if (Platform.OS === 'android') {
      ToastAndroid.show('Message copied to clipboard!', ToastAndroid.SHORT);
    } else {
      // Fallback for other platforms (like iOS) - you could use Alert or nothing
      Alert.alert('Copied!', 'Message copied to clipboard.');
    }
  };

  // Function to render AI text with bold formatting
  const renderAIResponse = (text: string) => {
    // Split the text by **
    const parts = text.split('**');
    const elements = [];

    for (let i = 0; i < parts.length; i++) {
      // Even indices are non-bold text
      if (i % 2 === 0) {
        if (parts[i]) { // Only add if the part is not empty
          elements.push(
            <Text key={`part-${i}`} style={{ fontFamily: 'GoogleSans-Regular', fontSize: 16, color: 'black' }}>
              {parts[i]}
            </Text>
          );
        }
      }
      // Odd indices are text that should be bold
      else {
        if (parts[i]) { // Only add if the part is not empty
          elements.push(
            <Text key={`part-${i}`} style={{ fontFamily: 'GoogleSans-Bold', fontSize: 16, color: 'black' }}>
              {parts[i]}
            </Text>
          );
        }
      }
    }
    return elements;
  };

  return (
    <MotiView
      from={isUser
        ? { opacity: 0, translateY: 10 }
        : { opacity: 0, translateY: 20, scale: 0.9 }
      }
      animate={isUser
        ? { opacity: 1, translateY: 0 }
        : { opacity: 1, translateY: 0, scale: 1 }
      }
      transition={isUser
        ? { type: 'timing', duration: 300 }
        : { type: 'spring', damping: 15, stiffness: 150 }
      }
      style={[
        tw`my-2 max-w-[80%]`,
        isUser ? tw`self-end` : tw`self-start flex-row`
      ]}
    >
      {/* Wrap the message content in TouchableOpacity */}
      <TouchableOpacity
        onPress={copyMessageToClipboard}
        activeOpacity={0.8} // Reduce opacity slightly on press
        style={[
          tw`p-3 rounded-[20px] py-3.5`,
          isUser
            ? tw`bg-[#BCFEFE] border-[#A2F6F7] border rounded-br-none`
            : tw`bg-[#F6F6F6] rounded-bl-none`
        ]}
      >
        {/* Conditional rendering based on isUser */}
        {isUser ? (
          // Render user message with standard Text
          <Text
            style={[
              tw`text-base`,
              tw`text-black`, // User text is always black
              { fontFamily: 'GoogleSans-Regular' }
            ]}
          >
            {message.text || ''}
          </Text>
        ) : (
          // Render AI message by parsing and applying styles
          // Wrap in a Text component to render inline
          <Text style={{ fontSize: 16, color: 'black', fontFamily: 'GoogleSans-Regular' }}>
             {renderAIResponse(displayText)}
          </Text>
        )}

        {message.time && (
          <Text
            style={[
              tw`text-xs text-gray-500 mt-1`,
              isUser ? tw`text-right` : tw`text-left`
            ]}
          >
            {message.time}
          </Text>
        )}
      </TouchableOpacity> {/* Close TouchableOpacity */}
    </MotiView>
  );
};

// Typing indicator compon  ent
const TypingIndicator = ({ isVisible }: { isVisible: boolean }) => {
  if (!isVisible) return null;

  return (
    <MotiView
      from={{ opacity: 0, translateY: 10 }}
      animate={{ opacity: 1, translateY: 0 }}
      transition={{ type: 'timing', duration: 300 }}
      style={tw`my-2 self-start`}
    >
      <View style={tw`p-3 rounded-[20px] bg-[#F6F6F6] rounded-bl-none flex-row items-center`}>
        <ActivityIndicator size="small" color="#000" />
        <Text style={tw`ml-2 text-sm text-gray-600`}>Typing...</Text>
      </View>
    </MotiView>
  );
};

// API endpoint for AI chat
const AI_API_URL = 'https://nx.ai.api.nityasha.com/chat';

export default function AI({ }: any) {
  const navigation = useNavigation();
  const [inputText, setInputText] = useState('');
  // Define message type with optional time property
  type Message = {
    id: number;
    text: string;
    isUser: boolean;
    time?: string;
  };

  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const inputRef = useRef<TextInput>(null);

  // Function to check battery level
  const checkBatteryLevel = async () => {
    try {
      const batteryLevel = await ExpoBattery.getBatteryLevelAsync();
      return `Your battery level is ${Math.round(batteryLevel * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't check your battery level.";
    }
  };

  // Function to check brightness
  const checkBrightness = async () => {
    try {
      const brightness = await Brightness.getBrightnessAsync();
      return `Your screen brightness is set to ${Math.round(brightness * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't check your screen brightness.";
    }
  };

  // Function to set brightness
  const setBrightness = async (level: number) => {
    try {
      await Brightness.setSystemBrightnessAsync(level);
      return `Screen brightness has been set to ${Math.round(level * 100)}%`;
    } catch (error) {
      return "Sorry, I couldn't adjust your screen brightness.";
    }
  };

  // Function to fetch weather information
  const checkWeather = async () => {
    try {
      // Request permission to access location
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        return "Sorry, I need location permission to fetch the weather.";
      }

      // Get current location
      let location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      const weatherApiUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&hourly=temperature_2m`;

      const response = await fetch(weatherApiUrl);
      if (!response.ok) {
        throw new Error(`Weather API request failed with status ${response.status}`);
      }
      const data = await response.json();

      if (data && data.hourly && data.hourly.time && data.hourly.temperature_2m) {
        // Find the current hour's temperature
        const now = new Date();
        // Ensure consistent format between API time and current time string
        const currentHourString = now.toISOString().slice(0, 13) + ':00';


        const currentTimeIndex = data.hourly.time.findIndex((time: string) => time.startsWith(currentHourString));

        if (currentTimeIndex !== -1 && currentTimeIndex < data.hourly.temperature_2m.length) {
          const currentTemperature = data.hourly.temperature_2m[currentTimeIndex];
          return `The current temperature at your location is ${currentTemperature}°C.`;
        } else {
          // Fallback to the first available temperature if current hour not found
          if (data.hourly.temperature_2m.length > 0) {
            const currentTemperature = data.hourly.temperature_2m[0];
            return `The temperature at your location is currently ${currentTemperature}°C according to the latest forecast data.`;
          }
          return "Could not find current temperature data for your location.";
        }
      } else {
        return "Sorry, I couldn't retrieve the weather information for your location.";
      }
    } catch (error) {
      console.error('Error fetching weather or location:', error);
      return "Sorry, I couldn't fetch the weather information for your location at the moment.";
    }
  };

  // Function to scroll to bottom of messages
  const scrollToBottom = (delay = 100) => {
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }
    }, delay);
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    // First immediate scroll attempt
    scrollToBottom(10);
    // Second attempt after animations likely complete
    scrollToBottom(300);
    // Final attempt after everything should be rendered
    scrollToBottom(600);
  }, [messages]);

  // Handle keyboard events to ensure scrolling when keyboard appears
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      scrollToBottom(100);
    });

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  // Function to send message to AI API
  const sendMessageToAI = async (userMessage: string) => {
    try {
      setIsLoading(true);

      // Check for illegal words
      const { hasIllegalWords } = checkForIllegalWords(userMessage);
      if (hasIllegalWords) {
        Alert.alert(
          'Inappropriate Content',
          `Your message contains inappropriate content that violates our terms of service. Please revise your message.`,
          [{ text: 'OK' }]
        );
        setIsLoading(false);
        return;
      }

      // Check for battery, brightness, weather, and train commands
      const lowerMessage = userMessage.toLowerCase();
      let response = '';

      if (lowerMessage.includes('battery') || lowerMessage.includes('battery level')) {
        response = await checkBatteryLevel();
      } else if (lowerMessage.includes('brightness') || lowerMessage.includes('screen brightness')) {
        if (lowerMessage.includes('set') || lowerMessage.includes('change')) {
          // Extract number from message (0-100)
          const match = lowerMessage.match(/\d+/);
          if (match) {
            const level = parseInt(match[0]) / 100;
            response = await setBrightness(Math.min(Math.max(level, 0), 1));
          } else {
            response = "Please specify a brightness level between 0 and 100.";
          }
        } else {
          response = await checkBrightness();
        }
      } else if (lowerMessage.includes('weather') || lowerMessage.includes('temperature')) { // Added weather condition
        response = await checkWeather();
      } else if (lowerMessage.includes('train status') || lowerMessage.includes('check train')) { // Added train status condition
        // Extract train number from message
        const trainNumberMatch = lowerMessage.match(/\d+/);
        if (trainNumberMatch) {
          const trainNumber = trainNumberMatch[0];
        } else {
          response = "Please provide a train number to check the status.";
        }
      }
      else {
        // Make API call for other messages
        const apiResponse = await fetch(AI_API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: `${userMessage}`
          }),
        });

        if (!apiResponse.ok) {
          throw new Error(`API request failed with status ${apiResponse.status}`);
        }

        const data = await apiResponse.json();
        response = data.response || data.message || "I'm sorry, I couldn't process that request.";
      }

      // Add AI response to messages with animation
      setMessages(prevMessages => [
        ...prevMessages,
        {
          id: Date.now() + 1,
          text: response,
          isUser: false,
          time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        }
      ]);

    } catch (error) {
      console.error('Error sending message to AI:', error);

      // Add error message
      setMessages(prevMessages => [
        ...prevMessages,
        {
          id: Date.now() + 1,
          text: "I'm sorry, I encountered an error processing your request. Please try again later.",
          isUser: false,
          time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        }
      ]);
    } finally {
      // Keep loading for a short moment to ensure smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  // Handle sending a message
  // Modified to accept an optional message string
  const handleSendMessage = (messageToSend?: string) => {
    // Determine the message to send: use the argument if provided, otherwise use inputText
    const message = messageToSend ? messageToSend.trim() : inputText.trim();

    // Check if message is empty or loading
    if (!message || isLoading) return;

    // Add user message to chat
    const newMessage = {
      id: Date.now(),
      text: message, // Use the determined message
      isUser: true,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prevMessages => [...prevMessages, newMessage]);

    // Ensure scroll happens after message is added
    scrollToBottom(10);

    // Clear input only if sending from the input field
    if (!messageToSend) {
      setInputText('');
    }


    // Focus the input field for next message (optional, depends on desired UX)
    // inputRef.current?.focus(); // You might want to remove this if tapping a suggestion shouldn't focus the input

    // Send to AI API
    sendMessageToAI(message); // Use the determined message

    // Additional scroll after a short delay to ensure UI has updated
    scrollToBottom(300);
  };

  return (
    <ImageBackground source={require('@/assets/screens/screen7th.png')} style={tw`h-full bg-white`}>

      {/* Chat messages */}
      <ScrollView
        ref={scrollViewRef}
        style={tw`flex-1 px-4`}
        contentContainerStyle={[tw`pb-4`, messages.length === 0 && !isLoading ? tw`flex-1 justify-center items-center` : {}]}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => scrollToBottom(50)}
        keyboardShouldPersistTaps="handled"
      >
        {messages.length === 0 && !isLoading ? (
          <View style={tw`flex py-6 h-full w-full`}>
            <Text style={[tw`text-4xl`, { fontFamily: 'Helvetica_bold' }]}>
              Hello, I'm Nityasha
            </Text>
            <Text style={[tw`text-[#7b7e7c] text-5xl `, { fontFamily: 'Helvetica_bold' }]}>How can I help you today?</Text>

            <View style={tw`mt-5`}>
              <ScrollView
                horizontal
                style={tw`w-full h-60`}
                showsHorizontalScrollIndicator={false} // optional for cleaner UI
              >
                <TouchableOpacity
                  style={tw`bg-[#F0F4F9] rounded-[40px] h-60 w-[250px] py-4 px-4 mr-4 flex items-center justify-between`}
                  onPress={() => handleSendMessage('Suggest beaches to visit in a city, including details')}
                >
                  <View style={tw`w-full`}>
                    <Text style={[tw`text-[20px] leading-[28px]`, { fontFamily: 'Helvetica_bold' }]}>Suggest beaches to visit in a city, including details</Text>
                  </View>
                  <View style={tw`w-full items-end`}>
                    <View style={tw`bg-white w-[40px] h-[40px] flex items-center justify-center rounded-full p-2`}>
                      <Compass size={24} color={'black'} />
                    </View>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={tw`bg-[#F0F4F9] rounded-[40px] h-60 w-[250px] py-4 px-4 mr-4 flex items-center justify-between`}
                  onPress={() => handleSendMessage('What are tips to improve public speaking skills?')}
                >
                  <View style={tw`w-full`}>
                    <Text style={[tw`text-[20px] leading-[28px]`, { fontFamily: 'Helvetica_bold' }]}>What are tips to improve public speaking skills?</Text>
                  </View>
                  <View style={tw`w-full items-end`}>
                    <View style={tw`bg-white w-[40px] h-[40px] flex items-center justify-center rounded-full p-2`}>
                      <Compass size={24} color={'black'} />
                    </View>
                  </View>
                </TouchableOpacity>
                </ScrollView>
            </View>
          </View>
        ) : (
          <>
            {messages.map(message => (
              <MessageBubble
                key={message.id}
                message={message}
                isUser={message.isUser}
              />
            ))}
          </>
        )}

        {/* Typing indicator */}
        <TypingIndicator isVisible={isLoading} />
      </ScrollView>
      <KeyboardAvoidingView behavior={"padding"} keyboardVerticalOffset={100}>

        {/* Input area */}
        <View style={tw`bg-[#F0F4F9] w-[90%] h-[60px] mb-4  rounded-full px-3 flex-row mx-5`}>
          <TextInput
            ref={inputRef}
            style={tw`flex-1 bg-gray-100 rounded-full px-4 py-2 mr-2`}
            placeholder="Enter a prompt here"
            value={inputText}
            onChangeText={setInputText}
            editable={!isLoading}
            onSubmitEditing={() => handleSendMessage()}
            onFocus={() => scrollToBottom(100)}
          />
          <TouchableOpacity
            style={[
              tw`mr-3 rounded-full items-center justify-center`]}
            onPress={() => handleSendMessage()}
            disabled={isLoading || !inputText.trim()}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#000" />
            ) : (
              <Send size={20} color="#000000" />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </ImageBackground>
  )
}