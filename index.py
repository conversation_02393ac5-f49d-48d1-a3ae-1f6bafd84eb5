import threading
import requests

DOWNLOAD_URL = "http://speed.hetzner.de/1GB.bin"
THREADS = 80  # You can increase to 30, 40, etc.

def download_forever():
    while True:
        try:
            with requests.get(DOWNLOAD_URL, stream=True, timeout=10) as r:
                total = 0
                for chunk in r.iter_content(chunk_size=8192):
                    if chunk:
                        total += len(chunk)
                print(f"Downloaded {total / 1e6:.2f} MB")
        except Exception as e:
            print(f"Error: {e}")

# Start threads
print(f"ZAMMING WiFi with {THREADS} threads... Ctrl+C to stop.")
for _ in range(THREADS):
    threading.Thread(target=download_forever, daemon=True).start()

# Keep alive
try:
    while True:
        pass
except KeyboardInterrupt:
    print("ZAM stopped.")
