import { initializeApp, getApps } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import config from '@/utils/config';

const firebaseConfig = {
  apiKey: "AIzaSyC6mmUk8v9Pwp7upeb5Qscc1vOz_QAkMGI",
  authDomain: "black-function-380411.firebaseapp.com",
  databaseURL: config.FIREBASE_URL, // Use environment variable
  projectId: "black-function-380411",
  storageBucket: "black-function-380411.appspot.com",
  messagingSenderId: "262010279934",
  appId: "1:262010279934:android:b176a5a796529350df12af",
  measurementId: "G-SOMEID"
};

// Check if the app is already initialized
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const db = getDatabase(app);

export { db };