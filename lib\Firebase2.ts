import { initializeApp } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import config from '@/utils/config';

const firebaseConfig = {
  apiKey: "AIzaSyC6mmUk8v9Pwp7upeb5Qscc1vOz_QAkMGI",
  authDomain: "black-function-380411.firebaseapp.com",
  databaseURL: config.FIREBASE_URL, // Use environment variable
  projectId: "black-function-380411",
  storageBucket: "black-function-380411.appspot.com",
  messagingSenderId: "262010279934",
  appId: "1:262010279934:android:b176a5a796529350df12af",
  measurementId: "G-SOMEID"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Get Realtime Database instance
const db = getDatabase(app);

export { db };