/**
 * Format a timestamp into a relative time string (e.g., "2m ago", "Yesterday")
 */
export function formatRelativeTime(timestamp: number): string {
    const now = Date.now()
    const date = new Date(timestamp)
    const secondsAgo = Math.floor((now - timestamp) / 1000)
  
    // Less than a minute
    if (secondsAgo < 60) {
      return "Just now"
    }
  
    // Less than an hour
    if (secondsAgo < 3600) {
      const minutes = Math.floor(secondsAgo / 60)
      return `${minutes}m ago`
    }
  
    // Less than a day
    if (secondsAgo < 86400) {
      const hours = Math.floor(secondsAgo / 3600)
      return `${hours}h ago`
    }
  
    // Less than two days
    if (secondsAgo < 172800) {
      return "Yesterday"
    }
  
    // Less than a week
    if (secondsAgo < 604800) {
      const days = Math.floor(secondsAgo / 86400)
      return `${days}d ago`
    }
  
    // Format as date
    const options: Intl.DateTimeFormatOptions = {
      month: "short",
      day: "numeric",
    }
  
    // Add year if not current year
    if (date.getFullYear() !== new Date().getFullYear()) {
      options.year = "numeric"
    }
  
    return date.toLocaleDateString(undefined, options)
  }
  
  