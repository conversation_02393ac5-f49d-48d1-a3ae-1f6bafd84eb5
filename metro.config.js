// metro.config.js

const { getDefaultConfig } = require('expo/metro-config');
const { wrapWithReanimatedMetroConfig } = require('react-native-reanimated/metro-config');

// Get default Expo config
let config = getDefaultConfig(__dirname);

// Ensure resolver.sourceExts exists
config.resolver.sourceExts = config.resolver.sourceExts || [];
if (!config.resolver.sourceExts.includes('cjs')) {
  config.resolver.sourceExts.push('cjs');
}

// Wrap with Reanimated support
module.exports = wrapWithReanimatedMetroConfig(config);
