{"name": "nity<PERSON>a", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@11labs/react": "^0.1.4", "@expo/config-plugins": "~9.0.0", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli-server-api": "^15.0.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "2.9.0", "@react-native-voice/voice": "^3.2.4", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/elements": "^2.0.3", "@react-navigation/native": "^7.0.0", "@react-navigation/native-stack": "^7.0.0", "@react-navigation/stack": "^7.0.0", "@supabase/supabase-js": "^2.48.1", "@umrashrf/react-native-snap-carousel": "^3.9.1", "axios": "^1.7.9", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "denque": "^2.1.0", "events": "^3.3.0", "expo": "~52.0.46", "expo-av": "~15.0.2", "expo-battery": "~9.0.2", "expo-blur": "~14.0.3", "expo-brightness": "~13.0.3", "expo-build-properties": "~0.13.3", "expo-checkbox": "~4.0.1", "expo-constants": "~17.0.3", "expo-contacts": "^14.0.5", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-font": "~13.0.1", "expo-haptics": "^14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-navigation-bar": "~4.0.9", "expo-notifications": "~0.29.14", "expo-random": "~14.0.1", "expo-router": "~4.0.21", "expo-screen-orientation": "~8.0.4", "expo-secure-store": "^14.0.1", "expo-sms": "~13.0.1", "expo-speech": "^13.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "express": "^4.21.2", "firebase": "^10.14.1", "fs": "^0.0.1-security", "install": "^0.13.0", "lodash": "^4.17.21", "lru.min": "^1.1.1", "lucide-react-native": "^0.445.0", "metro": "^0.81.0", "metro-config": "^0.81.0", "metro-resolver": "^0.81.0", "moti": "^0.30.0", "net": "^1.0.2", "path": "^0.12.7", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-animatable": "^1.4.0", "react-native-base64": "^0.2.1", "react-native-confetti-cannon": "^1.5.2", "react-native-crypto": "^2.2.0", "react-native-date-picker": "^5.0.10", "react-native-fast-image": "^8.6.3", "react-native-feather": "^1.1.2", "react-native-gesture-handler": "~2.20.2", "react-native-image-picker": "^7.1.2", "react-native-intent": "^1.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.13.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.16.7", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "~4.4.0", "react-native-sha256": "^1.4.10", "react-native-sim-cards-manager": "^1.0.27", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "^15.8.0", "react-native-tab-view": "^4.0.6", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "react-native-webview": "^13.12.5", "react-navigation": "^5.0.0", "semver": "^7.7.1", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "sqlstring": "^2.3.3", "stream": "^0.0.3", "timers": "^0.1.1", "tls": "^0.0.1", "twrnc": "^4.5.1", "undefined": "expo-auth-session/providers/google", "ws": "^8.18.1"}, "devDependencies": {"@babel/core": "^7.24.0", "@react-native-community/cli": "^15.1.3", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native-base64": "^0.2.2", "@types/react-native-razorpay": "^2.2.6", "@types/react-native-snap-carousel": "^3.8.11", "@types/react-test-renderer": "^18.0.7", "babel-plugin-module-resolver": "^5.0.2", "detox": "^20.34.0", "jest": "^29.4.0", "jest-expo": "~52.0.6", "react-test-renderer": "18.2.0", "tailwindcss": "^3.4.15", "typescript": "~5.3.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["expo-random", "react-native-crypto", "react-native-skeleton-placeholder", "react-native-snap-carousel", "lucide-react-native", "react-native-fast-image", "react-native-razorpay", "react-native-confetti-cannon", "react-native-keyboard-aware-scroll-view"], "listUnknownPackages": false}, "skipVersionCheck": true}}, "private": true, "resolutions": {"@expo/config-plugins": "~9.0.0"}}