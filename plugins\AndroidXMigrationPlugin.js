// AndroidXMigrationPlugin.js
const { withGradleProperties } = require('@expo/config-plugins');

const withAndroidXMigration = (config) => {
  return withGradleProperties(config, (config) => {
    // Add or update the AndroidX properties
    config.modResults = config.modResults.filter(
      (item) => 
        item.type !== 'property' || 
        (item.key !== 'android.useAndroidX' && 
         item.key !== 'android.enableJetifier')
    );
    
    config.modResults.push({
      type: 'property',
      key: 'android.useAndroidX',
      value: 'true',
    });
    
    config.modResults.push({
      type: 'property',
      key: 'android.enableJetifier',
      value: 'true',
    });
    
    return config;
  });
};

module.exports = withAndroidXMigration;
