//PhonepePlugin.js
const { withProjectBuildGradle } = require('@expo/config-plugins');

const withMyPlugin = (config) => {
    return withProjectBuildGradle(config, (config) => {
        if (config.modResults.language === 'groovy') {
            if(!config.modResults.contents.includes("phonepe.mycloudrepo.io")) {
                // to avoid multiple occurence
                config.modResults.contents = modify(config.modResults.contents)
            }
        } else {
            throw new Error("Can't add maven repository to the build.gradle because the project is using the 'kts' language.");
        }
        return config;
    });
};

function modify (str) {
    // Find the first occurrence of "google()"
    const firstIndex = str.indexOf("google()");

    // Check if the first occurrence is found
    if (firstIndex !== -1) {
        // Find the second occurrence starting from the index after the first occurrence
        const secondIndex = str.indexOf("google()", firstIndex + 1);

        // Check if the second occurrence is found
        if (secondIndex !== -1) {
            const result = str.slice(0, secondIndex + 8) + `
            maven {
                url  "https://phonepe.mycloudrepo.io/public/repositories/phonepe-intentsdk-android"
           }
            ` + str.slice(secondIndex + 8);
            return result;
        }
    }

    // Return the original string if not found
    return str;
}




module.exports = withMyPlugin;
