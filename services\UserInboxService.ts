import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/Firebase';
import { ref, onValue, update, set } from 'firebase/database';

// Define types
export interface MappedUser {
  id: number;
  name: string;
  initial: string;
  contactName?: string;
  phoneNumber?: string;
  originalEmail?: string;
  lastMessage: string;
  timestamp: string;
  is_online: boolean;
  last_seen?: string;
  statuses?: Status[];
  hasUnseenStory?: boolean;
  hasStatus?: boolean;
  messageStatus?: 'pending' | 'sent' | 'read';
  unreadCount?: number;
}

export interface Status {
  id: number;
  user_id: string;
  media_url: string;
  created_at: string;
}

export interface UnreadCounts {
  [key: string]: number;
}

// API base URL
const API_BASE_URL = 'http://nityasha-chat-rtdb.nityasha.com/api';

// Cache keys for storing data in AsyncStorage
const INBOX_DATA_CACHE_KEY = 'user_inbox_data';
const INBOX_DATA_TIMESTAMP_KEY = 'user_inbox_data_timestamp';
const CACHE_EXPIRY_TIME = 1000 * 60 * 30; // 30 minutes in milliseconds

/**
 * Standardized phone number cleaning function
 */
export const cleanPhoneNumber = (phoneNumber: string | null | undefined): string => {
  if (!phoneNumber) return '';

  // Step 1: Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // Step 2: Handle country codes
  // Remove leading country code if present (e.g., 91 for India)
  if (cleaned.startsWith('91') && cleaned.length > 10) {
    cleaned = cleaned.substring(2);
  }

  // Step 3: Remove leading zeros
  cleaned = cleaned.replace(/^0+/, '');

  // Step 4: Ensure we have a valid number (at least 10 digits for India)
  if (cleaned.length < 10) {
    // Number is too short, but we'll return it anyway
  }

  return cleaned;
};

/**
 * Get the current user ID from AsyncStorage
 */
export const getCurrentUserId = async (): Promise<string | null> => {
  try {
    const userSession = await AsyncStorage.getItem('userSession');
    if (!userSession) return null;

    const parsedSession = JSON.parse(userSession);
    return parsedSession?.userId?.toString() || null;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
};

/**
 * Get all user inbox data from the server
 */
export const getUserInboxData = async (forceRefresh = false): Promise<{
  users: MappedUser[];
  unreadCounts: UnreadCounts;
  totalUnread: number;
  statuses: Status[];
} | null> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) return null;

    // Check if we have cached data and it's not expired
    if (!forceRefresh) {
      const cachedData = await AsyncStorage.getItem(INBOX_DATA_CACHE_KEY);
      const cachedTimestamp = await AsyncStorage.getItem(INBOX_DATA_TIMESTAMP_KEY);

      if (cachedData && cachedTimestamp) {
        const now = Date.now();
        const timestamp = parseInt(cachedTimestamp);

        if (now - timestamp < CACHE_EXPIRY_TIME) {
          return JSON.parse(cachedData);
        }
      }
    }

    // Fetch fresh data from the server
    const response = await fetch(`${API_BASE_URL}/user-inbox-data/${userId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch user inbox data');
    }

    const data = await response.json();

    // Cache the data
    await AsyncStorage.setItem(INBOX_DATA_CACHE_KEY, JSON.stringify(data));
    await AsyncStorage.setItem(INBOX_DATA_TIMESTAMP_KEY, Date.now().toString());

    return data;
  } catch (error) {
    console.error('Error fetching user inbox data:', error);
    return null;
  }
};

/**
 * Match contacts with users
 */
export const matchContacts = async (phoneNumbers: string[]): Promise<any[]> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) return [];

    const response = await fetch(`${API_BASE_URL}/match-contacts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        phoneNumbers,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to match contacts');
    }

    const data = await response.json();
    return data.matches || [];
  } catch (error) {
    console.error('Error matching contacts:', error);
    return [];
  }
};

/**
 * Update user online status in both MySQL and Firebase
 */
export const updateOnlineStatus = async (isOnline: boolean): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) return false;

    // Update in Firebase directly
    const userStatusRef = ref(db, `users/${userId}/status`);
    await set(userStatusRef, {
      state: isOnline ? 'online' : 'offline',
      lastSeen: Date.now(),
    });

    // Also update in MySQL via API
    const response = await fetch(`${API_BASE_URL}/update-online-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        isOnline,
      }),
    });

    return response.ok;
  } catch (error) {
    console.error('Error updating online status:', error);
    return false;
  }
};

/**
 * Mark messages as read
 */
export const markMessagesAsRead = async (senderId: number | string): Promise<boolean> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) return false;

    // Update in Firebase directly
    const chatId = [userId, senderId.toString()].sort().join('_');
    const userChatRef = ref(db, `user_chats/${userId}/${senderId}`);
    await update(userChatRef, { unread: 0 });

    // Get all messages in this chat
    const messagesRef = ref(db, `user_messages/${chatId}`);
    onValue(messagesRef, (snapshot) => {
      const data = snapshot.val();
      if (!data) return;

      // Batch updates for better performance
      const updates: {[key: string]: any} = {};

      // Find messages from the other user that aren't marked as read
      Object.entries(data).forEach(([messageId, message]: [string, any]) => {
        if (message.senderId === senderId.toString() && message.status !== 'read') {
          // Add to batch update
          updates[`user_messages/${chatId}/${messageId}/status`] = 'read';
          updates[`user_messages/${chatId}/${messageId}/isRead`] = true;
          updates[`user_messages/${chatId}/${messageId}/readAt`] = Date.now();
        }
      });

      // Apply all updates in a single operation if there are any
      if (Object.keys(updates).length > 0) {
        update(ref(db), updates);
      }
    }, { onlyOnce: true }); // Only run once

    // Also update in MySQL via API
    const response = await fetch(`${API_BASE_URL}/mark-messages-read`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        senderId,
      }),
    });

    // Invalidate cache
    await AsyncStorage.removeItem(INBOX_DATA_CACHE_KEY);

    return response.ok;
  } catch (error) {
    console.error('Error marking messages as read:', error);
    return false;
  }
};

/**
 * Get unread message counts
 */
export const getUnreadCounts = async (): Promise<{
  pendingMessages: UnreadCounts;
  totalPending: number;
} | null> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) return null;

    const response = await fetch(`${API_BASE_URL}/unread-counts/${userId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch unread counts');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching unread counts:', error);
    return null;
  }
};

/**
 * Add a contact
 */
export const addContact = async (contactName: string, phoneNumber: string): Promise<any> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) throw new Error('User not logged in');

    const response = await fetch(`${API_BASE_URL}/add-contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        contactName,
        phoneNumber,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to add contact');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(INBOX_DATA_CACHE_KEY);

    return await response.json();
  } catch (error) {
    console.error('Error adding contact:', error);
    throw error;
  }
};

/**
 * Get all contacts
 */
export const getContacts = async (): Promise<any[]> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) return [];

    const response = await fetch(`${API_BASE_URL}/contacts/${userId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch contacts');
    }

    const data = await response.json();
    return data || [];
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return [];
  }
};

export default {
  getUserInboxData,
  matchContacts,
  updateOnlineStatus,
  markMessagesAsRead,
  getUnreadCounts,
  addContact,
  getContacts,
  cleanPhoneNumber,
  getCurrentUserId,
};
