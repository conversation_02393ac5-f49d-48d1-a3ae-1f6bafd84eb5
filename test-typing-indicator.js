// Test script for typing indicator functionality
// This script simulates two users chatting with each other
// and verifies that the typing indicator only shows up in the correct chat

const { ref, update, onValue } = require('firebase/database');
const { db } = require('./lib/Firebase');

// Simulate two users
const user1 = {
  id: 'user1',
  name: 'User 1'
};

const user2 = {
  id: 'user2',
  name: 'User 2'
};

const user3 = {
  id: 'user3',
  name: 'User 3'
};

// Function to update typing status
function updateTypingStatus(userId, isTyping, typingToUserId) {
  const userStatusRef = ref(db, `users/${userId}/status`);
  
  update(userStatusRef, {
    isTyping,
    typingTimestamp: Date.now(),
    typingInChatWith: isTyping ? typingToUserId : null
  });
  
  console.log(`${userId} typing status updated: isTyping=${isTyping}, typingToUserId=${typingToUserId || 'none'}`);
}

// Function to listen for typing status
function listenForTypingStatus(userId, otherUserId) {
  const userStatusRef = ref(db, `users/${otherUserId}/status`);
  
  onValue(userStatusRef, (snapshot) => {
    const status = snapshot.val();
    if (status) {
      const isTypingToMe = status.isTyping && status.typingInChatWith === userId;
      console.log(`${otherUserId} typing status for ${userId}: ${isTypingToMe ? 'TYPING TO ME' : 'NOT TYPING TO ME'}`);
    }
  });
}

// Test scenario
async function runTest() {
  console.log('Starting typing indicator test...');
  
  // Set up listeners
  listenForTypingStatus(user1.id, user2.id);
  listenForTypingStatus(user1.id, user3.id);
  listenForTypingStatus(user2.id, user1.id);
  listenForTypingStatus(user3.id, user1.id);
  
  // Wait for listeners to be set up
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test 1: User 1 types to User 2
  console.log('\nTest 1: User 1 types to User 2');
  updateTypingStatus(user1.id, true, user2.id);
  
  // Wait for status to update
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 2: User 1 stops typing
  console.log('\nTest 2: User 1 stops typing');
  updateTypingStatus(user1.id, false, null);
  
  // Wait for status to update
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 3: User 1 types to User 3
  console.log('\nTest 3: User 1 types to User 3');
  updateTypingStatus(user1.id, true, user3.id);
  
  // Wait for status to update
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 4: User 2 types to User 1
  console.log('\nTest 4: User 2 types to User 1');
  updateTypingStatus(user2.id, true, user1.id);
  
  // Wait for status to update
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Clean up
  updateTypingStatus(user1.id, false, null);
  updateTypingStatus(user2.id, false, null);
  
  console.log('\nTest completed!');
}

// Run the test
runTest().catch(console.error);
