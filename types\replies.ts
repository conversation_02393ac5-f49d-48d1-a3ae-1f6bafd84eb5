// Reply type definitions for the application

export interface Reply {
  id: string;
  post_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  parent_reply_id?: string;
  username?: string;
  user_avatar?: string;
  likes_count?: number;
  is_liked?: boolean;
  replies?: Reply[];
}

export interface CreateReplyRequest {
  post_id: string;
  user_id: string;
  content: string;
  parent_reply_id?: string;
}

export interface ReplyResponse {
  success: boolean;
  message: string;
  data?: Reply;
}

export interface RepliesResponse {
  success: boolean;
  message: string;
  data?: Reply[];
}

// Default export to fix warning
const replyTypes = {
  Reply,
  CreateReplyRequest,
  ReplyResponse,
  RepliesResponse
};

export default replyTypes;
