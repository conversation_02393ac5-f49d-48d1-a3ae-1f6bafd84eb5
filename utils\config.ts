/**
 * Configuration utility for Nityasha app
 *
 * This file provides a centralized way to access environment variables
 * and configuration settings across the app. It uses Expo's Constants.expoConfig.extra
 * to access environment variables set in eas.json for different build profiles.
 */

import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Default values for local development
const DEFAULT_CONFIG = {
  API_URL: 'https://chats.api.asia.nityasha.com',
  FIREBASE_URL: 'https://black-function-380411-default-rtdb.firebaseio.com',
  SUPABASE_URL: 'https://mrfxsffdigwixuvgiecp.supabase.co',
  SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.3_SWmjEOgn5jdnLVlsRTiLmxqYFtDR2AraPQISTNz04',
  TOGETHER_API_URL: 'https://nx.ai.api.nityasha.com/chat',
  BALANCE_WS_URL: 'wss://balance-app-api.nityasha.com',
  USER_API_URL: 'https://api.nityasha.com/api/v1/users',
  SEARCH_API_URL: 'https://api.search.nityasha.com',
  OFFICIAL_ACCOUNTS_API_URL: 'https://api.search.nityasha.com/official_accounts',
  SIGNUP_API_URL: 'https://nityasha.vercel.app/api/v1/users/register',
  CONSULTANTS_API_URL: 'https://nityasha.vercel.app/api/v1/consultants',
};

// Production configuration
const PRODUCTION_CONFIG = {
  API_URL: 'https://api.nityasha.com',
  FIREBASE_URL: 'https://black-function-380411-default-rtdb.firebaseio.com',
  SUPABASE_URL: 'https://mrfxsffdigwixuvgiecp.supabase.co',
  SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.3_SWmjEOgn5jdnLVlsRTiLmxqYFtDR2AraPQISTNz04',
  TOGETHER_API_URL: 'https://ai.api.nityasha.com/chat',
  BALANCE_WS_URL: 'wss://balance-api.nityasha.com',
  USER_API_URL: 'https://api.nityasha.com/api/v1/users',
  SEARCH_API_URL: 'https://api.nityasha.com/search',
  OFFICIAL_ACCOUNTS_API_URL: 'https://api.nityasha.com/official_accounts',
  SIGNUP_API_URL: 'https://api.nityasha.com/api/v1/users/register',
  CONSULTANTS_API_URL: 'https://api.nityasha.com/api/v1/consultants',
  COMMUNITY_API_URL: 'https://api.nityasha.com/api/channels',
};

/**
 * Get configuration values from environment variables or defaults
 */
const getConfig = (useProduction = false) => {
  // Access environment variables from Expo Constants
  const processEnv = process.env || {};

  // Use production config if specified
  const baseConfig = useProduction ? PRODUCTION_CONFIG : DEFAULT_CONFIG;

  return {
    API_URL: processEnv.EXPO_PUBLIC_API_URL || baseConfig.API_URL,
    FIREBASE_URL: processEnv.EXPO_PUBLIC_FIREBASE_URL || baseConfig.FIREBASE_URL,
    SUPABASE_URL: processEnv.EXPO_PUBLIC_SUPABASE_URL || baseConfig.SUPABASE_URL,
    SUPABASE_ANON_KEY: baseConfig.SUPABASE_ANON_KEY, // This should be handled more securely in a real app
    TOGETHER_API_URL: processEnv.EXPO_PUBLIC_TOGETHER_API_URL || baseConfig.TOGETHER_API_URL,
    BALANCE_WS_URL: processEnv.EXPO_PUBLIC_BALANCE_WS_URL || baseConfig.BALANCE_WS_URL,
    USER_API_URL: processEnv.EXPO_PUBLIC_USER_API_URL || baseConfig.USER_API_URL,
    SEARCH_API_URL: processEnv.EXPO_PUBLIC_SEARCH_API_URL || baseConfig.SEARCH_API_URL,
    OFFICIAL_ACCOUNTS_API_URL: baseConfig.OFFICIAL_ACCOUNTS_API_URL,
    SIGNUP_API_URL: baseConfig.SIGNUP_API_URL,
    CONSULTANTS_API_URL: baseConfig.CONSULTANTS_API_URL,
    COMMUNITY_API_URL: baseConfig.COMMUNITY_API_URL || 'https://api-asia-communtiy.nityasha.com/api/channels',
  };
};

// Default configuration
export const config = getConfig();

// Helper function to determine if we're in production mode based on NODE_ENV
export const isProduction = () => {
  return process.env.NODE_ENV === 'production';
};

// Prodiston functions for manually toggling production mode
let _isProdistonEnabled = false;

// Enable or disable prodiston (production mode)
export const setProdiston = async (enabled: boolean) => {
  _isProdistonEnabled = enabled;
  try {
    await AsyncStorage.setItem('prodiston_enabled', enabled ? 'true' : 'false');
  } catch (error) {
    console.error('Error saving prodiston state:', error);
  }
};

// Check if prodiston is enabled
export const isProdistonEnabled = async (): Promise<boolean> => {
  try {
    const value = await AsyncStorage.getItem('prodiston_enabled');
    return value === 'true';
  } catch (error) {
    console.error('Error reading prodiston state:', error);
    return false;
  }
};

// Get current prodiston state without async
export const getProdistonState = (): boolean => {
  return _isProdistonEnabled;
};

// Get config based on current prodiston state
export const getProdistonConfig = () => {
  return getConfig(_isProdistonEnabled);
};

export default config;
