import AsyncStorage from '@react-native-async-storage/async-storage';

// Constants
const LAST_SCREEN_KEY = 'last_visited_screen';

/**
 * Save the last visited screen to AsyncStorage
 * @param screenName The name of the screen to save
 * @param params Optional parameters for the screen
 */
export const saveLastVisitedScreen = async (screenName: string, params?: any) => {
  try {
    const screenData = {
      name: screenName,
      params: params || {},
      timestamp: Date.now()
    };
    await AsyncStorage.setItem(LAST_SCREEN_KEY, JSON.stringify(screenData));
    console.log(`Saved last visited screen: ${screenName}`);
  } catch (error) {
    console.error('Error saving last visited screen:', error);
  }
};

/**
 * Get the last visited screen from AsyncStorage
 * @returns The last visited screen data or null if not found
 */
export const getLastVisitedScreen = async () => {
  try {
    const screenData = await AsyncStorage.getItem(LAST_SCREEN_KEY);
    if (screenData) {
      return JSON.parse(screenData);
    }
    return null;
  } catch (error) {
    console.error('Error getting last visited screen:', error);
    return null;
  }
};

/**
 * Clear the last visited screen from AsyncStorage
 */
export const clearLastVisitedScreen = async () => {
  try {
    await AsyncStorage.removeItem(LAST_SCREEN_KEY);
    console.log('Cleared last visited screen');
  } catch (error) {
    console.error('Error clearing last visited screen:', error);
  }
};
