/**
 * Utility functions for phone number handling
 */

/**
 * Formats a phone number with country code
 * @param phoneNumber The phone number to format
 * @param countryCode The country code (default: '+91' for India)
 * @returns Formatted phone number with country code
 */
export const formatPhoneNumber = (phoneNumber: string, countryCode: string = '+91'): string => {
  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  // Check if the phone number already has a country code
  if (digitsOnly.startsWith('91') && digitsOnly.length > 10) {
    return `+${digitsOnly}`;
  }
  
  // Add the country code if it doesn't have one
  return `${countryCode}${digitsOnly}`;
};

/**
 * Validates if a phone number is valid
 * @param phoneNumber The phone number to validate
 * @returns Boolean indicating if the phone number is valid
 */
export const isValidPhoneNumber = (phoneNumber: string): boolean => {
  // Basic validation - can be enhanced based on requirements
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  return digitsOnly.length >= 10;
};

/**
 * Extracts the local part of a phone number (without country code)
 * @param phoneNumber The phone number with country code
 * @returns The local part of the phone number
 */
export const getLocalPhoneNumber = (phoneNumber: string): string => {
  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  // If it starts with country code (91 for India), remove it
  if (digitsOnly.startsWith('91') && digitsOnly.length > 10) {
    return digitsOnly.substring(2);
  }
  
  return digitsOnly;
};

// Default export to fix warning
const phoneUtils = {
  formatPhoneNumber,
  isValidPhoneNumber,
  getLocalPhoneNumber
};

export default phoneUtils;
